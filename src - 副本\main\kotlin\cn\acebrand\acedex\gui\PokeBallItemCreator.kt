package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.data.AllGenerationsProgress
import cn.acebrand.acedex.util.CobblemonItemHelper
import com.cobblemon.mod.common.api.pokeball.PokeBalls
import com.cobblemon.mod.common.pokeball.PokeBall
import org.bukkit.Material
import org.bukkit.enchantments.Enchantment
import org.bukkit.inventory.ItemFlag
import org.bukkit.inventory.ItemStack

/**
 * 精灵球物品创建器
 * 为分类主菜单创建像精灵模型一样的精灵球显示材质模型
 * 参考 PokemonItemCreator 的实现方式
 */
class PokeBallItemCreator(private val plugin: AceDex) {

    /**
     * 为世代分类创建精灵球物品（主要方法）- 使用配置的材质
     */
    fun createGenerationPokeBallItem(generationId: String, hasCaught: Boolean = false, isCompleted: Boolean = false): ItemStack {
        // 使用配置的材质创建物品
        val materialConfig = plugin.config.getGenerationMaterial(generationId)
        val item = parseGenerationMaterial(materialConfig)

        // 应用自定义显示效果
        return applyPokeBallDisplay(item, generationId, hasCaught, isCompleted)
    }

    /**
     * 解析世代材质配置
     * 支持多种格式：
     * 1. 普通材质: "ENDER_PEARL"
     * 2. 普通材质+CustomModelData: "PAPER:12345"
     */
    private fun parseGenerationMaterial(materialConfig: String): ItemStack {
        return when {
            // Paper CustomModelData 格式: PAPER:CustomModelData数字
            materialConfig.startsWith("PAPER:", ignoreCase = true) -> {
                val parts = materialConfig.split(":")
                val item = ItemStack(org.bukkit.Material.PAPER)
                if (parts.size >= 2) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }

            // Cobblemon 材质格式: COBBLEMON_POKE_BALL
            materialConfig.startsWith("COBBLEMON_", ignoreCase = true) -> {
                createCobblemonItem(materialConfig)
            }

            // 普通 Minecraft 材质，支持 CustomModelData
            else -> {
                val parts = materialConfig.split(":")
                val material = try {
                    org.bukkit.Material.valueOf(parts[0].uppercase())
                } catch (e: Exception) {
                    // 记录警告并使用默认材质
                    plugin.logger.warning("无效的材质名称: ${parts[0]}, 使用默认材质 PAPER")
                    org.bukkit.Material.PAPER
                }
                val item = ItemStack(material)

                if (parts.size > 1) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }
        }
    }

    /**
     * 创建 Cobblemon 物品
     * 尝试多种方法创建真正的Cobblemon物品
     */
    private fun createCobblemonItem(materialConfig: String): ItemStack {
        // 方法1：尝试直接通过Bukkit Material获取
        try {
            val material = org.bukkit.Material.valueOf(materialConfig.uppercase())
            val item = ItemStack(material)
            if (plugin.config.enableDebug) {
                plugin.logger.info("成功通过Bukkit Material创建Cobblemon物品: $materialConfig -> ${material.name}")
            }
            return item
        } catch (e: IllegalArgumentException) {
            if (plugin.config.enableDebug) {
                plugin.logger.info("Material不存在: $materialConfig")
                // 列出所有包含COBBLEMON的Material
                val cobblemonMaterials = org.bukkit.Material.values().filter {
                    it.name.contains("COBBLEMON", ignoreCase = true)
                }
                if (cobblemonMaterials.isNotEmpty()) {
                    plugin.logger.info("可用的Cobblemon Materials: ${cobblemonMaterials.map { it.name }}")
                } else {
                    plugin.logger.info("没有找到任何Cobblemon Materials")
                }
            }
        } catch (e: Exception) {
            if (plugin.config.enableDebug) {
                plugin.logger.warning("Bukkit Material方法失败: ${e.message}")
            }
        }

        // 方法2：尝试通过Cobblemon API获取
        try {
            val cobblemonItem = createCobblemonItemByAPI(materialConfig)
            if (cobblemonItem != null) {
                if (plugin.config.enableDebug) {
                    plugin.logger.info("成功通过Cobblemon API创建物品: $materialConfig")
                }
                return cobblemonItem
            }
        } catch (e: Exception) {
            if (plugin.config.enableDebug) {
                plugin.logger.info("Cobblemon API方法失败: ${e.message}")
            }
        }

        // 方法3：使用备用方案
        if (plugin.config.enableDebug) {
            plugin.logger.warning("无法创建Cobblemon物品 $materialConfig，使用备用方案")
        }
        return createFallbackPokeBallItem(materialConfig)
    }

    /**
     * 通过Cobblemon API创建物品
     */
    private fun createCobblemonItemByAPI(materialConfig: String): ItemStack? {
        return try {
            // 移除COBBLEMON_前缀，获取实际的物品名称
            val itemName = materialConfig.removePrefix("COBBLEMON_").lowercase()

            // 尝试通过反射获取CobblemonItems中的物品
            val cobblemonItemsClass = Class.forName("com.cobblemon.mod.common.CobblemonItems")
            val field = cobblemonItemsClass.getDeclaredField(itemName.uppercase())
            field.isAccessible = true
            val cobblemonItem = field.get(null)

            // 转换为Bukkit ItemStack
            if (cobblemonItem != null) {
                // 这里需要实现NMS到Bukkit的转换
                convertCobblemonItemToBukkit(cobblemonItem)
            } else {
                null
            }
        } catch (e: Exception) {
            if (plugin.config.enableDebug) {
                plugin.logger.info("Cobblemon API创建失败: ${e.message}")
            }
            null
        }
    }

    /**
     * 将Cobblemon物品转换为Bukkit ItemStack
     */
    private fun convertCobblemonItemToBukkit(cobblemonItem: Any): ItemStack? {
        return try {
            // 使用CobblemonItemHelper的转换方法
            val nmsItemStack = createNMSItemStack(cobblemonItem)
            if (nmsItemStack != null) {
                CobblemonItemHelper.convertNMSItemToBukkit(nmsItemStack)
            } else {
                null
            }
        } catch (e: Exception) {
            if (plugin.config.enableDebug) {
                plugin.logger.info("物品转换失败: ${e.message}")
            }
            null
        }
    }

    /**
     * 创建NMS ItemStack
     */
    private fun createNMSItemStack(item: Any): Any? {
        return try {
            // 使用反射创建ItemStack
            val itemStackClass = Class.forName("net.minecraft.world.item.ItemStack")
            val constructor = itemStackClass.getConstructor(Class.forName("net.minecraft.world.item.Item"))
            constructor.newInstance(item)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 创建备用精灵球物品（当 Cobblemon 物品不可用时）
     * 使用更合适的球形材质作为备用方案
     */
    private fun createFallbackPokeBallItem(materialConfig: String): ItemStack {
        return when (materialConfig.uppercase()) {
            "COBBLEMON_POKE_BALL" -> ItemStack(org.bukkit.Material.RED_CONCRETE_POWDER)      // 红色 - 精灵球
            "COBBLEMON_GREAT_BALL" -> ItemStack(org.bukkit.Material.BLUE_CONCRETE_POWDER)    // 蓝色 - 超级球
            "COBBLEMON_ULTRA_BALL" -> ItemStack(org.bukkit.Material.YELLOW_CONCRETE_POWDER)  // 黄色 - 高级球
            "COBBLEMON_MASTER_BALL" -> ItemStack(org.bukkit.Material.PURPLE_CONCRETE_POWDER) // 紫色 - 大师球
            "COBBLEMON_TIMER_BALL" -> ItemStack(org.bukkit.Material.BLACK_CONCRETE_POWDER)   // 黑色 - 计时球
            "COBBLEMON_LUXURY_BALL" -> ItemStack(org.bukkit.Material.GREEN_CONCRETE_POWDER)  // 绿色 - 豪华球
            "COBBLEMON_PREMIER_BALL" -> ItemStack(org.bukkit.Material.WHITE_CONCRETE_POWDER) // 白色 - 纪念球
            "COBBLEMON_DUSK_BALL" -> ItemStack(org.bukkit.Material.MAGENTA_CONCRETE_POWDER)  // 品红 - 黄昏球
            "COBBLEMON_QUICK_BALL" -> ItemStack(org.bukkit.Material.CYAN_CONCRETE_POWDER)    // 青色 - 先机球
            else -> ItemStack(org.bukkit.Material.GRAY_CONCRETE_POWDER)                      // 灰色 - 默认
        }
    }

    /**
     * 创建真实的mod精灵球物品
     */
    private fun createRealPokeBallItem(generationId: String): ItemStack? {
        return try {
            // 根据世代获取对应的精灵球类型
            val pokeBall = getGenerationPokeBall(generationId)

            // 使用CobblemonItemHelper直接创建精灵球物品（像精灵模型一样）
            val cobblemonItem = CobblemonItemHelper.createPokeBallItem(pokeBall)
            if (cobblemonItem != null) {
                return cobblemonItem
            } else {
            }

            null
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }



    /**
     * 根据世代ID获取对应的精灵球类型
     */
    private fun getGenerationPokeBall(generationId: String): PokeBall {
        return when (generationId) {
            "gen1" -> PokeBalls.POKE_BALL        // 精灵球 - 红色主题
            "gen2" -> PokeBalls.GREAT_BALL       // 超级球 - 蓝色主题
            "gen3" -> PokeBalls.ULTRA_BALL       // 高级球 - 黄色主题
            "gen4" -> PokeBalls.MASTER_BALL      // 大师球 - 紫色主题
            "gen5" -> PokeBalls.TIMER_BALL       // 计时球 - 黑色主题
            "gen6" -> PokeBalls.LUXURY_BALL      // 豪华球 - 金色主题
            "gen7" -> PokeBalls.PREMIER_BALL     // 纪念球 - 白色主题
            "gen8" -> PokeBalls.DUSK_BALL        // 黄昏球 - 绿色主题
            "gen9" -> PokeBalls.QUICK_BALL       // 先机球 - 蓝色主题
            else -> PokeBalls.POKE_BALL
        }
    }

    /**
     * 创建基础精灵球物品
     */
    private fun createBasePokeBallItem(generationId: String, hasCaught: Boolean, isCompleted: Boolean): ItemStack {
        // 根据世代和状态选择材质（参考精灵模型的材质选择方式）
        val material = when {
            isCompleted -> when (generationId) {
                "gen1" -> Material.REDSTONE_BLOCK      // 精灵球 - 红色
                "gen2" -> Material.LAPIS_BLOCK         // 超级球 - 蓝色
                "gen3" -> Material.GOLD_BLOCK          // 高级球 - 金色
                "gen4" -> Material.DIAMOND_BLOCK       // 大师球 - 钻石
                "gen5" -> Material.OBSIDIAN            // 计时球 - 黑色
                "gen6" -> Material.EMERALD_BLOCK       // 豪华球 - 绿色
                "gen7" -> Material.QUARTZ_BLOCK        // 纪念球 - 白色
                "gen8" -> Material.PURPUR_BLOCK        // 黄昏球 - 紫色
                "gen9" -> Material.PRISMARINE          // 先机球 - 青色
                else -> Material.IRON_BLOCK
            }
            hasCaught -> when (generationId) {
                "gen1" -> Material.RED_GLAZED_TERRACOTTA     // 精灵球纹理
                "gen2" -> Material.BLUE_GLAZED_TERRACOTTA    // 超级球纹理
                "gen3" -> Material.YELLOW_GLAZED_TERRACOTTA  // 高级球纹理
                "gen4" -> Material.PURPLE_GLAZED_TERRACOTTA  // 大师球纹理
                "gen5" -> Material.BLACK_GLAZED_TERRACOTTA   // 计时球纹理
                "gen6" -> Material.GREEN_GLAZED_TERRACOTTA   // 豪华球纹理
                "gen7" -> Material.WHITE_GLAZED_TERRACOTTA   // 纪念球纹理
                "gen8" -> Material.MAGENTA_GLAZED_TERRACOTTA // 黄昏球纹理
                "gen9" -> Material.CYAN_GLAZED_TERRACOTTA    // 先机球纹理
                else -> Material.GRAY_GLAZED_TERRACOTTA
            }
            else -> when (generationId) {
                "gen1" -> Material.RED_CONCRETE_POWDER      // 精灵球 - 未完成
                "gen2" -> Material.BLUE_CONCRETE_POWDER     // 超级球 - 未完成
                "gen3" -> Material.YELLOW_CONCRETE_POWDER   // 高级球 - 未完成
                "gen4" -> Material.PURPLE_CONCRETE_POWDER   // 大师球 - 未完成
                "gen5" -> Material.BLACK_CONCRETE_POWDER    // 计时球 - 未完成
                "gen6" -> Material.GREEN_CONCRETE_POWDER    // 豪华球 - 未完成
                "gen7" -> Material.WHITE_CONCRETE_POWDER    // 纪念球 - 未完成
                "gen8" -> Material.MAGENTA_CONCRETE_POWDER  // 黄昏球 - 未完成
                "gen9" -> Material.CYAN_CONCRETE_POWDER     // 先机球 - 未完成
                else -> Material.GRAY_CONCRETE_POWDER
            }
        }

        return ItemStack(material)
    }

    /**
     * 应用精灵球显示效果（参考精灵模型的显示方式）
     */
    private fun applyPokeBallDisplay(item: ItemStack, generationId: String, hasCaught: Boolean, isCompleted: Boolean): ItemStack {
        val meta = item.itemMeta
        if (meta != null) {
            // 设置显示名称（参考精灵模型的命名方式）
            val ballName = getPokeBallThemeName(generationId)
            val ballColor = getPokeBallThemeColor(generationId)

            val displayName = when {
                isCompleted -> "$ballColor§l$ballName §a✓"
                hasCaught -> "$ballColor§l$ballName §6★"
                else -> "$ballColor§l$ballName"
            }
            meta.setDisplayName(displayName)

            // 设置描述信息
            val lore = mutableListOf<String>()
            lore.add("§7精灵球类型: $ballColor$ballName")
            lore.add("§7适用世代: §f$generationId")
            lore.add("")

            when {
                isCompleted -> {
                    lore.add("§a§l已完成收集！")
                    lore.add("§7✦ 大师级收集家 ✦")
                }
                hasCaught -> {
                    lore.add("§6§l收集进行中...")
                    lore.add("§7✧ 努力收集中 ✧")
                }
                else -> {
                    lore.add("§7尚未开始收集")
                    lore.add("§7捕获精灵来解锁")
                }
            }

            lore.add("")
            lore.add("§8精灵球主题设计")
            meta.lore = lore

            // 隐藏耐久信息和其他不需要的信息
            meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
            meta.addItemFlags(ItemFlag.HIDE_DESTROYS)
            meta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
            meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
            meta.addItemFlags(ItemFlag.HIDE_DYE)
            // 隐藏耐久度信息
            meta.isUnbreakable = true
            meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)

            // 先设置基础 meta
            item.itemMeta = meta

            // 为已收集的精灵球添加附魔效果（参考精灵模型的附魔方式）
            if (hasCaught || isCompleted) {
                if (item.enchantments.isEmpty()) {
                    item.addUnsafeEnchantment(Enchantment.UNBREAKING, 1)
                }

                // 处理ItemMeta和ItemFlag
                val finalMeta = item.itemMeta
                if (finalMeta != null) {
                    // 添加HIDE_ENCHANTS标志来隐藏附魔文字，但保留光效
                    finalMeta.addItemFlags(ItemFlag.HIDE_ENCHANTS)
                    // 确保耐久信息仍然被隐藏
                    finalMeta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
                    finalMeta.addItemFlags(ItemFlag.HIDE_DESTROYS)
                    finalMeta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
                    finalMeta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
                    finalMeta.addItemFlags(ItemFlag.HIDE_DYE)
                    item.itemMeta = finalMeta
                }
            }
        }

        return item
    }

    /**
     * 创建总体进度显示物品 - 使用特殊的精灵球
     */
    fun createOverallProgressItem(allProgress: AllGenerationsProgress, canClaimReward: Boolean, hasClaimedReward: Boolean): ItemStack {
        // 根据完成状态选择精灵球类型
        val pokeBall = when {
            hasClaimedReward -> PokeBalls.MASTER_BALL      // 已领取：大师球（紫色）
            canClaimReward -> PokeBalls.ULTRA_BALL         // 可领取：高级球（黄色）
            allProgress.overallPercentage >= 75 -> PokeBalls.GREAT_BALL  // 75%+：超级球（蓝色）
            allProgress.overallPercentage >= 50 -> PokeBalls.POKE_BALL   // 50%+：精灵球（红色）
            else -> PokeBalls.PREMIER_BALL                 // 其他：纪念球（白色）
        }

        // 尝试创建真实的mod精灵球物品
        val realPokeBallItem = createRealOverallProgressPokeBall(pokeBall)
        if (realPokeBallItem != null) {
            return applyOverallProgressDisplay(realPokeBallItem, allProgress, canClaimReward, hasClaimedReward)
        }

        // 备用方案：创建基础物品
        val item = createBaseOverallProgressItem(allProgress, canClaimReward, hasClaimedReward)
        return applyOverallProgressDisplay(item, allProgress, canClaimReward, hasClaimedReward)
    }

    /**
     * 创建真实的总体进度精灵球物品
     */
    private fun createRealOverallProgressPokeBall(pokeBall: PokeBall): ItemStack? {
        return try {
            val cobblemonItem = CobblemonItemHelper.createPokeBallItem(pokeBall)
            if (cobblemonItem != null) {
                return cobblemonItem
            }
            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 创建基础总体进度物品
     */
    private fun createBaseOverallProgressItem(allProgress: AllGenerationsProgress, canClaimReward: Boolean, hasClaimedReward: Boolean): ItemStack {
        val material = when {
            hasClaimedReward -> Material.DIAMOND_BLOCK     // 已领取：钻石块（紫色主题）
            canClaimReward -> Material.GOLD_BLOCK          // 可领取：金块（黄色主题）
            allProgress.overallPercentage >= 75 -> Material.LAPIS_BLOCK    // 75%+：青金石块（蓝色主题）
            allProgress.overallPercentage >= 50 -> Material.REDSTONE_BLOCK // 50%+：红石块（红色主题）
            else -> Material.QUARTZ_BLOCK                  // 其他：石英块（白色主题）
        }

        return ItemStack(material)
    }

    /**
     * 应用总体进度显示效果
     */
    private fun applyOverallProgressDisplay(item: ItemStack, allProgress: AllGenerationsProgress, canClaimReward: Boolean, hasClaimedReward: Boolean): ItemStack {
        val meta = item.itemMeta ?: return item

        // 设置显示名称
        meta.setDisplayName("§d§l✦ 全世代收集进度 ✦")

        // 确保即使在异常情况下也有基础说明
        if (meta.lore == null || meta.lore!!.isEmpty()) {
            meta.lore = listOf(
                "§7═══════════════════════",
                "§6§l这是全世代收集进度显示",
                "§7点击查看详细进度信息",
                "§7═══════════════════════"
            )
        }

        val lore = mutableListOf<String>()
        lore.add("§7═══════════════════════")
        lore.add("")

        // 总体进度信息 - 详细显示
        lore.add("§6§l总体收集进度:")
        lore.add("§7完成度: ${allProgress.getOverallProgressBar()}")
        lore.add("§7精灵数量: §f${allProgress.caughtPokemon}§7/§f${allProgress.totalPokemon}")
        lore.add("§7总体百分比: §a${allProgress.overallPercentage}%")
        lore.add("")

        // 世代完成情况 - 详细显示
        lore.add("§e§l世代完成情况:")
        lore.add("§7已完成世代: §a${allProgress.completedGenerations}§7/§f${allProgress.totalGenerations}")
        lore.add("§7未完成世代: §c${allProgress.totalGenerations - allProgress.completedGenerations}")
        lore.add("§7世代进度条: ${getGenerationProgressBar(allProgress.completedGenerations, allProgress.totalGenerations)}")
        lore.add("")

        lore.add("")
        lore.add("§7═══════════════════════")

        // 动态显示奖励状态 - 根据条件显示不同信息
        when {
            hasClaimedReward -> {
                lore.add("§a§l✓ 精灵大师奖励已领取！")
                lore.add("§e你是真正的精灵大师！")
                lore.add("§7状态: §a已完成所有世代收集")
                lore.add("")
                lore.add("§e点击查看详细进度")
            }
            canClaimReward -> {
                lore.add("§6§l★ 达到领取要求！")
                lore.add("§e恭喜完成所有1-9世代收集！")
                lore.add("§7状态: §a可以领取精灵大师奖励")
                lore.add("")
                lore.add("§a§l左键: §7领取精灵大师奖励")
                lore.add("§e右键: §7查看详细进度")
            }
            else -> {
                val remaining = allProgress.totalGenerations - allProgress.completedGenerations
                val completionPercentage = (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                lore.add("§c§l暂未达到领取要求")
                lore.add("§7当前状态: §c未完成所有世代")
                lore.add("§7还需完成: §c$remaining §7个世代")
                lore.add("§7世代完成度: §e$completionPercentage% §7(需要100%)")
                lore.add("§7要求: §f完成所有1-9世代收集")
                lore.add("")
                lore.add("§e点击查看详细进度")
            }
        }

        meta.lore = lore

        // 隐藏耐久信息和其他不需要的信息
        meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
        meta.addItemFlags(ItemFlag.HIDE_DESTROYS)
        meta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
        meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
        meta.addItemFlags(ItemFlag.HIDE_DYE)
        // 隐藏耐久度信息
        meta.isUnbreakable = true
        meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)

        item.itemMeta = meta

        // 添加附魔效果
        if (canClaimReward || hasClaimedReward) {
            if (item.enchantments.isEmpty()) {
                item.addUnsafeEnchantment(Enchantment.UNBREAKING, 1)
            }

            val finalMeta = item.itemMeta
            if (finalMeta != null) {
                // 添加HIDE_ENCHANTS标志来隐藏附魔文字，但保留光效
                finalMeta.addItemFlags(ItemFlag.HIDE_ENCHANTS)
                // 确保耐久信息仍然被隐藏
                finalMeta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
                finalMeta.addItemFlags(ItemFlag.HIDE_DESTROYS)
                finalMeta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
                finalMeta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
                finalMeta.addItemFlags(ItemFlag.HIDE_DYE)
                item.itemMeta = finalMeta
            }
        }

        return item
    }

    /**
     * 获取短进度条
     */
    private fun getShortProgressBar(percentage: Int, length: Int): String {
        // 确保百分比在0-100范围内
        val safePercentage = percentage.coerceIn(0, 100)
        val filled = (safePercentage * length) / 100
        val empty = (length - filled).coerceAtLeast(0)
        return "§a" + "█".repeat(filled) + "§7" + "█".repeat(empty)
    }

    /**
     * 获取世代完成进度条
     */
    private fun getGenerationProgressBar(completedGenerations: Int, totalGenerations: Int, length: Int = 12): String {
        val percentage = if (totalGenerations > 0) (completedGenerations * 100) / totalGenerations else 0
        // 确保百分比在0-100范围内
        val safePercentage = percentage.coerceIn(0, 100)
        val filled = (safePercentage * length) / 100
        val empty = (length - filled).coerceAtLeast(0)
        return "§a" + "█".repeat(filled) + "§7" + "█".repeat(empty) + " §f$safePercentage%"
    }

    /**
     * 获取精灵球主题名称
     */
    fun getPokeBallThemeName(generationId: String): String {
        return when (generationId) {
            "gen1" -> "精灵球"
            "gen2" -> "超级球"
            "gen3" -> "高级球"
            "gen4" -> "大师球"
            "gen5" -> "计时球"
            "gen6" -> "豪华球"
            "gen7" -> "纪念球"
            "gen8" -> "黄昏球"
            "gen9" -> "先机球"
            else -> "普通球"
        }
    }

    /**
     * 获取精灵球主题颜色代码
     */
    fun getPokeBallThemeColor(generationId: String): String {
        return when (generationId) {
            "gen1" -> "§c"  // 红色
            "gen2" -> "§9"  // 蓝色
            "gen3" -> "§e"  // 黄色
            "gen4" -> "§5"  // 紫色
            "gen5" -> "§8"  // 黑色
            "gen6" -> "§a"  // 绿色
            "gen7" -> "§f"  // 白色
            "gen8" -> "§d"  // 品红色
            "gen9" -> "§b"  // 青色
            else -> "§7"    // 灰色
        }
    }

}
