/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.data

import cn.acebrand.acedex.util.PokemonInfo
import java.util.*

/**
 * 玩家数据存储结构
 * 用于JSON序列化和反序列化
 */
data class PlayerDataStorage(
    val playerId: String,
    val playerName: String,
    val lastUpdated: Long,
    val partyPokemon: List<StoredPokemonInfo>,
    val pcPokemon: List<StoredPokemonInfo>,
    val totalCaught: Int
) {
    companion object {
        /**
         * 从PlayerPokemonData创建存储对象
         */
        fun fromPlayerPokemonData(
            playerId: UUID,
            playerName: String,
            data: PlayerPokemonData
        ): PlayerDataStorage {
            return PlayerDataStorage(
                playerId = playerId.toString(),
                playerName = playerName,
                lastUpdated = System.currentTimeMillis(),
                partyPokemon = data.partyPokemon.map { StoredPokemonInfo.fromPokemonInfo(it) },
                pcPokemon = data.pcPokemon.map { StoredPokemonInfo.fromPokemonInfo(it) },
                totalCaught = data.totalCaught
            )
        }
    }

    /**
     * 转换为PlayerPokemonData
     */
    fun toPlayerPokemonData(): PlayerPokemonData {
        return PlayerPokemonData(
            partyPokemon = (partyPokemon ?: emptyList()).mapNotNull {
                try {
                    it?.toPokemonInfo()
                } catch (e: Exception) {
                    null
                }
            },
            pcPokemon = (pcPokemon ?: emptyList()).mapNotNull {
                try {
                    it?.toPokemonInfo()
                } catch (e: Exception) {
                    null
                }
            },
            totalCaught = totalCaught ?: 0
        )
    }
}

/**
 * 存储的精灵信息
 * 优化的JSON存储格式
 */
data class StoredPokemonInfo(
    val name: String,
    val nationalDex: Int,
    val generation: String
) {
    companion object {
        fun fromPokemonInfo(info: PokemonInfo): StoredPokemonInfo {
            return StoredPokemonInfo(
                name = info.name,
                nationalDex = info.nationalDex,
                generation = info.generation
            )
        }
    }

    fun toPokemonInfo(): PokemonInfo {
        return PokemonInfo(
            name = name,
            nationalDex = nationalDex,
            generation = generation
        )
    }
}

/**
 * 玩家精灵数据
 * 运行时使用的数据结构
 */
data class PlayerPokemonData(
    val partyPokemon: List<PokemonInfo>,
    val pcPokemon: List<PokemonInfo>,
    val totalCaught: Int
)

/**
 * 世代进度数据
 */
data class GenerationProgress(
    val total: Int,
    val caught: Int,
    val percentage: Int
) {
    fun getProgressBar(length: Int = 20): String {
        // 确保百分比在0-100范围内
        val safePercentage = percentage.coerceIn(0, 100)
        val filled = (safePercentage * length) / 100
        val empty = (length - filled).coerceAtLeast(0)
        return "§a" + "█".repeat(filled) + "§7" + "█".repeat(empty) + " §f$safePercentage%"
    }
}

/**
 * 所有世代进度数据
 */
data class AllGenerationsProgress(
    val totalGenerations: Int,
    val completedGenerations: Int,
    val totalPokemon: Int,
    val caughtPokemon: Int,
    val overallPercentage: Int,
    val generationProgresses: Map<String, GenerationProgress>
) {
    /**
     * 获取总体进度条显示
     */
    fun getOverallProgressBar(length: Int = 20): String {
        // 确保百分比在0-100范围内
        val safePercentage = overallPercentage.coerceIn(0, 100)
        val filled = (safePercentage * length) / 100
        val empty = (length - filled).coerceAtLeast(0)
        return "§a" + "█".repeat(filled) + "§7" + "█".repeat(empty) + " §f$safePercentage%"
    }

    /**
     * 检查是否完成了所有世代
     */
    fun isAllGenerationsCompleted(): Boolean {
        return completedGenerations >= totalGenerations
    }

    /**
     * 获取格式化的进度信息
     */
    fun getFormattedProgress(): List<String> {
        return listOf(
            "§6§l全世代收集进度",
            "§7已完成世代: §a$completedGenerations§7/§f$totalGenerations",
            "§7总体进度: §f$caughtPokemon§7/§f$totalPokemon",
            "§7完成度: ${getOverallProgressBar()}"
        )
    }
}
