{"input": "build/libs/AceDex-1.0.0.jar", "output": "obfuscated/AceDex-1.0.0-minimal-obfuscated.jar", "libraries": ["libs/CobblemonBukkitEvent-1.0-SNAPSHOT (1).jar", "libs/PlaceholderAPI-2.11.6.jar", "libs/nmsSpigot.jar"], "exempts": ["cn.acebrand.acedex.AceDex", "cn.acebrand.acedex.AceDex.*", "cn.acebrand.acedex.command.AceDexCommand", "cn.acebrand.acedex.command.AceDexCommand.*", "cn.acebrand.acedex.command.TestCommand", "cn.acebrand.acedex.command.TestCommand.*", "cn.acebrand.acedex.listener.CobblemonListener", "cn.acebrand.acedex.listener.CobblemonListener.*", "cn.acebrand.acedex.listener.CobblemonEventListener", "cn.acebrand.acedex.listener.CobblemonEventListener.*", "cn.acebrand.acedex.integration.PlaceholderAPIExpansion", "cn.acebrand.acedex.integration.PlaceholderAPIExpansion.*", "cn.acebrand.acedex.license.LicenseManager", "cn.acebrand.acedex.license.LicenseManager.*", "cn.acebrand.acedex.pokemon.PokemonDetector", "cn.acebrand.acedex.pokemon.PokemonDetector.*", "cn.acebrand.acedex.pokemon.PokemonNameMapping", "cn.acebrand.acedex.pokemon.PokemonNameMapping.*", "cn.acebrand.acedex.pokemon.generation.*", "cn.acebrand.acedex.config.AceDexConfig", "cn.acebrand.acedex.config.AceDexConfig.*", "cn.acebrand.acedex.config.AceDexConfig.<init>(*)", "cn.acebrand.acedex.gui.DexMainGui", "cn.acebrand.acedex.gui.DexMainGui.*", "cn.acebrand.acedex.gui.PokemonItemCreator", "cn.acebrand.acedex.gui.PokemonItemCreator.*", "cn.acebrand.acedex.gui.PokeBallItemCreator", "cn.acebrand.acedex.gui.PokeBallItemCreator.*", "cn.acebrand.acedex.data.PlayerDataManager", "cn.acebrand.acedex.data.PlayerDataManager.*", "**$Companion", "**$Companion.*", "**$DefaultConstructorMarker", "**$DefaultConstructorMarker.*", "**$WhenMappings", "**$WhenMappings.*", "<init>(*)", "<clinit>(*)", "plugin.yml", "META-INF/**", "org.bukkit.**", "net.md_5.**", "io.papermc.**", "me.clip.**", "com.cobblemon.**", "org.jetbrains.**", "kotlin.**", "kotlin.jvm.internal.**"], "transformers": {"flow": {"enabled": false, "intensity": 0, "config": {"bogusJumps": false, "switchStatements": false}}, "string": {"enabled": true, "intensity": 1, "config": {"pool": true, "encrypt": false}}, "number": {"enabled": false, "intensity": 0, "config": {"arithmetic": false, "bitwise": false}}, "reference": {"enabled": false, "intensity": 0}, "outlining": {"enabled": false, "intensity": 0}, "ahegao": {"enabled": true, "intensity": 1, "config": {"renameFields": false, "renameMethods": false, "renameClasses": false}}, "condition": {"enabled": false, "intensity": 0}, "bogus": {"enabled": false, "intensity": 0}, "switch": {"enabled": false, "intensity": 0}, "flatten": {"enabled": false, "intensity": 0}, "native": {"enabled": false, "intensity": 0}, "pure": {"enabled": false, "intensity": 0}}, "runtime": {"jvm": ["-Xmx4G", "-Xms2G"]}, "debug": false, "phantom": true, "verify": false, "computeFrames": false}