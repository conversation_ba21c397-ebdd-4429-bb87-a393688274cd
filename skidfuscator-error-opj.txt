handler=Block #WE, types=[Ljava/io/IOException;], range=[Block #CQ, Block #CP]
handler=Block #WI, types=[Ljava/lang/RuntimeException;], range=[Block #CT, Block #CS]
handler=Block #WM, types=[Ljava/lang/IllegalAccessException;], range=[Block #CW, Block #CV]
handler=Block #WQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #CZ, Block #CY]
handler=Block #WU, types=[Ljava/lang/IllegalAccessException;], range=[Block #DC, Block #DB]
handler=Block #WY, types=[Ljava/lang/IllegalAccessException;], range=[Block #DF, Block #DE]
handler=Block #XC, types=[Ljava/lang/IllegalAccessException;], range=[Block #DI, Block #DH]
handler=Block #XG, types=[Ljava/io/IOException;], range=[Block #DL, Block #DK]
handler=Block #XK, types=[Ljava/io/IOException;], range=[Block #DO, Block #DN]
handler=Block #XO, types=[Ljava/lang/RuntimeException;], range=[Block #DR, Block #DQ]
handler=Block #XS, types=[Ljava/lang/IllegalAccessException;], range=[Block #DU, Block #DT]
handler=Block #XW, types=[Ljava/lang/RuntimeException;], range=[Block #DX, Block #DW]
handler=Block #YA, types=[Ljava/io/IOException;], range=[Block #EA, Block #DZ]
handler=Block #YE, types=[Ljava/lang/IllegalAccessException;], range=[Block #ED, Block #EC]
handler=Block #YI, types=[Ljava/lang/RuntimeException;], range=[Block #EG, Block #EF]
handler=Block #YM, types=[Ljava/lang/RuntimeException;], range=[Block #EJ, Block #EI]
handler=Block #YQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #EM, Block #EL]
handler=Block #YU, types=[Ljava/lang/IllegalAccessException;], range=[Block #EP, Block #EO]
handler=Block #YY, types=[Ljava/lang/RuntimeException;], range=[Block #ES, Block #ER]
handler=Block #ZC, types=[Ljava/lang/RuntimeException;], range=[Block #EV, Block #EU]
handler=Block #ZG, types=[Ljava/lang/IllegalAccessException;], range=[Block #EY, Block #EX]
handler=Block #ZK, types=[Ljava/io/IOException;], range=[Block #FB, Block #FA]
handler=Block #ZO, types=[Ljava/lang/RuntimeException;], range=[Block #FE, Block #FD]
handler=Block #ZS, types=[Ljava/io/IOException;], range=[Block #FH, Block #FG]
handler=Block #ZW, types=[Ljava/lang/RuntimeException;], range=[Block #FK, Block #FJ]
handler=Block #AAA, types=[Ljava/lang/RuntimeException;], range=[Block #FN, Block #FM]
handler=Block #AAE, types=[Ljava/lang/RuntimeException;], range=[Block #FQ, Block #FP]
handler=Block #AAI, types=[Ljava/lang/IllegalAccessException;], range=[Block #FT, Block #FS]
handler=Block #AAM, types=[Ljava/lang/RuntimeException;], range=[Block #FW, Block #FV]
handler=Block #AAQ, types=[Ljava/lang/RuntimeException;], range=[Block #FZ, Block #FY]
handler=Block #AAU, types=[Ljava/lang/IllegalAccessException;], range=[Block #GC, Block #GB]
handler=Block #AAY, types=[Ljava/io/IOException;], range=[Block #GF, Block #GE]
handler=Block #ABC, types=[Ljava/lang/IllegalAccessException;], range=[Block #GI, Block #GH]
handler=Block #ABG, types=[Ljava/lang/RuntimeException;], range=[Block #GL, Block #GK]
handler=Block #ABK, types=[Ljava/io/IOException;], range=[Block #GO, Block #GN]
handler=Block #ABO, types=[Ljava/lang/RuntimeException;], range=[Block #GR, Block #GQ]
handler=Block #ABS, types=[Ljava/lang/RuntimeException;], range=[Block #GU, Block #GT]
handler=Block #ABW, types=[Ljava/lang/RuntimeException;], range=[Block #GX, Block #GW]
handler=Block #ACA, types=[Ljava/lang/RuntimeException;], range=[Block #HA, Block #GZ]
handler=Block #ACE, types=[Ljava/lang/IllegalAccessException;], range=[Block #HD, Block #HC]
handler=Block #ACI, types=[Ljava/lang/IllegalAccessException;], range=[Block #HG, Block #HF]
handler=Block #ACM, types=[Ljava/lang/IllegalAccessException;], range=[Block #HJ, Block #HI]
handler=Block #ACQ, types=[Ljava/io/IOException;], range=[Block #HM, Block #HL]
handler=Block #ACU, types=[Ljava/io/IOException;], range=[Block #HP, Block #HO]
handler=Block #ACY, types=[Ljava/lang/IllegalAccessException;], range=[Block #HS, Block #HR]
handler=Block #ADC, types=[Ljava/io/IOException;], range=[Block #HV, Block #HU]
handler=Block #ADG, types=[Ljava/io/IOException;], range=[Block #HY, Block #HX]
handler=Block #ADK, types=[Ljava/io/IOException;], range=[Block #IB, Block #IA]
handler=Block #ADO, types=[Ljava/lang/IllegalAccessException;], range=[Block #IE, Block #ID]
handler=Block #ADS, types=[Ljava/lang/IllegalAccessException;], range=[Block #IH, Block #IG]
handler=Block #ADW, types=[Ljava/lang/IllegalAccessException;], range=[Block #IK, Block #IJ]
handler=Block #AEA, types=[Ljava/io/IOException;], range=[Block #IN, Block #IM]
handler=Block #AEE, types=[Ljava/lang/IllegalAccessException;], range=[Block #IQ, Block #IP]
handler=Block #AEI, types=[Ljava/io/IOException;], range=[Block #IT, Block #IS]
handler=Block #AEM, types=[Ljava/lang/IllegalAccessException;], range=[Block #IW, Block #IV]
handler=Block #AEQ, types=[Ljava/io/IOException;], range=[Block #IZ, Block #IY]
===#Block A(size=6, flags=1)===
   0. lvar105 = {1523342719 ^ {2136884009 ^ 1913877927}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. synth(lvar2 = lvar2);
   4. synth(lvar3 = lvar3);
   5. lvar105 = {2103068900 ^ lvar105};
      -> Immediate #A -> #B
===#Block B(size=1, flags=0)===
   0. lvar105 = {876913393 ^ lvar105};
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=3, flags=0)===
   0. lvar5 = lvar3;
   1. if (lvar5 == {513769956 ^ lvar105})
      goto TJ
   2. lvar105 = {78197545 ^ lvar105};
      -> ConditionalJump[IF_ICMPEQ] #C -> #TJ
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=6, flags=0)===
   0. lvar10 = lvar1;
   1. lvar9 = lvar10;
   2. lvar11 = lvar9;
   3. lvar12 = lvar11.hashCode();
   4. svar107 = {lvar12 ^ lvar105};
   5. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(svar107)) {
      case 40729537:
      	 goto	#VE
      case 40729539:
      	 goto	#VF
      case 40729545:
      	 goto	#VG
      case 40729547:
      	 goto	#VH
      case 40729555:
      	 goto	#VJ
      case 40729557:
      	 goto	#VL
      case 40729559:
      	 goto	#VN
      case 40729565:
      	 goto	#VO
      case 40729567:
      	 goto	#VP
      default:
      	 goto	#VQ
   }
      -> Switch[40729565] #D -> #VO
      -> DefaultSwitch #D -> #VQ
      -> Switch[40729557] #D -> #VL
      -> Switch[40729567] #D -> #VP
      -> Switch[40729539] #D -> #VF
      -> Switch[40729545] #D -> #VG
      -> Switch[40729555] #D -> #VJ
      -> Switch[40729537] #D -> #VE
      -> Switch[40729547] #D -> #VH
      -> Switch[40729559] #D -> #VN
      <- Immediate #C -> #D
===#Block VN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 950203499);
   1. goto H
      -> UnconditionalJump[GOTO] #VN -> #H
      <- Switch[40729559] #D -> #VN
===#Block H(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar9;
   2. lvar76 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.cpzmfuuwdwwirvj(), lvar105);
   3. lvar17 = lvar16.equals(lvar76);
   4. if (lvar17 != {580142758 ^ lvar105})
      goto SG
   5. lvar105 = {937939982 ^ lvar105};
      -> Immediate #H -> #J
      -> ConditionalJump[IF_ICMPNE] #H -> #SG
      <- UnconditionalJump[GOTO] #VN -> #H
===#Block SG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 852279916);
   1. goto JN
      -> UnconditionalJump[GOTO] #SG -> #JN
      <- ConditionalJump[IF_ICMPNE] #H -> #SG
===#Block JN(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -2101074352)
      goto TQ
   1. goto NO
      -> ConditionalJump[IF_ICMPEQ] #JN -> #TQ
      -> UnconditionalJump[GOTO] #JN -> #NO
      <- UnconditionalJump[GOTO] #SG -> #JN
===#Block NO(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 262902047:
      	 goto	#NP
      case 499287040:
      	 goto	#JC
      case 659624328:
      	 goto	#NO
      case 1189133215:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[262902047] #NO -> #NP
      -> Switch[659624328] #NO -> #NO
      -> DefaultSwitch #NO -> #JC
      -> Immediate #NO -> #NP
      -> Switch[1189133215] #NO -> #JC
      <- Switch[659624328] #NO -> #NO
      <- UnconditionalJump[GOTO] #JN -> #NO
===#Block NP(size=2, flags=100)===
   0. lvar105 = {9286015 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NP -> #JC
      <- Switch[262902047] #NO -> #NP
      <- Immediate #NO -> #NP
===#Block TQ(size=2, flags=10100)===
   0. lvar105 = {1232229399 ^ lvar105};
   1. goto I
      -> UnconditionalJump[GOTO] #TQ -> #I
      <- ConditionalJump[IF_ICMPEQ] #JN -> #TQ
===#Block I(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.QUARTZ_BLOCK;
   2. goto PK
      -> UnconditionalJump[GOTO] #I -> #PK
      <- UnconditionalJump[GOTO] #TQ -> #I
===#Block PK(size=2, flags=10100)===
   0. lvar105 = {432628239 ^ lvar105};
   1. goto FZ
      -> UnconditionalJump[GOTO] #PK -> #FZ
      <- UnconditionalJump[GOTO] #I -> #PK
===#Block FZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 230255253)
      goto FY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FZ -> #FY
      -> TryCatch range: [FZ...FY] -> AAQ ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #PK -> #FZ
===#Block FY(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FZ...FY] -> AAQ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FZ -> #FY
===#Block AAQ(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1057125030:
      	 goto	#AAS
      case 119379602:
      	 goto	#AAR
      default:
      	 goto	#AAT
   }
      -> Switch[119379602] #AAQ -> #AAR
      -> DefaultSwitch #AAQ -> #AAT
      -> Switch[-1057125030] #AAQ -> #AAS
      <- TryCatch range: [FZ...FY] -> AAQ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FZ...FY] -> AAQ ([Ljava/lang/RuntimeException;])
===#Block AAS(size=2, flags=10100)===
   0. lvar105 = {1783129697 ^ lvar105};
   1. goto GA
      -> UnconditionalJump[GOTO] #AAS -> #GA
      <- Switch[-1057125030] #AAQ -> #AAS
===#Block AAT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AAQ -> #AAT
===#Block AAR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1924424984);
   1. goto GA
      -> UnconditionalJump[GOTO] #AAR -> #GA
      <- Switch[119379602] #AAQ -> #AAR
===#Block GA(size=2, flags=0)===
   0. _consume(catch());
   1. goto MB
      -> UnconditionalJump[GOTO] #GA -> #MB
      <- UnconditionalJump[GOTO] #AAR -> #GA
      <- UnconditionalJump[GOTO] #AAS -> #GA
===#Block MB(size=2, flags=10100)===
   0. lvar105 = {1543993458 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #MB -> #CN
      <- UnconditionalJump[GOTO] #GA -> #MB
===#Block J(size=1, flags=0)===
   0. goto MX
      -> UnconditionalJump[GOTO] #J -> #MX
      <- Immediate #H -> #J
===#Block MX(size=2, flags=10100)===
   0. lvar105 = {2134593811 ^ lvar105};
   1. goto DO
      -> UnconditionalJump[GOTO] #MX -> #DO
      <- UnconditionalJump[GOTO] #J -> #MX
===#Block DO(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 233607538)
      goto DN
   1. throw nullconst;
      -> TryCatch range: [DO...DN] -> XK ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DO -> #DN
      <- UnconditionalJump[GOTO] #MX -> #DO
===#Block DN(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DO...DN] -> XK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DO -> #DN
===#Block XK(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -682420264:
      	 goto	#XM
      case 1380326875:
      	 goto	#XL
      default:
      	 goto	#XN
   }
      -> DefaultSwitch #XK -> #XN
      -> Switch[-682420264] #XK -> #XM
      -> Switch[1380326875] #XK -> #XL
      <- TryCatch range: [DO...DN] -> XK ([Ljava/io/IOException;])
      <- TryCatch range: [DO...DN] -> XK ([Ljava/io/IOException;])
===#Block XL(size=2, flags=10100)===
   0. lvar105 = {915449151 ^ lvar105};
   1. goto DP
      -> UnconditionalJump[GOTO] #XL -> #DP
      <- Switch[1380326875] #XK -> #XL
===#Block XM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1177683071);
   1. goto DP
      -> UnconditionalJump[GOTO] #XM -> #DP
      <- Switch[-682420264] #XK -> #XM
===#Block DP(size=2, flags=0)===
   0. _consume(catch());
   1. goto KU
      -> UnconditionalJump[GOTO] #DP -> #KU
      <- UnconditionalJump[GOTO] #XM -> #DP
      <- UnconditionalJump[GOTO] #XL -> #DP
===#Block KU(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 65215047:
      	 goto	#KV
      case 803446414:
      	 goto	#JC
      case 1360080912:
      	 goto	#KU
      case 2095533824:
      	 goto	#AE
      default:
      	 goto	#JC
   }
      -> Switch[65215047] #KU -> #KV
      -> Switch[803446414] #KU -> #JC
      -> DefaultSwitch #KU -> #JC
      -> Immediate #KU -> #KV
      -> Switch[1360080912] #KU -> #KU
      -> Switch[2095533824] #KU -> #AE
      <- UnconditionalJump[GOTO] #DP -> #KU
      <- Switch[1360080912] #KU -> #KU
===#Block KV(size=2, flags=100)===
   0. lvar105 = {285750602 ^ lvar105};
   1. goto AE
      -> UnconditionalJump[GOTO] #KV -> #AE
      <- Switch[65215047] #KU -> #KV
      <- Immediate #KU -> #KV
===#Block XN(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #XK -> #XN
===#Block VH(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 46784603:
      	 goto	#VI
      case 622172940:
      	 goto	#T
      case 889327963:
      	 goto	#JC
      case 1964541117:
      	 goto	#VH
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #VH -> #JC
      -> Switch[46784603] #VH -> #VI
      -> Switch[889327963] #VH -> #JC
      -> Switch[622172940] #VH -> #T
      -> Immediate #VH -> #VI
      -> Switch[1964541117] #VH -> #VH
      <- Switch[1964541117] #VH -> #VH
      <- Switch[40729547] #D -> #VH
===#Block VI(size=2, flags=100)===
   0. lvar105 = {1662359739 ^ lvar105};
   1. goto T
      -> UnconditionalJump[GOTO] #VI -> #T
      <- Switch[46784603] #VH -> #VI
      <- Immediate #VH -> #VI
===#Block T(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar24 = lvar9;
   2. lvar80 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.mukafzievulzqwg(), lvar105);
   3. lvar25 = lvar24.equals(lvar80);
   4. if (lvar25 != {2032347766 ^ lvar105})
      goto UD
   5. lvar105 = {1816249793 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #T -> #UD
      -> Immediate #T -> #U
      <- Switch[622172940] #VH -> #T
      <- UnconditionalJump[GOTO] #VI -> #T
===#Block U(size=1, flags=0)===
   0. goto LL
      -> UnconditionalJump[GOTO] #U -> #LL
      <- Immediate #T -> #U
===#Block LL(size=2, flags=10100)===
   0. lvar105 = {1696359198 ^ lvar105};
   1. goto FK
      -> UnconditionalJump[GOTO] #LL -> #FK
      <- UnconditionalJump[GOTO] #U -> #LL
===#Block FK(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 167067897)
      goto FJ
   1. throw nullconst;
      -> TryCatch range: [FK...FJ] -> ZW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FK -> #FJ
      <- UnconditionalJump[GOTO] #LL -> #FK
===#Block FJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FK...FJ] -> ZW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FK -> #FJ
===#Block ZW(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -2081135285:
      	 goto	#ZX
      case -352268013:
      	 goto	#ZY
      default:
      	 goto	#ZZ
   }
      -> DefaultSwitch #ZW -> #ZZ
      -> Switch[-2081135285] #ZW -> #ZX
      -> Switch[-352268013] #ZW -> #ZY
      <- TryCatch range: [FK...FJ] -> ZW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FK...FJ] -> ZW ([Ljava/lang/RuntimeException;])
===#Block ZY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1908990286);
   1. goto FL
      -> UnconditionalJump[GOTO] #ZY -> #FL
      <- Switch[-352268013] #ZW -> #ZY
===#Block ZX(size=2, flags=10100)===
   0. lvar105 = {2094430149 ^ lvar105};
   1. goto FL
      -> UnconditionalJump[GOTO] #ZX -> #FL
      <- Switch[-2081135285] #ZW -> #ZX
===#Block FL(size=2, flags=0)===
   0. _consume(catch());
   1. goto PB
      -> UnconditionalJump[GOTO] #FL -> #PB
      <- UnconditionalJump[GOTO] #ZY -> #FL
      <- UnconditionalJump[GOTO] #ZX -> #FL
===#Block PB(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 93907207:
      	 goto	#JC
      case 143092617:
      	 goto	#PC
      case 432231863:
      	 goto	#AE
      case 962419538:
      	 goto	#PB
      default:
      	 goto	#JC
   }
      -> Switch[432231863] #PB -> #AE
      -> DefaultSwitch #PB -> #JC
      -> Immediate #PB -> #PC
      -> Switch[93907207] #PB -> #JC
      -> Switch[143092617] #PB -> #PC
      -> Switch[962419538] #PB -> #PB
      <- UnconditionalJump[GOTO] #FL -> #PB
      <- Switch[962419538] #PB -> #PB
===#Block PC(size=2, flags=100)===
   0. lvar105 = {1098426530 ^ lvar105};
   1. goto AE
      -> UnconditionalJump[GOTO] #PC -> #AE
      <- Immediate #PB -> #PC
      <- Switch[143092617] #PB -> #PC
===#Block ZZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ZW -> #ZZ
===#Block UD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 219443734);
   1. goto JS
      -> UnconditionalJump[GOTO] #UD -> #JS
      <- ConditionalJump[IF_ICMPNE] #T -> #UD
===#Block JS(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1581595901)
      goto TA
   1. goto KI
      -> ConditionalJump[IF_ICMPEQ] #JS -> #TA
      -> UnconditionalJump[GOTO] #JS -> #KI
      <- UnconditionalJump[GOTO] #UD -> #JS
===#Block KI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1692560853);
   1. goto JC
      -> UnconditionalJump[GOTO] #KI -> #JC
      <- UnconditionalJump[GOTO] #JS -> #KI
===#Block TA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 2051157383);
   1. goto V
      -> UnconditionalJump[GOTO] #TA -> #V
      <- ConditionalJump[IF_ICMPEQ] #JS -> #TA
===#Block V(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.OBSIDIAN;
   2. goto OP
      -> UnconditionalJump[GOTO] #V -> #OP
      <- UnconditionalJump[GOTO] #TA -> #V
===#Block OP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 528680309);
   1. goto CZ
      -> UnconditionalJump[GOTO] #OP -> #CZ
      <- UnconditionalJump[GOTO] #V -> #OP
===#Block CZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 47016940)
      goto CY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CZ -> #CY
      -> TryCatch range: [CZ...CY] -> WQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #OP -> #CZ
===#Block CY(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CZ...CY] -> WQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CZ -> #CY
===#Block WQ(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1884060528:
      	 goto	#WR
      case -1741278589:
      	 goto	#WS
      default:
      	 goto	#WT
   }
      -> DefaultSwitch #WQ -> #WT
      -> Switch[-1884060528] #WQ -> #WR
      -> Switch[-1741278589] #WQ -> #WS
      <- TryCatch range: [CZ...CY] -> WQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CZ...CY] -> WQ ([Ljava/lang/IllegalAccessException;])
===#Block WS(size=2, flags=10100)===
   0. lvar105 = {366823575 ^ lvar105};
   1. goto DA
      -> UnconditionalJump[GOTO] #WS -> #DA
      <- Switch[-1741278589] #WQ -> #WS
===#Block WR(size=2, flags=10100)===
   0. lvar105 = {1999502933 ^ lvar105};
   1. goto DA
      -> UnconditionalJump[GOTO] #WR -> #DA
      <- Switch[-1884060528] #WQ -> #WR
===#Block DA(size=2, flags=0)===
   0. _consume(catch());
   1. goto LX
      -> UnconditionalJump[GOTO] #DA -> #LX
      <- UnconditionalJump[GOTO] #WR -> #DA
      <- UnconditionalJump[GOTO] #WS -> #DA
===#Block LX(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 122225274:
      	 goto	#LY
      case 444310366:
      	 goto	#JC
      case 555967301:
      	 goto	#LX
      case 2023890301:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[555967301] #LX -> #LX
      -> Switch[122225274] #LX -> #LY
      -> Switch[444310366] #LX -> #JC
      -> DefaultSwitch #LX -> #JC
      -> Immediate #LX -> #LY
      -> Switch[2023890301] #LX -> #CN
      <- Switch[555967301] #LX -> #LX
      <- UnconditionalJump[GOTO] #DA -> #LX
===#Block LY(size=2, flags=100)===
   0. lvar105 = {143335807 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #LY -> #CN
      <- Switch[122225274] #LX -> #LY
      <- Immediate #LX -> #LY
===#Block WT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WQ -> #WT
===#Block VE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1926997124);
   1. goto Z
      -> UnconditionalJump[GOTO] #VE -> #Z
      <- Switch[40729537] #D -> #VE
===#Block Z(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar28 = lvar9;
   2. lvar82 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.qhbewokxemwjhlw(), lvar105);
   3. lvar29 = lvar28.equals(lvar82);
   4. if (lvar29 != {1760368201 ^ lvar105})
      goto TL
   5. lvar105 = {212601404 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #Z -> #TL
      -> Immediate #Z -> #AA
      <- UnconditionalJump[GOTO] #VE -> #Z
===#Block AA(size=1, flags=0)===
   0. goto NX
      -> UnconditionalJump[GOTO] #AA -> #NX
      <- Immediate #Z -> #AA
===#Block NX(size=2, flags=10100)===
   0. lvar105 = {173208434 ^ lvar105};
   1. goto GU
      -> UnconditionalJump[GOTO] #NX -> #GU
      <- UnconditionalJump[GOTO] #AA -> #NX
===#Block GU(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 88535310)
      goto GT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GU -> #GT
      -> TryCatch range: [GU...GT] -> ABS ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #NX -> #GU
===#Block GT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GU...GT] -> ABS ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GU -> #GT
===#Block ABS(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 1813889035:
      	 goto	#ABU
      case 1889499195:
      	 goto	#ABT
      default:
      	 goto	#ABV
   }
      -> DefaultSwitch #ABS -> #ABV
      -> Switch[1813889035] #ABS -> #ABU
      -> Switch[1889499195] #ABS -> #ABT
      <- TryCatch range: [GU...GT] -> ABS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GU...GT] -> ABS ([Ljava/lang/RuntimeException;])
===#Block ABT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 71730965);
   1. goto GV
      -> UnconditionalJump[GOTO] #ABT -> #GV
      <- Switch[1889499195] #ABS -> #ABT
===#Block ABU(size=2, flags=10100)===
   0. lvar105 = {131518483 ^ lvar105};
   1. goto GV
      -> UnconditionalJump[GOTO] #ABU -> #GV
      <- Switch[1813889035] #ABS -> #ABU
===#Block GV(size=2, flags=0)===
   0. _consume(catch());
   1. goto QB
      -> UnconditionalJump[GOTO] #GV -> #QB
      <- UnconditionalJump[GOTO] #ABU -> #GV
      <- UnconditionalJump[GOTO] #ABT -> #GV
===#Block QB(size=2, flags=10100)===
   0. lvar105 = {663043036 ^ lvar105};
   1. goto AE
      -> UnconditionalJump[GOTO] #QB -> #AE
      <- UnconditionalJump[GOTO] #GV -> #QB
===#Block ABV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABS -> #ABV
===#Block TL(size=2, flags=10100)===
   0. lvar105 = {5883460 ^ lvar105};
   1. goto JJ
      -> UnconditionalJump[GOTO] #TL -> #JJ
      <- ConditionalJump[IF_ICMPNE] #Z -> #TL
===#Block JJ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1168539755)
      goto SL
   1. goto PT
      -> UnconditionalJump[GOTO] #JJ -> #PT
      -> ConditionalJump[IF_ICMPEQ] #JJ -> #SL
      <- UnconditionalJump[GOTO] #TL -> #JJ
===#Block SL(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 78679848:
      	 goto	#AB
      case 182336429:
      	 goto	#SM
      case 530447982:
      	 goto	#SL
      case 724880127:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[530447982] #SL -> #SL
      -> Immediate #SL -> #SM
      -> Switch[78679848] #SL -> #AB
      -> DefaultSwitch #SL -> #JC
      -> Switch[182336429] #SL -> #SM
      -> Switch[724880127] #SL -> #JC
      <- Switch[530447982] #SL -> #SL
      <- ConditionalJump[IF_ICMPEQ] #JJ -> #SL
===#Block SM(size=2, flags=100)===
   0. lvar105 = {867171453 ^ lvar105};
   1. goto AB
      -> UnconditionalJump[GOTO] #SM -> #AB
      <- Immediate #SL -> #SM
      <- Switch[182336429] #SL -> #SM
===#Block AB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPUR_BLOCK;
   2. goto MM
      -> UnconditionalJump[GOTO] #AB -> #MM
      <- Switch[78679848] #SL -> #AB
      <- UnconditionalJump[GOTO] #SM -> #AB
===#Block MM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 8715522:
      	 goto	#MN
      case 299672152:
      	 goto	#IB
      case 1885540580:
      	 goto	#JC
      case 2010401276:
      	 goto	#MM
      default:
      	 goto	#JC
   }
      -> Switch[2010401276] #MM -> #MM
      -> Switch[299672152] #MM -> #IB
      -> Immediate #MM -> #MN
      -> DefaultSwitch #MM -> #JC
      -> Switch[8715522] #MM -> #MN
      -> Switch[1885540580] #MM -> #JC
      <- Switch[2010401276] #MM -> #MM
      <- UnconditionalJump[GOTO] #AB -> #MM
===#Block MN(size=2, flags=100)===
   0. lvar105 = {2009611822 ^ lvar105};
   1. goto IB
      -> UnconditionalJump[GOTO] #MN -> #IB
      <- Immediate #MM -> #MN
      <- Switch[8715522] #MM -> #MN
===#Block IB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 114876229)
      goto IA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IB -> #IA
      -> TryCatch range: [IB...IA] -> ADK ([Ljava/io/IOException;])
      <- Switch[299672152] #MM -> #IB
      <- UnconditionalJump[GOTO] #MN -> #IB
===#Block IA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IB...IA] -> ADK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IB -> #IA
===#Block ADK(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -231826632:
      	 goto	#ADM
      case 1721275121:
      	 goto	#ADL
      default:
      	 goto	#ADN
   }
      -> Switch[-231826632] #ADK -> #ADM
      -> DefaultSwitch #ADK -> #ADN
      -> Switch[1721275121] #ADK -> #ADL
      <- TryCatch range: [IB...IA] -> ADK ([Ljava/io/IOException;])
      <- TryCatch range: [IB...IA] -> ADK ([Ljava/io/IOException;])
===#Block ADL(size=2, flags=10100)===
   0. lvar105 = {1927526350 ^ lvar105};
   1. goto IC
      -> UnconditionalJump[GOTO] #ADL -> #IC
      <- Switch[1721275121] #ADK -> #ADL
===#Block ADN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ADK -> #ADN
===#Block ADM(size=2, flags=10100)===
   0. lvar105 = {1081439991 ^ lvar105};
   1. goto IC
      -> UnconditionalJump[GOTO] #ADM -> #IC
      <- Switch[-231826632] #ADK -> #ADM
===#Block IC(size=2, flags=0)===
   0. _consume(catch());
   1. goto NY
      -> UnconditionalJump[GOTO] #IC -> #NY
      <- UnconditionalJump[GOTO] #ADM -> #IC
      <- UnconditionalJump[GOTO] #ADL -> #IC
===#Block NY(size=2, flags=10100)===
   0. lvar105 = {811600424 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NY -> #CN
      <- UnconditionalJump[GOTO] #IC -> #NY
===#Block PT(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 182336429:
      	 goto	#PU
      case 1310867826:
      	 goto	#JC
      case 1396856899:
      	 goto	#JC
      case 1716405313:
      	 goto	#PT
      default:
      	 goto	#JC
   }
      -> Switch[1716405313] #PT -> #PT
      -> Switch[182336429] #PT -> #PU
      -> Switch[1310867826] #PT -> #JC
      -> DefaultSwitch #PT -> #JC
      -> Immediate #PT -> #PU
      <- Switch[1716405313] #PT -> #PT
      <- UnconditionalJump[GOTO] #JJ -> #PT
===#Block PU(size=2, flags=100)===
   0. lvar105 = {2019681720 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PU -> #JC
      <- Switch[182336429] #PT -> #PU
      <- Immediate #PT -> #PU
===#Block VJ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 46784603:
      	 goto	#VK
      case 308132600:
      	 goto	#JC
      case 631580999:
      	 goto	#W
      case 1814146327:
      	 goto	#VJ
      default:
      	 goto	#JC
   }
      -> Switch[631580999] #VJ -> #W
      -> Switch[1814146327] #VJ -> #VJ
      -> Switch[308132600] #VJ -> #JC
      -> Immediate #VJ -> #VK
      -> DefaultSwitch #VJ -> #JC
      -> Switch[46784603] #VJ -> #VK
      <- Switch[40729555] #D -> #VJ
      <- Switch[1814146327] #VJ -> #VJ
===#Block VK(size=2, flags=100)===
   0. lvar105 = {1507026065 ^ lvar105};
   1. goto W
      -> UnconditionalJump[GOTO] #VK -> #W
      <- Immediate #VJ -> #VK
      <- Switch[46784603] #VJ -> #VK
===#Block W(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar26 = lvar9;
   2. lvar81 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.otbgujqqdsjgcff(), lvar105);
   3. lvar27 = lvar26.equals(lvar81);
   4. if (lvar27 != {1139136092 ^ lvar105})
      goto TX
   5. lvar105 = {398839384 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #W -> #TX
      -> Immediate #W -> #Y
      <- Switch[631580999] #VJ -> #W
      <- UnconditionalJump[GOTO] #VK -> #W
===#Block Y(size=1, flags=0)===
   0. goto NH
      -> UnconditionalJump[GOTO] #Y -> #NH
      <- Immediate #W -> #Y
===#Block NH(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 50231975:
      	 goto	#NI
      case 150168872:
      	 goto	#NH
      case 579743057:
      	 goto	#EV
      case 1504006845:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1504006845] #NH -> #JC
      -> DefaultSwitch #NH -> #JC
      -> Switch[150168872] #NH -> #NH
      -> Immediate #NH -> #NI
      -> Switch[50231975] #NH -> #NI
      -> Switch[579743057] #NH -> #EV
      <- UnconditionalJump[GOTO] #Y -> #NH
      <- Switch[150168872] #NH -> #NH
===#Block NI(size=2, flags=100)===
   0. lvar105 = {1564319339 ^ lvar105};
   1. goto EV
      -> UnconditionalJump[GOTO] #NI -> #EV
      <- Immediate #NH -> #NI
      <- Switch[50231975] #NH -> #NI
===#Block EV(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 27868522)
      goto EU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EV -> #EU
      -> TryCatch range: [EV...EU] -> ZC ([Ljava/lang/RuntimeException;])
      <- Switch[579743057] #NH -> #EV
      <- UnconditionalJump[GOTO] #NI -> #EV
===#Block EU(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EV...EU] -> ZC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EV -> #EU
===#Block ZC(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1014261278:
      	 goto	#ZE
      case 1223439224:
      	 goto	#ZD
      default:
      	 goto	#ZF
   }
      -> DefaultSwitch #ZC -> #ZF
      -> Switch[1223439224] #ZC -> #ZD
      -> Switch[-1014261278] #ZC -> #ZE
      <- TryCatch range: [EV...EU] -> ZC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EV...EU] -> ZC ([Ljava/lang/RuntimeException;])
===#Block ZE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 18048740);
   1. goto EW
      -> UnconditionalJump[GOTO] #ZE -> #EW
      <- Switch[-1014261278] #ZC -> #ZE
===#Block ZD(size=2, flags=10100)===
   0. lvar105 = {1350538167 ^ lvar105};
   1. goto EW
      -> UnconditionalJump[GOTO] #ZD -> #EW
      <- Switch[1223439224] #ZC -> #ZD
===#Block EW(size=2, flags=0)===
   0. _consume(catch());
   1. goto NB
      -> UnconditionalJump[GOTO] #EW -> #NB
      <- UnconditionalJump[GOTO] #ZE -> #EW
      <- UnconditionalJump[GOTO] #ZD -> #EW
===#Block NB(size=2, flags=10100)===
   0. lvar105 = {347227670 ^ lvar105};
   1. goto AE
      -> UnconditionalJump[GOTO] #NB -> #AE
      <- UnconditionalJump[GOTO] #EW -> #NB
===#Block ZF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ZC -> #ZF
===#Block TX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 300623036);
   1. goto JQ
      -> UnconditionalJump[GOTO] #TX -> #JQ
      <- ConditionalJump[IF_ICMPNE] #W -> #TX
===#Block JQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1871210750)
      goto TY
   1. goto OX
      -> UnconditionalJump[GOTO] #JQ -> #OX
      -> ConditionalJump[IF_ICMPEQ] #JQ -> #TY
      <- UnconditionalJump[GOTO] #TX -> #JQ
===#Block TY(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 251438236:
      	 goto	#TZ
      case 977675860:
      	 goto	#X
      case 1193313816:
      	 goto	#TY
      case 1827974571:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[977675860] #TY -> #X
      -> Switch[251438236] #TY -> #TZ
      -> Switch[1193313816] #TY -> #TY
      -> Switch[1827974571] #TY -> #JC
      -> DefaultSwitch #TY -> #JC
      -> Immediate #TY -> #TZ
      <- Switch[1193313816] #TY -> #TY
      <- ConditionalJump[IF_ICMPEQ] #JQ -> #TY
===#Block TZ(size=2, flags=100)===
   0. lvar105 = {664399916 ^ lvar105};
   1. goto X
      -> UnconditionalJump[GOTO] #TZ -> #X
      <- Switch[251438236] #TY -> #TZ
      <- Immediate #TY -> #TZ
===#Block X(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.REDSTONE_BLOCK;
   2. goto QY
      -> UnconditionalJump[GOTO] #X -> #QY
      <- Switch[977675860] #TY -> #X
      <- UnconditionalJump[GOTO] #TZ -> #X
===#Block QY(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 64282876:
      	 goto	#QZ
      case 1155663029:
      	 goto	#GX
      case 1266689126:
      	 goto	#JC
      case 2040766351:
      	 goto	#QY
      default:
      	 goto	#JC
   }
      -> Switch[64282876] #QY -> #QZ
      -> Switch[2040766351] #QY -> #QY
      -> Immediate #QY -> #QZ
      -> DefaultSwitch #QY -> #JC
      -> Switch[1155663029] #QY -> #GX
      -> Switch[1266689126] #QY -> #JC
      <- UnconditionalJump[GOTO] #X -> #QY
      <- Switch[2040766351] #QY -> #QY
===#Block QZ(size=2, flags=100)===
   0. lvar105 = {388393579 ^ lvar105};
   1. goto GX
      -> UnconditionalJump[GOTO] #QZ -> #GX
      <- Switch[64282876] #QY -> #QZ
      <- Immediate #QY -> #QZ
===#Block GX(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 255318130)
      goto GW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GX -> #GW
      -> TryCatch range: [GX...GW] -> ABW ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #QZ -> #GX
      <- Switch[1155663029] #QY -> #GX
===#Block GW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GX...GW] -> ABW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GX -> #GW
===#Block ABW(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -2111853551:
      	 goto	#ABY
      case 361448763:
      	 goto	#ABX
      default:
      	 goto	#ABZ
   }
      -> DefaultSwitch #ABW -> #ABZ
      -> Switch[361448763] #ABW -> #ABX
      -> Switch[-2111853551] #ABW -> #ABY
      <- TryCatch range: [GX...GW] -> ABW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GX...GW] -> ABW ([Ljava/lang/RuntimeException;])
===#Block ABY(size=2, flags=10100)===
   0. lvar105 = {354499582 ^ lvar105};
   1. goto GY
      -> UnconditionalJump[GOTO] #ABY -> #GY
      <- Switch[-2111853551] #ABW -> #ABY
===#Block ABX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1205053147);
   1. goto GY
      -> UnconditionalJump[GOTO] #ABX -> #GY
      <- Switch[361448763] #ABW -> #ABX
===#Block GY(size=2, flags=0)===
   0. _consume(catch());
   1. goto NG
      -> UnconditionalJump[GOTO] #GY -> #NG
      <- UnconditionalJump[GOTO] #ABX -> #GY
      <- UnconditionalJump[GOTO] #ABY -> #GY
===#Block NG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1261593028);
   1. goto CN
      -> UnconditionalJump[GOTO] #NG -> #CN
      <- UnconditionalJump[GOTO] #GY -> #NG
===#Block ABZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABW -> #ABZ
===#Block OX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1121699669);
   1. goto JC
      -> UnconditionalJump[GOTO] #OX -> #JC
      <- UnconditionalJump[GOTO] #JQ -> #OX
===#Block VG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1489779348);
   1. goto N
      -> UnconditionalJump[GOTO] #VG -> #N
      <- Switch[40729545] #D -> #VG
===#Block N(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar9;
   2. lvar78 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ipsleyxqvziocdb(), lvar105);
   3. lvar21 = lvar20.equals(lvar78);
   4. if (lvar21 != {1123715161 ^ lvar105})
      goto UP
   5. lvar105 = {1261208013 ^ lvar105};
      -> Immediate #N -> #P
      -> ConditionalJump[IF_ICMPNE] #N -> #UP
      <- UnconditionalJump[GOTO] #VG -> #N
===#Block UP(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 29698774:
      	 goto	#UQ
      case 342444861:
      	 goto	#UP
      case 1149142018:
      	 goto	#JC
      case 1338424865:
      	 goto	#KE
      default:
      	 goto	#JC
   }
      -> Switch[29698774] #UP -> #UQ
      -> Switch[1338424865] #UP -> #KE
      -> Switch[1149142018] #UP -> #JC
      -> Switch[342444861] #UP -> #UP
      -> Immediate #UP -> #UQ
      -> DefaultSwitch #UP -> #JC
      <- Switch[342444861] #UP -> #UP
      <- ConditionalJump[IF_ICMPNE] #N -> #UP
===#Block UQ(size=2, flags=100)===
   0. lvar105 = {1022071924 ^ lvar105};
   1. goto KE
      -> UnconditionalJump[GOTO] #UQ -> #KE
      <- Switch[29698774] #UP -> #UQ
      <- Immediate #UP -> #UQ
===#Block KE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -259464853)
      goto TG
   1. goto ML
      -> UnconditionalJump[GOTO] #KE -> #ML
      -> ConditionalJump[IF_ICMPEQ] #KE -> #TG
      <- Switch[1338424865] #UP -> #KE
      <- UnconditionalJump[GOTO] #UQ -> #KE
===#Block TG(size=2, flags=10100)===
   0. lvar105 = {297160287 ^ lvar105};
   1. goto O
      -> UnconditionalJump[GOTO] #TG -> #O
      <- ConditionalJump[IF_ICMPEQ] #KE -> #TG
===#Block O(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.DIAMOND_BLOCK;
   2. goto OZ
      -> UnconditionalJump[GOTO] #O -> #OZ
      <- UnconditionalJump[GOTO] #TG -> #O
===#Block OZ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 139739163:
      	 goto	#PA
      case 614048416:
      	 goto	#OZ
      case 721475451:
      	 goto	#JC
      case 1460949931:
      	 goto	#FE
      default:
      	 goto	#JC
   }
      -> Switch[614048416] #OZ -> #OZ
      -> DefaultSwitch #OZ -> #JC
      -> Switch[721475451] #OZ -> #JC
      -> Switch[139739163] #OZ -> #PA
      -> Switch[1460949931] #OZ -> #FE
      -> Immediate #OZ -> #PA
      <- Switch[614048416] #OZ -> #OZ
      <- UnconditionalJump[GOTO] #O -> #OZ
===#Block PA(size=2, flags=100)===
   0. lvar105 = {1483922651 ^ lvar105};
   1. goto FE
      -> UnconditionalJump[GOTO] #PA -> #FE
      <- Switch[139739163] #OZ -> #PA
      <- Immediate #OZ -> #PA
===#Block FE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 204338098)
      goto FD
   1. throw nullconst;
      -> TryCatch range: [FE...FD] -> ZO ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FE -> #FD
      <- UnconditionalJump[GOTO] #PA -> #FE
      <- Switch[1460949931] #OZ -> #FE
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FE...FD] -> ZO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FE -> #FD
===#Block ZO(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1953958703:
      	 goto	#ZQ
      case -1095994039:
      	 goto	#ZP
      default:
      	 goto	#ZR
   }
      -> Switch[-1953958703] #ZO -> #ZQ
      -> DefaultSwitch #ZO -> #ZR
      -> Switch[-1095994039] #ZO -> #ZP
      <- TryCatch range: [FE...FD] -> ZO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FE...FD] -> ZO ([Ljava/lang/RuntimeException;])
===#Block ZP(size=2, flags=10100)===
   0. lvar105 = {655516771 ^ lvar105};
   1. goto FF
      -> UnconditionalJump[GOTO] #ZP -> #FF
      <- Switch[-1095994039] #ZO -> #ZP
===#Block ZR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZO -> #ZR
===#Block ZQ(size=2, flags=10100)===
   0. lvar105 = {565638224 ^ lvar105};
   1. goto FF
      -> UnconditionalJump[GOTO] #ZQ -> #FF
      <- Switch[-1953958703] #ZO -> #ZQ
===#Block FF(size=2, flags=0)===
   0. _consume(catch());
   1. goto MH
      -> UnconditionalJump[GOTO] #FF -> #MH
      <- UnconditionalJump[GOTO] #ZQ -> #FF
      <- UnconditionalJump[GOTO] #ZP -> #FF
===#Block MH(size=2, flags=10100)===
   0. lvar105 = {2123842930 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #MH -> #CN
      <- UnconditionalJump[GOTO] #FF -> #MH
===#Block ML(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1858349464);
   1. goto JC
      -> UnconditionalJump[GOTO] #ML -> #JC
      <- UnconditionalJump[GOTO] #KE -> #ML
===#Block P(size=1, flags=0)===
   0. goto QI
      -> UnconditionalJump[GOTO] #P -> #QI
      <- Immediate #N -> #P
===#Block QI(size=2, flags=10100)===
   0. lvar105 = {1181366808 ^ lvar105};
   1. goto EA
      -> UnconditionalJump[GOTO] #QI -> #EA
      <- UnconditionalJump[GOTO] #P -> #QI
===#Block EA(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 175959139)
      goto DZ
   1. throw nullconst;
      -> TryCatch range: [EA...DZ] -> YA ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #EA -> #DZ
      <- UnconditionalJump[GOTO] #QI -> #EA
===#Block DZ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EA...DZ] -> YA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EA -> #DZ
===#Block YA(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 475238640:
      	 goto	#YC
      case 2111937634:
      	 goto	#YB
      default:
      	 goto	#YD
   }
      -> Switch[2111937634] #YA -> #YB
      -> DefaultSwitch #YA -> #YD
      -> Switch[475238640] #YA -> #YC
      <- TryCatch range: [EA...DZ] -> YA ([Ljava/io/IOException;])
      <- TryCatch range: [EA...DZ] -> YA ([Ljava/io/IOException;])
===#Block YC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1539546460);
   1. goto EB
      -> UnconditionalJump[GOTO] #YC -> #EB
      <- Switch[475238640] #YA -> #YC
===#Block YD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YA -> #YD
===#Block YB(size=2, flags=10100)===
   0. lvar105 = {401988814 ^ lvar105};
   1. goto EB
      -> UnconditionalJump[GOTO] #YB -> #EB
      <- Switch[2111937634] #YA -> #YB
===#Block EB(size=2, flags=0)===
   0. _consume(catch());
   1. goto KQ
      -> UnconditionalJump[GOTO] #EB -> #KQ
      <- UnconditionalJump[GOTO] #YB -> #EB
      <- UnconditionalJump[GOTO] #YC -> #EB
===#Block KQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 362396812);
   1. goto AE
      -> UnconditionalJump[GOTO] #KQ -> #AE
      <- UnconditionalJump[GOTO] #EB -> #KQ
===#Block VF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1201323345);
   1. goto AC
      -> UnconditionalJump[GOTO] #VF -> #AC
      <- Switch[40729539] #D -> #VF
===#Block AC(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar30 = lvar9;
   2. lvar83 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.kpvwgbqpiuchqre(), lvar105);
   3. lvar31 = lvar30.equals(lvar83);
   4. if (lvar31 != {1571557276 ^ lvar105})
      goto RR
   5. lvar105 = {1115677688 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AC -> #RR
      -> Immediate #AC -> #AD
      <- UnconditionalJump[GOTO] #VF -> #AC
===#Block AD(size=1, flags=0)===
   0. goto MV
      -> UnconditionalJump[GOTO] #AD -> #MV
      <- Immediate #AC -> #AD
===#Block MV(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 229279186:
      	 goto	#MW
      case 810672428:
      	 goto	#MV
      case 1223335156:
      	 goto	#JC
      case 2067858543:
      	 goto	#DC
      default:
      	 goto	#JC
   }
      -> Switch[810672428] #MV -> #MV
      -> Switch[1223335156] #MV -> #JC
      -> DefaultSwitch #MV -> #JC
      -> Switch[2067858543] #MV -> #DC
      -> Switch[229279186] #MV -> #MW
      -> Immediate #MV -> #MW
      <- Switch[810672428] #MV -> #MV
      <- UnconditionalJump[GOTO] #AD -> #MV
===#Block MW(size=2, flags=100)===
   0. lvar105 = {631180328 ^ lvar105};
   1. goto DC
      -> UnconditionalJump[GOTO] #MW -> #DC
      <- Switch[229279186] #MV -> #MW
      <- Immediate #MV -> #MW
===#Block DC(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 16042207)
      goto DB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DC -> #DB
      -> TryCatch range: [DC...DB] -> WU ([Ljava/lang/IllegalAccessException;])
      <- Switch[2067858543] #MV -> #DC
      <- UnconditionalJump[GOTO] #MW -> #DC
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DC...DB] -> WU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DC -> #DB
===#Block WU(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -765001119:
      	 goto	#WV
      case -349145070:
      	 goto	#WW
      default:
      	 goto	#WX
   }
      -> DefaultSwitch #WU -> #WX
      -> Switch[-349145070] #WU -> #WW
      -> Switch[-765001119] #WU -> #WV
      <- TryCatch range: [DC...DB] -> WU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DC...DB] -> WU ([Ljava/lang/IllegalAccessException;])
===#Block WV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 371426471);
   1. goto DD
      -> UnconditionalJump[GOTO] #WV -> #DD
      <- Switch[-765001119] #WU -> #WV
===#Block WW(size=2, flags=10100)===
   0. lvar105 = {1896442857 ^ lvar105};
   1. goto DD
      -> UnconditionalJump[GOTO] #WW -> #DD
      <- Switch[-349145070] #WU -> #WW
===#Block DD(size=2, flags=0)===
   0. _consume(catch());
   1. goto MT
      -> UnconditionalJump[GOTO] #DD -> #MT
      <- UnconditionalJump[GOTO] #WW -> #DD
      <- UnconditionalJump[GOTO] #WV -> #DD
===#Block MT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1639921445);
   1. goto AE
      -> UnconditionalJump[GOTO] #MT -> #AE
      <- UnconditionalJump[GOTO] #DD -> #MT
===#Block WX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WU -> #WX
===#Block RR(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 92078994:
      	 goto	#RS
      case 1423123614:
      	 goto	#RR
      case 1683976205:
      	 goto	#JC
      case 2012850631:
      	 goto	#JD
      default:
      	 goto	#JC
   }
      -> Switch[2012850631] #RR -> #JD
      -> Switch[1683976205] #RR -> #JC
      -> DefaultSwitch #RR -> #JC
      -> Immediate #RR -> #RS
      -> Switch[92078994] #RR -> #RS
      -> Switch[1423123614] #RR -> #RR
      <- ConditionalJump[IF_ICMPNE] #AC -> #RR
      <- Switch[1423123614] #RR -> #RR
===#Block RS(size=2, flags=100)===
   0. lvar105 = {1131242254 ^ lvar105};
   1. goto JD
      -> UnconditionalJump[GOTO] #RS -> #JD
      <- Immediate #RR -> #RS
      <- Switch[92078994] #RR -> #RS
===#Block JD(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -167025520)
      goto UB
   1. goto PI
      -> ConditionalJump[IF_ICMPEQ] #JD -> #UB
      -> UnconditionalJump[GOTO] #JD -> #PI
      <- Switch[2012850631] #RR -> #JD
      <- UnconditionalJump[GOTO] #RS -> #JD
===#Block PI(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 165401711:
      	 goto	#JC
      case 194436251:
      	 goto	#PJ
      case 1645385975:
      	 goto	#JC
      case 1651170623:
      	 goto	#PI
      default:
      	 goto	#JC
   }
      -> Switch[1645385975] #PI -> #JC
      -> DefaultSwitch #PI -> #JC
      -> Switch[194436251] #PI -> #PJ
      -> Switch[1651170623] #PI -> #PI
      -> Immediate #PI -> #PJ
      <- Switch[1651170623] #PI -> #PI
      <- UnconditionalJump[GOTO] #JD -> #PI
===#Block PJ(size=2, flags=100)===
   0. lvar105 = {236214567 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PJ -> #JC
      <- Switch[194436251] #PI -> #PJ
      <- Immediate #PI -> #PJ
===#Block UB(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 194436251:
      	 goto	#UC
      case 633506292:
      	 goto	#JC
      case 1194190110:
      	 goto	#UB
      case 1264020909:
      	 goto	#AF
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #UB -> #JC
      -> Switch[1194190110] #UB -> #UB
      -> Immediate #UB -> #UC
      -> Switch[633506292] #UB -> #JC
      -> Switch[194436251] #UB -> #UC
      -> Switch[1264020909] #UB -> #AF
      <- ConditionalJump[IF_ICMPEQ] #JD -> #UB
      <- Switch[1194190110] #UB -> #UB
===#Block UC(size=2, flags=100)===
   0. lvar105 = {1666076773 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #UC -> #AF
      <- Immediate #UB -> #UC
      <- Switch[194436251] #UB -> #UC
===#Block AF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PRISMARINE;
   2. goto QM
      -> UnconditionalJump[GOTO] #AF -> #QM
      <- UnconditionalJump[GOTO] #UC -> #AF
      <- Switch[1264020909] #UB -> #AF
===#Block QM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 32721374:
      	 goto	#JC
      case 54918033:
      	 goto	#QN
      case 395016881:
      	 goto	#QM
      case 2092463575:
      	 goto	#EM
      default:
      	 goto	#JC
   }
      -> Switch[54918033] #QM -> #QN
      -> Immediate #QM -> #QN
      -> Switch[2092463575] #QM -> #EM
      -> Switch[32721374] #QM -> #JC
      -> DefaultSwitch #QM -> #JC
      -> Switch[395016881] #QM -> #QM
      <- UnconditionalJump[GOTO] #AF -> #QM
      <- Switch[395016881] #QM -> #QM
===#Block QN(size=2, flags=100)===
   0. lvar105 = {6111383 ^ lvar105};
   1. goto EM
      -> UnconditionalJump[GOTO] #QN -> #EM
      <- Switch[54918033] #QM -> #QN
      <- Immediate #QM -> #QN
===#Block EM(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 63417640)
      goto EL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EM -> #EL
      -> TryCatch range: [EM...EL] -> YQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #QN -> #EM
      <- Switch[2092463575] #QM -> #EM
===#Block EL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EM...EL] -> YQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EM -> #EL
===#Block YQ(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -292297981:
      	 goto	#YR
      case -9197509:
      	 goto	#YS
      default:
      	 goto	#YT
   }
      -> Switch[-292297981] #YQ -> #YR
      -> Switch[-9197509] #YQ -> #YS
      -> DefaultSwitch #YQ -> #YT
      <- TryCatch range: [EM...EL] -> YQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EM...EL] -> YQ ([Ljava/lang/IllegalAccessException;])
===#Block YT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #YQ -> #YT
===#Block YS(size=2, flags=10100)===
   0. lvar105 = {1121052343 ^ lvar105};
   1. goto EN
      -> UnconditionalJump[GOTO] #YS -> #EN
      <- Switch[-9197509] #YQ -> #YS
===#Block YR(size=2, flags=10100)===
   0. lvar105 = {1089335248 ^ lvar105};
   1. goto EN
      -> UnconditionalJump[GOTO] #YR -> #EN
      <- Switch[-292297981] #YQ -> #YR
===#Block EN(size=2, flags=0)===
   0. _consume(catch());
   1. goto RL
      -> UnconditionalJump[GOTO] #EN -> #RL
      <- UnconditionalJump[GOTO] #YR -> #EN
      <- UnconditionalJump[GOTO] #YS -> #EN
===#Block RL(size=2, flags=10100)===
   0. lvar105 = {1399798792 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #RL -> #CN
      <- UnconditionalJump[GOTO] #EN -> #RL
===#Block VP(size=2, flags=10100)===
   0. lvar105 = {151808205 ^ lvar105};
   1. goto K
      -> UnconditionalJump[GOTO] #VP -> #K
      <- Switch[40729567] #D -> #VP
===#Block K(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar18 = lvar9;
   2. lvar77 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ijyfdgbmhitezqj(), lvar105);
   3. lvar19 = lvar18.equals(lvar77);
   4. if (lvar19 != {322624000 ^ lvar105})
      goto SY
   5. lvar105 = {1211009479 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #K -> #SY
      -> Immediate #K -> #L
      <- UnconditionalJump[GOTO] #VP -> #K
===#Block L(size=1, flags=0)===
   0. goto LT
      -> UnconditionalJump[GOTO] #L -> #LT
      <- Immediate #K -> #L
===#Block LT(size=2, flags=10100)===
   0. lvar105 = {1702944405 ^ lvar105};
   1. goto GI
      -> UnconditionalJump[GOTO] #LT -> #GI
      <- UnconditionalJump[GOTO] #L -> #LT
===#Block GI(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 155184986)
      goto GH
   1. throw nullconst;
      -> TryCatch range: [GI...GH] -> ABC ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #GI -> #GH
      <- UnconditionalJump[GOTO] #LT -> #GI
===#Block GH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GI...GH] -> ABC ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GI -> #GH
===#Block ABC(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -190551407:
      	 goto	#ABD
      case 2083159561:
      	 goto	#ABE
      default:
      	 goto	#ABF
   }
      -> DefaultSwitch #ABC -> #ABF
      -> Switch[-190551407] #ABC -> #ABD
      -> Switch[2083159561] #ABC -> #ABE
      <- TryCatch range: [GI...GH] -> ABC ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GI...GH] -> ABC ([Ljava/lang/IllegalAccessException;])
===#Block ABE(size=2, flags=10100)===
   0. lvar105 = {843059452 ^ lvar105};
   1. goto GJ
      -> UnconditionalJump[GOTO] #ABE -> #GJ
      <- Switch[2083159561] #ABC -> #ABE
===#Block ABD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 592564847);
   1. goto GJ
      -> UnconditionalJump[GOTO] #ABD -> #GJ
      <- Switch[-190551407] #ABC -> #ABD
===#Block GJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto ON
      -> UnconditionalJump[GOTO] #GJ -> #ON
      <- UnconditionalJump[GOTO] #ABE -> #GJ
      <- UnconditionalJump[GOTO] #ABD -> #GJ
===#Block ON(size=2, flags=10100)===
   0. lvar105 = {1343556851 ^ lvar105};
   1. goto AE
      -> UnconditionalJump[GOTO] #ON -> #AE
      <- UnconditionalJump[GOTO] #GJ -> #ON
===#Block ABF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ABC -> #ABF
===#Block SY(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 88216922:
      	 goto	#SZ
      case 131801294:
      	 goto	#KC
      case 518060310:
      	 goto	#SY
      case 1555573450:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[518060310] #SY -> #SY
      -> Immediate #SY -> #SZ
      -> Switch[1555573450] #SY -> #JC
      -> DefaultSwitch #SY -> #JC
      -> Switch[88216922] #SY -> #SZ
      -> Switch[131801294] #SY -> #KC
      <- ConditionalJump[IF_ICMPNE] #K -> #SY
      <- Switch[518060310] #SY -> #SY
===#Block SZ(size=2, flags=100)===
   0. lvar105 = {1209020404 ^ lvar105};
   1. goto KC
      -> UnconditionalJump[GOTO] #SZ -> #KC
      <- Immediate #SY -> #SZ
      <- Switch[88216922] #SY -> #SZ
===#Block KC(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -648564830)
      goto SJ
   1. goto OH
      -> ConditionalJump[IF_ICMPEQ] #KC -> #SJ
      -> UnconditionalJump[GOTO] #KC -> #OH
      <- UnconditionalJump[GOTO] #SZ -> #KC
      <- Switch[131801294] #SY -> #KC
===#Block OH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1275052097);
   1. goto JC
      -> UnconditionalJump[GOTO] #OH -> #JC
      <- UnconditionalJump[GOTO] #KC -> #OH
===#Block SJ(size=2, flags=10100)===
   0. lvar105 = {209565913 ^ lvar105};
   1. goto M
      -> UnconditionalJump[GOTO] #SJ -> #M
      <- ConditionalJump[IF_ICMPEQ] #KC -> #SJ
===#Block M(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GOLD_BLOCK;
   2. goto OK
      -> UnconditionalJump[GOTO] #M -> #OK
      <- UnconditionalJump[GOTO] #SJ -> #M
===#Block OK(size=2, flags=10100)===
   0. lvar105 = {1940352217 ^ lvar105};
   1. goto IZ
      -> UnconditionalJump[GOTO] #OK -> #IZ
      <- UnconditionalJump[GOTO] #M -> #OK
===#Block IZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 126969144)
      goto IY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IZ -> #IY
      -> TryCatch range: [IZ...IY] -> AEQ ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #OK -> #IZ
===#Block IY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IZ...IY] -> AEQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IZ -> #IY
===#Block AEQ(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 302503658:
      	 goto	#AES
      case 662769569:
      	 goto	#AER
      default:
      	 goto	#AET
   }
      -> DefaultSwitch #AEQ -> #AET
      -> Switch[302503658] #AEQ -> #AES
      -> Switch[662769569] #AEQ -> #AER
      <- TryCatch range: [IZ...IY] -> AEQ ([Ljava/io/IOException;])
      <- TryCatch range: [IZ...IY] -> AEQ ([Ljava/io/IOException;])
===#Block AER(size=2, flags=10100)===
   0. lvar105 = {42244027 ^ lvar105};
   1. goto JA
      -> UnconditionalJump[GOTO] #AER -> #JA
      <- Switch[662769569] #AEQ -> #AER
===#Block AES(size=2, flags=10100)===
   0. lvar105 = {1681149074 ^ lvar105};
   1. goto JA
      -> UnconditionalJump[GOTO] #AES -> #JA
      <- Switch[302503658] #AEQ -> #AES
===#Block JA(size=2, flags=0)===
   0. _consume(catch());
   1. goto OV
      -> UnconditionalJump[GOTO] #JA -> #OV
      <- UnconditionalJump[GOTO] #AES -> #JA
      <- UnconditionalJump[GOTO] #AER -> #JA
===#Block OV(size=2, flags=10100)===
   0. lvar105 = {1210325495 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OV -> #CN
      <- UnconditionalJump[GOTO] #JA -> #OV
===#Block AET(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEQ -> #AET
===#Block VL(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 46784603:
      	 goto	#VM
      case 1253988535:
      	 goto	#Q
      case 1779947879:
      	 goto	#VL
      case 1932450288:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[46784603] #VL -> #VM
      -> Switch[1253988535] #VL -> #Q
      -> Switch[1779947879] #VL -> #VL
      -> Switch[1932450288] #VL -> #JC
      -> Immediate #VL -> #VM
      -> DefaultSwitch #VL -> #JC
      <- Switch[40729557] #D -> #VL
      <- Switch[1779947879] #VL -> #VL
===#Block VM(size=2, flags=100)===
   0. lvar105 = {1614454612 ^ lvar105};
   1. goto Q
      -> UnconditionalJump[GOTO] #VM -> #Q
      <- Switch[46784603] #VL -> #VM
      <- Immediate #VL -> #VM
===#Block Q(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar9;
   2. lvar79 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.nkjogvxbwjmsalr(), lvar105);
   3. lvar23 = lvar22.equals(lvar79);
   4. if (lvar23 != {2047618457 ^ lvar105})
      goto SR
   5. lvar105 = {1562069772 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #Q -> #SR
      -> Immediate #Q -> #S
      <- UnconditionalJump[GOTO] #VM -> #Q
      <- Switch[1253988535] #VL -> #Q
===#Block S(size=1, flags=0)===
   0. goto QG
      -> UnconditionalJump[GOTO] #S -> #QG
      <- Immediate #Q -> #S
===#Block QG(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 180824216:
      	 goto	#JC
      case 196964823:
      	 goto	#QH
      case 475096582:
      	 goto	#DX
      case 1174940927:
      	 goto	#QG
      default:
      	 goto	#JC
   }
      -> Immediate #QG -> #QH
      -> Switch[475096582] #QG -> #DX
      -> Switch[1174940927] #QG -> #QG
      -> Switch[196964823] #QG -> #QH
      -> DefaultSwitch #QG -> #JC
      -> Switch[180824216] #QG -> #JC
      <- UnconditionalJump[GOTO] #S -> #QG
      <- Switch[1174940927] #QG -> #QG
===#Block QH(size=2, flags=100)===
   0. lvar105 = {65201255 ^ lvar105};
   1. goto DX
      -> UnconditionalJump[GOTO] #QH -> #DX
      <- Immediate #QG -> #QH
      <- Switch[196964823] #QG -> #QH
===#Block DX(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 127633729)
      goto DW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DX -> #DW
      -> TryCatch range: [DX...DW] -> XW ([Ljava/lang/RuntimeException;])
      <- Switch[475096582] #QG -> #DX
      <- UnconditionalJump[GOTO] #QH -> #DX
===#Block DW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DX...DW] -> XW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DX -> #DW
===#Block XW(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 665589649:
      	 goto	#XX
      case 2122572066:
      	 goto	#XY
      default:
      	 goto	#XZ
   }
      -> Switch[2122572066] #XW -> #XY
      -> DefaultSwitch #XW -> #XZ
      -> Switch[665589649] #XW -> #XX
      <- TryCatch range: [DX...DW] -> XW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DX...DW] -> XW ([Ljava/lang/RuntimeException;])
===#Block XX(size=2, flags=10100)===
   0. lvar105 = {1935942342 ^ lvar105};
   1. goto DY
      -> UnconditionalJump[GOTO] #XX -> #DY
      <- Switch[665589649] #XW -> #XX
===#Block XZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #XW -> #XZ
===#Block XY(size=2, flags=10100)===
   0. lvar105 = {406969360 ^ lvar105};
   1. goto DY
      -> UnconditionalJump[GOTO] #XY -> #DY
      <- Switch[2122572066] #XW -> #XY
===#Block DY(size=2, flags=0)===
   0. _consume(catch());
   1. goto RF
      -> UnconditionalJump[GOTO] #DY -> #RF
      <- UnconditionalJump[GOTO] #XX -> #DY
      <- UnconditionalJump[GOTO] #XY -> #DY
===#Block RF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 440529914);
   1. goto AE
      -> UnconditionalJump[GOTO] #RF -> #AE
      <- UnconditionalJump[GOTO] #DY -> #RF
===#Block SR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1968572421);
   1. goto JV
      -> UnconditionalJump[GOTO] #SR -> #JV
      <- ConditionalJump[IF_ICMPNE] #Q -> #SR
===#Block JV(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 2060528864)
      goto SS
   1. goto QC
      -> UnconditionalJump[GOTO] #JV -> #QC
      -> ConditionalJump[IF_ICMPEQ] #JV -> #SS
      <- UnconditionalJump[GOTO] #SR -> #JV
===#Block SS(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1563065987);
   1. goto R
      -> UnconditionalJump[GOTO] #SS -> #R
      <- ConditionalJump[IF_ICMPEQ] #JV -> #SS
===#Block R(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.EMERALD_BLOCK;
   2. goto MQ
      -> UnconditionalJump[GOTO] #R -> #MQ
      <- UnconditionalJump[GOTO] #SS -> #R
===#Block MQ(size=2, flags=10100)===
   0. lvar105 = {1554548695 ^ lvar105};
   1. goto IN
      -> UnconditionalJump[GOTO] #MQ -> #IN
      <- UnconditionalJump[GOTO] #R -> #MQ
===#Block IN(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 214181147)
      goto IM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IN -> #IM
      -> TryCatch range: [IN...IM] -> AEA ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #MQ -> #IN
===#Block IM(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IN...IM] -> AEA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IN -> #IM
===#Block AEA(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -931217455:
      	 goto	#AEC
      case 1992746560:
      	 goto	#AEB
      default:
      	 goto	#AED
   }
      -> Switch[1992746560] #AEA -> #AEB
      -> DefaultSwitch #AEA -> #AED
      -> Switch[-931217455] #AEA -> #AEC
      <- TryCatch range: [IN...IM] -> AEA ([Ljava/io/IOException;])
      <- TryCatch range: [IN...IM] -> AEA ([Ljava/io/IOException;])
===#Block AEC(size=2, flags=10100)===
   0. lvar105 = {347207947 ^ lvar105};
   1. goto IO
      -> UnconditionalJump[GOTO] #AEC -> #IO
      <- Switch[-931217455] #AEA -> #AEC
===#Block AED(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEA -> #AED
===#Block AEB(size=2, flags=10100)===
   0. lvar105 = {593949241 ^ lvar105};
   1. goto IO
      -> UnconditionalJump[GOTO] #AEB -> #IO
      <- Switch[1992746560] #AEA -> #AEB
===#Block IO(size=2, flags=0)===
   0. _consume(catch());
   1. goto OQ
      -> UnconditionalJump[GOTO] #IO -> #OQ
      <- UnconditionalJump[GOTO] #AEB -> #IO
      <- UnconditionalJump[GOTO] #AEC -> #IO
===#Block OQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1139704137);
   1. goto CN
      -> UnconditionalJump[GOTO] #OQ -> #CN
      <- UnconditionalJump[GOTO] #IO -> #OQ
===#Block QC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 529469481);
   1. goto JC
      -> UnconditionalJump[GOTO] #QC -> #JC
      <- UnconditionalJump[GOTO] #JV -> #QC
===#Block VQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1474748675);
   1. goto AE
      -> UnconditionalJump[GOTO] #VQ -> #AE
      <- DefaultSwitch #D -> #VQ
===#Block VO(size=2, flags=10100)===
   0. lvar105 = {950852742 ^ lvar105};
   1. goto E
      -> UnconditionalJump[GOTO] #VO -> #E
      <- Switch[40729565] #D -> #VO
===#Block E(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar13 = lvar9;
   2. lvar6 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.riibyjjmwjkrcqe(), lvar105);
   3. lvar14 = lvar13.equals(lvar6);
   4. if (lvar14 != {580546123 ^ lvar105})
      goto SN
   5. lvar105 = {249613513 ^ lvar105};
      -> Immediate #E -> #G
      -> ConditionalJump[IF_ICMPNE] #E -> #SN
      <- UnconditionalJump[GOTO] #VO -> #E
===#Block SN(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 51058475:
      	 goto	#SO
      case 990213422:
      	 goto	#JC
      case 1459575463:
      	 goto	#SN
      case 1492524032:
      	 goto	#JT
      default:
      	 goto	#JC
   }
      -> Immediate #SN -> #SO
      -> Switch[990213422] #SN -> #JC
      -> DefaultSwitch #SN -> #JC
      -> Switch[1459575463] #SN -> #SN
      -> Switch[51058475] #SN -> #SO
      -> Switch[1492524032] #SN -> #JT
      <- Switch[1459575463] #SN -> #SN
      <- ConditionalJump[IF_ICMPNE] #E -> #SN
===#Block SO(size=2, flags=100)===
   0. lvar105 = {1537591631 ^ lvar105};
   1. goto JT
      -> UnconditionalJump[GOTO] #SO -> #JT
      <- Immediate #SN -> #SO
      <- Switch[51058475] #SN -> #SO
===#Block JT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -906151901)
      goto TT
   1. goto OM
      -> ConditionalJump[IF_ICMPEQ] #JT -> #TT
      -> UnconditionalJump[GOTO] #JT -> #OM
      <- UnconditionalJump[GOTO] #SO -> #JT
      <- Switch[1492524032] #SN -> #JT
===#Block OM(size=2, flags=10100)===
   0. lvar105 = {1776980657 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OM -> #JC
      <- UnconditionalJump[GOTO] #JT -> #OM
===#Block TT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 606439503);
   1. goto F
      -> UnconditionalJump[GOTO] #TT -> #F
      <- ConditionalJump[IF_ICMPEQ] #JT -> #TT
===#Block F(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.LAPIS_BLOCK;
   2. goto KX
      -> UnconditionalJump[GOTO] #F -> #KX
      <- UnconditionalJump[GOTO] #TT -> #F
===#Block KX(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 73590459:
      	 goto	#KY
      case 101327393:
      	 goto	#JC
      case 1381302277:
      	 goto	#ED
      case 2126015285:
      	 goto	#KX
      default:
      	 goto	#JC
   }
      -> Switch[2126015285] #KX -> #KX
      -> Switch[101327393] #KX -> #JC
      -> Switch[73590459] #KX -> #KY
      -> DefaultSwitch #KX -> #JC
      -> Immediate #KX -> #KY
      -> Switch[1381302277] #KX -> #ED
      <- UnconditionalJump[GOTO] #F -> #KX
      <- Switch[2126015285] #KX -> #KX
===#Block KY(size=2, flags=100)===
   0. lvar105 = {1434429522 ^ lvar105};
   1. goto ED
      -> UnconditionalJump[GOTO] #KY -> #ED
      <- Switch[73590459] #KX -> #KY
      <- Immediate #KX -> #KY
===#Block ED(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 4504997)
      goto EC
   1. throw nullconst;
      -> TryCatch range: [ED...EC] -> YE ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #ED -> #EC
      <- UnconditionalJump[GOTO] #KY -> #ED
      <- Switch[1381302277] #KX -> #ED
===#Block EC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [ED...EC] -> YE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #ED -> #EC
===#Block YE(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 13933224:
      	 goto	#YG
      case 1126971592:
      	 goto	#YF
      default:
      	 goto	#YH
   }
      -> Switch[13933224] #YE -> #YG
      -> Switch[1126971592] #YE -> #YF
      -> DefaultSwitch #YE -> #YH
      <- TryCatch range: [ED...EC] -> YE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [ED...EC] -> YE ([Ljava/lang/IllegalAccessException;])
===#Block YH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #YE -> #YH
===#Block YF(size=2, flags=10100)===
   0. lvar105 = {647888201 ^ lvar105};
   1. goto EE
      -> UnconditionalJump[GOTO] #YF -> #EE
      <- Switch[1126971592] #YE -> #YF
===#Block YG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 786622725);
   1. goto EE
      -> UnconditionalJump[GOTO] #YG -> #EE
      <- Switch[13933224] #YE -> #YG
===#Block EE(size=2, flags=0)===
   0. _consume(catch());
   1. goto KM
      -> UnconditionalJump[GOTO] #EE -> #KM
      <- UnconditionalJump[GOTO] #YG -> #EE
      <- UnconditionalJump[GOTO] #YF -> #EE
===#Block KM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 184631811:
      	 goto	#KN
      case 572105805:
      	 goto	#KM
      case 1979505819:
      	 goto	#CN
      case 2122880631:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #KM -> #JC
      -> Switch[2122880631] #KM -> #JC
      -> Switch[184631811] #KM -> #KN
      -> Switch[572105805] #KM -> #KM
      -> Switch[1979505819] #KM -> #CN
      -> Immediate #KM -> #KN
      <- UnconditionalJump[GOTO] #EE -> #KM
      <- Switch[572105805] #KM -> #KM
===#Block KN(size=2, flags=100)===
   0. lvar105 = {1084806632 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #KN -> #CN
      <- Switch[184631811] #KM -> #KN
      <- Immediate #KM -> #KN
===#Block G(size=1, flags=0)===
   0. goto MU
      -> UnconditionalJump[GOTO] #G -> #MU
      <- Immediate #E -> #G
===#Block MU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1577453072);
   1. goto CW
      -> UnconditionalJump[GOTO] #MU -> #CW
      <- UnconditionalJump[GOTO] #G -> #MU
===#Block CW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 231804070)
      goto CV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CW -> #CV
      -> TryCatch range: [CW...CV] -> WM ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #MU -> #CW
===#Block CV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CW...CV] -> WM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CW -> #CV
===#Block WM(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1813699437:
      	 goto	#WN
      case 1509882139:
      	 goto	#WO
      default:
      	 goto	#WP
   }
      -> DefaultSwitch #WM -> #WP
      -> Switch[1509882139] #WM -> #WO
      -> Switch[-1813699437] #WM -> #WN
      <- TryCatch range: [CW...CV] -> WM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CW...CV] -> WM ([Ljava/lang/IllegalAccessException;])
===#Block WN(size=2, flags=10100)===
   0. lvar105 = {849058959 ^ lvar105};
   1. goto CX
      -> UnconditionalJump[GOTO] #WN -> #CX
      <- Switch[-1813699437] #WM -> #WN
===#Block WO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 735635262);
   1. goto CX
      -> UnconditionalJump[GOTO] #WO -> #CX
      <- Switch[1509882139] #WM -> #WO
===#Block CX(size=2, flags=0)===
   0. _consume(catch());
   1. goto NQ
      -> UnconditionalJump[GOTO] #CX -> #NQ
      <- UnconditionalJump[GOTO] #WO -> #CX
      <- UnconditionalJump[GOTO] #WN -> #CX
===#Block NQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 221735891);
   1. goto AE
      -> UnconditionalJump[GOTO] #NQ -> #AE
      <- UnconditionalJump[GOTO] #CX -> #NQ
===#Block AE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.IRON_BLOCK;
   2. goto RI
      -> UnconditionalJump[GOTO] #AE -> #RI
      <- UnconditionalJump[GOTO] #ON -> #AE
      <- UnconditionalJump[GOTO] #NQ -> #AE
      <- UnconditionalJump[GOTO] #QB -> #AE
      <- Switch[2095533824] #KU -> #AE
      <- UnconditionalJump[GOTO] #NB -> #AE
      <- UnconditionalJump[GOTO] #VQ -> #AE
      <- UnconditionalJump[GOTO] #PC -> #AE
      <- UnconditionalJump[GOTO] #MT -> #AE
      <- Switch[432231863] #PB -> #AE
      <- UnconditionalJump[GOTO] #KV -> #AE
      <- UnconditionalJump[GOTO] #RF -> #AE
      <- UnconditionalJump[GOTO] #KQ -> #AE
===#Block RI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1720942126);
   1. goto HY
      -> UnconditionalJump[GOTO] #RI -> #HY
      <- UnconditionalJump[GOTO] #AE -> #RI
===#Block HY(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 64095137)
      goto HX
   1. throw nullconst;
      -> TryCatch range: [HY...HX] -> ADG ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HY -> #HX
      <- UnconditionalJump[GOTO] #RI -> #HY
===#Block HX(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HY...HX] -> ADG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HY -> #HX
===#Block ADG(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 1511616257:
      	 goto	#ADH
      case 1775144210:
      	 goto	#ADI
      default:
      	 goto	#ADJ
   }
      -> Switch[1775144210] #ADG -> #ADI
      -> Switch[1511616257] #ADG -> #ADH
      -> DefaultSwitch #ADG -> #ADJ
      <- TryCatch range: [HY...HX] -> ADG ([Ljava/io/IOException;])
      <- TryCatch range: [HY...HX] -> ADG ([Ljava/io/IOException;])
===#Block ADJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ADG -> #ADJ
===#Block ADH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 606182017);
   1. goto HZ
      -> UnconditionalJump[GOTO] #ADH -> #HZ
      <- Switch[1511616257] #ADG -> #ADH
===#Block ADI(size=2, flags=10100)===
   0. lvar105 = {1113286339 ^ lvar105};
   1. goto HZ
      -> UnconditionalJump[GOTO] #ADI -> #HZ
      <- Switch[1775144210] #ADG -> #ADI
===#Block HZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto QP
      -> UnconditionalJump[GOTO] #HZ -> #QP
      <- UnconditionalJump[GOTO] #ADI -> #HZ
      <- UnconditionalJump[GOTO] #ADH -> #HZ
===#Block QP(size=2, flags=10100)===
   0. lvar105 = {1630673113 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QP -> #CN
      <- UnconditionalJump[GOTO] #HZ -> #QP
===#Block WP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #WM -> #WP
===#Block TJ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 190125366:
      	 goto	#TK
      case 498031335:
      	 goto	#TJ
      case 910767890:
      	 goto	#JI
      case 1596036641:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[190125366] #TJ -> #TK
      -> Switch[498031335] #TJ -> #TJ
      -> Switch[910767890] #TJ -> #JI
      -> Switch[1596036641] #TJ -> #JC
      -> DefaultSwitch #TJ -> #JC
      -> Immediate #TJ -> #TK
      <- Switch[498031335] #TJ -> #TJ
      <- ConditionalJump[IF_ICMPEQ] #C -> #TJ
===#Block TK(size=2, flags=100)===
   0. lvar105 = {1335899282 ^ lvar105};
   1. goto JI
      -> UnconditionalJump[GOTO] #TK -> #JI
      <- Switch[190125366] #TJ -> #TK
      <- Immediate #TJ -> #TK
===#Block JI(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1979864142)
      goto SU
   1. goto QK
      -> ConditionalJump[IF_ICMPEQ] #JI -> #SU
      -> UnconditionalJump[GOTO] #JI -> #QK
      <- UnconditionalJump[GOTO] #TK -> #JI
      <- Switch[910767890] #TJ -> #JI
===#Block QK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1105887427);
   1. goto JC
      -> UnconditionalJump[GOTO] #QK -> #JC
      <- UnconditionalJump[GOTO] #JI -> #QK
===#Block SU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1864392050);
   1. goto AG
      -> UnconditionalJump[GOTO] #SU -> #AG
      <- ConditionalJump[IF_ICMPEQ] #JI -> #SU
===#Block AG(size=4, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar32 = lvar2;
   2. if (lvar32 == {1042278404 ^ lvar105})
      goto UJ
   3. lvar105 = {1014933580 ^ lvar105};
      -> ConditionalJump[IF_ICMPEQ] #AG -> #UJ
      -> Immediate #AG -> #AH
      <- UnconditionalJump[GOTO] #SU -> #AG
===#Block AH(size=6, flags=0)===
   0. lvar33 = lvar1;
   1. lvar102 = lvar33;
   2. lvar34 = lvar102;
   3. lvar35 = lvar34.hashCode();
   4. svar107 = {lvar35 ^ lvar105};
   5. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(svar107)) {
      case 36440743:
      	 goto	#VR
      case 36440750:
      	 goto	#VT
      case 36440751:
      	 goto	#VV
      case 36440912:
      	 goto	#VW
      case 36440913:
      	 goto	#VX
      case 36440914:
      	 goto	#VY
      case 36440915:
      	 goto	#WA
      case 36440917:
      	 goto	#WB
      default:
      	 goto	#WD
   }
      -> Switch[36440914] #AH -> #VY
      -> Switch[36440743] #AH -> #VR
      -> Switch[36440912] #AH -> #VW
      -> Switch[36440751] #AH -> #VV
      -> Switch[36440917] #AH -> #WB
      -> Switch[36440913] #AH -> #VX
      -> Switch[36440750] #AH -> #VT
      -> Switch[36440915] #AH -> #WA
      -> DefaultSwitch #AH -> #WD
      <- Immediate #AG -> #AH
===#Block WD(size=2, flags=10100)===
   0. lvar105 = {960236558 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #WD -> #BI
      <- DefaultSwitch #AH -> #WD
===#Block WA(size=2, flags=10100)===
   0. lvar105 = {137442360 ^ lvar105};
   1. goto AX
      -> UnconditionalJump[GOTO] #WA -> #AX
      <- Switch[36440915] #AH -> #WA
===#Block AX(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar46 = lvar102;
   2. lvar89 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.sctozekszthyqxt(), lvar105);
   3. lvar47 = lvar46.equals(lvar89);
   4. if (lvar47 != {173044848 ^ lvar105})
      goto ST
   5. lvar105 = {417675962 ^ lvar105};
      -> Immediate #AX -> #AZ
      -> ConditionalJump[IF_ICMPNE] #AX -> #ST
      <- UnconditionalJump[GOTO] #WA -> #AX
===#Block ST(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1029176077);
   1. goto KA
      -> UnconditionalJump[GOTO] #ST -> #KA
      <- ConditionalJump[IF_ICMPNE] #AX -> #ST
===#Block KA(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1203995671)
      goto RT
   1. goto LG
      -> UnconditionalJump[GOTO] #KA -> #LG
      -> ConditionalJump[IF_ICMPEQ] #KA -> #RT
      <- UnconditionalJump[GOTO] #ST -> #KA
===#Block RT(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 178163973:
      	 goto	#RU
      case 834011399:
      	 goto	#AY
      case 1069023921:
      	 goto	#RT
      case 1451321211:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[178163973] #RT -> #RU
      -> Immediate #RT -> #RU
      -> Switch[834011399] #RT -> #AY
      -> DefaultSwitch #RT -> #JC
      -> Switch[1069023921] #RT -> #RT
      -> Switch[1451321211] #RT -> #JC
      <- Switch[1069023921] #RT -> #RT
      <- ConditionalJump[IF_ICMPEQ] #KA -> #RT
===#Block RU(size=2, flags=100)===
   0. lvar105 = {1657408973 ^ lvar105};
   1. goto AY
      -> UnconditionalJump[GOTO] #RU -> #AY
      <- Switch[178163973] #RT -> #RU
      <- Immediate #RT -> #RU
===#Block AY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_GLAZED_TERRACOTTA;
   2. goto KR
      -> UnconditionalJump[GOTO] #AY -> #KR
      <- UnconditionalJump[GOTO] #RU -> #AY
      <- Switch[834011399] #RT -> #AY
===#Block KR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1325651631);
   1. goto DU
      -> UnconditionalJump[GOTO] #KR -> #DU
      <- UnconditionalJump[GOTO] #AY -> #KR
===#Block DU(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 66050098)
      goto DT
   1. throw nullconst;
      -> TryCatch range: [DU...DT] -> XS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DU -> #DT
      <- UnconditionalJump[GOTO] #KR -> #DU
===#Block DT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DU...DT] -> XS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DU -> #DT
===#Block XS(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -697130760:
      	 goto	#XT
      case 63501579:
      	 goto	#XU
      default:
      	 goto	#XV
   }
      -> Switch[63501579] #XS -> #XU
      -> Switch[-697130760] #XS -> #XT
      -> DefaultSwitch #XS -> #XV
      <- TryCatch range: [DU...DT] -> XS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DU...DT] -> XS ([Ljava/lang/IllegalAccessException;])
===#Block XV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XS -> #XV
===#Block XT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1230886820);
   1. goto DV
      -> UnconditionalJump[GOTO] #XT -> #DV
      <- Switch[-697130760] #XS -> #XT
===#Block XU(size=2, flags=10100)===
   0. lvar105 = {871013658 ^ lvar105};
   1. goto DV
      -> UnconditionalJump[GOTO] #XU -> #DV
      <- Switch[63501579] #XS -> #XU
===#Block DV(size=2, flags=0)===
   0. _consume(catch());
   1. goto PS
      -> UnconditionalJump[GOTO] #DV -> #PS
      <- UnconditionalJump[GOTO] #XU -> #DV
      <- UnconditionalJump[GOTO] #XT -> #DV
===#Block PS(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1036200963);
   1. goto CN
      -> UnconditionalJump[GOTO] #PS -> #CN
      <- UnconditionalJump[GOTO] #DV -> #PS
===#Block LG(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 178163973:
      	 goto	#LH
      case 945875035:
      	 goto	#JC
      case 959029927:
      	 goto	#LG
      case 1663658691:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[959029927] #LG -> #LG
      -> DefaultSwitch #LG -> #JC
      -> Switch[1663658691] #LG -> #JC
      -> Switch[178163973] #LG -> #LH
      -> Immediate #LG -> #LH
      <- Switch[959029927] #LG -> #LG
      <- UnconditionalJump[GOTO] #KA -> #LG
===#Block LH(size=2, flags=100)===
   0. lvar105 = {668121800 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LH -> #JC
      <- Switch[178163973] #LG -> #LH
      <- Immediate #LG -> #LH
===#Block AZ(size=1, flags=0)===
   0. goto MF
      -> UnconditionalJump[GOTO] #AZ -> #MF
      <- Immediate #AX -> #AZ
===#Block MF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 113127719);
   1. goto HA
      -> UnconditionalJump[GOTO] #MF -> #HA
      <- UnconditionalJump[GOTO] #AZ -> #MF
===#Block HA(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 114709456)
      goto GZ
   1. throw nullconst;
      -> TryCatch range: [HA...GZ] -> ACA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #HA -> #GZ
      <- UnconditionalJump[GOTO] #MF -> #HA
===#Block GZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HA...GZ] -> ACA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HA -> #GZ
===#Block ACA(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1604583576:
      	 goto	#ACB
      case -1599681400:
      	 goto	#ACC
      default:
      	 goto	#ACD
   }
      -> Switch[-1604583576] #ACA -> #ACB
      -> Switch[-1599681400] #ACA -> #ACC
      -> DefaultSwitch #ACA -> #ACD
      <- TryCatch range: [HA...GZ] -> ACA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HA...GZ] -> ACA ([Ljava/lang/RuntimeException;])
===#Block ACD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ACA -> #ACD
===#Block ACC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1815219567);
   1. goto HB
      -> UnconditionalJump[GOTO] #ACC -> #HB
      <- Switch[-1599681400] #ACA -> #ACC
===#Block ACB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1814932243);
   1. goto HB
      -> UnconditionalJump[GOTO] #ACB -> #HB
      <- Switch[-1604583576] #ACA -> #ACB
===#Block HB(size=2, flags=0)===
   0. _consume(catch());
   1. goto MI
      -> UnconditionalJump[GOTO] #HB -> #MI
      <- UnconditionalJump[GOTO] #ACC -> #HB
      <- UnconditionalJump[GOTO] #ACB -> #HB
===#Block MI(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 147539082:
      	 goto	#MJ
      case 150203155:
      	 goto	#BI
      case 403655039:
      	 goto	#JC
      case 1245552058:
      	 goto	#MI
      default:
      	 goto	#JC
   }
      -> Switch[403655039] #MI -> #JC
      -> Immediate #MI -> #MJ
      -> DefaultSwitch #MI -> #JC
      -> Switch[150203155] #MI -> #BI
      -> Switch[147539082] #MI -> #MJ
      -> Switch[1245552058] #MI -> #MI
      <- Switch[1245552058] #MI -> #MI
      <- UnconditionalJump[GOTO] #HB -> #MI
===#Block MJ(size=2, flags=100)===
   0. lvar105 = {1132173496 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #MJ -> #BI
      <- Immediate #MI -> #MJ
      <- Switch[147539082] #MI -> #MJ
===#Block VT(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 37432866:
      	 goto	#VU
      case 177827515:
      	 goto	#JC
      case 339818103:
      	 goto	#AI
      case 432892368:
      	 goto	#VT
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #VT -> #JC
      -> Switch[432892368] #VT -> #VT
      -> Switch[177827515] #VT -> #JC
      -> Immediate #VT -> #VU
      -> Switch[339818103] #VT -> #AI
      -> Switch[37432866] #VT -> #VU
      <- Switch[432892368] #VT -> #VT
      <- Switch[36440750] #AH -> #VT
===#Block VU(size=2, flags=100)===
   0. lvar105 = {1666698588 ^ lvar105};
   1. goto AI
      -> UnconditionalJump[GOTO] #VU -> #AI
      <- Immediate #VT -> #VU
      <- Switch[37432866] #VT -> #VU
===#Block AI(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar36 = lvar102;
   2. lvar84 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bnutizvbzkuurtn(), lvar105);
   3. lvar37 = lvar36.equals(lvar84);
   4. if (lvar37 != {1630965012 ^ lvar105})
      goto TI
   5. lvar105 = {1422992730 ^ lvar105};
      -> Immediate #AI -> #AK
      -> ConditionalJump[IF_ICMPNE] #AI -> #TI
      <- UnconditionalJump[GOTO] #VU -> #AI
      <- Switch[339818103] #VT -> #AI
===#Block TI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 28735569);
   1. goto JG
      -> UnconditionalJump[GOTO] #TI -> #JG
      <- ConditionalJump[IF_ICMPNE] #AI -> #TI
===#Block JG(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 67611179)
      goto TO
   1. goto NM
      -> UnconditionalJump[GOTO] #JG -> #NM
      -> ConditionalJump[IF_ICMPEQ] #JG -> #TO
      <- UnconditionalJump[GOTO] #TI -> #JG
===#Block TO(size=2, flags=10100)===
   0. lvar105 = {1759793062 ^ lvar105};
   1. goto AJ
      -> UnconditionalJump[GOTO] #TO -> #AJ
      <- ConditionalJump[IF_ICMPEQ] #JG -> #TO
===#Block AJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLUE_GLAZED_TERRACOTTA;
   2. goto NU
      -> UnconditionalJump[GOTO] #AJ -> #NU
      <- UnconditionalJump[GOTO] #TO -> #AJ
===#Block NU(size=2, flags=10100)===
   0. lvar105 = {285742349 ^ lvar105};
   1. goto GF
      -> UnconditionalJump[GOTO] #NU -> #GF
      <- UnconditionalJump[GOTO] #AJ -> #NU
===#Block GF(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 21138865)
      goto GE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GF -> #GE
      -> TryCatch range: [GF...GE] -> AAY ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #NU -> #GF
===#Block GE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [GF...GE] -> AAY ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #GF -> #GE
===#Block AAY(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -882540688:
      	 goto	#AAZ
      case -686942143:
      	 goto	#ABA
      default:
      	 goto	#ABB
   }
      -> Switch[-882540688] #AAY -> #AAZ
      -> DefaultSwitch #AAY -> #ABB
      -> Switch[-686942143] #AAY -> #ABA
      <- TryCatch range: [GF...GE] -> AAY ([Ljava/io/IOException;])
      <- TryCatch range: [GF...GE] -> AAY ([Ljava/io/IOException;])
===#Block ABA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1668350425);
   1. goto GG
      -> UnconditionalJump[GOTO] #ABA -> #GG
      <- Switch[-686942143] #AAY -> #ABA
===#Block ABB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAY -> #ABB
===#Block AAZ(size=2, flags=10100)===
   0. lvar105 = {1090284863 ^ lvar105};
   1. goto GG
      -> UnconditionalJump[GOTO] #AAZ -> #GG
      <- Switch[-882540688] #AAY -> #AAZ
===#Block GG(size=2, flags=0)===
   0. _consume(catch());
   1. goto NV
      -> UnconditionalJump[GOTO] #GG -> #NV
      <- UnconditionalJump[GOTO] #ABA -> #GG
      <- UnconditionalJump[GOTO] #AAZ -> #GG
===#Block NV(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 227025124:
      	 goto	#NW
      case 1273789595:
      	 goto	#CN
      case 1944002288:
      	 goto	#JC
      case 2003994815:
      	 goto	#NV
      default:
      	 goto	#JC
   }
      -> Switch[2003994815] #NV -> #NV
      -> DefaultSwitch #NV -> #JC
      -> Immediate #NV -> #NW
      -> Switch[1944002288] #NV -> #JC
      -> Switch[227025124] #NV -> #NW
      -> Switch[1273789595] #NV -> #CN
      <- Switch[2003994815] #NV -> #NV
      <- UnconditionalJump[GOTO] #GG -> #NV
===#Block NW(size=2, flags=100)===
   0. lvar105 = {935357801 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NW -> #CN
      <- Immediate #NV -> #NW
      <- Switch[227025124] #NV -> #NW
===#Block NM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 184138677:
      	 goto	#NN
      case 582415333:
      	 goto	#JC
      case 704942389:
      	 goto	#JC
      case 861638860:
      	 goto	#NM
      default:
      	 goto	#JC
   }
      -> Switch[184138677] #NM -> #NN
      -> Switch[861638860] #NM -> #NM
      -> Immediate #NM -> #NN
      -> DefaultSwitch #NM -> #JC
      -> Switch[582415333] #NM -> #JC
      <- Switch[861638860] #NM -> #NM
      <- UnconditionalJump[GOTO] #JG -> #NM
===#Block NN(size=2, flags=100)===
   0. lvar105 = {1884668144 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NN -> #JC
      <- Switch[184138677] #NM -> #NN
      <- Immediate #NM -> #NN
===#Block AK(size=1, flags=0)===
   0. goto OU
      -> UnconditionalJump[GOTO] #AK -> #OU
      <- Immediate #AI -> #AK
===#Block OU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1070037099);
   1. goto DR
      -> UnconditionalJump[GOTO] #OU -> #DR
      <- UnconditionalJump[GOTO] #AK -> #OU
===#Block DR(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 60791143)
      goto DQ
   1. throw nullconst;
      -> TryCatch range: [DR...DQ] -> XO ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #DR -> #DQ
      <- UnconditionalJump[GOTO] #OU -> #DR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DR...DQ] -> XO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DR -> #DQ
===#Block XO(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -114574464:
      	 goto	#XQ
      case 1359380776:
      	 goto	#XP
      default:
      	 goto	#XR
   }
      -> DefaultSwitch #XO -> #XR
      -> Switch[-114574464] #XO -> #XQ
      -> Switch[1359380776] #XO -> #XP
      <- TryCatch range: [DR...DQ] -> XO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DR...DQ] -> XO ([Ljava/lang/RuntimeException;])
===#Block XP(size=2, flags=10100)===
   0. lvar105 = {1903639011 ^ lvar105};
   1. goto DS
      -> UnconditionalJump[GOTO] #XP -> #DS
      <- Switch[1359380776] #XO -> #XP
===#Block XQ(size=2, flags=10100)===
   0. lvar105 = {1685233334 ^ lvar105};
   1. goto DS
      -> UnconditionalJump[GOTO] #XQ -> #DS
      <- Switch[-114574464] #XO -> #XQ
===#Block DS(size=2, flags=0)===
   0. _consume(catch());
   1. goto OO
      -> UnconditionalJump[GOTO] #DS -> #OO
      <- UnconditionalJump[GOTO] #XP -> #DS
      <- UnconditionalJump[GOTO] #XQ -> #DS
===#Block OO(size=2, flags=10100)===
   0. lvar105 = {1074439552 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #OO -> #BI
      <- UnconditionalJump[GOTO] #DS -> #OO
===#Block XR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #XO -> #XR
===#Block VX(size=2, flags=10100)===
   0. lvar105 = {1682799614 ^ lvar105};
   1. goto BA
      -> UnconditionalJump[GOTO] #VX -> #BA
      <- Switch[36440913] #AH -> #VX
===#Block BA(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar48 = lvar102;
   2. lvar90 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ypfiglmvzfugrhq(), lvar105);
   3. lvar49 = lvar48.equals(lvar90);
   4. if (lvar49 != {1714174902 ^ lvar105})
      goto SK
   5. lvar105 = {1461359089 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BA -> #SK
      -> Immediate #BA -> #BC
      <- UnconditionalJump[GOTO] #VX -> #BA
===#Block BC(size=1, flags=0)===
   0. goto PQ
      -> UnconditionalJump[GOTO] #BC -> #PQ
      <- Immediate #BA -> #BC
===#Block PQ(size=2, flags=10100)===
   0. lvar105 = {186345839 ^ lvar105};
   1. goto HJ
      -> UnconditionalJump[GOTO] #PQ -> #HJ
      <- UnconditionalJump[GOTO] #BC -> #PQ
===#Block HJ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 12095536)
      goto HI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HJ -> #HI
      -> TryCatch range: [HJ...HI] -> ACM ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #PQ -> #HJ
===#Block HI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HJ...HI] -> ACM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HJ -> #HI
===#Block ACM(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -*********:
      	 goto	#ACN
      case 1186107584:
      	 goto	#ACO
      default:
      	 goto	#ACP
   }
      -> DefaultSwitch #ACM -> #ACP
      -> Switch[-*********] #ACM -> #ACN
      -> Switch[1186107584] #ACM -> #ACO
      <- TryCatch range: [HJ...HI] -> ACM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HJ...HI] -> ACM ([Ljava/lang/IllegalAccessException;])
===#Block ACO(size=2, flags=10100)===
   0. lvar105 = {1368677492 ^ lvar105};
   1. goto HK
      -> UnconditionalJump[GOTO] #ACO -> #HK
      <- Switch[1186107584] #ACM -> #ACO
===#Block ACN(size=2, flags=10100)===
   0. lvar105 = {1668271428 ^ lvar105};
   1. goto HK
      -> UnconditionalJump[GOTO] #ACN -> #HK
      <- Switch[-*********] #ACM -> #ACN
===#Block HK(size=2, flags=0)===
   0. _consume(catch());
   1. goto OA
      -> UnconditionalJump[GOTO] #HK -> #OA
      <- UnconditionalJump[GOTO] #ACO -> #HK
      <- UnconditionalJump[GOTO] #ACN -> #HK
===#Block OA(size=2, flags=10100)===
   0. lvar105 = {1646215722 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #OA -> #BI
      <- UnconditionalJump[GOTO] #HK -> #OA
===#Block ACP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACM -> #ACP
===#Block SK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1270382793);
   1. goto JR
      -> UnconditionalJump[GOTO] #SK -> #JR
      <- ConditionalJump[IF_ICMPNE] #BA -> #SK
===#Block JR(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1822809081)
      goto TF
   1. goto LQ
      -> ConditionalJump[IF_ICMPEQ] #JR -> #TF
      -> UnconditionalJump[GOTO] #JR -> #LQ
      <- UnconditionalJump[GOTO] #SK -> #JR
===#Block LQ(size=2, flags=10100)===
   0. lvar105 = {1027706570 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LQ -> #JC
      <- UnconditionalJump[GOTO] #JR -> #LQ
===#Block TF(size=2, flags=10100)===
   0. lvar105 = {417673997 ^ lvar105};
   1. goto BB
      -> UnconditionalJump[GOTO] #TF -> #BB
      <- ConditionalJump[IF_ICMPEQ] #JR -> #TF
===#Block BB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_GLAZED_TERRACOTTA;
   2. goto PV
      -> UnconditionalJump[GOTO] #BB -> #PV
      <- UnconditionalJump[GOTO] #TF -> #BB
===#Block PV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1705958186);
   1. goto IE
      -> UnconditionalJump[GOTO] #PV -> #IE
      <- UnconditionalJump[GOTO] #BB -> #PV
===#Block IE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 212918693)
      goto ID
   1. throw nullconst;
      -> TryCatch range: [IE...ID] -> ADO ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IE -> #ID
      <- UnconditionalJump[GOTO] #PV -> #IE
===#Block ID(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IE...ID] -> ADO ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IE -> #ID
===#Block ADO(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -2030396734:
      	 goto	#ADP
      case -1976678880:
      	 goto	#ADQ
      default:
      	 goto	#ADR
   }
      -> Switch[-1976678880] #ADO -> #ADQ
      -> DefaultSwitch #ADO -> #ADR
      -> Switch[-2030396734] #ADO -> #ADP
      <- TryCatch range: [IE...ID] -> ADO ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IE...ID] -> ADO ([Ljava/lang/IllegalAccessException;])
===#Block ADP(size=2, flags=10100)===
   0. lvar105 = {1212248011 ^ lvar105};
   1. goto IF
      -> UnconditionalJump[GOTO] #ADP -> #IF
      <- Switch[-2030396734] #ADO -> #ADP
===#Block ADR(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADO -> #ADR
===#Block ADQ(size=2, flags=10100)===
   0. lvar105 = {165409879 ^ lvar105};
   1. goto IF
      -> UnconditionalJump[GOTO] #ADQ -> #IF
      <- Switch[-1976678880] #ADO -> #ADQ
===#Block IF(size=2, flags=0)===
   0. _consume(catch());
   1. goto OW
      -> UnconditionalJump[GOTO] #IF -> #OW
      <- UnconditionalJump[GOTO] #ADQ -> #IF
      <- UnconditionalJump[GOTO] #ADP -> #IF
===#Block OW(size=2, flags=10100)===
   0. lvar105 = {1993249579 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OW -> #CN
      <- UnconditionalJump[GOTO] #IF -> #OW
===#Block WB(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 37432866:
      	 goto	#WC
      case 531398410:
      	 goto	#BD
      case 1562965640:
      	 goto	#WB
      case 1766753954:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #WB -> #WC
      -> Switch[37432866] #WB -> #WC
      -> Switch[1562965640] #WB -> #WB
      -> Switch[1766753954] #WB -> #JC
      -> DefaultSwitch #WB -> #JC
      -> Switch[531398410] #WB -> #BD
      <- Switch[1562965640] #WB -> #WB
      <- Switch[36440917] #AH -> #WB
===#Block WC(size=2, flags=100)===
   0. lvar105 = {199062026 ^ lvar105};
   1. goto BD
      -> UnconditionalJump[GOTO] #WC -> #BD
      <- Immediate #WB -> #WC
      <- Switch[37432866] #WB -> #WC
===#Block BD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar50 = lvar102;
   2. lvar91 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.lchdawfkdlrigzy(), lvar105);
   3. lvar51 = lvar50.equals(lvar91);
   4. if (lvar51 != {163328578 ^ lvar105})
      goto TH
   5. lvar105 = {492134064 ^ lvar105};
      -> Immediate #BD -> #BF
      -> ConditionalJump[IF_ICMPNE] #BD -> #TH
      <- Switch[531398410] #WB -> #BD
      <- UnconditionalJump[GOTO] #WC -> #BD
===#Block TH(size=2, flags=10100)===
   0. lvar105 = {1725138026 ^ lvar105};
   1. goto JF
      -> UnconditionalJump[GOTO] #TH -> #JF
      <- ConditionalJump[IF_ICMPNE] #BD -> #TH
===#Block JF(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 2071834947)
      goto SH
   1. goto OC
      -> ConditionalJump[IF_ICMPEQ] #JF -> #SH
      -> UnconditionalJump[GOTO] #JF -> #OC
      <- UnconditionalJump[GOTO] #TH -> #JF
===#Block OC(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 132652930:
      	 goto	#OD
      case 972909094:
      	 goto	#JC
      case 1171612868:
      	 goto	#JC
      case 2134078864:
      	 goto	#OC
      default:
      	 goto	#JC
   }
      -> Switch[972909094] #OC -> #JC
      -> DefaultSwitch #OC -> #JC
      -> Switch[2134078864] #OC -> #OC
      -> Immediate #OC -> #OD
      -> Switch[132652930] #OC -> #OD
      <- Switch[2134078864] #OC -> #OC
      <- UnconditionalJump[GOTO] #JF -> #OC
===#Block OD(size=2, flags=100)===
   0. lvar105 = {2142929821 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OD -> #JC
      <- Immediate #OC -> #OD
      <- Switch[132652930] #OC -> #OD
===#Block SH(size=2, flags=10100)===
   0. lvar105 = {1266419312 ^ lvar105};
   1. goto BE
      -> UnconditionalJump[GOTO] #SH -> #BE
      <- ConditionalJump[IF_ICMPEQ] #JF -> #SH
===#Block BE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_GLAZED_TERRACOTTA;
   2. goto PE
      -> UnconditionalJump[GOTO] #BE -> #PE
      <- UnconditionalJump[GOTO] #SH -> #BE
===#Block PE(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 98959801:
      	 goto	#PF
      case 222402633:
      	 goto	#FN
      case 677674420:
      	 goto	#PE
      case 1031925414:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[677674420] #PE -> #PE
      -> Switch[98959801] #PE -> #PF
      -> Immediate #PE -> #PF
      -> Switch[222402633] #PE -> #FN
      -> DefaultSwitch #PE -> #JC
      -> Switch[1031925414] #PE -> #JC
      <- Switch[677674420] #PE -> #PE
      <- UnconditionalJump[GOTO] #BE -> #PE
===#Block PF(size=2, flags=100)===
   0. lvar105 = {1282197510 ^ lvar105};
   1. goto FN
      -> UnconditionalJump[GOTO] #PF -> #FN
      <- Switch[98959801] #PE -> #PF
      <- Immediate #PE -> #PF
===#Block FN(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 175542601)
      goto FM
   1. throw nullconst;
      -> TryCatch range: [FN...FM] -> AAA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FN -> #FM
      <- UnconditionalJump[GOTO] #PF -> #FN
      <- Switch[222402633] #PE -> #FN
===#Block FM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FN...FM] -> AAA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FN -> #FM
===#Block AAA(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -308443965:
      	 goto	#AAC
      case 1140572915:
      	 goto	#AAB
      default:
      	 goto	#AAD
   }
      -> Switch[1140572915] #AAA -> #AAB
      -> Switch[-308443965] #AAA -> #AAC
      -> DefaultSwitch #AAA -> #AAD
      <- TryCatch range: [FN...FM] -> AAA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FN...FM] -> AAA ([Ljava/lang/RuntimeException;])
===#Block AAD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAA -> #AAD
===#Block AAC(size=2, flags=10100)===
   0. lvar105 = {109618697 ^ lvar105};
   1. goto FO
      -> UnconditionalJump[GOTO] #AAC -> #FO
      <- Switch[-308443965] #AAA -> #AAC
===#Block AAB(size=2, flags=10100)===
   0. lvar105 = {323251919 ^ lvar105};
   1. goto FO
      -> UnconditionalJump[GOTO] #AAB -> #FO
      <- Switch[1140572915] #AAA -> #AAB
===#Block FO(size=2, flags=0)===
   0. _consume(catch());
   1. goto PR
      -> UnconditionalJump[GOTO] #FO -> #PR
      <- UnconditionalJump[GOTO] #AAC -> #FO
      <- UnconditionalJump[GOTO] #AAB -> #FO
===#Block PR(size=2, flags=10100)===
   0. lvar105 = {359381289 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PR -> #CN
      <- UnconditionalJump[GOTO] #FO -> #PR
===#Block BF(size=1, flags=0)===
   0. goto LI
      -> UnconditionalJump[GOTO] #BF -> #LI
      <- Immediate #BD -> #BF
===#Block LI(size=2, flags=10100)===
   0. lvar105 = {1212230255 ^ lvar105};
   1. goto EY
      -> UnconditionalJump[GOTO] #LI -> #EY
      <- UnconditionalJump[GOTO] #BF -> #LI
===#Block EY(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 59135096)
      goto EX
   1. throw nullconst;
      -> TryCatch range: [EY...EX] -> ZG ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EY -> #EX
      <- UnconditionalJump[GOTO] #LI -> #EY
===#Block EX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EY...EX] -> ZG ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EY -> #EX
===#Block ZG(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1774580216:
      	 goto	#ZI
      case -448645910:
      	 goto	#ZH
      default:
      	 goto	#ZJ
   }
      -> DefaultSwitch #ZG -> #ZJ
      -> Switch[-448645910] #ZG -> #ZH
      -> Switch[-1774580216] #ZG -> #ZI
      <- TryCatch range: [EY...EX] -> ZG ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EY...EX] -> ZG ([Ljava/lang/IllegalAccessException;])
===#Block ZI(size=2, flags=10100)===
   0. lvar105 = {7558254 ^ lvar105};
   1. goto EZ
      -> UnconditionalJump[GOTO] #ZI -> #EZ
      <- Switch[-1774580216] #ZG -> #ZI
===#Block ZH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1310479154);
   1. goto EZ
      -> UnconditionalJump[GOTO] #ZH -> #EZ
      <- Switch[-448645910] #ZG -> #ZH
===#Block EZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto PO
      -> UnconditionalJump[GOTO] #EZ -> #PO
      <- UnconditionalJump[GOTO] #ZI -> #EZ
      <- UnconditionalJump[GOTO] #ZH -> #EZ
===#Block PO(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 48419573:
      	 goto	#JC
      case 71104695:
      	 goto	#PP
      case 128423899:
      	 goto	#BI
      case 1317079956:
      	 goto	#PO
      default:
      	 goto	#JC
   }
      -> Immediate #PO -> #PP
      -> Switch[1317079956] #PO -> #PO
      -> Switch[71104695] #PO -> #PP
      -> Switch[48419573] #PO -> #JC
      -> DefaultSwitch #PO -> #JC
      -> Switch[128423899] #PO -> #BI
      <- Switch[1317079956] #PO -> #PO
      <- UnconditionalJump[GOTO] #EZ -> #PO
===#Block PP(size=2, flags=100)===
   0. lvar105 = {703158761 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #PP -> #BI
      <- Immediate #PO -> #PP
      <- Switch[71104695] #PO -> #PP
===#Block ZJ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ZG -> #ZJ
===#Block VV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1242541943);
   1. goto AL
      -> UnconditionalJump[GOTO] #VV -> #AL
      <- Switch[36440751] #AH -> #VV
===#Block AL(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar38 = lvar102;
   2. lvar85 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.spaclzqpxinkeug(), lvar105);
   3. lvar39 = lvar38.equals(lvar85);
   4. if (lvar39 != {1215229759 ^ lvar105})
      goto UO
   5. lvar105 = {2051079923 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AL -> #UO
      -> Immediate #AL -> #AM
      <- UnconditionalJump[GOTO] #VV -> #AL
===#Block AM(size=1, flags=0)===
   0. goto RN
      -> UnconditionalJump[GOTO] #AM -> #RN
      <- Immediate #AL -> #AM
===#Block RN(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 20541525:
      	 goto	#RO
      case 1217363309:
      	 goto	#IW
      case 1528327721:
      	 goto	#RN
      case 2145538329:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[2145538329] #RN -> #JC
      -> DefaultSwitch #RN -> #JC
      -> Immediate #RN -> #RO
      -> Switch[20541525] #RN -> #RO
      -> Switch[1217363309] #RN -> #IW
      -> Switch[1528327721] #RN -> #RN
      <- Switch[1528327721] #RN -> #RN
      <- UnconditionalJump[GOTO] #AM -> #RN
===#Block RO(size=2, flags=100)===
   0. lvar105 = {1388292514 ^ lvar105};
   1. goto IW
      -> UnconditionalJump[GOTO] #RO -> #IW
      <- Immediate #RN -> #RO
      <- Switch[20541525] #RN -> #RO
===#Block IW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 186267076)
      goto IV
   1. throw nullconst;
      -> TryCatch range: [IW...IV] -> AEM ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IW -> #IV
      <- Switch[1217363309] #RN -> #IW
      <- UnconditionalJump[GOTO] #RO -> #IW
===#Block IV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IW...IV] -> AEM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IW -> #IV
===#Block AEM(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1327644918:
      	 goto	#AEO
      case 76399475:
      	 goto	#AEN
      default:
      	 goto	#AEP
   }
      -> Switch[76399475] #AEM -> #AEN
      -> DefaultSwitch #AEM -> #AEP
      -> Switch[-1327644918] #AEM -> #AEO
      <- TryCatch range: [IW...IV] -> AEM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IW...IV] -> AEM ([Ljava/lang/IllegalAccessException;])
===#Block AEO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1420431305);
   1. goto IX
      -> UnconditionalJump[GOTO] #AEO -> #IX
      <- Switch[-1327644918] #AEM -> #AEO
===#Block AEP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEM -> #AEP
===#Block AEN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1646264390);
   1. goto IX
      -> UnconditionalJump[GOTO] #AEN -> #IX
      <- Switch[76399475] #AEM -> #AEN
===#Block IX(size=2, flags=0)===
   0. _consume(catch());
   1. goto NK
      -> UnconditionalJump[GOTO] #IX -> #NK
      <- UnconditionalJump[GOTO] #AEO -> #IX
      <- UnconditionalJump[GOTO] #AEN -> #IX
===#Block NK(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 42376724:
      	 goto	#NL
      case 208115268:
      	 goto	#BI
      case 1165158668:
      	 goto	#NK
      case 1688390075:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[42376724] #NK -> #NL
      -> Immediate #NK -> #NL
      -> Switch[208115268] #NK -> #BI
      -> Switch[1688390075] #NK -> #JC
      -> DefaultSwitch #NK -> #JC
      -> Switch[1165158668] #NK -> #NK
      <- UnconditionalJump[GOTO] #IX -> #NK
      <- Switch[1165158668] #NK -> #NK
===#Block NL(size=2, flags=100)===
   0. lvar105 = {971830382 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #NL -> #BI
      <- Switch[42376724] #NK -> #NL
      <- Immediate #NK -> #NL
===#Block UO(size=2, flags=10100)===
   0. lvar105 = {42548799 ^ lvar105};
   1. goto KD
      -> UnconditionalJump[GOTO] #UO -> #KD
      <- ConditionalJump[IF_ICMPNE] #AL -> #UO
===#Block KD(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1463715842)
      goto TR
   1. goto OI
      -> UnconditionalJump[GOTO] #KD -> #OI
      -> ConditionalJump[IF_ICMPEQ] #KD -> #TR
      <- UnconditionalJump[GOTO] #UO -> #KD
===#Block TR(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 18968087:
      	 goto	#TS
      case 858991019:
      	 goto	#AN
      case 863957088:
      	 goto	#TR
      case 918567726:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[858991019] #TR -> #AN
      -> Immediate #TR -> #TS
      -> DefaultSwitch #TR -> #JC
      -> Switch[918567726] #TR -> #JC
      -> Switch[863957088] #TR -> #TR
      -> Switch[18968087] #TR -> #TS
      <- Switch[863957088] #TR -> #TR
      <- ConditionalJump[IF_ICMPEQ] #KD -> #TR
===#Block TS(size=2, flags=100)===
   0. lvar105 = {559489580 ^ lvar105};
   1. goto AN
      -> UnconditionalJump[GOTO] #TS -> #AN
      <- Immediate #TR -> #TS
      <- Switch[18968087] #TR -> #TS
===#Block AN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_GLAZED_TERRACOTTA;
   2. goto NF
      -> UnconditionalJump[GOTO] #AN -> #NF
      <- Switch[858991019] #TR -> #AN
      <- UnconditionalJump[GOTO] #TS -> #AN
===#Block NF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 895378576);
   1. goto EP
      -> UnconditionalJump[GOTO] #NF -> #EP
      <- UnconditionalJump[GOTO] #AN -> #NF
===#Block EP(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 131300124)
      goto EO
   1. throw nullconst;
      -> TryCatch range: [EP...EO] -> YU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EP -> #EO
      <- UnconditionalJump[GOTO] #NF -> #EP
===#Block EO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EP...EO] -> YU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EP -> #EO
===#Block YU(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -150684190:
      	 goto	#YV
      case 1356405826:
      	 goto	#YW
      default:
      	 goto	#YX
   }
      -> DefaultSwitch #YU -> #YX
      -> Switch[-150684190] #YU -> #YV
      -> Switch[1356405826] #YU -> #YW
      <- TryCatch range: [EP...EO] -> YU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EP...EO] -> YU ([Ljava/lang/IllegalAccessException;])
===#Block YW(size=2, flags=10100)===
   0. lvar105 = {1488470859 ^ lvar105};
   1. goto EQ
      -> UnconditionalJump[GOTO] #YW -> #EQ
      <- Switch[1356405826] #YU -> #YW
===#Block YV(size=2, flags=10100)===
   0. lvar105 = {1279522687 ^ lvar105};
   1. goto EQ
      -> UnconditionalJump[GOTO] #YV -> #EQ
      <- Switch[-150684190] #YU -> #YV
===#Block EQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto QL
      -> UnconditionalJump[GOTO] #EQ -> #QL
      <- UnconditionalJump[GOTO] #YV -> #EQ
      <- UnconditionalJump[GOTO] #YW -> #EQ
===#Block QL(size=2, flags=10100)===
   0. lvar105 = {2096353147 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QL -> #CN
      <- UnconditionalJump[GOTO] #EQ -> #QL
===#Block YX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #YU -> #YX
===#Block OI(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 18968087:
      	 goto	#OJ
      case 655336617:
      	 goto	#OI
      case 1337307058:
      	 goto	#JC
      case 1996071807:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1996071807] #OI -> #JC
      -> DefaultSwitch #OI -> #JC
      -> Immediate #OI -> #OJ
      -> Switch[655336617] #OI -> #OI
      -> Switch[18968087] #OI -> #OJ
      <- UnconditionalJump[GOTO] #KD -> #OI
      <- Switch[655336617] #OI -> #OI
===#Block OJ(size=2, flags=100)===
   0. lvar105 = {1513284789 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OJ -> #JC
      <- Immediate #OI -> #OJ
      <- Switch[18968087] #OI -> #OJ
===#Block VW(size=2, flags=10100)===
   0. lvar105 = {1558565431 ^ lvar105};
   1. goto AR
      -> UnconditionalJump[GOTO] #VW -> #AR
      <- Switch[36440912] #AH -> #VW
===#Block AR(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar42 = lvar102;
   2. lvar87 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.vvwvuangnhppqjm(), lvar105);
   3. lvar43 = lvar42.equals(lvar87);
   4. if (lvar43 != {1585746559 ^ lvar105})
      goto RY
   5. lvar105 = {1862780129 ^ lvar105};
      -> Immediate #AR -> #AT
      -> ConditionalJump[IF_ICMPNE] #AR -> #RY
      <- UnconditionalJump[GOTO] #VW -> #AR
===#Block RY(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 119571794:
      	 goto	#RZ
      case 800422524:
      	 goto	#RY
      case 1079882277:
      	 goto	#JC
      case 1745046872:
      	 goto	#JE
      default:
      	 goto	#JC
   }
      -> Switch[800422524] #RY -> #RY
      -> DefaultSwitch #RY -> #JC
      -> Immediate #RY -> #RZ
      -> Switch[1079882277] #RY -> #JC
      -> Switch[119571794] #RY -> #RZ
      -> Switch[1745046872] #RY -> #JE
      <- Switch[800422524] #RY -> #RY
      <- ConditionalJump[IF_ICMPNE] #AR -> #RY
===#Block RZ(size=2, flags=100)===
   0. lvar105 = {651882664 ^ lvar105};
   1. goto JE
      -> UnconditionalJump[GOTO] #RZ -> #JE
      <- Immediate #RY -> #RZ
      <- Switch[119571794] #RY -> #RZ
===#Block JE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1024256325)
      goto RV
   1. goto LO
      -> ConditionalJump[IF_ICMPEQ] #JE -> #RV
      -> UnconditionalJump[GOTO] #JE -> #LO
      <- UnconditionalJump[GOTO] #RZ -> #JE
      <- Switch[1745046872] #RY -> #JE
===#Block LO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1753962338);
   1. goto JC
      -> UnconditionalJump[GOTO] #LO -> #JC
      <- UnconditionalJump[GOTO] #JE -> #LO
===#Block RV(size=2, flags=10100)===
   0. lvar105 = {33149411 ^ lvar105};
   1. goto AS
      -> UnconditionalJump[GOTO] #RV -> #AS
      <- ConditionalJump[IF_ICMPEQ] #JE -> #RV
===#Block AS(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_GLAZED_TERRACOTTA;
   2. goto RH
      -> UnconditionalJump[GOTO] #AS -> #RH
      <- UnconditionalJump[GOTO] #RV -> #AS
===#Block RH(size=2, flags=10100)===
   0. lvar105 = {1017457452 ^ lvar105};
   1. goto HS
      -> UnconditionalJump[GOTO] #RH -> #HS
      <- UnconditionalJump[GOTO] #AS -> #RH
===#Block HS(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 95765420)
      goto HR
   1. throw nullconst;
      -> TryCatch range: [HS...HR] -> ACY ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HS -> #HR
      <- UnconditionalJump[GOTO] #RH -> #HS
===#Block HR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HS...HR] -> ACY ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HS -> #HR
===#Block ACY(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1367278247:
      	 goto	#ADA
      case 672448706:
      	 goto	#ACZ
      default:
      	 goto	#ADB
   }
      -> Switch[-1367278247] #ACY -> #ADA
      -> DefaultSwitch #ACY -> #ADB
      -> Switch[672448706] #ACY -> #ACZ
      <- TryCatch range: [HS...HR] -> ACY ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HS...HR] -> ACY ([Ljava/lang/IllegalAccessException;])
===#Block ACZ(size=2, flags=10100)===
   0. lvar105 = {1318207117 ^ lvar105};
   1. goto HT
      -> UnconditionalJump[GOTO] #ACZ -> #HT
      <- Switch[672448706] #ACY -> #ACZ
===#Block ADB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACY -> #ADB
===#Block ADA(size=2, flags=10100)===
   0. lvar105 = {1044429118 ^ lvar105};
   1. goto HT
      -> UnconditionalJump[GOTO] #ADA -> #HT
      <- Switch[-1367278247] #ACY -> #ADA
===#Block HT(size=2, flags=0)===
   0. _consume(catch());
   1. goto QU
      -> UnconditionalJump[GOTO] #HT -> #QU
      <- UnconditionalJump[GOTO] #ACZ -> #HT
      <- UnconditionalJump[GOTO] #ADA -> #HT
===#Block QU(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 107497808:
      	 goto	#QV
      case 508352250:
      	 goto	#CN
      case 1886042420:
      	 goto	#JC
      case 2088890621:
      	 goto	#QU
      default:
      	 goto	#JC
   }
      -> Switch[508352250] #QU -> #CN
      -> Switch[107497808] #QU -> #QV
      -> DefaultSwitch #QU -> #JC
      -> Switch[1886042420] #QU -> #JC
      -> Immediate #QU -> #QV
      -> Switch[2088890621] #QU -> #QU
      <- UnconditionalJump[GOTO] #HT -> #QU
      <- Switch[2088890621] #QU -> #QU
===#Block QV(size=2, flags=100)===
   0. lvar105 = {1707084077 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QV -> #CN
      <- Switch[107497808] #QU -> #QV
      <- Immediate #QU -> #QV
===#Block AT(size=1, flags=0)===
   0. goto KK
      -> UnconditionalJump[GOTO] #AT -> #KK
      <- Immediate #AR -> #AT
===#Block KK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1848192480);
   1. goto DI
      -> UnconditionalJump[GOTO] #KK -> #DI
      <- UnconditionalJump[GOTO] #AT -> #KK
===#Block DI(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 156897518)
      goto DH
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DI -> #DH
      -> TryCatch range: [DI...DH] -> XC ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #KK -> #DI
===#Block DH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DI...DH] -> XC ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DI -> #DH
===#Block XC(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1994629880:
      	 goto	#XE
      case -44835854:
      	 goto	#XD
      default:
      	 goto	#XF
   }
      -> Switch[-44835854] #XC -> #XD
      -> Switch[-1994629880] #XC -> #XE
      -> DefaultSwitch #XC -> #XF
      <- TryCatch range: [DI...DH] -> XC ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DI...DH] -> XC ([Ljava/lang/IllegalAccessException;])
===#Block XF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #XC -> #XF
===#Block XE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 452889093);
   1. goto DJ
      -> UnconditionalJump[GOTO] #XE -> #DJ
      <- Switch[-1994629880] #XC -> #XE
===#Block XD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1417116378);
   1. goto DJ
      -> UnconditionalJump[GOTO] #XD -> #DJ
      <- Switch[-44835854] #XC -> #XD
===#Block DJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto RA
      -> UnconditionalJump[GOTO] #DJ -> #RA
      <- UnconditionalJump[GOTO] #XE -> #DJ
      <- UnconditionalJump[GOTO] #XD -> #DJ
===#Block RA(size=2, flags=10100)===
   0. lvar105 = {813714914 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #RA -> #BI
      <- UnconditionalJump[GOTO] #DJ -> #RA
===#Block VR(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 37432866:
      	 goto	#VS
      case 340014067:
      	 goto	#JC
      case 357376513:
      	 goto	#BG
      case 1748585702:
      	 goto	#VR
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #VR -> #JC
      -> Switch[357376513] #VR -> #BG
      -> Switch[340014067] #VR -> #JC
      -> Switch[37432866] #VR -> #VS
      -> Switch[1748585702] #VR -> #VR
      -> Immediate #VR -> #VS
      <- Switch[36440743] #AH -> #VR
      <- Switch[1748585702] #VR -> #VR
===#Block VS(size=2, flags=100)===
   0. lvar105 = {632064764 ^ lvar105};
   1. goto BG
      -> UnconditionalJump[GOTO] #VS -> #BG
      <- Switch[37432866] #VR -> #VS
      <- Immediate #VR -> #VS
===#Block BG(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar52 = lvar102;
   2. lvar92 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.kkxvwiveqaegeol(), lvar105);
   3. lvar53 = lvar52.equals(lvar92);
   4. if (lvar53 != {667798196 ^ lvar105})
      goto TU
   5. lvar105 = {711285364 ^ lvar105};
      -> Immediate #BG -> #BH
      -> ConditionalJump[IF_ICMPNE] #BG -> #TU
      <- Switch[357376513] #VR -> #BG
      <- UnconditionalJump[GOTO] #VS -> #BG
===#Block TU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1831910526);
   1. goto JO
      -> UnconditionalJump[GOTO] #TU -> #JO
      <- ConditionalJump[IF_ICMPNE] #BG -> #TU
===#Block JO(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1475065426)
      goto UM
   1. goto RE
      -> UnconditionalJump[GOTO] #JO -> #RE
      -> ConditionalJump[IF_ICMPEQ] #JO -> #UM
      <- UnconditionalJump[GOTO] #TU -> #JO
===#Block UM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 21716186:
      	 goto	#UN
      case 259191471:
      	 goto	#UM
      case 1446140237:
      	 goto	#BJ
      case 1524292605:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[21716186] #UM -> #UN
      -> Switch[1446140237] #UM -> #BJ
      -> Immediate #UM -> #UN
      -> Switch[1524292605] #UM -> #JC
      -> Switch[259191471] #UM -> #UM
      -> DefaultSwitch #UM -> #JC
      <- Switch[259191471] #UM -> #UM
      <- ConditionalJump[IF_ICMPEQ] #JO -> #UM
===#Block UN(size=2, flags=100)===
   0. lvar105 = {454457246 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #UN -> #BJ
      <- Switch[21716186] #UM -> #UN
      <- Immediate #UM -> #UN
===#Block BJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_GLAZED_TERRACOTTA;
   2. goto LZ
      -> UnconditionalJump[GOTO] #BJ -> #LZ
      <- Switch[1446140237] #UM -> #BJ
      <- UnconditionalJump[GOTO] #UN -> #BJ
===#Block LZ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 246875001:
      	 goto	#MA
      case 964159690:
      	 goto	#LZ
      case 994337953:
      	 goto	#JC
      case 1583968801:
      	 goto	#GO
      default:
      	 goto	#JC
   }
      -> Switch[246875001] #LZ -> #MA
      -> DefaultSwitch #LZ -> #JC
      -> Switch[964159690] #LZ -> #LZ
      -> Switch[1583968801] #LZ -> #GO
      -> Switch[994337953] #LZ -> #JC
      -> Immediate #LZ -> #MA
      <- Switch[964159690] #LZ -> #LZ
      <- UnconditionalJump[GOTO] #BJ -> #LZ
===#Block MA(size=2, flags=100)===
   0. lvar105 = {1168824522 ^ lvar105};
   1. goto GO
      -> UnconditionalJump[GOTO] #MA -> #GO
      <- Switch[246875001] #LZ -> #MA
      <- Immediate #LZ -> #MA
===#Block GO(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 121602531)
      goto GN
   1. throw nullconst;
      -> TryCatch range: [GO...GN] -> ABK ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #GO -> #GN
      <- UnconditionalJump[GOTO] #MA -> #GO
      <- Switch[1583968801] #LZ -> #GO
===#Block GN(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [GO...GN] -> ABK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #GO -> #GN
===#Block ABK(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1576088336:
      	 goto	#ABL
      case -1124094101:
      	 goto	#ABM
      default:
      	 goto	#ABN
   }
      -> DefaultSwitch #ABK -> #ABN
      -> Switch[-1124094101] #ABK -> #ABM
      -> Switch[-1576088336] #ABK -> #ABL
      <- TryCatch range: [GO...GN] -> ABK ([Ljava/io/IOException;])
      <- TryCatch range: [GO...GN] -> ABK ([Ljava/io/IOException;])
===#Block ABL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 2090024303);
   1. goto GP
      -> UnconditionalJump[GOTO] #ABL -> #GP
      <- Switch[-1576088336] #ABK -> #ABL
===#Block ABM(size=2, flags=10100)===
   0. lvar105 = {525144348 ^ lvar105};
   1. goto GP
      -> UnconditionalJump[GOTO] #ABM -> #GP
      <- Switch[-1124094101] #ABK -> #ABM
===#Block GP(size=2, flags=0)===
   0. _consume(catch());
   1. goto QD
      -> UnconditionalJump[GOTO] #GP -> #QD
      <- UnconditionalJump[GOTO] #ABM -> #GP
      <- UnconditionalJump[GOTO] #ABL -> #GP
===#Block QD(size=2, flags=10100)===
   0. lvar105 = {109202249 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QD -> #CN
      <- UnconditionalJump[GOTO] #GP -> #QD
===#Block ABN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABK -> #ABN
===#Block RE(size=2, flags=10100)===
   0. lvar105 = {1512588159 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #RE -> #JC
      <- UnconditionalJump[GOTO] #JO -> #RE
===#Block BH(size=1, flags=0)===
   0. goto RM
      -> UnconditionalJump[GOTO] #BH -> #RM
      <- Immediate #BG -> #BH
===#Block RM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 777102724);
   1. goto IT
      -> UnconditionalJump[GOTO] #RM -> #IT
      <- UnconditionalJump[GOTO] #BH -> #RM
===#Block IT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 95614154)
      goto IS
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IT -> #IS
      -> TryCatch range: [IT...IS] -> AEI ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #RM -> #IT
===#Block IS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IT...IS] -> AEI ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IT -> #IS
===#Block AEI(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -226320672:
      	 goto	#AEK
      case 533309985:
      	 goto	#AEJ
      default:
      	 goto	#AEL
   }
      -> DefaultSwitch #AEI -> #AEL
      -> Switch[533309985] #AEI -> #AEJ
      -> Switch[-226320672] #AEI -> #AEK
      <- TryCatch range: [IT...IS] -> AEI ([Ljava/io/IOException;])
      <- TryCatch range: [IT...IS] -> AEI ([Ljava/io/IOException;])
===#Block AEK(size=2, flags=10100)===
   0. lvar105 = {191121874 ^ lvar105};
   1. goto IU
      -> UnconditionalJump[GOTO] #AEK -> #IU
      <- Switch[-226320672] #AEI -> #AEK
===#Block AEJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 919416650);
   1. goto IU
      -> UnconditionalJump[GOTO] #AEJ -> #IU
      <- Switch[533309985] #AEI -> #AEJ
===#Block IU(size=2, flags=0)===
   0. _consume(catch());
   1. goto KW
      -> UnconditionalJump[GOTO] #IU -> #KW
      <- UnconditionalJump[GOTO] #AEK -> #IU
      <- UnconditionalJump[GOTO] #AEJ -> #IU
===#Block KW(size=2, flags=10100)===
   0. lvar105 = {778655304 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #KW -> #BI
      <- UnconditionalJump[GOTO] #IU -> #KW
===#Block AEL(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AEI -> #AEL
===#Block VY(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 37432866:
      	 goto	#VZ
      case 1220846699:
      	 goto	#JC
      case 1275210501:
      	 goto	#VY
      case 1573673881:
      	 goto	#AU
      default:
      	 goto	#JC
   }
      -> Switch[1573673881] #VY -> #AU
      -> Immediate #VY -> #VZ
      -> Switch[37432866] #VY -> #VZ
      -> Switch[1220846699] #VY -> #JC
      -> DefaultSwitch #VY -> #JC
      -> Switch[1275210501] #VY -> #VY
      <- Switch[36440914] #AH -> #VY
      <- Switch[1275210501] #VY -> #VY
===#Block VZ(size=2, flags=100)===
   0. lvar105 = {756428246 ^ lvar105};
   1. goto AU
      -> UnconditionalJump[GOTO] #VZ -> #AU
      <- Immediate #VY -> #VZ
      <- Switch[37432866] #VY -> #VZ
===#Block AU(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar44 = lvar102;
   2. lvar88 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.oszvjpwpvgvmngu(), lvar105);
   3. lvar45 = lvar44.equals(lvar88);
   4. if (lvar45 != {796355998 ^ lvar105})
      goto UI
   5. lvar105 = {107830046 ^ lvar105};
      -> Immediate #AU -> #AW
      -> ConditionalJump[IF_ICMPNE] #AU -> #UI
      <- Switch[1573673881] #VY -> #AU
      <- UnconditionalJump[GOTO] #VZ -> #AU
===#Block UI(size=2, flags=10100)===
   0. lvar105 = {1800121171 ^ lvar105};
   1. goto JY
      -> UnconditionalJump[GOTO] #UI -> #JY
      <- ConditionalJump[IF_ICMPNE] #AU -> #UI
===#Block JY(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 568735338)
      goto UA
   1. goto PG
      -> UnconditionalJump[GOTO] #JY -> #PG
      -> ConditionalJump[IF_ICMPEQ] #JY -> #UA
      <- UnconditionalJump[GOTO] #UI -> #JY
===#Block UA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 828766584);
   1. goto AV
      -> UnconditionalJump[GOTO] #UA -> #AV
      <- ConditionalJump[IF_ICMPEQ] #JY -> #UA
===#Block AV(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_GLAZED_TERRACOTTA;
   2. goto OE
      -> UnconditionalJump[GOTO] #AV -> #OE
      <- UnconditionalJump[GOTO] #UA -> #AV
===#Block OE(size=2, flags=10100)===
   0. lvar105 = {43751640 ^ lvar105};
   1. goto HV
      -> UnconditionalJump[GOTO] #OE -> #HV
      <- UnconditionalJump[GOTO] #AV -> #OE
===#Block HV(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 134828961)
      goto HU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HV -> #HU
      -> TryCatch range: [HV...HU] -> ADC ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #OE -> #HV
===#Block HU(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HV...HU] -> ADC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HV -> #HU
===#Block ADC(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1105904789:
      	 goto	#ADD
      case -988545432:
      	 goto	#ADE
      default:
      	 goto	#ADF
   }
      -> Switch[-1105904789] #ADC -> #ADD
      -> Switch[-988545432] #ADC -> #ADE
      -> DefaultSwitch #ADC -> #ADF
      <- TryCatch range: [HV...HU] -> ADC ([Ljava/io/IOException;])
      <- TryCatch range: [HV...HU] -> ADC ([Ljava/io/IOException;])
===#Block ADF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADC -> #ADF
===#Block ADE(size=2, flags=10100)===
   0. lvar105 = {242444752 ^ lvar105};
   1. goto HW
      -> UnconditionalJump[GOTO] #ADE -> #HW
      <- Switch[-988545432] #ADC -> #ADE
===#Block ADD(size=2, flags=10100)===
   0. lvar105 = {1628680560 ^ lvar105};
   1. goto HW
      -> UnconditionalJump[GOTO] #ADD -> #HW
      <- Switch[-1105904789] #ADC -> #ADD
===#Block HW(size=2, flags=0)===
   0. _consume(catch());
   1. goto NA
      -> UnconditionalJump[GOTO] #HW -> #NA
      <- UnconditionalJump[GOTO] #ADD -> #HW
      <- UnconditionalJump[GOTO] #ADE -> #HW
===#Block NA(size=2, flags=10100)===
   0. lvar105 = {2021767589 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NA -> #CN
      <- UnconditionalJump[GOTO] #HW -> #NA
===#Block PG(size=2, flags=10100)===
   0. lvar105 = {1424618360 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PG -> #JC
      <- UnconditionalJump[GOTO] #JY -> #PG
===#Block AW(size=1, flags=0)===
   0. goto QT
      -> UnconditionalJump[GOTO] #AW -> #QT
      <- Immediate #AU -> #AW
===#Block QT(size=2, flags=10100)===
   0. lvar105 = {659101283 ^ lvar105};
   1. goto FH
      -> UnconditionalJump[GOTO] #QT -> #FH
      <- UnconditionalJump[GOTO] #AW -> #QT
===#Block FH(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 197198308)
      goto FG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FH -> #FG
      -> TryCatch range: [FH...FG] -> ZS ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #QT -> #FH
===#Block FG(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FH...FG] -> ZS ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FH -> #FG
===#Block ZS(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -2093110240:
      	 goto	#ZU
      case 1922631448:
      	 goto	#ZT
      default:
      	 goto	#ZV
   }
      -> Switch[1922631448] #ZS -> #ZT
      -> Switch[-2093110240] #ZS -> #ZU
      -> DefaultSwitch #ZS -> #ZV
      <- TryCatch range: [FH...FG] -> ZS ([Ljava/io/IOException;])
      <- TryCatch range: [FH...FG] -> ZS ([Ljava/io/IOException;])
===#Block ZV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZS -> #ZV
===#Block ZU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1976378402);
   1. goto FI
      -> UnconditionalJump[GOTO] #ZU -> #FI
      <- Switch[-2093110240] #ZS -> #ZU
===#Block ZT(size=2, flags=10100)===
   0. lvar105 = {1811525957 ^ lvar105};
   1. goto FI
      -> UnconditionalJump[GOTO] #ZT -> #FI
      <- Switch[1922631448] #ZS -> #ZT
===#Block FI(size=2, flags=0)===
   0. _consume(catch());
   1. goto LB
      -> UnconditionalJump[GOTO] #FI -> #LB
      <- UnconditionalJump[GOTO] #ZU -> #FI
      <- UnconditionalJump[GOTO] #ZT -> #FI
===#Block LB(size=2, flags=10100)===
   0. lvar105 = {1593302496 ^ lvar105};
   1. goto BI
      -> UnconditionalJump[GOTO] #LB -> #BI
      <- UnconditionalJump[GOTO] #FI -> #LB
===#Block BI(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_GLAZED_TERRACOTTA;
   2. goto MR
      -> UnconditionalJump[GOTO] #BI -> #MR
      <- UnconditionalJump[GOTO] #PP -> #BI
      <- UnconditionalJump[GOTO] #NJ -> #BI
      <- Switch[208115268] #NK -> #BI
      <- UnconditionalJump[GOTO] #WD -> #BI
      <- UnconditionalJump[GOTO] #MJ -> #BI
      <- UnconditionalJump[GOTO] #LB -> #BI
      <- UnconditionalJump[GOTO] #OA -> #BI
      <- UnconditionalJump[GOTO] #KW -> #BI
      <- Switch[150203155] #MI -> #BI
      <- UnconditionalJump[GOTO] #OO -> #BI
      <- UnconditionalJump[GOTO] #NL -> #BI
      <- UnconditionalJump[GOTO] #RA -> #BI
      <- Switch[128423899] #PO -> #BI
===#Block MR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1287517227);
   1. goto IQ
      -> UnconditionalJump[GOTO] #MR -> #IQ
      <- UnconditionalJump[GOTO] #BI -> #MR
===#Block IQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 138613427)
      goto IP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IQ -> #IP
      -> TryCatch range: [IQ...IP] -> AEE ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #MR -> #IQ
===#Block IP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IQ...IP] -> AEE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IQ -> #IP
===#Block AEE(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -2010838528:
      	 goto	#AEG
      case -1090141333:
      	 goto	#AEF
      default:
      	 goto	#AEH
   }
      -> Switch[-1090141333] #AEE -> #AEF
      -> Switch[-2010838528] #AEE -> #AEG
      -> DefaultSwitch #AEE -> #AEH
      <- TryCatch range: [IQ...IP] -> AEE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IQ...IP] -> AEE ([Ljava/lang/IllegalAccessException;])
===#Block AEH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEE -> #AEH
===#Block AEG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 991822769);
   1. goto IR
      -> UnconditionalJump[GOTO] #AEG -> #IR
      <- Switch[-2010838528] #AEE -> #AEG
===#Block AEF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1576671900);
   1. goto IR
      -> UnconditionalJump[GOTO] #AEF -> #IR
      <- Switch[-1090141333] #AEE -> #AEF
===#Block IR(size=2, flags=0)===
   0. _consume(catch());
   1. goto LU
      -> UnconditionalJump[GOTO] #IR -> #LU
      <- UnconditionalJump[GOTO] #AEG -> #IR
      <- UnconditionalJump[GOTO] #AEF -> #IR
===#Block LU(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 26456488:
      	 goto	#LV
      case 442588872:
      	 goto	#JC
      case 546786312:
      	 goto	#LU
      case 1795033837:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[26456488] #LU -> #LV
      -> Switch[442588872] #LU -> #JC
      -> DefaultSwitch #LU -> #JC
      -> Switch[546786312] #LU -> #LU
      -> Switch[1795033837] #LU -> #CN
      -> Immediate #LU -> #LV
      <- UnconditionalJump[GOTO] #IR -> #LU
      <- Switch[546786312] #LU -> #LU
===#Block LV(size=2, flags=100)===
   0. lvar105 = {1145703753 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #LV -> #CN
      <- Switch[26456488] #LU -> #LV
      <- Immediate #LU -> #LV
===#Block UJ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 140373912:
      	 goto	#UK
      case 300397027:
      	 goto	#JZ
      case 1183495208:
      	 goto	#JC
      case 1468737508:
      	 goto	#UJ
      default:
      	 goto	#JC
   }
      -> Switch[140373912] #UJ -> #UK
      -> Switch[1468737508] #UJ -> #UJ
      -> Switch[300397027] #UJ -> #JZ
      -> Switch[1183495208] #UJ -> #JC
      -> DefaultSwitch #UJ -> #JC
      -> Immediate #UJ -> #UK
      <- ConditionalJump[IF_ICMPEQ] #AG -> #UJ
      <- Switch[1468737508] #UJ -> #UJ
===#Block UK(size=2, flags=100)===
   0. lvar105 = {417649916 ^ lvar105};
   1. goto JZ
      -> UnconditionalJump[GOTO] #UK -> #JZ
      <- Switch[140373912] #UJ -> #UK
      <- Immediate #UJ -> #UK
===#Block JZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 937027521)
      goto TB
   1. goto KO
      -> ConditionalJump[IF_ICMPEQ] #JZ -> #TB
      -> UnconditionalJump[GOTO] #JZ -> #KO
      <- UnconditionalJump[GOTO] #UK -> #JZ
      <- Switch[300397027] #UJ -> #JZ
===#Block KO(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 66967085:
      	 goto	#JC
      case 193388763:
      	 goto	#KP
      case 381205364:
      	 goto	#JC
      case 1946963578:
      	 goto	#KO
      default:
      	 goto	#JC
   }
      -> Switch[381205364] #KO -> #JC
      -> DefaultSwitch #KO -> #JC
      -> Immediate #KO -> #KP
      -> Switch[193388763] #KO -> #KP
      -> Switch[1946963578] #KO -> #KO
      <- Switch[1946963578] #KO -> #KO
      <- UnconditionalJump[GOTO] #JZ -> #KO
===#Block KP(size=2, flags=100)===
   0. lvar105 = {908986701 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #KP -> #JC
      <- Immediate #KO -> #KP
      <- Switch[193388763] #KO -> #KP
===#Block TB(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 3088581:
      	 goto	#BK
      case 193388763:
      	 goto	#TC
      case 199511714:
      	 goto	#JC
      case 748103959:
      	 goto	#TB
      default:
      	 goto	#JC
   }
      -> Switch[199511714] #TB -> #JC
      -> Switch[748103959] #TB -> #TB
      -> DefaultSwitch #TB -> #JC
      -> Switch[193388763] #TB -> #TC
      -> Immediate #TB -> #TC
      -> Switch[3088581] #TB -> #BK
      <- ConditionalJump[IF_ICMPEQ] #JZ -> #TB
      <- Switch[748103959] #TB -> #TB
===#Block TC(size=2, flags=100)===
   0. lvar105 = {1518374144 ^ lvar105};
   1. goto BK
      -> UnconditionalJump[GOTO] #TC -> #BK
      <- Switch[193388763] #TB -> #TC
      <- Immediate #TB -> #TC
===#Block BK(size=7, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar54 = lvar1;
   2. lvar103 = lvar54;
   3. lvar55 = lvar103;
   4. lvar56 = lvar55.hashCode();
   5. svar107 = {lvar56 ^ lvar105};
   6. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(svar107)) {
      case 13855524:
      	 goto	#UR
      case 13855547:
      	 goto	#US
      case 13855681:
      	 goto	#UU
      case 13855683:
      	 goto	#UV
      case 13855687:
      	 goto	#UW
      case 13855689:
      	 goto	#UY
      case 13855691:
      	 goto	#UZ
      case 13855693:
      	 goto	#VA
      case 13855695:
      	 goto	#VC
      default:
      	 goto	#VD
   }
      -> Switch[13855695] #BK -> #VC
      -> Switch[13855689] #BK -> #UY
      -> Switch[13855681] #BK -> #UU
      -> Switch[13855691] #BK -> #UZ
      -> DefaultSwitch #BK -> #VD
      -> Switch[13855683] #BK -> #UV
      -> Switch[13855687] #BK -> #UW
      -> Switch[13855693] #BK -> #VA
      -> Switch[13855547] #BK -> #US
      -> Switch[13855524] #BK -> #UR
      <- UnconditionalJump[GOTO] #TC -> #BK
      <- Switch[3088581] #TB -> #BK
===#Block UR(size=2, flags=10100)===
   0. lvar105 = {1142445035 ^ lvar105};
   1. goto CA
      -> UnconditionalJump[GOTO] #UR -> #CA
      <- Switch[13855524] #BK -> #UR
===#Block CA(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar67 = lvar103;
   2. lvar98 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bnkreeconiugdhg(), lvar105);
   3. lvar68 = lvar67.equals(lvar98);
   4. if (lvar68 != {946070035 ^ lvar105})
      goto UE
   5. lvar105 = {460303672 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CA -> #UE
      -> Immediate #CA -> #CB
      <- UnconditionalJump[GOTO] #UR -> #CA
===#Block CB(size=1, flags=0)===
   0. goto MK
      -> UnconditionalJump[GOTO] #CB -> #MK
      <- Immediate #CA -> #CB
===#Block MK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 30074702);
   1. goto HP
      -> UnconditionalJump[GOTO] #MK -> #HP
      <- UnconditionalJump[GOTO] #CB -> #MK
===#Block HP(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 56673413)
      goto HO
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HP -> #HO
      -> TryCatch range: [HP...HO] -> ACU ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #MK -> #HP
===#Block HO(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HP...HO] -> ACU ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HP -> #HO
===#Block ACU(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1482865752:
      	 goto	#ACW
      case 372589353:
      	 goto	#ACV
      default:
      	 goto	#ACX
   }
      -> DefaultSwitch #ACU -> #ACX
      -> Switch[-1482865752] #ACU -> #ACW
      -> Switch[372589353] #ACU -> #ACV
      <- TryCatch range: [HP...HO] -> ACU ([Ljava/io/IOException;])
      <- TryCatch range: [HP...HO] -> ACU ([Ljava/io/IOException;])
===#Block ACV(size=2, flags=10100)===
   0. lvar105 = {395039727 ^ lvar105};
   1. goto HQ
      -> UnconditionalJump[GOTO] #ACV -> #HQ
      <- Switch[372589353] #ACU -> #ACV
===#Block ACW(size=2, flags=10100)===
   0. lvar105 = {566154239 ^ lvar105};
   1. goto HQ
      -> UnconditionalJump[GOTO] #ACW -> #HQ
      <- Switch[-1482865752] #ACU -> #ACW
===#Block HQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto NR
      -> UnconditionalJump[GOTO] #HQ -> #NR
      <- UnconditionalJump[GOTO] #ACW -> #HQ
      <- UnconditionalJump[GOTO] #ACV -> #HQ
===#Block NR(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 122038806:
      	 goto	#NS
      case 634990675:
      	 goto	#CM
      case 766176798:
      	 goto	#JC
      case 1639769013:
      	 goto	#NR
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #NR -> #JC
      -> Switch[1639769013] #NR -> #NR
      -> Switch[122038806] #NR -> #NS
      -> Immediate #NR -> #NS
      -> Switch[766176798] #NR -> #JC
      -> Switch[634990675] #NR -> #CM
      <- UnconditionalJump[GOTO] #HQ -> #NR
      <- Switch[1639769013] #NR -> #NR
===#Block NS(size=2, flags=100)===
   0. lvar105 = {979664297 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #NS -> #CM
      <- Switch[122038806] #NR -> #NS
      <- Immediate #NR -> #NS
===#Block ACX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ACU -> #ACX
===#Block UE(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 222412391:
      	 goto	#UF
      case 908498335:
      	 goto	#JC
      case 1839486278:
      	 goto	#JW
      case 1948508456:
      	 goto	#UE
      default:
      	 goto	#JC
   }
      -> Switch[1948508456] #UE -> #UE
      -> Switch[1839486278] #UE -> #JW
      -> Switch[222412391] #UE -> #UF
      -> DefaultSwitch #UE -> #JC
      -> Switch[908498335] #UE -> #JC
      -> Immediate #UE -> #UF
      <- Switch[1948508456] #UE -> #UE
      <- ConditionalJump[IF_ICMPNE] #CA -> #UE
===#Block UF(size=2, flags=100)===
   0. lvar105 = {42353367 ^ lvar105};
   1. goto JW
      -> UnconditionalJump[GOTO] #UF -> #JW
      <- Switch[222412391] #UE -> #UF
      <- Immediate #UE -> #UF
===#Block JW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -684915167)
      goto RQ
   1. goto LC
      -> UnconditionalJump[GOTO] #JW -> #LC
      -> ConditionalJump[IF_ICMPEQ] #JW -> #RQ
      <- Switch[1839486278] #UE -> #JW
      <- UnconditionalJump[GOTO] #UF -> #JW
===#Block RQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 508886454);
   1. goto CC
      -> UnconditionalJump[GOTO] #RQ -> #CC
      <- ConditionalJump[IF_ICMPEQ] #JW -> #RQ
===#Block CC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.MAGENTA_CONCRETE_POWDER;
   2. goto KF
      -> UnconditionalJump[GOTO] #CC -> #KF
      <- UnconditionalJump[GOTO] #RQ -> #CC
===#Block KF(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 118981053:
      	 goto	#KG
      case 576622583:
      	 goto	#JC
      case 1403311582:
      	 goto	#KF
      case 1923020386:
      	 goto	#CQ
      default:
      	 goto	#JC
   }
      -> Immediate #KF -> #KG
      -> Switch[1403311582] #KF -> #KF
      -> Switch[1923020386] #KF -> #CQ
      -> DefaultSwitch #KF -> #JC
      -> Switch[576622583] #KF -> #JC
      -> Switch[118981053] #KF -> #KG
      <- Switch[1403311582] #KF -> #KF
      <- UnconditionalJump[GOTO] #CC -> #KF
===#Block KG(size=2, flags=100)===
   0. lvar105 = {3307372 ^ lvar105};
   1. goto CQ
      -> UnconditionalJump[GOTO] #KG -> #CQ
      <- Immediate #KF -> #KG
      <- Switch[118981053] #KF -> #KG
===#Block CQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 113116441)
      goto CP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CQ -> #CP
      -> TryCatch range: [CQ...CP] -> WE ([Ljava/io/IOException;])
      <- Switch[1923020386] #KF -> #CQ
      <- UnconditionalJump[GOTO] #KG -> #CQ
===#Block CP(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CQ...CP] -> WE ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CQ -> #CP
===#Block WE(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -848740390:
      	 goto	#WG
      case 605638897:
      	 goto	#WF
      default:
      	 goto	#WH
   }
      -> DefaultSwitch #WE -> #WH
      -> Switch[605638897] #WE -> #WF
      -> Switch[-848740390] #WE -> #WG
      <- TryCatch range: [CQ...CP] -> WE ([Ljava/io/IOException;])
      <- TryCatch range: [CQ...CP] -> WE ([Ljava/io/IOException;])
===#Block WG(size=2, flags=10100)===
   0. lvar105 = {1240702631 ^ lvar105};
   1. goto CR
      -> UnconditionalJump[GOTO] #WG -> #CR
      <- Switch[-848740390] #WE -> #WG
===#Block WF(size=2, flags=10100)===
   0. lvar105 = {886936386 ^ lvar105};
   1. goto CR
      -> UnconditionalJump[GOTO] #WF -> #CR
      <- Switch[605638897] #WE -> #WF
===#Block CR(size=2, flags=0)===
   0. _consume(catch());
   1. goto MC
      -> UnconditionalJump[GOTO] #CR -> #MC
      <- UnconditionalJump[GOTO] #WG -> #CR
      <- UnconditionalJump[GOTO] #WF -> #CR
===#Block MC(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 143864967:
      	 goto	#MC
      case 263690108:
      	 goto	#MD
      case 883703828:
      	 goto	#CN
      case 1025857328:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #MC -> #JC
      -> Switch[143864967] #MC -> #MC
      -> Switch[263690108] #MC -> #MD
      -> Switch[1025857328] #MC -> #JC
      -> Immediate #MC -> #MD
      -> Switch[883703828] #MC -> #CN
      <- Switch[143864967] #MC -> #MC
      <- UnconditionalJump[GOTO] #CR -> #MC
===#Block MD(size=2, flags=100)===
   0. lvar105 = {2114852580 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #MD -> #CN
      <- Switch[263690108] #MC -> #MD
      <- Immediate #MC -> #MD
===#Block WH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #WE -> #WH
===#Block LC(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 35446174:
      	 goto	#LD
      case 716986023:
      	 goto	#JC
      case 729687055:
      	 goto	#JC
      case 1616395415:
      	 goto	#LC
      default:
      	 goto	#JC
   }
      -> Switch[1616395415] #LC -> #LC
      -> Switch[729687055] #LC -> #JC
      -> DefaultSwitch #LC -> #JC
      -> Immediate #LC -> #LD
      -> Switch[35446174] #LC -> #LD
      <- UnconditionalJump[GOTO] #JW -> #LC
      <- Switch[1616395415] #LC -> #LC
===#Block LD(size=2, flags=100)===
   0. lvar105 = {707827057 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LD -> #JC
      <- Immediate #LC -> #LD
      <- Switch[35446174] #LC -> #LD
===#Block US(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 19911851:
      	 goto	#UT
      case 479180353:
      	 goto	#US
      case 787661875:
      	 goto	#JC
      case 1194002366:
      	 goto	#BL
      default:
      	 goto	#JC
   }
      -> Immediate #US -> #UT
      -> Switch[19911851] #US -> #UT
      -> Switch[479180353] #US -> #US
      -> Switch[787661875] #US -> #JC
      -> DefaultSwitch #US -> #JC
      -> Switch[1194002366] #US -> #BL
      <- Switch[13855547] #BK -> #US
      <- Switch[479180353] #US -> #US
===#Block UT(size=2, flags=100)===
   0. lvar105 = {926282581 ^ lvar105};
   1. goto BL
      -> UnconditionalJump[GOTO] #UT -> #BL
      <- Immediate #US -> #UT
      <- Switch[19911851] #US -> #UT
===#Block BL(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar57 = lvar103;
   2. lvar93 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.mblioqoctzcrvpb(), lvar105);
   3. lvar58 = lvar57.equals(lvar93);
   4. if (lvar58 != {1263420077 ^ lvar105})
      goto SF
   5. lvar105 = {479495179 ^ lvar105};
      -> Immediate #BL -> #BN
      -> ConditionalJump[IF_ICMPNE] #BL -> #SF
      <- UnconditionalJump[GOTO] #UT -> #BL
      <- Switch[1194002366] #US -> #BL
===#Block SF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 2022779109);
   1. goto JK
      -> UnconditionalJump[GOTO] #SF -> #JK
      <- ConditionalJump[IF_ICMPNE] #BL -> #SF
===#Block JK(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1627696575)
      goto TE
   1. goto KZ
      -> ConditionalJump[IF_ICMPEQ] #JK -> #TE
      -> UnconditionalJump[GOTO] #JK -> #KZ
      <- UnconditionalJump[GOTO] #SF -> #JK
===#Block KZ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 75558452:
      	 goto	#LA
      case 132452546:
      	 goto	#JC
      case 921014590:
      	 goto	#KZ
      case 2089527516:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[75558452] #KZ -> #LA
      -> Switch[132452546] #KZ -> #JC
      -> DefaultSwitch #KZ -> #JC
      -> Switch[921014590] #KZ -> #KZ
      -> Immediate #KZ -> #LA
      <- UnconditionalJump[GOTO] #JK -> #KZ
      <- Switch[921014590] #KZ -> #KZ
===#Block LA(size=2, flags=100)===
   0. lvar105 = {587882493 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LA -> #JC
      <- Switch[75558452] #KZ -> #LA
      <- Immediate #KZ -> #LA
===#Block TE(size=2, flags=10100)===
   0. lvar105 = {1217138868 ^ lvar105};
   1. goto BM
      -> UnconditionalJump[GOTO] #TE -> #BM
      <- ConditionalJump[IF_ICMPEQ] #JK -> #TE
===#Block BM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_CONCRETE_POWDER;
   2. goto ND
      -> UnconditionalJump[GOTO] #BM -> #ND
      <- UnconditionalJump[GOTO] #TE -> #BM
===#Block ND(size=2, flags=10100)===
   0. lvar105 = {621424627 ^ lvar105};
   1. goto EG
      -> UnconditionalJump[GOTO] #ND -> #EG
      <- UnconditionalJump[GOTO] #BM -> #ND
===#Block EG(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 114117268)
      goto EF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EG -> #EF
      -> TryCatch range: [EG...EF] -> YI ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #ND -> #EG
===#Block EF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EG...EF] -> YI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EG -> #EF
===#Block YI(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -221599622:
      	 goto	#YJ
      case 1587218400:
      	 goto	#YK
      default:
      	 goto	#YL
   }
      -> Switch[-221599622] #YI -> #YJ
      -> Switch[1587218400] #YI -> #YK
      -> DefaultSwitch #YI -> #YL
      <- TryCatch range: [EG...EF] -> YI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EG...EF] -> YI ([Ljava/lang/RuntimeException;])
===#Block YL(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #YI -> #YL
===#Block YK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1880702973);
   1. goto EH
      -> UnconditionalJump[GOTO] #YK -> #EH
      <- Switch[1587218400] #YI -> #YK
===#Block YJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 630393358);
   1. goto EH
      -> UnconditionalJump[GOTO] #YJ -> #EH
      <- Switch[-221599622] #YI -> #YJ
===#Block EH(size=2, flags=0)===
   0. _consume(catch());
   1. goto OR
      -> UnconditionalJump[GOTO] #EH -> #OR
      <- UnconditionalJump[GOTO] #YK -> #EH
      <- UnconditionalJump[GOTO] #YJ -> #EH
===#Block OR(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 192111288:
      	 goto	#JC
      case 265839579:
      	 goto	#OS
      case 414102900:
      	 goto	#OR
      case 1533946474:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[265839579] #OR -> #OS
      -> Switch[1533946474] #OR -> #CN
      -> Immediate #OR -> #OS
      -> Switch[192111288] #OR -> #JC
      -> DefaultSwitch #OR -> #JC
      -> Switch[414102900] #OR -> #OR
      <- UnconditionalJump[GOTO] #EH -> #OR
      <- Switch[414102900] #OR -> #OR
===#Block OS(size=2, flags=100)===
   0. lvar105 = {362475705 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OS -> #CN
      <- Switch[265839579] #OR -> #OS
      <- Immediate #OR -> #OS
===#Block BN(size=1, flags=0)===
   0. goto NE
      -> UnconditionalJump[GOTO] #BN -> #NE
      <- Immediate #BL -> #BN
===#Block NE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 807340819);
   1. goto EJ
      -> UnconditionalJump[GOTO] #NE -> #EJ
      <- UnconditionalJump[GOTO] #BN -> #NE
===#Block EJ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 152000843)
      goto EI
   1. throw nullconst;
      -> TryCatch range: [EJ...EI] -> YM ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EJ -> #EI
      <- UnconditionalJump[GOTO] #NE -> #EJ
===#Block EI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EJ...EI] -> YM ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EJ -> #EI
===#Block YM(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1791386880:
      	 goto	#YO
      case 1043230123:
      	 goto	#YN
      default:
      	 goto	#YP
   }
      -> Switch[-1791386880] #YM -> #YO
      -> Switch[1043230123] #YM -> #YN
      -> DefaultSwitch #YM -> #YP
      <- TryCatch range: [EJ...EI] -> YM ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EJ...EI] -> YM ([Ljava/lang/RuntimeException;])
===#Block YP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YM -> #YP
===#Block YN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 424107584);
   1. goto EK
      -> UnconditionalJump[GOTO] #YN -> #EK
      <- Switch[1043230123] #YM -> #YN
===#Block YO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1814405269);
   1. goto EK
      -> UnconditionalJump[GOTO] #YO -> #EK
      <- Switch[-1791386880] #YM -> #YO
===#Block EK(size=2, flags=0)===
   0. _consume(catch());
   1. goto PZ
      -> UnconditionalJump[GOTO] #EK -> #PZ
      <- UnconditionalJump[GOTO] #YN -> #EK
      <- UnconditionalJump[GOTO] #YO -> #EK
===#Block PZ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 85753896:
      	 goto	#QA
      case 604246729:
      	 goto	#JC
      case 1114780597:
      	 goto	#CM
      case 1159821189:
      	 goto	#PZ
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #PZ -> #JC
      -> Switch[1159821189] #PZ -> #PZ
      -> Switch[604246729] #PZ -> #JC
      -> Switch[1114780597] #PZ -> #CM
      -> Switch[85753896] #PZ -> #QA
      -> Immediate #PZ -> #QA
      <- UnconditionalJump[GOTO] #EK -> #PZ
      <- Switch[1159821189] #PZ -> #PZ
===#Block QA(size=2, flags=100)===
   0. lvar105 = {1907070422 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #QA -> #CM
      <- Switch[85753896] #PZ -> #QA
      <- Immediate #PZ -> #QA
===#Block VA(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 19911851:
      	 goto	#VB
      case 296088025:
      	 goto	#CG
      case 806942124:
      	 goto	#JC
      case 2118762872:
      	 goto	#VA
      default:
      	 goto	#JC
   }
      -> Switch[19911851] #VA -> #VB
      -> Switch[2118762872] #VA -> #VA
      -> Switch[806942124] #VA -> #JC
      -> DefaultSwitch #VA -> #JC
      -> Immediate #VA -> #VB
      -> Switch[296088025] #VA -> #CG
      <- Switch[13855693] #BK -> #VA
      <- Switch[2118762872] #VA -> #VA
===#Block VB(size=2, flags=100)===
   0. lvar105 = {2022612926 ^ lvar105};
   1. goto CG
      -> UnconditionalJump[GOTO] #VB -> #CG
      <- Switch[19911851] #VA -> #VB
      <- Immediate #VA -> #VB
===#Block CG(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar71 = lvar103;
   2. lvar100 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.fmfhzcdzyfbvjmu(), lvar105);
   3. lvar72 = lvar71.equals(lvar100);
   4. if (lvar72 != {83177030 ^ lvar105})
      goto SE
   5. lvar105 = {1889476755 ^ lvar105};
      -> Immediate #CG -> #CI
      -> ConditionalJump[IF_ICMPNE] #CG -> #SE
      <- UnconditionalJump[GOTO] #VB -> #CG
      <- Switch[296088025] #VA -> #CG
===#Block SE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1323974674);
   1. goto JH
      -> UnconditionalJump[GOTO] #SE -> #JH
      <- ConditionalJump[IF_ICMPNE] #CG -> #SE
===#Block JH(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1358656162)
      goto TD
   1. goto KS
      -> ConditionalJump[IF_ICMPEQ] #JH -> #TD
      -> UnconditionalJump[GOTO] #JH -> #KS
      <- UnconditionalJump[GOTO] #SE -> #JH
===#Block KS(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 261926877:
      	 goto	#KT
      case 1063436749:
      	 goto	#JC
      case 1150740098:
      	 goto	#JC
      case 1257274560:
      	 goto	#KS
      default:
      	 goto	#JC
   }
      -> Immediate #KS -> #KT
      -> Switch[1063436749] #KS -> #JC
      -> DefaultSwitch #KS -> #JC
      -> Switch[1257274560] #KS -> #KS
      -> Switch[261926877] #KS -> #KT
      <- Switch[1257274560] #KS -> #KS
      <- UnconditionalJump[GOTO] #JH -> #KS
===#Block KT(size=2, flags=100)===
   0. lvar105 = {1523211233 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #KT -> #JC
      <- Immediate #KS -> #KT
      <- Switch[261926877] #KS -> #KT
===#Block TD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1168517250);
   1. goto CH
      -> UnconditionalJump[GOTO] #TD -> #CH
      <- ConditionalJump[IF_ICMPEQ] #JH -> #TD
===#Block CH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_CONCRETE_POWDER;
   2. goto MO
      -> UnconditionalJump[GOTO] #CH -> #MO
      <- UnconditionalJump[GOTO] #TD -> #CH
===#Block MO(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 242677383:
      	 goto	#MP
      case 404673943:
      	 goto	#JC
      case 1429669115:
      	 goto	#IH
      case 1572974756:
      	 goto	#MO
      default:
      	 goto	#JC
   }
      -> Switch[1572974756] #MO -> #MO
      -> Switch[242677383] #MO -> #MP
      -> Switch[1429669115] #MO -> #IH
      -> DefaultSwitch #MO -> #JC
      -> Switch[404673943] #MO -> #JC
      -> Immediate #MO -> #MP
      <- Switch[1572974756] #MO -> #MO
      <- UnconditionalJump[GOTO] #CH -> #MO
===#Block MP(size=2, flags=100)===
   0. lvar105 = {159719303 ^ lvar105};
   1. goto IH
      -> UnconditionalJump[GOTO] #MP -> #IH
      <- Switch[242677383] #MO -> #MP
      <- Immediate #MO -> #MP
===#Block IH(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 98080135)
      goto IG
   1. throw nullconst;
      -> TryCatch range: [IH...IG] -> ADS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IH -> #IG
      <- UnconditionalJump[GOTO] #MP -> #IH
      <- Switch[1429669115] #MO -> #IH
===#Block IG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IH...IG] -> ADS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IH -> #IG
===#Block ADS(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -701876752:
      	 goto	#ADU
      case 836946568:
      	 goto	#ADT
      default:
      	 goto	#ADV
   }
      -> DefaultSwitch #ADS -> #ADV
      -> Switch[836946568] #ADS -> #ADT
      -> Switch[-701876752] #ADS -> #ADU
      <- TryCatch range: [IH...IG] -> ADS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IH...IG] -> ADS ([Ljava/lang/IllegalAccessException;])
===#Block ADU(size=2, flags=10100)===
   0. lvar105 = {434607839 ^ lvar105};
   1. goto II
      -> UnconditionalJump[GOTO] #ADU -> #II
      <- Switch[-701876752] #ADS -> #ADU
===#Block ADT(size=2, flags=10100)===
   0. lvar105 = {85887280 ^ lvar105};
   1. goto II
      -> UnconditionalJump[GOTO] #ADT -> #II
      <- Switch[836946568] #ADS -> #ADT
===#Block II(size=2, flags=0)===
   0. _consume(catch());
   1. goto PY
      -> UnconditionalJump[GOTO] #II -> #PY
      <- UnconditionalJump[GOTO] #ADT -> #II
      <- UnconditionalJump[GOTO] #ADU -> #II
===#Block PY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1836220377);
   1. goto CN
      -> UnconditionalJump[GOTO] #PY -> #CN
      <- UnconditionalJump[GOTO] #II -> #PY
===#Block ADV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADS -> #ADV
===#Block CI(size=1, flags=0)===
   0. goto LP
      -> UnconditionalJump[GOTO] #CI -> #LP
      <- Immediate #CG -> #CI
===#Block LP(size=2, flags=10100)===
   0. lvar105 = {1897142264 ^ lvar105};
   1. goto FW
      -> UnconditionalJump[GOTO] #LP -> #FW
      <- UnconditionalJump[GOTO] #CI -> #LP
===#Block FW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 86389476)
      goto FV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FW -> #FV
      -> TryCatch range: [FW...FV] -> AAM ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #LP -> #FW
===#Block FV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FW...FV] -> AAM ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FW -> #FV
===#Block AAM(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 737200488:
      	 goto	#AAN
      case 1475010073:
      	 goto	#AAO
      default:
      	 goto	#AAP
   }
      -> DefaultSwitch #AAM -> #AAP
      -> Switch[737200488] #AAM -> #AAN
      -> Switch[1475010073] #AAM -> #AAO
      <- TryCatch range: [FW...FV] -> AAM ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FW...FV] -> AAM ([Ljava/lang/RuntimeException;])
===#Block AAO(size=2, flags=10100)===
   0. lvar105 = {741765932 ^ lvar105};
   1. goto FX
      -> UnconditionalJump[GOTO] #AAO -> #FX
      <- Switch[1475010073] #AAM -> #AAO
===#Block AAN(size=2, flags=10100)===
   0. lvar105 = {62206402 ^ lvar105};
   1. goto FX
      -> UnconditionalJump[GOTO] #AAN -> #FX
      <- Switch[737200488] #AAM -> #AAN
===#Block FX(size=2, flags=0)===
   0. _consume(catch());
   1. goto MS
      -> UnconditionalJump[GOTO] #FX -> #MS
      <- UnconditionalJump[GOTO] #AAN -> #FX
      <- UnconditionalJump[GOTO] #AAO -> #FX
===#Block MS(size=2, flags=10100)===
   0. lvar105 = {165818060 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #MS -> #CM
      <- UnconditionalJump[GOTO] #FX -> #MS
===#Block AAP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AAM -> #AAP
===#Block UW(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 19911851:
      	 goto	#UX
      case 377445044:
      	 goto	#UW
      case 453196671:
      	 goto	#BX
      case 973331369:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[973331369] #UW -> #JC
      -> Switch[19911851] #UW -> #UX
      -> Switch[453196671] #UW -> #BX
      -> Switch[377445044] #UW -> #UW
      -> Immediate #UW -> #UX
      -> DefaultSwitch #UW -> #JC
      <- Switch[13855687] #BK -> #UW
      <- Switch[377445044] #UW -> #UW
===#Block UX(size=2, flags=100)===
   0. lvar105 = {837992831 ^ lvar105};
   1. goto BX
      -> UnconditionalJump[GOTO] #UX -> #BX
      <- Switch[19911851] #UW -> #UX
      <- Immediate #UW -> #UX
===#Block BX(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar65 = lvar103;
   2. lvar97 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bhxrstgudtyznzn(), lvar105);
   3. lvar66 = lvar65.equals(lvar97);
   4. if (lvar66 != {1300852871 ^ lvar105})
      goto RP
   5. lvar105 = {232158686 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BX -> #RP
      -> Immediate #BX -> #BZ
      <- UnconditionalJump[GOTO] #UX -> #BX
      <- Switch[453196671] #UW -> #BX
===#Block BZ(size=1, flags=0)===
   0. goto RJ
      -> UnconditionalJump[GOTO] #BZ -> #RJ
      <- Immediate #BX -> #BZ
===#Block RJ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 213408259:
      	 goto	#RK
      case 213597694:
      	 goto	#RJ
      case 440547604:
      	 goto	#IK
      case 2040508821:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #RJ -> #RK
      -> Switch[440547604] #RJ -> #IK
      -> DefaultSwitch #RJ -> #JC
      -> Switch[2040508821] #RJ -> #JC
      -> Switch[213408259] #RJ -> #RK
      -> Switch[213597694] #RJ -> #RJ
      <- Switch[213597694] #RJ -> #RJ
      <- UnconditionalJump[GOTO] #BZ -> #RJ
===#Block RK(size=2, flags=100)===
   0. lvar105 = {717206816 ^ lvar105};
   1. goto IK
      -> UnconditionalJump[GOTO] #RK -> #IK
      <- Immediate #RJ -> #RK
      <- Switch[213408259] #RJ -> #RK
===#Block IK(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 252919562)
      goto IJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IK -> #IJ
      -> TryCatch range: [IK...IJ] -> ADW ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #RK -> #IK
      <- Switch[440547604] #RJ -> #IK
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IK...IJ] -> ADW ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IK -> #IJ
===#Block ADW(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1090702197:
      	 goto	#ADY
      case 1459979211:
      	 goto	#ADX
      default:
      	 goto	#ADZ
   }
      -> DefaultSwitch #ADW -> #ADZ
      -> Switch[1459979211] #ADW -> #ADX
      -> Switch[-1090702197] #ADW -> #ADY
      <- TryCatch range: [IK...IJ] -> ADW ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IK...IJ] -> ADW ([Ljava/lang/IllegalAccessException;])
===#Block ADY(size=2, flags=10100)===
   0. lvar105 = {787365661 ^ lvar105};
   1. goto IL
      -> UnconditionalJump[GOTO] #ADY -> #IL
      <- Switch[-1090702197] #ADW -> #ADY
===#Block ADX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 869345781);
   1. goto IL
      -> UnconditionalJump[GOTO] #ADX -> #IL
      <- Switch[1459979211] #ADW -> #ADX
===#Block IL(size=2, flags=0)===
   0. _consume(catch());
   1. goto NZ
      -> UnconditionalJump[GOTO] #IL -> #NZ
      <- UnconditionalJump[GOTO] #ADX -> #IL
      <- UnconditionalJump[GOTO] #ADY -> #IL
===#Block NZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1444454319);
   1. goto CM
      -> UnconditionalJump[GOTO] #NZ -> #CM
      <- UnconditionalJump[GOTO] #IL -> #NZ
===#Block ADZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADW -> #ADZ
===#Block RP(size=2, flags=10100)===
   0. lvar105 = {1612155353 ^ lvar105};
   1. goto JB
      -> UnconditionalJump[GOTO] #RP -> #JB
      <- ConditionalJump[IF_ICMPNE] #BX -> #RP
===#Block JB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1828178673)
      goto SX
   1. goto QS
      -> ConditionalJump[IF_ICMPEQ] #JB -> #SX
      -> UnconditionalJump[GOTO] #JB -> #QS
      <- UnconditionalJump[GOTO] #RP -> #JB
===#Block QS(size=2, flags=10100)===
   0. lvar105 = {1028377835 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #QS -> #JC
      <- UnconditionalJump[GOTO] #JB -> #QS
===#Block SX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1249072148);
   1. goto BY
      -> UnconditionalJump[GOTO] #SX -> #BY
      <- ConditionalJump[IF_ICMPEQ] #JB -> #SX
===#Block BY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_CONCRETE_POWDER;
   2. goto KH
      -> UnconditionalJump[GOTO] #BY -> #KH
      <- UnconditionalJump[GOTO] #SX -> #BY
===#Block KH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 811163939);
   1. goto CT
      -> UnconditionalJump[GOTO] #KH -> #CT
      <- UnconditionalJump[GOTO] #BY -> #KH
===#Block CT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 166599615)
      goto CS
   1. throw nullconst;
      -> TryCatch range: [CT...CS] -> WI ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #CT -> #CS
      <- UnconditionalJump[GOTO] #KH -> #CT
===#Block CS(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CT...CS] -> WI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CT -> #CS
===#Block WI(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1113169078:
      	 goto	#WJ
      case 1535360307:
      	 goto	#WK
      default:
      	 goto	#WL
   }
      -> Switch[-1113169078] #WI -> #WJ
      -> DefaultSwitch #WI -> #WL
      -> Switch[1535360307] #WI -> #WK
      <- TryCatch range: [CT...CS] -> WI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CT...CS] -> WI ([Ljava/lang/RuntimeException;])
===#Block WK(size=2, flags=10100)===
   0. lvar105 = {827027476 ^ lvar105};
   1. goto CU
      -> UnconditionalJump[GOTO] #WK -> #CU
      <- Switch[1535360307] #WI -> #WK
===#Block WL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #WI -> #WL
===#Block WJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 227527643);
   1. goto CU
      -> UnconditionalJump[GOTO] #WJ -> #CU
      <- Switch[-1113169078] #WI -> #WJ
===#Block CU(size=2, flags=0)===
   0. _consume(catch());
   1. goto QQ
      -> UnconditionalJump[GOTO] #CU -> #QQ
      <- UnconditionalJump[GOTO] #WK -> #CU
      <- UnconditionalJump[GOTO] #WJ -> #CU
===#Block QQ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 248644819:
      	 goto	#QR
      case 511010265:
      	 goto	#JC
      case 919356278:
      	 goto	#CN
      case 1805994260:
      	 goto	#QQ
      default:
      	 goto	#JC
   }
      -> Switch[248644819] #QQ -> #QR
      -> Switch[511010265] #QQ -> #JC
      -> DefaultSwitch #QQ -> #JC
      -> Immediate #QQ -> #QR
      -> Switch[919356278] #QQ -> #CN
      -> Switch[1805994260] #QQ -> #QQ
      <- UnconditionalJump[GOTO] #CU -> #QQ
      <- Switch[1805994260] #QQ -> #QQ
===#Block QR(size=2, flags=100)===
   0. lvar105 = {879470602 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QR -> #CN
      <- Switch[248644819] #QQ -> #QR
      <- Immediate #QQ -> #QR
===#Block UV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1602564483);
   1. goto BR
      -> UnconditionalJump[GOTO] #UV -> #BR
      <- Switch[13855683] #BK -> #UV
===#Block BR(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar61 = lvar103;
   2. lvar95 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ywtlzczccfwtbsy(), lvar105);
   3. lvar62 = lvar61.equals(lvar95);
   4. if (lvar62 != {603881595 ^ lvar105})
      goto TP
   5. lvar105 = {1575684951 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BR -> #TP
      -> Immediate #BR -> #BS
      <- UnconditionalJump[GOTO] #UV -> #BR
===#Block BS(size=1, flags=0)===
   0. goto QW
      -> UnconditionalJump[GOTO] #BS -> #QW
      <- Immediate #BR -> #BS
===#Block QW(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 71943088:
      	 goto	#QX
      case 837938990:
      	 goto	#GR
      case 1182018795:
      	 goto	#QW
      case 1316268841:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1316268841] #QW -> #JC
      -> DefaultSwitch #QW -> #JC
      -> Switch[837938990] #QW -> #GR
      -> Immediate #QW -> #QX
      -> Switch[71943088] #QW -> #QX
      -> Switch[1182018795] #QW -> #QW
      <- UnconditionalJump[GOTO] #BS -> #QW
      <- Switch[1182018795] #QW -> #QW
===#Block QX(size=2, flags=100)===
   0. lvar105 = {966776300 ^ lvar105};
   1. goto GR
      -> UnconditionalJump[GOTO] #QX -> #GR
      <- Immediate #QW -> #QX
      <- Switch[71943088] #QW -> #QX
===#Block GR(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 178016286)
      goto GQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GR -> #GQ
      -> TryCatch range: [GR...GQ] -> ABO ([Ljava/lang/RuntimeException;])
      <- Switch[837938990] #QW -> #GR
      <- UnconditionalJump[GOTO] #QX -> #GR
===#Block GQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GR...GQ] -> ABO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GR -> #GQ
===#Block ABO(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 726184339:
      	 goto	#ABQ
      case 1012069890:
      	 goto	#ABP
      default:
      	 goto	#ABR
   }
      -> Switch[726184339] #ABO -> #ABQ
      -> DefaultSwitch #ABO -> #ABR
      -> Switch[1012069890] #ABO -> #ABP
      <- TryCatch range: [GR...GQ] -> ABO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GR...GQ] -> ABO ([Ljava/lang/RuntimeException;])
===#Block ABP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1711754961);
   1. goto GS
      -> UnconditionalJump[GOTO] #ABP -> #GS
      <- Switch[1012069890] #ABO -> #ABP
===#Block ABR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABO -> #ABR
===#Block ABQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1155794467);
   1. goto GS
      -> UnconditionalJump[GOTO] #ABQ -> #GS
      <- Switch[726184339] #ABO -> #ABQ
===#Block GS(size=2, flags=0)===
   0. _consume(catch());
   1. goto LR
      -> UnconditionalJump[GOTO] #GS -> #LR
      <- UnconditionalJump[GOTO] #ABQ -> #GS
      <- UnconditionalJump[GOTO] #ABP -> #GS
===#Block LR(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 16871085:
      	 goto	#LS
      case 33254982:
      	 goto	#JC
      case 420567627:
      	 goto	#CM
      case 1269718550:
      	 goto	#LR
      default:
      	 goto	#JC
   }
      -> Switch[1269718550] #LR -> #LR
      -> Switch[16871085] #LR -> #LS
      -> Immediate #LR -> #LS
      -> Switch[33254982] #LR -> #JC
      -> Switch[420567627] #LR -> #CM
      -> DefaultSwitch #LR -> #JC
      <- Switch[1269718550] #LR -> #LR
      <- UnconditionalJump[GOTO] #GS -> #LR
===#Block LS(size=2, flags=100)===
   0. lvar105 = {782505522 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #LS -> #CM
      <- Switch[16871085] #LR -> #LS
      <- Immediate #LR -> #LS
===#Block TP(size=2, flags=10100)===
   0. lvar105 = {2106911076 ^ lvar105};
   1. goto JM
      -> UnconditionalJump[GOTO] #TP -> #JM
      <- ConditionalJump[IF_ICMPNE] #BR -> #TP
===#Block JM(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -212645638)
      goto SD
   1. goto NC
      -> ConditionalJump[IF_ICMPEQ] #JM -> #SD
      -> UnconditionalJump[GOTO] #JM -> #NC
      <- UnconditionalJump[GOTO] #TP -> #JM
===#Block NC(size=2, flags=10100)===
   0. lvar105 = {1321163946 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NC -> #JC
      <- UnconditionalJump[GOTO] #JM -> #NC
===#Block SD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 2083590064);
   1. goto BT
      -> UnconditionalJump[GOTO] #SD -> #BT
      <- ConditionalJump[IF_ICMPEQ] #JM -> #SD
===#Block BT(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_CONCRETE_POWDER;
   2. goto RB
      -> UnconditionalJump[GOTO] #BT -> #RB
      <- UnconditionalJump[GOTO] #SD -> #BT
===#Block RB(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 43070632:
      	 goto	#RC
      case 283560675:
      	 goto	#RB
      case 1212440062:
      	 goto	#JC
      case 1676001108:
      	 goto	#HD
      default:
      	 goto	#JC
   }
      -> Immediate #RB -> #RC
      -> Switch[43070632] #RB -> #RC
      -> Switch[1676001108] #RB -> #HD
      -> Switch[283560675] #RB -> #RB
      -> Switch[1212440062] #RB -> #JC
      -> DefaultSwitch #RB -> #JC
      <- UnconditionalJump[GOTO] #BT -> #RB
      <- Switch[283560675] #RB -> #RB
===#Block RC(size=2, flags=100)===
   0. lvar105 = {1306407568 ^ lvar105};
   1. goto HD
      -> UnconditionalJump[GOTO] #RC -> #HD
      <- Immediate #RB -> #RC
      <- Switch[43070632] #RB -> #RC
===#Block HD(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 135412927)
      goto HC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HD -> #HC
      -> TryCatch range: [HD...HC] -> ACE ([Ljava/lang/IllegalAccessException;])
      <- Switch[1676001108] #RB -> #HD
      <- UnconditionalJump[GOTO] #RC -> #HD
===#Block HC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HD...HC] -> ACE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HD -> #HC
===#Block ACE(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 780182544:
      	 goto	#ACG
      case 2083168763:
      	 goto	#ACF
      default:
      	 goto	#ACH
   }
      -> DefaultSwitch #ACE -> #ACH
      -> Switch[780182544] #ACE -> #ACG
      -> Switch[2083168763] #ACE -> #ACF
      <- TryCatch range: [HD...HC] -> ACE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HD...HC] -> ACE ([Ljava/lang/IllegalAccessException;])
===#Block ACF(size=2, flags=10100)===
   0. lvar105 = {1558095163 ^ lvar105};
   1. goto HE
      -> UnconditionalJump[GOTO] #ACF -> #HE
      <- Switch[2083168763] #ACE -> #ACF
===#Block ACG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 915137926);
   1. goto HE
      -> UnconditionalJump[GOTO] #ACG -> #HE
      <- Switch[780182544] #ACE -> #ACG
===#Block HE(size=2, flags=0)===
   0. _consume(catch());
   1. goto PW
      -> UnconditionalJump[GOTO] #HE -> #PW
      <- UnconditionalJump[GOTO] #ACG -> #HE
      <- UnconditionalJump[GOTO] #ACF -> #HE
===#Block PW(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 58855852:
      	 goto	#PX
      case 176042565:
      	 goto	#CN
      case 372138468:
      	 goto	#PW
      case 1690446048:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1690446048] #PW -> #JC
      -> Switch[176042565] #PW -> #CN
      -> Immediate #PW -> #PX
      -> Switch[58855852] #PW -> #PX
      -> Switch[372138468] #PW -> #PW
      -> DefaultSwitch #PW -> #JC
      <- UnconditionalJump[GOTO] #HE -> #PW
      <- Switch[372138468] #PW -> #PW
===#Block PX(size=2, flags=100)===
   0. lvar105 = {1561026236 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PX -> #CN
      <- Immediate #PW -> #PX
      <- Switch[58855852] #PW -> #PX
===#Block ACH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ACE -> #ACH
===#Block VD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1934800859);
   1. goto CM
      -> UnconditionalJump[GOTO] #VD -> #CM
      <- DefaultSwitch #BK -> #VD
===#Block UZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 398886875);
   1. goto BU
      -> UnconditionalJump[GOTO] #UZ -> #BU
      <- Switch[13855691] #BK -> #UZ
===#Block BU(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar63 = lvar103;
   2. lvar96 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bpphycvdavmhjeo(), lvar105);
   3. lvar64 = lvar63.equals(lvar96);
   4. if (lvar64 != {1807562275 ^ lvar105})
      goto TM
   5. lvar105 = {1347801223 ^ lvar105};
      -> Immediate #BU -> #BV
      -> ConditionalJump[IF_ICMPNE] #BU -> #TM
      <- UnconditionalJump[GOTO] #UZ -> #BU
===#Block TM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 12514142:
      	 goto	#TN
      case 610511379:
      	 goto	#JC
      case 1545956683:
      	 goto	#JL
      case 1578112793:
      	 goto	#TM
      default:
      	 goto	#JC
   }
      -> Immediate #TM -> #TN
      -> DefaultSwitch #TM -> #JC
      -> Switch[1545956683] #TM -> #JL
      -> Switch[610511379] #TM -> #JC
      -> Switch[1578112793] #TM -> #TM
      -> Switch[12514142] #TM -> #TN
      <- ConditionalJump[IF_ICMPNE] #BU -> #TM
      <- Switch[1578112793] #TM -> #TM
===#Block TN(size=2, flags=100)===
   0. lvar105 = {444999782 ^ lvar105};
   1. goto JL
      -> UnconditionalJump[GOTO] #TN -> #JL
      <- Immediate #TM -> #TN
      <- Switch[12514142] #TM -> #TN
===#Block JL(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1982270933)
      goto SB
   1. goto MY
      -> UnconditionalJump[GOTO] #JL -> #MY
      -> ConditionalJump[IF_ICMPEQ] #JL -> #SB
      <- Switch[1545956683] #TM -> #JL
      <- UnconditionalJump[GOTO] #TN -> #JL
===#Block SB(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 191036318:
      	 goto	#SC
      case 847039866:
      	 goto	#JC
      case 1003807101:
      	 goto	#BW
      case 1488169888:
      	 goto	#SB
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #SB -> #JC
      -> Switch[191036318] #SB -> #SC
      -> Switch[847039866] #SB -> #JC
      -> Switch[1003807101] #SB -> #BW
      -> Switch[1488169888] #SB -> #SB
      -> Immediate #SB -> #SC
      <- ConditionalJump[IF_ICMPEQ] #JL -> #SB
      <- Switch[1488169888] #SB -> #SB
===#Block SC(size=2, flags=100)===
   0. lvar105 = {354937470 ^ lvar105};
   1. goto BW
      -> UnconditionalJump[GOTO] #SC -> #BW
      <- Switch[191036318] #SB -> #SC
      <- Immediate #SB -> #SC
===#Block BW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_CONCRETE_POWDER;
   2. goto KL
      -> UnconditionalJump[GOTO] #BW -> #KL
      <- Switch[1003807101] #SB -> #BW
      <- UnconditionalJump[GOTO] #SC -> #BW
===#Block KL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1096479776);
   1. goto DL
      -> UnconditionalJump[GOTO] #KL -> #DL
      <- UnconditionalJump[GOTO] #BW -> #KL
===#Block DL(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 137856370)
      goto DK
   1. throw nullconst;
      -> TryCatch range: [DL...DK] -> XG ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DL -> #DK
      <- UnconditionalJump[GOTO] #KL -> #DL
===#Block DK(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DL...DK] -> XG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DL -> #DK
===#Block XG(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 472657856:
      	 goto	#XI
      case 707789017:
      	 goto	#XH
      default:
      	 goto	#XJ
   }
      -> DefaultSwitch #XG -> #XJ
      -> Switch[707789017] #XG -> #XH
      -> Switch[472657856] #XG -> #XI
      <- TryCatch range: [DL...DK] -> XG ([Ljava/io/IOException;])
      <- TryCatch range: [DL...DK] -> XG ([Ljava/io/IOException;])
===#Block XI(size=2, flags=10100)===
   0. lvar105 = {258555822 ^ lvar105};
   1. goto DM
      -> UnconditionalJump[GOTO] #XI -> #DM
      <- Switch[472657856] #XG -> #XI
===#Block XH(size=2, flags=10100)===
   0. lvar105 = {699054669 ^ lvar105};
   1. goto DM
      -> UnconditionalJump[GOTO] #XH -> #DM
      <- Switch[707789017] #XG -> #XH
===#Block DM(size=2, flags=0)===
   0. _consume(catch());
   1. goto QE
      -> UnconditionalJump[GOTO] #DM -> #QE
      <- UnconditionalJump[GOTO] #XI -> #DM
      <- UnconditionalJump[GOTO] #XH -> #DM
===#Block QE(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 17823472:
      	 goto	#JC
      case 151692778:
      	 goto	#QF
      case 342572971:
      	 goto	#CN
      case 1731497044:
      	 goto	#QE
      default:
      	 goto	#JC
   }
      -> Immediate #QE -> #QF
      -> DefaultSwitch #QE -> #JC
      -> Switch[151692778] #QE -> #QF
      -> Switch[17823472] #QE -> #JC
      -> Switch[342572971] #QE -> #CN
      -> Switch[1731497044] #QE -> #QE
      <- UnconditionalJump[GOTO] #DM -> #QE
      <- Switch[1731497044] #QE -> #QE
===#Block QF(size=2, flags=100)===
   0. lvar105 = {1656493550 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QF -> #CN
      <- Immediate #QE -> #QF
      <- Switch[151692778] #QE -> #QF
===#Block XJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #XG -> #XJ
===#Block MY(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 191036318:
      	 goto	#MZ
      case 268618062:
      	 goto	#JC
      case 1479882270:
      	 goto	#MY
      case 1502349104:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #MY -> #MZ
      -> Switch[191036318] #MY -> #MZ
      -> Switch[1479882270] #MY -> #MY
      -> DefaultSwitch #MY -> #JC
      -> Switch[1502349104] #MY -> #JC
      <- UnconditionalJump[GOTO] #JL -> #MY
      <- Switch[1479882270] #MY -> #MY
===#Block MZ(size=2, flags=100)===
   0. lvar105 = {1642998768 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MZ -> #JC
      <- Immediate #MY -> #MZ
      <- Switch[191036318] #MY -> #MZ
===#Block BV(size=1, flags=0)===
   0. goto RD
      -> UnconditionalJump[GOTO] #BV -> #RD
      <- Immediate #BU -> #BV
===#Block RD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1362450844);
   1. goto HG
      -> UnconditionalJump[GOTO] #RD -> #HG
      <- UnconditionalJump[GOTO] #BV -> #RD
===#Block HG(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 252561569)
      goto HF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HG -> #HF
      -> TryCatch range: [HG...HF] -> ACI ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #RD -> #HG
===#Block HF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HG...HF] -> ACI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HG -> #HF
===#Block ACI(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case 1004781563:
      	 goto	#ACK
      case 1458436547:
      	 goto	#ACJ
      default:
      	 goto	#ACL
   }
      -> DefaultSwitch #ACI -> #ACL
      -> Switch[1004781563] #ACI -> #ACK
      -> Switch[1458436547] #ACI -> #ACJ
      <- TryCatch range: [HG...HF] -> ACI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HG...HF] -> ACI ([Ljava/lang/IllegalAccessException;])
===#Block ACJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 288470146);
   1. goto HH
      -> UnconditionalJump[GOTO] #ACJ -> #HH
      <- Switch[1458436547] #ACI -> #ACJ
===#Block ACK(size=2, flags=10100)===
   0. lvar105 = {479231045 ^ lvar105};
   1. goto HH
      -> UnconditionalJump[GOTO] #ACK -> #HH
      <- Switch[1004781563] #ACI -> #ACK
===#Block HH(size=2, flags=0)===
   0. _consume(catch());
   1. goto QO
      -> UnconditionalJump[GOTO] #HH -> #QO
      <- UnconditionalJump[GOTO] #ACK -> #HH
      <- UnconditionalJump[GOTO] #ACJ -> #HH
===#Block QO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1959071129);
   1. goto CM
      -> UnconditionalJump[GOTO] #QO -> #CM
      <- UnconditionalJump[GOTO] #HH -> #QO
===#Block ACL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACI -> #ACL
===#Block UU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1805488639);
   1. goto CD
      -> UnconditionalJump[GOTO] #UU -> #CD
      <- Switch[13855681] #BK -> #UU
===#Block CD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar69 = lvar103;
   2. lvar99 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.jocevainbklktid(), lvar105);
   3. lvar70 = lvar69.equals(lvar99);
   4. if (lvar70 != {400957447 ^ lvar105})
      goto SP
   5. lvar105 = {143580986 ^ lvar105};
      -> Immediate #CD -> #CE
      -> ConditionalJump[IF_ICMPNE] #CD -> #SP
      <- UnconditionalJump[GOTO] #UU -> #CD
===#Block SP(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 239978987:
      	 goto	#SQ
      case 671661505:
      	 goto	#JU
      case 1101415716:
      	 goto	#SP
      case 2129663995:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[2129663995] #SP -> #JC
      -> DefaultSwitch #SP -> #JC
      -> Switch[239978987] #SP -> #SQ
      -> Switch[671661505] #SP -> #JU
      -> Switch[1101415716] #SP -> #SP
      -> Immediate #SP -> #SQ
      <- Switch[1101415716] #SP -> #SP
      <- ConditionalJump[IF_ICMPNE] #CD -> #SP
===#Block SQ(size=2, flags=100)===
   0. lvar105 = {1630275063 ^ lvar105};
   1. goto JU
      -> UnconditionalJump[GOTO] #SQ -> #JU
      <- Switch[239978987] #SP -> #SQ
      <- Immediate #SP -> #SQ
===#Block JU(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1236193405)
      goto SA
   1. goto ME
      -> ConditionalJump[IF_ICMPEQ] #JU -> #SA
      -> UnconditionalJump[GOTO] #JU -> #ME
      <- Switch[671661505] #SP -> #JU
      <- UnconditionalJump[GOTO] #SQ -> #JU
===#Block ME(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1713311813);
   1. goto JC
      -> UnconditionalJump[GOTO] #ME -> #JC
      <- UnconditionalJump[GOTO] #JU -> #ME
===#Block SA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 879834323);
   1. goto CF
      -> UnconditionalJump[GOTO] #SA -> #CF
      <- ConditionalJump[IF_ICMPEQ] #JU -> #SA
===#Block CF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_CONCRETE_POWDER;
   2. goto LJ
      -> UnconditionalJump[GOTO] #CF -> #LJ
      <- UnconditionalJump[GOTO] #SA -> #CF
===#Block LJ(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 21640072:
      	 goto	#LK
      case 82595728:
      	 goto	#LJ
      case 163669964:
      	 goto	#JC
      case 881023207:
      	 goto	#FB
      default:
      	 goto	#JC
   }
      -> Switch[881023207] #LJ -> #FB
      -> Immediate #LJ -> #LK
      -> DefaultSwitch #LJ -> #JC
      -> Switch[163669964] #LJ -> #JC
      -> Switch[21640072] #LJ -> #LK
      -> Switch[82595728] #LJ -> #LJ
      <- UnconditionalJump[GOTO] #CF -> #LJ
      <- Switch[82595728] #LJ -> #LJ
===#Block LK(size=2, flags=100)===
   0. lvar105 = {2019955150 ^ lvar105};
   1. goto FB
      -> UnconditionalJump[GOTO] #LK -> #FB
      <- Immediate #LJ -> #LK
      <- Switch[21640072] #LJ -> #LK
===#Block FB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 34343702)
      goto FA
   1. throw nullconst;
      -> TryCatch range: [FB...FA] -> ZK ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #FB -> #FA
      <- Switch[881023207] #LJ -> #FB
      <- UnconditionalJump[GOTO] #LK -> #FB
===#Block FA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FB...FA] -> ZK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FB -> #FA
===#Block ZK(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -689436823:
      	 goto	#ZL
      case -191757968:
      	 goto	#ZM
      default:
      	 goto	#ZN
   }
      -> DefaultSwitch #ZK -> #ZN
      -> Switch[-191757968] #ZK -> #ZM
      -> Switch[-689436823] #ZK -> #ZL
      <- TryCatch range: [FB...FA] -> ZK ([Ljava/io/IOException;])
      <- TryCatch range: [FB...FA] -> ZK ([Ljava/io/IOException;])
===#Block ZL(size=2, flags=10100)===
   0. lvar105 = {1688044859 ^ lvar105};
   1. goto FC
      -> UnconditionalJump[GOTO] #ZL -> #FC
      <- Switch[-689436823] #ZK -> #ZL
===#Block ZM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1087555064);
   1. goto FC
      -> UnconditionalJump[GOTO] #ZM -> #FC
      <- Switch[-191757968] #ZK -> #ZM
===#Block FC(size=2, flags=0)===
   0. _consume(catch());
   1. goto RG
      -> UnconditionalJump[GOTO] #FC -> #RG
      <- UnconditionalJump[GOTO] #ZM -> #FC
      <- UnconditionalJump[GOTO] #ZL -> #FC
===#Block RG(size=2, flags=10100)===
   0. lvar105 = {806365806 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #RG -> #CN
      <- UnconditionalJump[GOTO] #FC -> #RG
===#Block ZN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZK -> #ZN
===#Block CE(size=1, flags=0)===
   0. goto PH
      -> UnconditionalJump[GOTO] #CE -> #PH
      <- Immediate #CD -> #CE
===#Block PH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 425298060);
   1. goto FQ
      -> UnconditionalJump[GOTO] #PH -> #FQ
      <- UnconditionalJump[GOTO] #CE -> #PH
===#Block FQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 97412612)
      goto FP
   1. throw nullconst;
      -> TryCatch range: [FQ...FP] -> AAE ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FQ -> #FP
      <- UnconditionalJump[GOTO] #PH -> #FQ
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FQ...FP] -> AAE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FQ -> #FP
===#Block AAE(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -1922790208:
      	 goto	#AAG
      case 831241608:
      	 goto	#AAF
      default:
      	 goto	#AAH
   }
      -> Switch[-1922790208] #AAE -> #AAG
      -> Switch[831241608] #AAE -> #AAF
      -> DefaultSwitch #AAE -> #AAH
      <- TryCatch range: [FQ...FP] -> AAE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FQ...FP] -> AAE ([Ljava/lang/RuntimeException;])
===#Block AAH(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAE -> #AAH
===#Block AAF(size=2, flags=10100)===
   0. lvar105 = {1241124321 ^ lvar105};
   1. goto FR
      -> UnconditionalJump[GOTO] #AAF -> #FR
      <- Switch[831241608] #AAE -> #AAF
===#Block AAG(size=2, flags=10100)===
   0. lvar105 = {1583868872 ^ lvar105};
   1. goto FR
      -> UnconditionalJump[GOTO] #AAG -> #FR
      <- Switch[-1922790208] #AAE -> #AAG
===#Block FR(size=2, flags=0)===
   0. _consume(catch());
   1. goto KJ
      -> UnconditionalJump[GOTO] #FR -> #KJ
      <- UnconditionalJump[GOTO] #AAF -> #FR
      <- UnconditionalJump[GOTO] #AAG -> #FR
===#Block KJ(size=2, flags=10100)===
   0. lvar105 = {1088584819 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #KJ -> #CM
      <- UnconditionalJump[GOTO] #FR -> #KJ
===#Block UY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1133060179);
   1. goto CJ
      -> UnconditionalJump[GOTO] #UY -> #CJ
      <- Switch[13855689] #BK -> #UY
===#Block CJ(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar73 = lvar103;
   2. lvar101 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.fvwfnwmwdldmmri(), lvar105);
   3. lvar74 = lvar73.equals(lvar101);
   4. if (lvar74 != {1072861611 ^ lvar105})
      goto SV
   5. lvar105 = {1389624169 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CJ -> #SV
      -> Immediate #CJ -> #CL
      <- UnconditionalJump[GOTO] #UY -> #CJ
===#Block CL(size=1, flags=0)===
   0. goto OB
      -> UnconditionalJump[GOTO] #CL -> #OB
      <- Immediate #CJ -> #CL
===#Block OB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1906547383);
   1. goto HM
      -> UnconditionalJump[GOTO] #OB -> #HM
      <- UnconditionalJump[GOTO] #CL -> #OB
===#Block HM(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 121482592)
      goto HL
   1. throw nullconst;
      -> TryCatch range: [HM...HL] -> ACQ ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HM -> #HL
      <- UnconditionalJump[GOTO] #OB -> #HM
===#Block HL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HM...HL] -> ACQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HM -> #HL
===#Block ACQ(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -468221016:
      	 goto	#ACR
      case -399646270:
      	 goto	#ACS
      default:
      	 goto	#ACT
   }
      -> Switch[-399646270] #ACQ -> #ACS
      -> Switch[-468221016] #ACQ -> #ACR
      -> DefaultSwitch #ACQ -> #ACT
      <- TryCatch range: [HM...HL] -> ACQ ([Ljava/io/IOException;])
      <- TryCatch range: [HM...HL] -> ACQ ([Ljava/io/IOException;])
===#Block ACT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACQ -> #ACT
===#Block ACR(size=2, flags=10100)===
   0. lvar105 = {296168976 ^ lvar105};
   1. goto HN
      -> UnconditionalJump[GOTO] #ACR -> #HN
      <- Switch[-468221016] #ACQ -> #ACR
===#Block ACS(size=2, flags=10100)===
   0. lvar105 = {1344299613 ^ lvar105};
   1. goto HN
      -> UnconditionalJump[GOTO] #ACS -> #HN
      <- Switch[-399646270] #ACQ -> #ACS
===#Block HN(size=2, flags=0)===
   0. _consume(catch());
   1. goto PD
      -> UnconditionalJump[GOTO] #HN -> #PD
      <- UnconditionalJump[GOTO] #ACR -> #HN
      <- UnconditionalJump[GOTO] #ACS -> #HN
===#Block PD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 34396230);
   1. goto CM
      -> UnconditionalJump[GOTO] #PD -> #CM
      <- UnconditionalJump[GOTO] #HN -> #PD
===#Block SV(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 199607753:
      	 goto	#SW
      case 568488013:
      	 goto	#JC
      case 1132141106:
      	 goto	#KB
      case 2071544366:
      	 goto	#SV
      default:
      	 goto	#JC
   }
      -> Switch[1132141106] #SV -> #KB
      -> Immediate #SV -> #SW
      -> Switch[2071544366] #SV -> #SV
      -> Switch[199607753] #SV -> #SW
      -> Switch[568488013] #SV -> #JC
      -> DefaultSwitch #SV -> #JC
      <- ConditionalJump[IF_ICMPNE] #CJ -> #SV
      <- Switch[2071544366] #SV -> #SV
===#Block SW(size=2, flags=100)===
   0. lvar105 = {73414725 ^ lvar105};
   1. goto KB
      -> UnconditionalJump[GOTO] #SW -> #KB
      <- Immediate #SV -> #SW
      <- Switch[199607753] #SV -> #SW
===#Block KB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -594194575)
      goto RW
   1. goto LW
      -> UnconditionalJump[GOTO] #KB -> #LW
      -> ConditionalJump[IF_ICMPEQ] #KB -> #RW
      <- UnconditionalJump[GOTO] #SW -> #KB
      <- Switch[1132141106] #SV -> #KB
===#Block RW(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 57414319:
      	 goto	#RX
      case 674354271:
      	 goto	#CK
      case 817184170:
      	 goto	#RW
      case 1106970681:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #RW -> #JC
      -> Switch[1106970681] #RW -> #JC
      -> Switch[674354271] #RW -> #CK
      -> Immediate #RW -> #RX
      -> Switch[817184170] #RW -> #RW
      -> Switch[57414319] #RW -> #RX
      <- Switch[817184170] #RW -> #RW
      <- ConditionalJump[IF_ICMPEQ] #KB -> #RW
===#Block RX(size=2, flags=100)===
   0. lvar105 = {1177863550 ^ lvar105};
   1. goto CK
      -> UnconditionalJump[GOTO] #RX -> #CK
      <- Immediate #RW -> #RX
      <- Switch[57414319] #RW -> #RX
===#Block CK(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLUE_CONCRETE_POWDER;
   2. goto PL
      -> UnconditionalJump[GOTO] #CK -> #PL
      <- Switch[674354271] #RW -> #CK
      <- UnconditionalJump[GOTO] #RX -> #CK
===#Block PL(size=2, flags=10100)===
   0. lvar105 = {382028498 ^ lvar105};
   1. goto GC
      -> UnconditionalJump[GOTO] #PL -> #GC
      <- UnconditionalJump[GOTO] #CK -> #PL
===#Block GC(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 1077628)
      goto GB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GC -> #GB
      -> TryCatch range: [GC...GB] -> AAU ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #PL -> #GC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GC...GB] -> AAU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GC -> #GB
===#Block AAU(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -661080264:
      	 goto	#AAW
      case 1528377875:
      	 goto	#AAV
      default:
      	 goto	#AAX
   }
      -> DefaultSwitch #AAU -> #AAX
      -> Switch[-661080264] #AAU -> #AAW
      -> Switch[1528377875] #AAU -> #AAV
      <- TryCatch range: [GC...GB] -> AAU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GC...GB] -> AAU ([Ljava/lang/IllegalAccessException;])
===#Block AAV(size=2, flags=10100)===
   0. lvar105 = {1191317837 ^ lvar105};
   1. goto GD
      -> UnconditionalJump[GOTO] #AAV -> #GD
      <- Switch[1528377875] #AAU -> #AAV
===#Block AAW(size=2, flags=10100)===
   0. lvar105 = {930226664 ^ lvar105};
   1. goto GD
      -> UnconditionalJump[GOTO] #AAW -> #GD
      <- Switch[-661080264] #AAU -> #AAW
===#Block GD(size=2, flags=0)===
   0. _consume(catch());
   1. goto OL
      -> UnconditionalJump[GOTO] #GD -> #OL
      <- UnconditionalJump[GOTO] #AAW -> #GD
      <- UnconditionalJump[GOTO] #AAV -> #GD
===#Block OL(size=2, flags=10100)===
   0. lvar105 = {1110546615 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OL -> #CN
      <- UnconditionalJump[GOTO] #GD -> #OL
===#Block AAX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AAU -> #AAX
===#Block LW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 726113371);
   1. goto JC
      -> UnconditionalJump[GOTO] #LW -> #JC
      <- UnconditionalJump[GOTO] #KB -> #LW
===#Block VC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 106749020);
   1. goto BO
      -> UnconditionalJump[GOTO] #VC -> #BO
      <- Switch[13855695] #BK -> #VC
===#Block BO(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar59 = lvar103;
   2. lvar94 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.meyungshfaklqzh(), lvar105);
   3. lvar60 = lvar59.equals(lvar94);
   4. if (lvar60 != {2049404324 ^ lvar105})
      goto UG
   5. lvar105 = {2044805314 ^ lvar105};
      -> Immediate #BO -> #BP
      -> ConditionalJump[IF_ICMPNE] #BO -> #UG
      <- UnconditionalJump[GOTO] #VC -> #BO
===#Block UG(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 212606538:
      	 goto	#UH
      case 1355919252:
      	 goto	#JC
      case 1575440465:
      	 goto	#JX
      case 2090631002:
      	 goto	#UG
      default:
      	 goto	#JC
   }
      -> Switch[212606538] #UG -> #UH
      -> DefaultSwitch #UG -> #JC
      -> Switch[2090631002] #UG -> #UG
      -> Switch[1575440465] #UG -> #JX
      -> Switch[1355919252] #UG -> #JC
      -> Immediate #UG -> #UH
      <- Switch[2090631002] #UG -> #UG
      <- ConditionalJump[IF_ICMPNE] #BO -> #UG
===#Block UH(size=2, flags=100)===
   0. lvar105 = {494362068 ^ lvar105};
   1. goto JX
      -> UnconditionalJump[GOTO] #UH -> #JX
      <- Switch[212606538] #UG -> #UH
      <- Immediate #UG -> #UH
===#Block JX(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 981566339)
      goto UL
   1. goto QJ
      -> UnconditionalJump[GOTO] #JX -> #QJ
      -> ConditionalJump[IF_ICMPEQ] #JX -> #UL
      <- UnconditionalJump[GOTO] #UH -> #JX
      <- Switch[1575440465] #UG -> #JX
===#Block UL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 585144595);
   1. goto BQ
      -> UnconditionalJump[GOTO] #UL -> #BQ
      <- ConditionalJump[IF_ICMPEQ] #JX -> #UL
===#Block BQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_CONCRETE_POWDER;
   2. goto LM
      -> UnconditionalJump[GOTO] #BQ -> #LM
      <- UnconditionalJump[GOTO] #UL -> #BQ
===#Block LM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 117837215:
      	 goto	#LN
      case 195608354:
      	 goto	#LM
      case 548071908:
      	 goto	#FT
      case 1206655150:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #LM -> #JC
      -> Switch[117837215] #LM -> #LN
      -> Switch[548071908] #LM -> #FT
      -> Immediate #LM -> #LN
      -> Switch[1206655150] #LM -> #JC
      -> Switch[195608354] #LM -> #LM
      <- UnconditionalJump[GOTO] #BQ -> #LM
      <- Switch[195608354] #LM -> #LM
===#Block LN(size=2, flags=100)===
   0. lvar105 = {462588834 ^ lvar105};
   1. goto FT
      -> UnconditionalJump[GOTO] #LN -> #FT
      <- Switch[117837215] #LM -> #LN
      <- Immediate #LM -> #LN
===#Block FT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 107117943)
      goto FS
   1. throw nullconst;
      -> TryCatch range: [FT...FS] -> AAI ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #FT -> #FS
      <- Switch[548071908] #LM -> #FT
      <- UnconditionalJump[GOTO] #LN -> #FT
===#Block FS(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FT...FS] -> AAI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FT -> #FS
===#Block AAI(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -611183949:
      	 goto	#AAK
      case -250538486:
      	 goto	#AAJ
      default:
      	 goto	#AAL
   }
      -> DefaultSwitch #AAI -> #AAL
      -> Switch[-611183949] #AAI -> #AAK
      -> Switch[-250538486] #AAI -> #AAJ
      <- TryCatch range: [FT...FS] -> AAI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FT...FS] -> AAI ([Ljava/lang/IllegalAccessException;])
===#Block AAJ(size=2, flags=10100)===
   0. lvar105 = {206027635 ^ lvar105};
   1. goto FU
      -> UnconditionalJump[GOTO] #AAJ -> #FU
      <- Switch[-250538486] #AAI -> #AAJ
===#Block AAK(size=2, flags=10100)===
   0. lvar105 = {689429476 ^ lvar105};
   1. goto FU
      -> UnconditionalJump[GOTO] #AAK -> #FU
      <- Switch[-611183949] #AAI -> #AAK
===#Block FU(size=2, flags=0)===
   0. _consume(catch());
   1. goto OY
      -> UnconditionalJump[GOTO] #FU -> #OY
      <- UnconditionalJump[GOTO] #AAJ -> #FU
      <- UnconditionalJump[GOTO] #AAK -> #FU
===#Block OY(size=2, flags=10100)===
   0. lvar105 = {1010120202 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OY -> #CN
      <- UnconditionalJump[GOTO] #FU -> #OY
===#Block AAL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAI -> #AAL
===#Block QJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 2005206469);
   1. goto JC
      -> UnconditionalJump[GOTO] #QJ -> #JC
      <- UnconditionalJump[GOTO] #JX -> #QJ
===#Block BP(size=1, flags=0)===
   0. goto PM
      -> UnconditionalJump[GOTO] #BP -> #PM
      <- Immediate #BO -> #BP
===#Block PM(size=1, flags=10100)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105)) {
      case 59361609:
      	 goto	#PN
      case 1164961385:
      	 goto	#JC
      case 1336037836:
      	 goto	#GL
      case 1542879703:
      	 goto	#PM
      default:
      	 goto	#JC
   }
      -> Switch[1164961385] #PM -> #JC
      -> DefaultSwitch #PM -> #JC
      -> Switch[1336037836] #PM -> #GL
      -> Switch[1542879703] #PM -> #PM
      -> Switch[59361609] #PM -> #PN
      -> Immediate #PM -> #PN
      <- UnconditionalJump[GOTO] #BP -> #PM
      <- Switch[1542879703] #PM -> #PM
===#Block PN(size=2, flags=100)===
   0. lvar105 = {1053055291 ^ lvar105};
   1. goto GL
      -> UnconditionalJump[GOTO] #PN -> #GL
      <- Switch[59361609] #PM -> #PN
      <- Immediate #PM -> #PN
===#Block GL(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 104135798)
      goto GK
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GL -> #GK
      -> TryCatch range: [GL...GK] -> ABG ([Ljava/lang/RuntimeException;])
      <- Switch[1336037836] #PM -> #GL
      <- UnconditionalJump[GOTO] #PN -> #GL
===#Block GK(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GL...GK] -> ABG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GL -> #GK
===#Block ABG(size=1, flags=0)===
   0. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.kqquujrhogvexljl(lvar105)) {
      case -401366295:
      	 goto	#ABH
      case 1276845778:
      	 goto	#ABI
      default:
      	 goto	#ABJ
   }
      -> Switch[1276845778] #ABG -> #ABI
      -> DefaultSwitch #ABG -> #ABJ
      -> Switch[-401366295] #ABG -> #ABH
      <- TryCatch range: [GL...GK] -> ABG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GL...GK] -> ABG ([Ljava/lang/RuntimeException;])
===#Block ABH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 1163218908);
   1. goto GM
      -> UnconditionalJump[GOTO] #ABH -> #GM
      <- Switch[-401366295] #ABG -> #ABH
===#Block ABJ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABG -> #ABJ
===#Block ABI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.oiuucwdnnoymongi(lvar105, 835999835);
   1. goto GM
      -> UnconditionalJump[GOTO] #ABI -> #GM
      <- Switch[1276845778] #ABG -> #ABI
===#Block GM(size=2, flags=0)===
   0. _consume(catch());
   1. goto MG
      -> UnconditionalJump[GOTO] #GM -> #MG
      <- UnconditionalJump[GOTO] #ABI -> #GM
      <- UnconditionalJump[GOTO] #ABH -> #GM
===#Block MG(size=2, flags=10100)===
   0. lvar105 = {2004762018 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #MG -> #CM
      <- UnconditionalJump[GOTO] #GM -> #MG
===#Block CM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_CONCRETE_POWDER;
   2. lvar105 = {1635363227 ^ lvar105};
      -> Immediate #CM -> #CN
      <- UnconditionalJump[GOTO] #QO -> #CM
      <- Switch[1114780597] #PZ -> #CM
      <- UnconditionalJump[GOTO] #NZ -> #CM
      <- UnconditionalJump[GOTO] #MG -> #CM
      <- Switch[420567627] #LR -> #CM
      <- UnconditionalJump[GOTO] #QA -> #CM
      <- UnconditionalJump[GOTO] #PD -> #CM
      <- UnconditionalJump[GOTO] #LS -> #CM
      <- UnconditionalJump[GOTO] #VD -> #CM
      <- UnconditionalJump[GOTO] #KJ -> #CM
      <- UnconditionalJump[GOTO] #MS -> #CM
      <- Switch[634990675] #NR -> #CM
      <- UnconditionalJump[GOTO] #NS -> #CM
===#Block CN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[1] [org/bukkit/Material]
   1. lvar8 = lvar75;
   2. lvar105 = {1992438237 ^ lvar105};
      -> Immediate #CN -> #CO
      <- UnconditionalJump[GOTO] #NA -> #CN
      <- Switch[176042565] #PW -> #CN
      <- UnconditionalJump[GOTO] #QD -> #CN
      <- UnconditionalJump[GOTO] #OS -> #CN
      <- UnconditionalJump[GOTO] #LV -> #CN
      <- UnconditionalJump[GOTO] #PS -> #CN
      <- Switch[342572971] #QE -> #CN
      <- UnconditionalJump[GOTO] #OW -> #CN
      <- UnconditionalJump[GOTO] #PR -> #CN
      <- UnconditionalJump[GOTO] #OV -> #CN
      <- UnconditionalJump[GOTO] #QP -> #CN
      <- UnconditionalJump[GOTO] #MH -> #CN
      <- UnconditionalJump[GOTO] #OL -> #CN
      <- Switch[508352250] #QU -> #CN
      <- UnconditionalJump[GOTO] #PX -> #CN
      <- UnconditionalJump[GOTO] #QR -> #CN
      <- UnconditionalJump[GOTO] #PY -> #CN
      <- Switch[1795033837] #LU -> #CN
      <- Switch[883703828] #MC -> #CN
      <- UnconditionalJump[GOTO] #NW -> #CN
      <- Switch[1273789595] #NV -> #CN
      <- UnconditionalJump[GOTO] #NT -> #CN
      <- UnconditionalJump[GOTO] #KN -> #CN
      <- UnconditionalJump[GOTO] #QL -> #CN
      <- Switch[1533946474] #OR -> #CN
      <- Switch[2023890301] #LX -> #CN
      <- UnconditionalJump[GOTO] #LY -> #CN
      <- UnconditionalJump[GOTO] #MD -> #CN
      <- Switch[1979505819] #KM -> #CN
      <- UnconditionalJump[GOTO] #NG -> #CN
      <- UnconditionalJump[GOTO] #RG -> #CN
      <- UnconditionalJump[GOTO] #QV -> #CN
      <- UnconditionalJump[GOTO] #MB -> #CN
      <- UnconditionalJump[GOTO] #NY -> #CN
      <- UnconditionalJump[GOTO] #QF -> #CN
      <- Immediate #CM -> #CN
      <- UnconditionalJump[GOTO] #OY -> #CN
      <- UnconditionalJump[GOTO] #RL -> #CN
      <- Switch[919356278] #QQ -> #CN
      <- UnconditionalJump[GOTO] #OQ -> #CN
===#Block CO(size=4, flags=0)===
   0. lvar15 = new org.bukkit.inventory.ItemStack;
   1. lvar7 = lvar8;
   2. _consume(lvar15.<init>(lvar7));
   3. return lvar15;
      <- Immediate #CN -> #CO
===#Block JC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      <- UnconditionalJump[GOTO] #LQ -> #JC
      <- DefaultSwitch #PZ -> #JC
      <- Switch[604246729] #PZ -> #JC
      <- DefaultSwitch #QE -> #JC
      <- Switch[17823472] #QE -> #JC
      <- Switch[806942124] #VA -> #JC
      <- DefaultSwitch #VA -> #JC
      <- DefaultSwitch #QG -> #JC
      <- Switch[180824216] #QG -> #JC
      <- DefaultSwitch #LM -> #JC
      <- Switch[2145538329] #RN -> #JC
      <- DefaultSwitch #RN -> #JC
      <- Switch[1206655150] #LM -> #JC
      <- DefaultSwitch #QU -> #JC
      <- Switch[1886042420] #QU -> #JC
      <- Switch[787661875] #US -> #JC
      <- DefaultSwitch #US -> #JC
      <- DefaultSwitch #RT -> #JC
      <- Switch[1451321211] #RT -> #JC
      <- Switch[803446414] #KU -> #JC
      <- DefaultSwitch #OZ -> #JC
      <- DefaultSwitch #KU -> #JC
      <- Switch[721475451] #OZ -> #JC
      <- Switch[1932450288] #VL -> #JC
      <- DefaultSwitch #VL -> #JC
      <- Switch[199511714] #TB -> #JC
      <- DefaultSwitch #TB -> #JC
      <- DefaultSwitch #TR -> #JC
      <- DefaultSwitch #NM -> #JC
      <- Switch[918567726] #TR -> #JC
      <- Switch[582415333] #NM -> #JC
      <- UnconditionalJump[GOTO] #PU -> #JC
      <- DefaultSwitch #NO -> #JC
      <- Switch[1189133215] #NO -> #JC
      <- Switch[1504006845] #NH -> #JC
      <- DefaultSwitch #NH -> #JC
      <- UnconditionalJump[GOTO] #RE -> #JC
      <- Switch[134185512] #TV -> #JC
      <- DefaultSwitch #TV -> #JC
      <- DefaultSwitch #LE -> #JC
      <- Switch[695272258] #LE -> #JC
      <- Switch[990213422] #SN -> #JC
      <- DefaultSwitch #LG -> #JC
      <- DefaultSwitch #SN -> #JC
      <- Switch[1663658691] #LG -> #JC
      <- Switch[1555573450] #SY -> #JC
      <- UnconditionalJump[GOTO] #LW -> #JC
      <- DefaultSwitch #SY -> #JC
      <- Switch[1696047864] #OF -> #JC
      <- Switch[33254982] #LR -> #JC
      <- DefaultSwitch #OF -> #JC
      <- DefaultSwitch #LR -> #JC
      <- Switch[1690446048] #PW -> #JC
      <- DefaultSwitch #KF -> #JC
      <- Switch[576622583] #KF -> #JC
      <- DefaultSwitch #PW -> #JC
      <- DefaultSwitch #VH -> #JC
      <- Switch[889327963] #VH -> #JC
      <- UnconditionalJump[GOTO] #LO -> #JC
      <- Switch[1149142018] #UP -> #JC
      <- Switch[1220846699] #VY -> #JC
      <- UnconditionalJump[GOTO] #NN -> #JC
      <- DefaultSwitch #VY -> #JC
      <- DefaultSwitch #UP -> #JC
      <- DefaultSwitch #VT -> #JC
      <- Switch[177827515] #VT -> #JC
      <- DefaultSwitch #UG -> #JC
      <- Switch[1355919252] #UG -> #JC
      <- Switch[2129663995] #SP -> #JC
      <- DefaultSwitch #SP -> #JC
      <- UnconditionalJump[GOTO] #NC -> #JC
      <- Switch[1596036641] #TJ -> #JC
      <- DefaultSwitch #TJ -> #JC
      <- DefaultSwitch #VR -> #JC
      <- Switch[973331369] #UW -> #JC
      <- Switch[340014067] #VR -> #JC
      <- DefaultSwitch #RJ -> #JC
      <- Switch[2040508821] #RJ -> #JC
      <- DefaultSwitch #UW -> #JC
      <- Switch[101327393] #KX -> #JC
      <- Switch[972909094] #OC -> #JC
      <- DefaultSwitch #OC -> #JC
      <- UnconditionalJump[GOTO] #MZ -> #JC
      <- Switch[1688390075] #NK -> #JC
      <- DefaultSwitch #NK -> #JC
      <- DefaultSwitch #KX -> #JC
      <- DefaultSwitch #LZ -> #JC
      <- Switch[994337953] #LZ -> #JC
      <- Switch[1766753954] #WB -> #JC
      <- DefaultSwitch #WB -> #JC
      <- UnconditionalJump[GOTO] #KP -> #JC
      <- UnconditionalJump[GOTO] #KT -> #JC
      <- Switch[1316268841] #QW -> #JC
      <- DefaultSwitch #QW -> #JC
      <- Switch[1683976205] #RR -> #JC
      <- Switch[444310366] #LX -> #JC
      <- DefaultSwitch #LX -> #JC
      <- DefaultSwitch #RR -> #JC
      <- Switch[1827974571] #TY -> #JC
      <- DefaultSwitch #TY -> #JC
      <- UnconditionalJump[GOTO] #PG -> #JC
      <- UnconditionalJump[GOTO] #PJ -> #JC
      <- Switch[1524292605] #UM -> #JC
      <- DefaultSwitch #PE -> #JC
      <- Switch[1031925414] #PE -> #JC
      <- UnconditionalJump[GOTO] #QS -> #JC
      <- DefaultSwitch #UM -> #JC
      <- Switch[381205364] #KO -> #JC
      <- Switch[1996071807] #OI -> #JC
      <- DefaultSwitch #KO -> #JC
      <- DefaultSwitch #OI -> #JC
      <- Switch[1063436749] #KS -> #JC
      <- DefaultSwitch #KS -> #JC
      <- UnconditionalJump[GOTO] #QK -> #JC
      <- DefaultSwitch #PB -> #JC
      <- DefaultSwitch #MO -> #JC
      <- Switch[93907207] #PB -> #JC
      <- Switch[404673943] #MO -> #JC
      <- UnconditionalJump[GOTO] #KI -> #JC
      <- Switch[308132600] #VJ -> #JC
      <- DefaultSwitch #VJ -> #JC
      <- Switch[511010265] #QQ -> #JC
      <- DefaultSwitch #QQ -> #JC
      <- UnconditionalJump[GOTO] #LA -> #JC
      <- Switch[442588872] #LU -> #JC
      <- DefaultSwitch #NV -> #JC
      <- DefaultSwitch #LJ -> #JC
      <- DefaultSwitch #LU -> #JC
      <- Switch[1944002288] #NV -> #JC
      <- DefaultSwitch #UE -> #JC
      <- Switch[163669964] #LJ -> #JC
      <- Switch[908498335] #UE -> #JC
      <- Switch[568488013] #SV -> #JC
      <- DefaultSwitch #SV -> #JC
      <- DefaultSwitch #TM -> #JC
      <- Switch[610511379] #TM -> #JC
      <- Switch[132452546] #KZ -> #JC
      <- DefaultSwitch #RY -> #JC
      <- DefaultSwitch #KZ -> #JC
      <- Switch[1079882277] #RY -> #JC
      <- UnconditionalJump[GOTO] #OX -> #JC
      <- UnconditionalJump[GOTO] #LH -> #JC
      <- DefaultSwitch #UB -> #JC
      <- Switch[633506292] #UB -> #JC
      <- DefaultSwitch #RW -> #JC
      <- UnconditionalJump[GOTO] #QC -> #JC
      <- Switch[1106970681] #RW -> #JC
      <- Switch[48419573] #PO -> #JC
      <- DefaultSwitch #PO -> #JC
      <- Switch[32721374] #QM -> #JC
      <- DefaultSwitch #QM -> #JC
      <- Switch[1212440062] #RB -> #JC
      <- DefaultSwitch #SL -> #JC
      <- UnconditionalJump[GOTO] #OG -> #JC
      <- DefaultSwitch #RB -> #JC
      <- Switch[724880127] #SL -> #JC
      <- Switch[1164961385] #PM -> #JC
      <- DefaultSwitch #PM -> #JC
      <- Switch[1183495208] #UJ -> #JC
      <- UnconditionalJump[GOTO] #NP -> #JC
      <- DefaultSwitch #UJ -> #JC
      <- UnconditionalJump[GOTO] #OH -> #JC
      <- DefaultSwitch #SB -> #JC
      <- Switch[403655039] #MI -> #JC
      <- Switch[847039866] #SB -> #JC
      <- DefaultSwitch #MI -> #JC
      <- UnconditionalJump[GOTO] #LD -> #JC
      <- Switch[1645385975] #PI -> #JC
      <- DefaultSwitch #PI -> #JC
      <- UnconditionalJump[GOTO] #OJ -> #JC
      <- DefaultSwitch #MC -> #JC
      <- Switch[1025857328] #MC -> #JC
      <- UnconditionalJump[GOTO] #OD -> #JC
      <- UnconditionalJump[GOTO] #QJ -> #JC
      <- UnconditionalJump[GOTO] #ME -> #JC
      <- UnconditionalJump[GOTO] #OM -> #JC
      <- DefaultSwitch #QY -> #JC
      <- Switch[1266689126] #QY -> #JC
      <- DefaultSwitch #MM -> #JC
      <- Switch[766176798] #NR -> #JC
      <- Switch[1885540580] #MM -> #JC
      <- Switch[1310867826] #PT -> #JC
      <- DefaultSwitch #NR -> #JC
      <- Switch[729687055] #LC -> #JC
      <- DefaultSwitch #PT -> #JC
      <- DefaultSwitch #LC -> #JC
      <- Switch[1223335156] #MV -> #JC
      <- DefaultSwitch #MV -> #JC
      <- Switch[192111288] #OR -> #JC
      <- DefaultSwitch #KM -> #JC
      <- DefaultSwitch #OR -> #JC
      <- Switch[2122880631] #KM -> #JC
      <- UnconditionalJump[GOTO] #ML -> #JC
      <- DefaultSwitch #MY -> #JC
      <- Switch[1502349104] #MY -> #JC
