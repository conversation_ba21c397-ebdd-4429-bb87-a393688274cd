# AceDx 测试配置文件示例
# 这是一个示例配置文件，展示了新增的放生保留图鉴功能配置

# 基础设置
rewards:
  enabled: true
  default-cooldown: 3600000
  generation:
    enabled: true
  completion:
    enabled: true

gui:
  enabled: true

debug:
  enabled: false

# 精灵相关设置
pokemon:
  # 新增功能：放生时是否保留图鉴记录
  # true: 放生精灵时保留图鉴记录
  # false: 放生精灵时如果没有其他相同精灵则移除图鉴记录（默认行为）
  keep-dex-on-release: false

# 许可证配置
license-key: ""

# 测试场景说明：
# 
# 场景1：keep-dex-on-release: false（默认）
# - 玩家捕获了皮卡丘
# - 玩家放生了皮卡丘
# - 如果玩家没有其他皮卡丘，图鉴中皮卡丘记录被移除
# - 如果玩家还有其他皮卡丘，图鉴中皮卡丘记录保留
#
# 场景2：keep-dex-on-release: true
# - 玩家捕获了皮卡丘
# - 玩家放生了皮卡丘
# - 无论玩家是否还有其他皮卡丘，图鉴中皮卡丘记录都保留

# 管理员可以使用以下命令动态修改此配置：
# /acedx config keep-dex-on-release true   # 启用保留图鉴记录
# /acedx config keep-dex-on-release false  # 禁用保留图鉴记录
# /acedx config keep-dex-on-release status # 查看当前状态
