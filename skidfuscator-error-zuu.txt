handler=Block #CR, types=[Ljava/lang/RuntimeException;], range=[Block #CQ, Block #CP]
handler=Block #CU, types=[Ljava/io/IOException;], range=[Block #CT, Block #CS]
handler=Block #CX, types=[Ljava/lang/RuntimeException;], range=[Block #CW, Block #CV]
handler=Block #DA, types=[Ljava/lang/IllegalAccessException;], range=[Block #CZ, Block #CY]
handler=Block #DD, types=[Ljava/lang/RuntimeException;], range=[Block #DC, Block #DB]
handler=Block #DG, types=[Ljava/io/IOException;], range=[Block #DF, Block #DE]
handler=Block #DJ, types=[Ljava/io/IOException;], range=[Block #DI, Block #DH]
handler=Block #DM, types=[Ljava/lang/RuntimeException;], range=[Block #DL, Block #DK]
handler=Block #DP, types=[Ljava/lang/IllegalAccessException;], range=[Block #DO, Block #DN]
handler=Block #DS, types=[Ljava/lang/IllegalAccessException;], range=[Block #DR, Block #DQ]
handler=Block #DV, types=[Ljava/lang/IllegalAccessException;], range=[Block #DU, Block #DT]
handler=Block #DY, types=[Ljava/lang/RuntimeException;], range=[Block #DX, Block #DW]
handler=Block #EB, types=[Ljava/lang/RuntimeException;], range=[Block #EA, Block #DZ]
handler=Block #EE, types=[Ljava/io/IOException;], range=[Block #ED, Block #EC]
handler=Block #EH, types=[Ljava/lang/RuntimeException;], range=[Block #EG, Block #EF]
handler=Block #EK, types=[Ljava/lang/RuntimeException;], range=[Block #EJ, Block #EI]
handler=Block #EN, types=[Ljava/lang/RuntimeException;], range=[Block #EM, Block #EL]
handler=Block #EQ, types=[Ljava/io/IOException;], range=[Block #EP, Block #EO]
handler=Block #ET, types=[Ljava/lang/IllegalAccessException;], range=[Block #ES, Block #ER]
handler=Block #EW, types=[Ljava/io/IOException;], range=[Block #EV, Block #EU]
handler=Block #EZ, types=[Ljava/lang/RuntimeException;], range=[Block #EY, Block #EX]
handler=Block #FC, types=[Ljava/lang/RuntimeException;], range=[Block #FB, Block #FA]
handler=Block #FF, types=[Ljava/lang/RuntimeException;], range=[Block #FE, Block #FD]
handler=Block #FI, types=[Ljava/lang/IllegalAccessException;], range=[Block #FH, Block #FG]
handler=Block #FL, types=[Ljava/io/IOException;], range=[Block #FK, Block #FJ]
handler=Block #FO, types=[Ljava/lang/IllegalAccessException;], range=[Block #FN, Block #FM]
handler=Block #FR, types=[Ljava/lang/RuntimeException;], range=[Block #FQ, Block #FP]
handler=Block #FU, types=[Ljava/lang/RuntimeException;], range=[Block #FT, Block #FS]
handler=Block #FX, types=[Ljava/lang/IllegalAccessException;], range=[Block #FW, Block #FV]
handler=Block #GA, types=[Ljava/lang/IllegalAccessException;], range=[Block #FZ, Block #FY]
handler=Block #GD, types=[Ljava/lang/RuntimeException;], range=[Block #GC, Block #GB]
handler=Block #GG, types=[Ljava/lang/RuntimeException;], range=[Block #GF, Block #GE]
handler=Block #GJ, types=[Ljava/lang/RuntimeException;], range=[Block #GI, Block #GH]
handler=Block #GM, types=[Ljava/lang/IllegalAccessException;], range=[Block #GL, Block #GK]
handler=Block #GP, types=[Ljava/lang/IllegalAccessException;], range=[Block #GO, Block #GN]
handler=Block #GS, types=[Ljava/lang/RuntimeException;], range=[Block #GR, Block #GQ]
handler=Block #GV, types=[Ljava/io/IOException;], range=[Block #GU, Block #GT]
handler=Block #GY, types=[Ljava/lang/RuntimeException;], range=[Block #GX, Block #GW]
handler=Block #HB, types=[Ljava/lang/RuntimeException;], range=[Block #HA, Block #GZ]
handler=Block #HE, types=[Ljava/lang/RuntimeException;], range=[Block #HD, Block #HC]
handler=Block #HH, types=[Ljava/lang/IllegalAccessException;], range=[Block #HG, Block #HF]
handler=Block #HK, types=[Ljava/io/IOException;], range=[Block #HJ, Block #HI]
handler=Block #HN, types=[Ljava/io/IOException;], range=[Block #HM, Block #HL]
handler=Block #HQ, types=[Ljava/io/IOException;], range=[Block #HP, Block #HO]
handler=Block #HT, types=[Ljava/io/IOException;], range=[Block #HS, Block #HR]
handler=Block #HW, types=[Ljava/lang/RuntimeException;], range=[Block #HV, Block #HU]
handler=Block #HZ, types=[Ljava/lang/RuntimeException;], range=[Block #HY, Block #HX]
handler=Block #IC, types=[Ljava/lang/RuntimeException;], range=[Block #IB, Block #IA]
handler=Block #IF, types=[Ljava/io/IOException;], range=[Block #IE, Block #ID]
handler=Block #II, types=[Ljava/lang/IllegalAccessException;], range=[Block #IH, Block #IG]
handler=Block #IL, types=[Ljava/lang/IllegalAccessException;], range=[Block #IK, Block #IJ]
handler=Block #IO, types=[Ljava/lang/IllegalAccessException;], range=[Block #IN, Block #IM]
handler=Block #IR, types=[Ljava/lang/IllegalAccessException;], range=[Block #IQ, Block #IP]
handler=Block #IU, types=[Ljava/lang/IllegalAccessException;], range=[Block #IT, Block #IS]
handler=Block #IX, types=[Ljava/lang/RuntimeException;], range=[Block #IW, Block #IV]
handler=Block #JA, types=[Ljava/io/IOException;], range=[Block #IZ, Block #IY]
===#Block A(size=4, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
   2. synth(lvar2 = lvar2);
   3. synth(lvar3 = lvar3);
      -> Immediate #A -> #B
===#Block B(size=0, flags=0)===
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=2, flags=0)===
   0. lvar4 = lvar3;
   1. if (lvar4 == {38131573 ^ lvar105})
      goto JL
      -> ConditionalJump[IF_ICMPEQ] #C -> #JL
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=6, flags=0)===
   0. lvar9 = lvar1;
   1. lvar8 = lvar9;
   2. lvar10 = lvar8;
   3. lvar11 = lvar10.hashCode();
   4. svar107 = {lvar11 ^ lvar105};
   5. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(svar107)) {
      case 223448008:
      	 goto	#W
      case 223448010:
      	 goto	#H
      case 223448014:
      	 goto	#N
      case 223448016:
      	 goto	#Z
      case 223448018:
      	 goto	#AC
      case 223448020:
      	 goto	#E
      case 223448022:
      	 goto	#Q
      case 223448024:
      	 goto	#T
      case 223448028:
      	 goto	#K
      default:
      	 goto	#AF
   }
      -> DefaultSwitch #D -> #AF
      -> Switch[223448018] #D -> #AC
      -> Switch[223448016] #D -> #Z
      -> Switch[223448008] #D -> #W
      -> Switch[223448024] #D -> #T
      -> Switch[223448022] #D -> #Q
      -> Switch[223448014] #D -> #N
      -> Switch[223448028] #D -> #K
      -> Switch[223448010] #D -> #H
      -> Switch[223448020] #D -> #E
      <- Immediate #C -> #D
===#Block E(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar8;
   2. lvar5 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.vypunnokmfnwmtl(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar13 = lvar12.equals(lvar5);
   4. if (lvar13 != {617682160 ^ lvar105})
      goto JW
      -> Immediate #E -> #F
      -> ConditionalJump[IF_ICMPNE] #E -> #JW
      <- Switch[223448020] #D -> #E
===#Block JW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 2093248914)
      goto G
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JW -> #G
      -> UnconditionalJump[GOTO] #JW -> #JC
      <- ConditionalJump[IF_ICMPNE] #E -> #JW
===#Block G(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.QUARTZ_BLOCK;
   2. goto DX
      -> UnconditionalJump[GOTO] #G -> #DX
      <- ConditionalJump[IF_ICMPEQ] #JW -> #G
===#Block DX(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 104713635)
      goto DW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DX -> #DW
      -> TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #G -> #DX
===#Block DW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DX -> #DW
===#Block DY(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DY -> #CN
      <- TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
===#Block F(size=1, flags=0)===
   0. goto IK
      -> UnconditionalJump[GOTO] #F -> #IK
      <- Immediate #E -> #F
===#Block IK(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 1439571)
      goto IJ
   1. throw nullconst;
      -> TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IK -> #IJ
      <- UnconditionalJump[GOTO] #F -> #IK
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IK -> #IJ
===#Block IL(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #IL -> #AF
      <- TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
===#Block H(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar15 = lvar8;
   2. lvar75 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.vgfgkcpbrnoumdz(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar16 = lvar15.equals(lvar75);
   4. if (lvar16 != {286990878 ^ lvar105})
      goto JO
      -> ConditionalJump[IF_ICMPNE] #H -> #JO
      -> Immediate #H -> #I
      <- Switch[223448010] #D -> #H
===#Block I(size=1, flags=0)===
   0. goto HM
      -> UnconditionalJump[GOTO] #I -> #HM
      <- Immediate #H -> #I
===#Block HM(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 111733677)
      goto HL
   1. throw nullconst;
      -> TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HM -> #HL
      <- UnconditionalJump[GOTO] #I -> #HM
===#Block HL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HM -> #HL
===#Block HN(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #HN -> #AF
      <- TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
===#Block JO(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1894415830)
      goto J
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JO -> #J
      -> UnconditionalJump[GOTO] #JO -> #JC
      <- ConditionalJump[IF_ICMPNE] #H -> #JO
===#Block J(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.EMERALD_BLOCK;
   2. goto EY
      -> UnconditionalJump[GOTO] #J -> #EY
      <- ConditionalJump[IF_ICMPEQ] #JO -> #J
===#Block EY(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 49123436)
      goto EX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EY -> #EX
      -> TryCatch range: [EY...EX] -> EZ ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #J -> #EY
===#Block EX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EY...EX] -> EZ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EY -> #EX
===#Block EZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EZ -> #CN
      <- TryCatch range: [EY...EX] -> EZ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EY...EX] -> EZ ([Ljava/lang/RuntimeException;])
===#Block K(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar8;
   2. lvar76 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.veuknfauotwmwkm(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar18 = lvar17.equals(lvar76);
   4. if (lvar18 != {1914733919 ^ lvar105})
      goto JB
      -> Immediate #K -> #M
      -> ConditionalJump[IF_ICMPNE] #K -> #JB
      <- Switch[223448028] #D -> #K
===#Block JB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -428326567)
      goto L
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JB -> #L
      -> UnconditionalJump[GOTO] #JB -> #JC
      <- ConditionalJump[IF_ICMPNE] #K -> #JB
===#Block L(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GOLD_BLOCK;
   2. goto CQ
      -> UnconditionalJump[GOTO] #L -> #CQ
      <- ConditionalJump[IF_ICMPEQ] #JB -> #L
===#Block CQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 44081304)
      goto CP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CQ -> #CP
      -> TryCatch range: [CQ...CP] -> CR ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #L -> #CQ
===#Block CP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CQ...CP] -> CR ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CQ -> #CP
===#Block CR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #CR -> #CN
      <- TryCatch range: [CQ...CP] -> CR ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CQ...CP] -> CR ([Ljava/lang/RuntimeException;])
===#Block M(size=1, flags=0)===
   0. goto GC
      -> UnconditionalJump[GOTO] #M -> #GC
      <- Immediate #K -> #M
===#Block GC(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 109289864)
      goto GB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GC -> #GB
      -> TryCatch range: [GC...GB] -> GD ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #M -> #GC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GC...GB] -> GD ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GC -> #GB
===#Block GD(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #GD -> #AF
      <- TryCatch range: [GC...GB] -> GD ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GC...GB] -> GD ([Ljava/lang/RuntimeException;])
===#Block N(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar8;
   2. lvar77 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.mkirvyoppkeahey(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar20 = lvar19.equals(lvar77);
   4. if (lvar20 != {2061263092 ^ lvar105})
      goto KC
      -> ConditionalJump[IF_ICMPNE] #N -> #KC
      -> Immediate #N -> #O
      <- Switch[223448014] #D -> #N
===#Block O(size=1, flags=0)===
   0. goto FN
      -> UnconditionalJump[GOTO] #O -> #FN
      <- Immediate #N -> #O
===#Block FN(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 164658548)
      goto FM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FN -> #FM
      -> TryCatch range: [FN...FM] -> FO ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #O -> #FN
===#Block FM(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FN...FM] -> FO ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FN -> #FM
===#Block FO(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #FO -> #AF
      <- TryCatch range: [FN...FM] -> FO ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FN...FM] -> FO ([Ljava/lang/IllegalAccessException;])
===#Block KC(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 1171650880)
      goto P
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KC -> #P
      -> UnconditionalJump[GOTO] #KC -> #JC
      <- ConditionalJump[IF_ICMPNE] #N -> #KC
===#Block P(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.PURPUR_BLOCK;
   2. goto HJ
      -> UnconditionalJump[GOTO] #P -> #HJ
      <- ConditionalJump[IF_ICMPEQ] #KC -> #P
===#Block HJ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 6239018)
      goto HI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HJ -> #HI
      -> TryCatch range: [HJ...HI] -> HK ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #P -> #HJ
===#Block HI(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HJ...HI] -> HK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HJ -> #HI
===#Block HK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HK -> #CN
      <- TryCatch range: [HJ...HI] -> HK ([Ljava/io/IOException;])
      <- TryCatch range: [HJ...HI] -> HK ([Ljava/io/IOException;])
===#Block Q(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar8;
   2. lvar78 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.dfkhxadsbxfbjyn(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar22 = lvar21.equals(lvar78);
   4. if (lvar22 != {615369482 ^ lvar105})
      goto JS
      -> Immediate #Q -> #R
      -> ConditionalJump[IF_ICMPNE] #Q -> #JS
      <- Switch[223448022] #D -> #Q
===#Block JS(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -990793208)
      goto S
   1. goto JC
      -> UnconditionalJump[GOTO] #JS -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JS -> #S
      <- ConditionalJump[IF_ICMPNE] #Q -> #JS
===#Block S(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.DIAMOND_BLOCK;
   2. goto DC
      -> UnconditionalJump[GOTO] #S -> #DC
      <- ConditionalJump[IF_ICMPEQ] #JS -> #S
===#Block DC(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 201664153)
      goto DB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DC -> #DB
      -> TryCatch range: [DC...DB] -> DD ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #S -> #DC
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DC...DB] -> DD ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DC -> #DB
===#Block DD(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DD -> #CN
      <- TryCatch range: [DC...DB] -> DD ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DC...DB] -> DD ([Ljava/lang/RuntimeException;])
===#Block R(size=1, flags=0)===
   0. goto IN
      -> UnconditionalJump[GOTO] #R -> #IN
      <- Immediate #Q -> #R
===#Block IN(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 198786068)
      goto IM
   1. throw nullconst;
      -> TryCatch range: [IN...IM] -> IO ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IN -> #IM
      <- UnconditionalJump[GOTO] #R -> #IN
===#Block IM(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IN...IM] -> IO ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IN -> #IM
===#Block IO(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #IO -> #AF
      <- TryCatch range: [IN...IM] -> IO ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IN...IM] -> IO ([Ljava/lang/IllegalAccessException;])
===#Block T(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar23 = lvar8;
   2. lvar79 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.wwyjlldqasrhvsc(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar24 = lvar23.equals(lvar79);
   4. if (lvar24 != {286922998 ^ lvar105})
      goto KB
      -> ConditionalJump[IF_ICMPNE] #T -> #KB
      -> Immediate #T -> #U
      <- Switch[223448024] #D -> #T
===#Block U(size=1, flags=0)===
   0. goto FQ
      -> UnconditionalJump[GOTO] #U -> #FQ
      <- Immediate #T -> #U
===#Block FQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 19789153)
      goto FP
   1. throw nullconst;
      -> TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FQ -> #FP
      <- UnconditionalJump[GOTO] #U -> #FQ
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FQ -> #FP
===#Block FR(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #FR -> #AF
      <- TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
===#Block KB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -94417184)
      goto V
   1. goto JC
      -> UnconditionalJump[GOTO] #KB -> #JC
      -> ConditionalJump[IF_ICMPEQ] #KB -> #V
      <- ConditionalJump[IF_ICMPNE] #T -> #KB
===#Block V(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.REDSTONE_BLOCK;
   2. goto EA
      -> UnconditionalJump[GOTO] #V -> #EA
      <- ConditionalJump[IF_ICMPEQ] #KB -> #V
===#Block EA(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 101500358)
      goto DZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EA -> #DZ
      -> TryCatch range: [EA...DZ] -> EB ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #V -> #EA
===#Block DZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EA...DZ] -> EB ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EA -> #DZ
===#Block EB(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EB -> #CN
      <- TryCatch range: [EA...DZ] -> EB ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EA...DZ] -> EB ([Ljava/lang/RuntimeException;])
===#Block W(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar8;
   2. lvar80 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.kjeeaxnqlghsenk(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar26 = lvar25.equals(lvar80);
   4. if (lvar26 != {995459986 ^ lvar105})
      goto JX
      -> Immediate #W -> #X
      -> ConditionalJump[IF_ICMPNE] #W -> #JX
      <- Switch[223448008] #D -> #W
===#Block JX(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -689633365)
      goto Y
   1. goto JC
      -> UnconditionalJump[GOTO] #JX -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JX -> #Y
      <- ConditionalJump[IF_ICMPNE] #W -> #JX
===#Block Y(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.PRISMARINE;
   2. goto EJ
      -> UnconditionalJump[GOTO] #Y -> #EJ
      <- ConditionalJump[IF_ICMPEQ] #JX -> #Y
===#Block EJ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 219123513)
      goto EI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EJ -> #EI
      -> TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #Y -> #EJ
===#Block EI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EJ -> #EI
===#Block EK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EK -> #CN
      <- TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
===#Block X(size=1, flags=0)===
   0. goto GF
      -> UnconditionalJump[GOTO] #X -> #GF
      <- Immediate #W -> #X
===#Block GF(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 170744055)
      goto GE
   1. throw nullconst;
      -> TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GF -> #GE
      <- UnconditionalJump[GOTO] #X -> #GF
===#Block GE(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GF -> #GE
===#Block GG(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #GG -> #AF
      <- TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
===#Block Z(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = lvar8;
   2. lvar81 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.aeuaybikyrwpwer(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar28 = lvar27.equals(lvar81);
   4. if (lvar28 != {777271355 ^ lvar105})
      goto JY
      -> ConditionalJump[IF_ICMPNE] #Z -> #JY
      -> Immediate #Z -> #AA
      <- Switch[223448016] #D -> #Z
===#Block AA(size=1, flags=0)===
   0. goto DU
      -> UnconditionalJump[GOTO] #AA -> #DU
      <- Immediate #Z -> #AA
===#Block DU(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 205863636)
      goto DT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DU -> #DT
      -> TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AA -> #DU
===#Block DT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DU -> #DT
===#Block DV(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #DV -> #AF
      <- TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
===#Block JY(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1833724581)
      goto AB
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JY -> #AB
      -> UnconditionalJump[GOTO] #JY -> #JC
      <- ConditionalJump[IF_ICMPNE] #Z -> #JY
===#Block AB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.OBSIDIAN;
   2. goto FE
      -> UnconditionalJump[GOTO] #AB -> #FE
      <- ConditionalJump[IF_ICMPEQ] #JY -> #AB
===#Block FE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 186962806)
      goto FD
   1. throw nullconst;
      -> TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FE -> #FD
      <- UnconditionalJump[GOTO] #AB -> #FE
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FE -> #FD
===#Block FF(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FF -> #CN
      <- TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
===#Block AC(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar8;
   2. lvar82 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.xcbigesvzirrjzu(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar30 = lvar29.equals(lvar82);
   4. if (lvar30 != {1032521015 ^ lvar105})
      goto KD
      -> Immediate #AC -> #AE
      -> ConditionalJump[IF_ICMPNE] #AC -> #KD
      <- Switch[223448018] #D -> #AC
===#Block KD(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 1010944104)
      goto AD
   1. goto JC
      -> UnconditionalJump[GOTO] #KD -> #JC
      -> ConditionalJump[IF_ICMPEQ] #KD -> #AD
      <- ConditionalJump[IF_ICMPNE] #AC -> #KD
===#Block AD(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.LAPIS_BLOCK;
   2. goto CZ
      -> UnconditionalJump[GOTO] #AD -> #CZ
      <- ConditionalJump[IF_ICMPEQ] #KD -> #AD
===#Block CZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 202701622)
      goto CY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CZ -> #CY
      -> TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AD -> #CZ
===#Block CY(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CZ -> #CY
===#Block DA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DA -> #CN
      <- TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
===#Block AE(size=1, flags=0)===
   0. goto DO
      -> UnconditionalJump[GOTO] #AE -> #DO
      <- Immediate #AC -> #AE
===#Block DO(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 84663718)
      goto DN
   1. throw nullconst;
      -> TryCatch range: [DO...DN] -> DP ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DO -> #DN
      <- UnconditionalJump[GOTO] #AE -> #DO
===#Block DN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DO...DN] -> DP ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DO -> #DN
===#Block DP(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #DP -> #AF
      <- TryCatch range: [DO...DN] -> DP ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DO...DN] -> DP ([Ljava/lang/IllegalAccessException;])
===#Block AF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.IRON_BLOCK;
   2. goto HD
      -> UnconditionalJump[GOTO] #AF -> #HD
      <- DefaultSwitch #D -> #AF
      <- UnconditionalJump[GOTO] #GG -> #AF
      <- UnconditionalJump[GOTO] #GD -> #AF
      <- UnconditionalJump[GOTO] #FR -> #AF
      <- UnconditionalJump[GOTO] #HN -> #AF
      <- UnconditionalJump[GOTO] #DV -> #AF
      <- UnconditionalJump[GOTO] #IL -> #AF
      <- UnconditionalJump[GOTO] #FO -> #AF
      <- UnconditionalJump[GOTO] #DP -> #AF
      <- UnconditionalJump[GOTO] #IO -> #AF
===#Block HD(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 175444193)
      goto HC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HD -> #HC
      -> TryCatch range: [HD...HC] -> HE ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AF -> #HD
===#Block HC(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HD...HC] -> HE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HD -> #HC
===#Block HE(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HE -> #CN
      <- TryCatch range: [HD...HC] -> HE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HD...HC] -> HE ([Ljava/lang/RuntimeException;])
===#Block JL(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -26344702)
      goto AG
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JL -> #AG
      -> UnconditionalJump[GOTO] #JL -> #JC
      <- ConditionalJump[IF_ICMPEQ] #C -> #JL
===#Block AG(size=3, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar31 = lvar2;
   2. if (lvar31 == {685777941 ^ lvar105})
      goto KE
      -> ConditionalJump[IF_ICMPEQ] #AG -> #KE
      -> Immediate #AG -> #AH
      <- ConditionalJump[IF_ICMPEQ] #JL -> #AG
===#Block AH(size=6, flags=0)===
   0. lvar32 = lvar1;
   1. lvar101 = lvar32;
   2. lvar33 = lvar101;
   3. lvar34 = lvar33.hashCode();
   4. svar107 = {lvar34 ^ lvar105};
   5. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(svar107)) {
      case 74142274:
      	 goto	#BD
      case 74142277:
      	 goto	#AI
      case 74142278:
      	 goto	#AU
      case 74142279:
      	 goto	#BA
      case 74142284:
      	 goto	#BG
      case 74142287:
      	 goto	#AR
      case 74142296:
      	 goto	#AX
      case 74142297:
      	 goto	#AO
      default:
      	 goto	#BJ
   }
      -> Switch[74142284] #AH -> #BG
      -> DefaultSwitch #AH -> #BJ
      -> Switch[74142274] #AH -> #BD
      -> Switch[74142279] #AH -> #BA
      -> Switch[74142296] #AH -> #AX
      -> Switch[74142278] #AH -> #AU
      -> Switch[74142287] #AH -> #AR
      -> Switch[74142297] #AH -> #AO
      -> Switch[74142277] #AH -> #AI
      <- Immediate #AG -> #AH
===#Block AI(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar35 = lvar101;
   2. lvar83 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.amyshjbprxqwwbg(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar36 = lvar35.equals(lvar83);
   4. if (lvar36 != {122470188 ^ lvar105})
      goto JU
      -> ConditionalJump[IF_ICMPNE] #AI -> #JU
      -> Immediate #AI -> #AK
      <- Switch[74142277] #AH -> #AI
===#Block AK(size=1, flags=0)===
   0. goto FT
      -> UnconditionalJump[GOTO] #AK -> #FT
      <- Immediate #AI -> #AK
===#Block FT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 34304186)
      goto FS
   1. throw nullconst;
      -> TryCatch range: [FT...FS] -> FU ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FT -> #FS
      <- UnconditionalJump[GOTO] #AK -> #FT
===#Block FS(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FT...FS] -> FU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FT -> #FS
===#Block FU(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #FU -> #BJ
      <- TryCatch range: [FT...FS] -> FU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FT...FS] -> FU ([Ljava/lang/RuntimeException;])
===#Block JU(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -183134535)
      goto AJ
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JU -> #AJ
      -> UnconditionalJump[GOTO] #JU -> #JC
      <- ConditionalJump[IF_ICMPNE] #AI -> #JU
===#Block AJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.YELLOW_GLAZED_TERRACOTTA;
   2. goto HP
      -> UnconditionalJump[GOTO] #AJ -> #HP
      <- ConditionalJump[IF_ICMPEQ] #JU -> #AJ
===#Block HP(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 64150224)
      goto HO
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HP -> #HO
      -> TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AJ -> #HP
===#Block HO(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HP -> #HO
===#Block HQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HQ -> #CN
      <- TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
      <- TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
===#Block AO(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar39 = lvar101;
   2. lvar85 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.pwsgkjhqdnfmvse(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar40 = lvar39.equals(lvar85);
   4. if (lvar40 != {323526929 ^ lvar105})
      goto JT
      -> ConditionalJump[IF_ICMPNE] #AO -> #JT
      -> Immediate #AO -> #AP
      <- Switch[74142297] #AH -> #AO
===#Block AP(size=1, flags=0)===
   0. goto CT
      -> UnconditionalJump[GOTO] #AP -> #CT
      <- Immediate #AO -> #AP
===#Block CT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 251677779)
      goto CS
   1. throw nullconst;
      -> TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CT -> #CS
      <- UnconditionalJump[GOTO] #AP -> #CT
===#Block CS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CT -> #CS
===#Block CU(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #CU -> #BJ
      <- TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
      <- TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
===#Block JT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1674276952)
      goto AQ
   1. goto JC
      -> UnconditionalJump[GOTO] #JT -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JT -> #AQ
      <- ConditionalJump[IF_ICMPNE] #AO -> #JT
===#Block AQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.WHITE_GLAZED_TERRACOTTA;
   2. goto IW
      -> UnconditionalJump[GOTO] #AQ -> #IW
      <- ConditionalJump[IF_ICMPEQ] #JT -> #AQ
===#Block IW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 207077477)
      goto IV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IW -> #IV
      -> TryCatch range: [IW...IV] -> IX ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AQ -> #IW
===#Block IV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IW...IV] -> IX ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IW -> #IV
===#Block IX(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IX -> #CN
      <- TryCatch range: [IW...IV] -> IX ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IW...IV] -> IX ([Ljava/lang/RuntimeException;])
===#Block AR(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar41 = lvar101;
   2. lvar86 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.fnzabfdarxpejkp(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar42 = lvar41.equals(lvar86);
   4. if (lvar42 != {673666800 ^ lvar105})
      goto KA
      -> Immediate #AR -> #AS
      -> ConditionalJump[IF_ICMPNE] #AR -> #KA
      <- Switch[74142287] #AH -> #AR
===#Block KA(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 650823826)
      goto AT
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KA -> #AT
      -> UnconditionalJump[GOTO] #KA -> #JC
      <- ConditionalJump[IF_ICMPNE] #AR -> #KA
===#Block AT(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.CYAN_GLAZED_TERRACOTTA;
   2. goto FH
      -> UnconditionalJump[GOTO] #AT -> #FH
      <- ConditionalJump[IF_ICMPEQ] #KA -> #AT
===#Block FH(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 179953541)
      goto FG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FH -> #FG
      -> TryCatch range: [FH...FG] -> FI ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AT -> #FH
===#Block FG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FH...FG] -> FI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FH -> #FG
===#Block FI(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FI -> #CN
      <- TryCatch range: [FH...FG] -> FI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FH...FG] -> FI ([Ljava/lang/IllegalAccessException;])
===#Block AS(size=1, flags=0)===
   0. goto GI
      -> UnconditionalJump[GOTO] #AS -> #GI
      <- Immediate #AR -> #AS
===#Block GI(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 20758917)
      goto GH
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GI -> #GH
      -> TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AS -> #GI
===#Block GH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GI -> #GH
===#Block GJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #GJ -> #BJ
      <- TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
===#Block AU(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar43 = lvar101;
   2. lvar87 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.kaqomqnavbsavha(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar44 = lvar43.equals(lvar87);
   4. if (lvar44 != {644842593 ^ lvar105})
      goto JE
      -> ConditionalJump[IF_ICMPNE] #AU -> #JE
      -> Immediate #AU -> #AV
      <- Switch[74142278] #AH -> #AU
===#Block AV(size=1, flags=0)===
   0. goto GO
      -> UnconditionalJump[GOTO] #AV -> #GO
      <- Immediate #AU -> #AV
===#Block GO(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 246902963)
      goto GN
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GO -> #GN
      -> TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AV -> #GO
===#Block GN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GO -> #GN
===#Block GP(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #GP -> #BJ
      <- TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
===#Block JE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 686396417)
      goto AW
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JE -> #AW
      -> UnconditionalJump[GOTO] #JE -> #JC
      <- ConditionalJump[IF_ICMPNE] #AU -> #JE
===#Block AW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GREEN_GLAZED_TERRACOTTA;
   2. goto IB
      -> UnconditionalJump[GOTO] #AW -> #IB
      <- ConditionalJump[IF_ICMPEQ] #JE -> #AW
===#Block IB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 46056518)
      goto IA
   1. throw nullconst;
      -> TryCatch range: [IB...IA] -> IC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #IB -> #IA
      <- UnconditionalJump[GOTO] #AW -> #IB
===#Block IA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IB...IA] -> IC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IB -> #IA
===#Block IC(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IC -> #CN
      <- TryCatch range: [IB...IA] -> IC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IB...IA] -> IC ([Ljava/lang/RuntimeException;])
===#Block AX(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar45 = lvar101;
   2. lvar88 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.iwdhwfexgbzmsvo(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar46 = lvar45.equals(lvar88);
   4. if (lvar46 != {486755307 ^ lvar105})
      goto JR
      -> ConditionalJump[IF_ICMPNE] #AX -> #JR
      -> Immediate #AX -> #AZ
      <- Switch[74142296] #AH -> #AX
===#Block AZ(size=1, flags=0)===
   0. goto DL
      -> UnconditionalJump[GOTO] #AZ -> #DL
      <- Immediate #AX -> #AZ
===#Block DL(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 102129740)
      goto DK
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DL -> #DK
      -> TryCatch range: [DL...DK] -> DM ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AZ -> #DL
===#Block DK(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DL...DK] -> DM ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DL -> #DK
===#Block DM(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #DM -> #BJ
      <- TryCatch range: [DL...DK] -> DM ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DL...DK] -> DM ([Ljava/lang/RuntimeException;])
===#Block JR(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 235340515)
      goto AY
   1. goto JC
      -> UnconditionalJump[GOTO] #JR -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JR -> #AY
      <- ConditionalJump[IF_ICMPNE] #AX -> #JR
===#Block AY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLACK_GLAZED_TERRACOTTA;
   2. goto DF
      -> UnconditionalJump[GOTO] #AY -> #DF
      <- ConditionalJump[IF_ICMPEQ] #JR -> #AY
===#Block DF(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 90323181)
      goto DE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DF -> #DE
      -> TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AY -> #DF
===#Block DE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DF -> #DE
===#Block DG(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DG -> #CN
      <- TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
      <- TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
===#Block BA(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar48 = lvar101;
   2. lvar89 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.wkrkueqcoimgyqu(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar49 = lvar48.equals(lvar89);
   4. if (lvar49 != {1033407840 ^ lvar105})
      goto JN
      -> Immediate #BA -> #BB
      -> ConditionalJump[IF_ICMPNE] #BA -> #JN
      <- Switch[74142279] #AH -> #BA
===#Block JN(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -400540199)
      goto BC
   1. goto JC
      -> UnconditionalJump[GOTO] #JN -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JN -> #BC
      <- ConditionalJump[IF_ICMPNE] #BA -> #JN
===#Block BC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.RED_GLAZED_TERRACOTTA;
   2. goto FK
      -> UnconditionalJump[GOTO] #BC -> #FK
      <- ConditionalJump[IF_ICMPEQ] #JN -> #BC
===#Block FK(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 85871226)
      goto FJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FK -> #FJ
      -> TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BC -> #FK
===#Block FJ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FK -> #FJ
===#Block FL(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FL -> #CN
      <- TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
      <- TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
===#Block BB(size=1, flags=0)===
   0. goto GR
      -> UnconditionalJump[GOTO] #BB -> #GR
      <- Immediate #BA -> #BB
===#Block GR(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 129573693)
      goto GQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GR -> #GQ
      -> TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BB -> #GR
===#Block GQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GR -> #GQ
===#Block GS(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #GS -> #BJ
      <- TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
===#Block BD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar50 = lvar101;
   2. lvar90 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.hmurkkzzjnrgtei(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar51 = lvar50.equals(lvar90);
   4. if (lvar51 != {8721304 ^ lvar105})
      goto JG
      -> ConditionalJump[IF_ICMPNE] #BD -> #JG
      -> Immediate #BD -> #BE
      <- Switch[74142274] #AH -> #BD
===#Block BE(size=1, flags=0)===
   0. goto HG
      -> UnconditionalJump[GOTO] #BE -> #HG
      <- Immediate #BD -> #BE
===#Block HG(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 66794996)
      goto HF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HG -> #HF
      -> TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BE -> #HG
===#Block HF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HG -> #HF
===#Block HH(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #HH -> #BJ
      <- TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
===#Block JG(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1170199207)
      goto BF
   1. goto JC
      -> UnconditionalJump[GOTO] #JG -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JG -> #BF
      <- ConditionalJump[IF_ICMPNE] #BD -> #JG
===#Block BF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLUE_GLAZED_TERRACOTTA;
   2. goto IE
      -> UnconditionalJump[GOTO] #BF -> #IE
      <- ConditionalJump[IF_ICMPEQ] #JG -> #BF
===#Block IE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 10486863)
      goto ID
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IE -> #ID
      -> TryCatch range: [IE...ID] -> IF ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BF -> #IE
===#Block ID(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IE...ID] -> IF ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IE -> #ID
===#Block IF(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IF -> #CN
      <- TryCatch range: [IE...ID] -> IF ([Ljava/io/IOException;])
      <- TryCatch range: [IE...ID] -> IF ([Ljava/io/IOException;])
===#Block BG(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar52 = lvar101;
   2. lvar91 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.hbfwnxlzucdudiq(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar53 = lvar52.equals(lvar91);
   4. if (lvar53 != {274765875 ^ lvar105})
      goto JK
      -> Immediate #BG -> #BI
      -> ConditionalJump[IF_ICMPNE] #BG -> #JK
      <- Switch[74142284] #AH -> #BG
===#Block JK(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -407677502)
      goto BH
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JK -> #BH
      -> UnconditionalJump[GOTO] #JK -> #JC
      <- ConditionalJump[IF_ICMPNE] #BG -> #JK
===#Block BH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.MAGENTA_GLAZED_TERRACOTTA;
   2. goto HY
      -> UnconditionalJump[GOTO] #BH -> #HY
      <- ConditionalJump[IF_ICMPEQ] #JK -> #BH
===#Block HY(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 62868264)
      goto HX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HY -> #HX
      -> TryCatch range: [HY...HX] -> HZ ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BH -> #HY
===#Block HX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HY...HX] -> HZ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HY -> #HX
===#Block HZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HZ -> #CN
      <- TryCatch range: [HY...HX] -> HZ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HY...HX] -> HZ ([Ljava/lang/RuntimeException;])
===#Block BI(size=1, flags=0)===
   0. goto FW
      -> UnconditionalJump[GOTO] #BI -> #FW
      <- Immediate #BG -> #BI
===#Block FW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 245345565)
      goto FV
   1. throw nullconst;
      -> TryCatch range: [FW...FV] -> FX ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #FW -> #FV
      <- UnconditionalJump[GOTO] #BI -> #FW
===#Block FV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FW...FV] -> FX ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FW -> #FV
===#Block FX(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #FX -> #BJ
      <- TryCatch range: [FW...FV] -> FX ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FW...FV] -> FX ([Ljava/lang/IllegalAccessException;])
===#Block BJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GRAY_GLAZED_TERRACOTTA;
   2. goto ES
      -> UnconditionalJump[GOTO] #BJ -> #ES
      <- UnconditionalJump[GOTO] #GM -> #BJ
      <- DefaultSwitch #AH -> #BJ
      <- UnconditionalJump[GOTO] #CU -> #BJ
      <- UnconditionalJump[GOTO] #FX -> #BJ
      <- UnconditionalJump[GOTO] #FU -> #BJ
      <- UnconditionalJump[GOTO] #DM -> #BJ
      <- UnconditionalJump[GOTO] #HH -> #BJ
      <- UnconditionalJump[GOTO] #GP -> #BJ
      <- UnconditionalJump[GOTO] #GS -> #BJ
      <- UnconditionalJump[GOTO] #GJ -> #BJ
===#Block ES(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 36552)
      goto ER
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #ES -> #ER
      -> TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BJ -> #ES
===#Block ER(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #ES -> #ER
===#Block ET(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #ET -> #CN
      <- TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
===#Block KE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1323350495)
      goto BK
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KE -> #BK
      -> UnconditionalJump[GOTO] #KE -> #JC
      <- ConditionalJump[IF_ICMPEQ] #AG -> #KE
===#Block BK(size=7, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar54 = lvar1;
   2. lvar102 = lvar54;
   3. lvar55 = lvar102;
   4. lvar56 = lvar55.hashCode();
   5. svar107 = {lvar56 ^ lvar105};
   6. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(svar107)) {
      case 177480617:
      	 goto	#BX
      case 177480619:
      	 goto	#BU
      case 177480623:
      	 goto	#CG
      case 177480625:
      	 goto	#CA
      case 177480627:
      	 goto	#BL
      case 177480629:
      	 goto	#BO
      case 177480631:
      	 goto	#CD
      case 177480637:
      	 goto	#BR
      case 177480639:
      	 goto	#CJ
      default:
      	 goto	#CL
   }
      -> Switch[177480639] #BK -> #CJ
      -> Switch[177480623] #BK -> #CG
      -> Switch[177480631] #BK -> #CD
      -> Switch[177480625] #BK -> #CA
      -> Switch[177480617] #BK -> #BX
      -> Switch[177480619] #BK -> #BU
      -> DefaultSwitch #BK -> #CL
      -> Switch[177480637] #BK -> #BR
      -> Switch[177480629] #BK -> #BO
      -> Switch[177480627] #BK -> #BL
      <- ConditionalJump[IF_ICMPEQ] #KE -> #BK
===#Block BL(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar57 = lvar102;
   2. lvar92 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.wgdehfwmnqxzlee(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar58 = lvar57.equals(lvar92);
   4. if (lvar58 != {744164026 ^ lvar105})
      goto JH
      -> ConditionalJump[IF_ICMPNE] #BL -> #JH
      -> Immediate #BL -> #BN
      <- Switch[177480627] #BK -> #BL
===#Block BN(size=1, flags=0)===
   0. goto GU
      -> UnconditionalJump[GOTO] #BN -> #GU
      <- Immediate #BL -> #BN
===#Block GU(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 143553013)
      goto GT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GU -> #GT
      -> TryCatch range: [GU...GT] -> GV ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BN -> #GU
===#Block GT(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [GU...GT] -> GV ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #GU -> #GT
===#Block GV(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #GV -> #CL
      <- TryCatch range: [GU...GT] -> GV ([Ljava/io/IOException;])
      <- TryCatch range: [GU...GT] -> GV ([Ljava/io/IOException;])
===#Block JH(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 350687554)
      goto BM
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JH -> #BM
      -> UnconditionalJump[GOTO] #JH -> #JC
      <- ConditionalJump[IF_ICMPNE] #BL -> #JH
===#Block BM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.WHITE_CONCRETE_POWDER;
   2. goto CW
      -> UnconditionalJump[GOTO] #BM -> #CW
      <- ConditionalJump[IF_ICMPEQ] #JH -> #BM
===#Block CW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 106084594)
      goto CV
   1. throw nullconst;
      -> TryCatch range: [CW...CV] -> CX ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #CW -> #CV
      <- UnconditionalJump[GOTO] #BM -> #CW
===#Block CV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CW...CV] -> CX ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CW -> #CV
===#Block CX(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #CX -> #CN
      <- TryCatch range: [CW...CV] -> CX ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CW...CV] -> CX ([Ljava/lang/RuntimeException;])
===#Block BO(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar59 = lvar102;
   2. lvar93 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.bfidogxfiyuansa(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar60 = lvar59.equals(lvar93);
   4. if (lvar60 != {445452881 ^ lvar105})
      goto JP
      -> ConditionalJump[IF_ICMPNE] #BO -> #JP
      -> Immediate #BO -> #BP
      <- Switch[177480629] #BK -> #BO
===#Block BP(size=1, flags=0)===
   0. goto HS
      -> UnconditionalJump[GOTO] #BP -> #HS
      <- Immediate #BO -> #BP
===#Block HS(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 132326790)
      goto HR
   1. throw nullconst;
      -> TryCatch range: [HS...HR] -> HT ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HS -> #HR
      <- UnconditionalJump[GOTO] #BP -> #HS
===#Block HR(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HS...HR] -> HT ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HS -> #HR
===#Block HT(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #HT -> #CL
      <- TryCatch range: [HS...HR] -> HT ([Ljava/io/IOException;])
      <- TryCatch range: [HS...HR] -> HT ([Ljava/io/IOException;])
===#Block JP(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -2090806774)
      goto BQ
   1. goto JC
      -> UnconditionalJump[GOTO] #JP -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JP -> #BQ
      <- ConditionalJump[IF_ICMPNE] #BO -> #JP
===#Block BQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.PURPLE_CONCRETE_POWDER;
   2. goto FB
      -> UnconditionalJump[GOTO] #BQ -> #FB
      <- ConditionalJump[IF_ICMPEQ] #JP -> #BQ
===#Block FB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 206897435)
      goto FA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FB -> #FA
      -> TryCatch range: [FB...FA] -> FC ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BQ -> #FB
===#Block FA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FB...FA] -> FC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FB -> #FA
===#Block FC(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FC -> #CN
      <- TryCatch range: [FB...FA] -> FC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FB...FA] -> FC ([Ljava/lang/RuntimeException;])
===#Block BR(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar61 = lvar102;
   2. lvar94 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.bnjbpnhustapfib(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar62 = lvar61.equals(lvar94);
   4. if (lvar62 != {465198987 ^ lvar105})
      goto JM
      -> Immediate #BR -> #BS
      -> ConditionalJump[IF_ICMPNE] #BR -> #JM
      <- Switch[177480637] #BK -> #BR
===#Block JM(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -394408957)
      goto BT
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JM -> #BT
      -> UnconditionalJump[GOTO] #JM -> #JC
      <- ConditionalJump[IF_ICMPNE] #BR -> #JM
===#Block BT(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.MAGENTA_CONCRETE_POWDER;
   2. goto DI
      -> UnconditionalJump[GOTO] #BT -> #DI
      <- ConditionalJump[IF_ICMPEQ] #JM -> #BT
===#Block DI(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 220826101)
      goto DH
   1. throw nullconst;
      -> TryCatch range: [DI...DH] -> DJ ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DI -> #DH
      <- UnconditionalJump[GOTO] #BT -> #DI
===#Block DH(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DI...DH] -> DJ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DI -> #DH
===#Block DJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DJ -> #CN
      <- TryCatch range: [DI...DH] -> DJ ([Ljava/io/IOException;])
      <- TryCatch range: [DI...DH] -> DJ ([Ljava/io/IOException;])
===#Block BS(size=1, flags=0)===
   0. goto DR
      -> UnconditionalJump[GOTO] #BS -> #DR
      <- Immediate #BR -> #BS
===#Block DR(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 122919824)
      goto DQ
   1. throw nullconst;
      -> TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DR -> #DQ
      <- UnconditionalJump[GOTO] #BS -> #DR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DR -> #DQ
===#Block DS(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #DS -> #CL
      <- TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
===#Block BU(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar63 = lvar102;
   2. lvar95 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.cedhrnrirbscepn(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar64 = lvar63.equals(lvar95);
   4. if (lvar64 != {332068532 ^ lvar105})
      goto JF
      -> Immediate #BU -> #BW
      -> ConditionalJump[IF_ICMPNE] #BU -> #JF
      <- Switch[177480619] #BK -> #BU
===#Block JF(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -2561038)
      goto BV
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JF -> #BV
      -> UnconditionalJump[GOTO] #JF -> #JC
      <- ConditionalJump[IF_ICMPNE] #BU -> #JF
===#Block BV(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.YELLOW_CONCRETE_POWDER;
   2. goto IT
      -> UnconditionalJump[GOTO] #BV -> #IT
      <- ConditionalJump[IF_ICMPEQ] #JF -> #BV
===#Block IT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 131801216)
      goto IS
   1. throw nullconst;
      -> TryCatch range: [IT...IS] -> IU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IT -> #IS
      <- UnconditionalJump[GOTO] #BV -> #IT
===#Block IS(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IT...IS] -> IU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IT -> #IS
===#Block IU(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IU -> #CN
      <- TryCatch range: [IT...IS] -> IU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IT...IS] -> IU ([Ljava/lang/IllegalAccessException;])
===#Block BW(size=1, flags=0)===
   0. goto HA
      -> UnconditionalJump[GOTO] #BW -> #HA
      <- Immediate #BU -> #BW
===#Block HA(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 19001314)
      goto GZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HA -> #GZ
      -> TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BW -> #HA
===#Block GZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HA -> #GZ
===#Block HB(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #HB -> #CL
      <- TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
===#Block BX(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar65 = lvar102;
   2. lvar96 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.awldnhtdopepktk(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar66 = lvar65.equals(lvar96);
   4. if (lvar66 != {109678246 ^ lvar105})
      goto JQ
      -> Immediate #BX -> #BZ
      -> ConditionalJump[IF_ICMPNE] #BX -> #JQ
      <- Switch[177480617] #BK -> #BX
===#Block JQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 509096539)
      goto BY
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JQ -> #BY
      -> UnconditionalJump[GOTO] #JQ -> #JC
      <- ConditionalJump[IF_ICMPNE] #BX -> #JQ
===#Block BY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLUE_CONCRETE_POWDER;
   2. goto ED
      -> UnconditionalJump[GOTO] #BY -> #ED
      <- ConditionalJump[IF_ICMPEQ] #JQ -> #BY
===#Block ED(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 116160155)
      goto EC
   1. throw nullconst;
      -> TryCatch range: [ED...EC] -> EE ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #ED -> #EC
      <- UnconditionalJump[GOTO] #BY -> #ED
===#Block EC(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [ED...EC] -> EE ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #ED -> #EC
===#Block EE(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EE -> #CN
      <- TryCatch range: [ED...EC] -> EE ([Ljava/io/IOException;])
      <- TryCatch range: [ED...EC] -> EE ([Ljava/io/IOException;])
===#Block BZ(size=1, flags=0)===
   0. goto EV
      -> UnconditionalJump[GOTO] #BZ -> #EV
      <- Immediate #BX -> #BZ
===#Block EV(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 19764687)
      goto EU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EV -> #EU
      -> TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BZ -> #EV
===#Block EU(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EV -> #EU
===#Block EW(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #EW -> #CL
      <- TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
      <- TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
===#Block CA(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar67 = lvar102;
   2. lvar97 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.avlofnbbvgchwvl(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar68 = lvar67.equals(lvar97);
   4. if (lvar68 != {1104339724 ^ lvar105})
      goto JJ
      -> ConditionalJump[IF_ICMPNE] #CA -> #JJ
      -> Immediate #CA -> #CC
      <- Switch[177480625] #BK -> #CA
===#Block CC(size=1, flags=0)===
   0. goto GX
      -> UnconditionalJump[GOTO] #CC -> #GX
      <- Immediate #CA -> #CC
===#Block GX(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 77660172)
      goto GW
   1. throw nullconst;
      -> TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GX -> #GW
      <- UnconditionalJump[GOTO] #CC -> #GX
===#Block GW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GX -> #GW
===#Block GY(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #GY -> #CL
      <- TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
===#Block JJ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -555126384)
      goto CB
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JJ -> #CB
      -> UnconditionalJump[GOTO] #JJ -> #JC
      <- ConditionalJump[IF_ICMPNE] #CA -> #JJ
===#Block CB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GREEN_CONCRETE_POWDER;
   2. goto IQ
      -> UnconditionalJump[GOTO] #CB -> #IQ
      <- ConditionalJump[IF_ICMPEQ] #JJ -> #CB
===#Block IQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 253853879)
      goto IP
   1. throw nullconst;
      -> TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IQ -> #IP
      <- UnconditionalJump[GOTO] #CB -> #IQ
===#Block IP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IQ -> #IP
===#Block IR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IR -> #CN
      <- TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
===#Block CD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar69 = lvar102;
   2. lvar98 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.xrmdsqhjxfmajau(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar70 = lvar69.equals(lvar98);
   4. if (lvar70 != {982747412 ^ lvar105})
      goto JD
      -> Immediate #CD -> #CF
      -> ConditionalJump[IF_ICMPNE] #CD -> #JD
      <- Switch[177480631] #BK -> #CD
===#Block JD(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -111157143)
      goto CE
   1. goto JC
      -> UnconditionalJump[GOTO] #JD -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JD -> #CE
      <- ConditionalJump[IF_ICMPNE] #CD -> #JD
===#Block CE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLACK_CONCRETE_POWDER;
   2. goto IH
      -> UnconditionalJump[GOTO] #CE -> #IH
      <- ConditionalJump[IF_ICMPEQ] #JD -> #CE
===#Block IH(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 237589913)
      goto IG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IH -> #IG
      -> TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CE -> #IH
===#Block IG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IH -> #IG
===#Block II(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #II -> #CN
      <- TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
===#Block CF(size=1, flags=0)===
   0. goto EG
      -> UnconditionalJump[GOTO] #CF -> #EG
      <- Immediate #CD -> #CF
===#Block EG(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 177624065)
      goto EF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EG -> #EF
      -> TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CF -> #EG
===#Block EF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EG -> #EF
===#Block EH(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #EH -> #CL
      <- TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
===#Block CG(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar71 = lvar102;
   2. lvar99 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.gdiivelbjailnxu(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar72 = lvar71.equals(lvar99);
   4. if (lvar72 != {1550985398 ^ lvar105})
      goto JZ
      -> Immediate #CG -> #CI
      -> ConditionalJump[IF_ICMPNE] #CG -> #JZ
      <- Switch[177480623] #BK -> #CG
===#Block JZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1049842807)
      goto CH
   1. goto JC
      -> UnconditionalJump[GOTO] #JZ -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JZ -> #CH
      <- ConditionalJump[IF_ICMPNE] #CG -> #JZ
===#Block CH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.RED_CONCRETE_POWDER;
   2. goto EM
      -> UnconditionalJump[GOTO] #CH -> #EM
      <- ConditionalJump[IF_ICMPEQ] #JZ -> #CH
===#Block EM(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 255006675)
      goto EL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EM -> #EL
      -> TryCatch range: [EM...EL] -> EN ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CH -> #EM
===#Block EL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EM...EL] -> EN ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EM -> #EL
===#Block EN(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EN -> #CN
      <- TryCatch range: [EM...EL] -> EN ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EM...EL] -> EN ([Ljava/lang/RuntimeException;])
===#Block CI(size=1, flags=0)===
   0. goto HV
      -> UnconditionalJump[GOTO] #CI -> #HV
      <- Immediate #CG -> #CI
===#Block HV(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 188126056)
      goto HU
   1. throw nullconst;
      -> TryCatch range: [HV...HU] -> HW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #HV -> #HU
      <- UnconditionalJump[GOTO] #CI -> #HV
===#Block HU(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HV...HU] -> HW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HV -> #HU
===#Block HW(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #HW -> #CL
      <- TryCatch range: [HV...HU] -> HW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HV...HU] -> HW ([Ljava/lang/RuntimeException;])
===#Block CJ(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar73 = lvar102;
   2. lvar100 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.nltfbnawgicmzkh(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar74 = lvar73.equals(lvar100);
   4. if (lvar74 != {662420135 ^ lvar105})
      goto JV
      -> ConditionalJump[IF_ICMPNE] #CJ -> #JV
      -> Immediate #CJ -> #CK
      <- Switch[177480639] #BK -> #CJ
===#Block CK(size=1, flags=0)===
   0. goto IZ
      -> UnconditionalJump[GOTO] #CK -> #IZ
      <- Immediate #CJ -> #CK
===#Block IZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 267531608)
      goto IY
   1. throw nullconst;
      -> TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #IZ -> #IY
      <- UnconditionalJump[GOTO] #CK -> #IZ
===#Block IY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IZ -> #IY
===#Block JA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #JA -> #CL
      <- TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
      <- TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
===#Block CL(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GRAY_CONCRETE_POWDER;
      -> Immediate #CL -> #CN
      <- UnconditionalJump[GOTO] #DS -> #CL
      <- UnconditionalJump[GOTO] #GV -> #CL
      <- UnconditionalJump[GOTO] #JA -> #CL
      <- UnconditionalJump[GOTO] #HW -> #CL
      <- UnconditionalJump[GOTO] #HB -> #CL
      <- UnconditionalJump[GOTO] #EH -> #CL
      <- UnconditionalJump[GOTO] #HT -> #CL
      <- DefaultSwitch #BK -> #CL
      <- UnconditionalJump[GOTO] #EW -> #CL
      <- UnconditionalJump[GOTO] #GY -> #CL
===#Block JV(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -289079206)
      goto CM
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JV -> #CM
      -> UnconditionalJump[GOTO] #JV -> #JC
      <- ConditionalJump[IF_ICMPNE] #CJ -> #JV
===#Block JC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      <- UnconditionalJump[GOTO] #JT -> #JC
      <- UnconditionalJump[GOTO] #KD -> #JC
      <- UnconditionalJump[GOTO] #JG -> #JC
      <- UnconditionalJump[GOTO] #JB -> #JC
      <- UnconditionalJump[GOTO] #JN -> #JC
      <- UnconditionalJump[GOTO] #JV -> #JC
      <- UnconditionalJump[GOTO] #KC -> #JC
      <- UnconditionalJump[GOTO] #JW -> #JC
      <- UnconditionalJump[GOTO] #JR -> #JC
      <- UnconditionalJump[GOTO] #KA -> #JC
      <- UnconditionalJump[GOTO] #JI -> #JC
      <- UnconditionalJump[GOTO] #KE -> #JC
      <- UnconditionalJump[GOTO] #JY -> #JC
      <- UnconditionalJump[GOTO] #JP -> #JC
      <- UnconditionalJump[GOTO] #JZ -> #JC
      <- UnconditionalJump[GOTO] #JK -> #JC
      <- UnconditionalJump[GOTO] #JF -> #JC
      <- UnconditionalJump[GOTO] #JX -> #JC
      <- UnconditionalJump[GOTO] #JD -> #JC
      <- UnconditionalJump[GOTO] #KB -> #JC
      <- UnconditionalJump[GOTO] #JM -> #JC
      <- UnconditionalJump[GOTO] #JL -> #JC
      <- UnconditionalJump[GOTO] #JS -> #JC
      <- UnconditionalJump[GOTO] #JO -> #JC
      <- UnconditionalJump[GOTO] #JJ -> #JC
      <- UnconditionalJump[GOTO] #JH -> #JC
      <- UnconditionalJump[GOTO] #JQ -> #JC
      <- UnconditionalJump[GOTO] #JU -> #JC
      <- UnconditionalJump[GOTO] #JE -> #JC
===#Block CM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.CYAN_CONCRETE_POWDER;
   2. goto FZ
      -> UnconditionalJump[GOTO] #CM -> #FZ
      <- ConditionalJump[IF_ICMPEQ] #JV -> #CM
===#Block FZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 22468246)
      goto FY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FZ -> #FY
      -> TryCatch range: [FZ...FY] -> GA ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CM -> #FZ
===#Block FY(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FZ...FY] -> GA ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FZ -> #FY
===#Block GA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GA -> #CN
      <- TryCatch range: [FZ...FY] -> GA ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FZ...FY] -> GA ([Ljava/lang/IllegalAccessException;])
===#Block CN(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[1] [org/bukkit/Material]
   1. lvar7 = lvar47;
      -> Immediate #CN -> #CO
      <- UnconditionalJump[GOTO] #IX -> #CN
      <- UnconditionalJump[GOTO] #GA -> #CN
      <- UnconditionalJump[GOTO] #FL -> #CN
      <- UnconditionalJump[GOTO] #IR -> #CN
      <- UnconditionalJump[GOTO] #DG -> #CN
      <- UnconditionalJump[GOTO] #HE -> #CN
      <- Immediate #CL -> #CN
      <- UnconditionalJump[GOTO] #HK -> #CN
      <- UnconditionalJump[GOTO] #HZ -> #CN
      <- UnconditionalJump[GOTO] #EQ -> #CN
      <- UnconditionalJump[GOTO] #DY -> #CN
      <- UnconditionalJump[GOTO] #IF -> #CN
      <- UnconditionalJump[GOTO] #EB -> #CN
      <- UnconditionalJump[GOTO] #DJ -> #CN
      <- UnconditionalJump[GOTO] #II -> #CN
      <- UnconditionalJump[GOTO] #IU -> #CN
      <- UnconditionalJump[GOTO] #IC -> #CN
      <- UnconditionalJump[GOTO] #DD -> #CN
      <- UnconditionalJump[GOTO] #HQ -> #CN
      <- UnconditionalJump[GOTO] #CR -> #CN
      <- UnconditionalJump[GOTO] #ET -> #CN
      <- UnconditionalJump[GOTO] #EK -> #CN
      <- UnconditionalJump[GOTO] #EN -> #CN
      <- UnconditionalJump[GOTO] #EZ -> #CN
      <- UnconditionalJump[GOTO] #FF -> #CN
      <- UnconditionalJump[GOTO] #DA -> #CN
      <- UnconditionalJump[GOTO] #FI -> #CN
      <- UnconditionalJump[GOTO] #EE -> #CN
      <- UnconditionalJump[GOTO] #CX -> #CN
      <- UnconditionalJump[GOTO] #FC -> #CN
===#Block CO(size=4, flags=0)===
   0. lvar14 = new org.bukkit.inventory.ItemStack;
   1. lvar6 = lvar7;
   2. _consume(lvar14.<init>(lvar6));
   3. return lvar14;
      <- Immediate #CN -> #CO
