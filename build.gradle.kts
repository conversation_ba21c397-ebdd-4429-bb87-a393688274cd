import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("org.jetbrains.kotlin.jvm") version "2.0.20"
    id("architectury-plugin") version "3.4-SNAPSHOT"
    id("dev.architectury.loom") version "1.7-SNAPSHOT"
}

group = "cn.acebrand"
version = "1.0.0"

// 设置构建输出名称
base.archivesName.set("AceDex")

architectury {
    minecraft = "1.21.1"
}

allprojects{
    apply(plugin = "org.jetbrains.kotlin.jvm")
    apply(plugin = "dev.architectury.loom")

    // 配置Java工具链
    java {
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(17))
        }
    }

    repositories {
        mavenLocal()
        mavenCentral()

        // Fabric 仓库 - 用于 server-intermediary（LGPokemonMenu 使用的映射）
        maven {
            name = "FabricMC"
            url = uri("https://maven.fabricmc.net/")
        }

        // ParchmentMC 仓库 - 另一个映射源
        maven {
            name = "ParchmentMC"
            url = uri("https://maven.parchmentmc.org/")
        }

        maven {
            isAllowInsecureProtocol = true
            url = uri("http://server.pokemtd.top:31647/snapshots")
        }
        maven {
            url = uri("https://repo.extendedclip.com/releases/")
        }
        maven {
            name = "spigotmc-repo"
            url = uri("https://hub.spigotmc.org/nexus/content/repositories/snapshots/")
        }
        maven {
            url = uri("https://maven.impactdev.net/repository/development/")
        }
        maven {
            url = uri("https://artefacts.cobblemon.com/releases")
        }
    }

    dependencies {
        minecraft("net.minecraft:minecraft:1.21.1")
        mappings(loom.officialMojangMappings())

        // Cobblemon 依赖 - 使用正确的仓库路径
        modCompileOnly("com.cobblemon:mod:1.6.1+1.21.1")

        // 暂时移除有问题的依赖，使用纯 Cobblemon API 方案

        // Bukkit/Spigot 依赖
        compileOnly("org.spigotmc:spigot-api:1.21.1-R0.1-SNAPSHOT")

        // PlaceholderAPI 依赖
        compileOnly("me.clip:placeholderapi:2.11.6")

        // Kotlin 依赖
        compileOnly("org.jetbrains.kotlin:kotlin-reflect")

        // 本地库
        implementation(fileTree("libs") {
            include("*.jar")
        })

        testImplementation(kotlin("test"))
    }

    tasks.test {
        useJUnitPlatform()
    }

    // 配置Java编译任务
    tasks.withType<JavaCompile>{
        options.encoding = "UTF-8"
        sourceCompatibility = "17"
        targetCompatibility = "17"
    }

    // 配置Kotlin编译任务
    tasks.withType<KotlinCompile> {
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
            freeCompilerArgs.add("-Xjsr305=strict")
        }
    }

    // 配置jar任务
    tasks.jar {
        archiveBaseName.set("AceDex")
        archiveVersion.set("1.0.0")
        archiveClassifier.set("")

        manifest {
            attributes(
                "Main-Class" to "cn.acebrand.acedex.AceDex"
            )
        }
    }
}
