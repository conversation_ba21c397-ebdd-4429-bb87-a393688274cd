/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.reward

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.generation.Generation
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import java.util.*
import java.util.concurrent.CompletableFuture

/**
 * 奖励管理器
 * 负责管理精灵收集奖励系统
 */
class RewardManager(private val plugin: AceDex) {
    
    // 玩家奖励冷却时间记录
    private val rewardCooldowns = mutableMapOf<UUID, MutableMap<String, Long>>()

    // 奖励配置
    private val generationRewards = mutableMapOf<String, List<GenerationReward>>()

    // 进度奖励配置
    private val generationProgressRewards = mutableMapOf<String, List<ProgressReward>>()

    // 全世代进度奖励配置
    private val overallProgressRewards = mutableListOf<OverallProgressReward>()

    // 世代完成奖励记录
    private val claimedGenerationRewards = mutableMapOf<UUID, MutableSet<String>>()

    // 全世代完成奖励记录
    private val claimedAllGenerationsRewards = mutableMapOf<UUID, Boolean>()

    // 进度奖励记录 - 格式: "gen_世代ID_进度百分比"
    private val claimedProgressRewards = mutableMapOf<UUID, MutableSet<String>>()

    // 全世代进度奖励记录 - 格式: "overall_进度百分比"
    private val claimedOverallProgressRewards = mutableMapOf<UUID, MutableSet<String>>()

    // === 付费奖励相关数据结构 ===

    // 付费世代完成奖励配置
    private val premiumGenerationRewards = mutableMapOf<String, List<GenerationReward>>()

    // 付费进度奖励配置
    private val premiumGenerationProgressRewards = mutableMapOf<String, List<ProgressReward>>()

    // 付费全世代进度奖励配置
    private val premiumOverallProgressRewards = mutableListOf<OverallProgressReward>()

    // 付费世代完成奖励记录
    private val claimedPremiumGenerationRewards = mutableMapOf<UUID, MutableSet<String>>()

    // 付费全世代完成奖励记录
    private val claimedPremiumAllGenerationsRewards = mutableMapOf<UUID, Boolean>()

    // 付费进度奖励记录 - 格式: "premium_progress_世代ID_进度百分比"
    private val claimedPremiumProgressRewards = mutableMapOf<UUID, MutableSet<String>>()

    // 付费全世代进度奖励记录 - 格式: "premium_overall_进度百分比"
    private val claimedPremiumOverallProgressRewards = mutableMapOf<UUID, MutableSet<String>>()


    /**
     * 初始化奖励管理器
     */
    fun initialize() {
        plugin.logger.info("正在初始化奖励管理器...")

        // 加载奖励配置
        loadRewardConfigs()

        // 加载已领取奖励数据
        loadRewardData()

        plugin.logger.info("奖励管理器初始化完成")
    }
    
    /**
     * 重新加载奖励配置
     */
    fun reload() {
        generationRewards.clear()
        generationProgressRewards.clear()
        overallProgressRewards.clear()

        // 清除付费奖励配置
        premiumGenerationRewards.clear()
        premiumGenerationProgressRewards.clear()
        premiumOverallProgressRewards.clear()

        loadRewardConfigs()
    }
    
    /**
     * 加载奖励配置
     */
    private fun loadRewardConfigs() {
        val config = plugin.config.getConfig()

        // 加载世代完成奖励配置（100%完成）
        for (generation in plugin.generationManager.getAllGenerations()) {
            val rewards = mutableListOf<GenerationReward>()
            val commands = config.getStringList("rewards.generation.${generation.id}.completion.commands")
            val descriptions = config.getStringList("rewards.generation.${generation.id}.completion.descriptions")

            if (commands.isNotEmpty()) {
                for (i in commands.indices) {
                    val description = if (i < descriptions.size) descriptions[i] else "完成${generation.name}"
                    rewards.add(
                        GenerationReward(
                            generationId = generation.id,
                            requiredPercentage = 100,
                            command = commands[i],
                            description = description
                        )
                    )
                }
            }

            generationRewards[generation.id] = rewards

            // 加载世代进度奖励配置
            val progressRewards = mutableListOf<ProgressReward>()
            val progressSection = config.getConfigurationSection("rewards.generation.${generation.id}.progress")

            if (progressSection != null) {
                for (percentageKey in progressSection.getKeys(false)) {
                    try {
                        val percentage = percentageKey.toInt()
                        val progressCommands = config.getStringList("rewards.generation.${generation.id}.progress.$percentageKey.commands")
                        val progressDescriptions = config.getStringList("rewards.generation.${generation.id}.progress.$percentageKey.descriptions")

                        if (progressCommands.isNotEmpty()) {
                            for (i in progressCommands.indices) {
                                val description = if (i < progressDescriptions.size) progressDescriptions[i] else "${generation.name} ${percentage}%进度奖励"
                                progressRewards.add(
                                    ProgressReward(
                                        generationId = generation.id,
                                        requiredPercentage = percentage,
                                        command = progressCommands[i],
                                        description = description
                                    )
                                )
                            }
                        }
                    } catch (e: NumberFormatException) {
                        plugin.logger.warning("无效的进度百分比配置: ${generation.id}.$percentageKey")
                    }
                }
            }

            generationProgressRewards[generation.id] = progressRewards
        }

        // 加载全世代进度奖励配置
        val overallProgressSection = config.getConfigurationSection("rewards.overall-progress")
        if (overallProgressSection != null && config.getBoolean("rewards.overall-progress.enabled", false)) {
            for (percentageKey in overallProgressSection.getKeys(false)) {
                if (percentageKey == "enabled") continue

                try {
                    val percentage = percentageKey.toInt()
                    val name = config.getString("rewards.overall-progress.$percentageKey.name") ?: "进度奖励"
                    val description = config.getString("rewards.overall-progress.$percentageKey.description") ?: "全世代进度达到${percentage}%"
                    val commands = config.getStringList("rewards.overall-progress.$percentageKey.commands")
                    val descriptions = config.getStringList("rewards.overall-progress.$percentageKey.descriptions")

                    if (commands.isNotEmpty()) {
                        overallProgressRewards.add(
                            OverallProgressReward(
                                requiredPercentage = percentage,
                                name = name,
                                description = description,
                                commands = commands,
                                descriptions = descriptions
                            )
                        )
                    }
                } catch (e: NumberFormatException) {
                    plugin.logger.warning("无效的全世代进度百分比配置: $percentageKey")
                }
            }

            // 按进度百分比排序
            overallProgressRewards.sortBy { it.requiredPercentage }
        }

        // 加载付费世代完成奖励配置
        for (generation in plugin.generationManager.getAllGenerations()) {
            val premiumRewards = mutableListOf<GenerationReward>()
            val premiumCommands = plugin.premiumRewardConfig.getPremiumGenerationCompletionCommands(generation.id)
            val premiumDescriptions = plugin.premiumRewardConfig.getPremiumGenerationCompletionDescriptions(generation.id)

            if (premiumCommands.isNotEmpty()) {
                for (i in premiumCommands.indices) {
                    val description = if (i < premiumDescriptions.size) premiumDescriptions[i] else "付费完成${generation.name}"
                    premiumRewards.add(
                        GenerationReward(
                            generationId = generation.id,
                            requiredPercentage = 100,
                            command = premiumCommands[i],
                            description = description
                        )
                    )
                }
            }

            premiumGenerationRewards[generation.id] = premiumRewards

            // 加载付费世代进度奖励配置
            val premiumProgressRewards = mutableListOf<ProgressReward>()
            val premiumProgressPercentages = plugin.premiumRewardConfig.getPremiumGenerationProgressPercentages(generation.id)

            for (percentage in premiumProgressPercentages) {
                val premiumProgressCommands = plugin.premiumRewardConfig.getPremiumGenerationProgressCommands(generation.id, percentage)
                val premiumProgressDescriptions = plugin.premiumRewardConfig.getPremiumGenerationProgressDescriptions(generation.id, percentage)

                if (premiumProgressCommands.isNotEmpty()) {
                    for (i in premiumProgressCommands.indices) {
                        val description = if (i < premiumProgressDescriptions.size) premiumProgressDescriptions[i] else "付费进度${percentage}%"
                        premiumProgressRewards.add(
                            ProgressReward(
                                generationId = generation.id,
                                requiredPercentage = percentage,
                                command = premiumProgressCommands[i],
                                description = description
                            )
                        )
                    }
                }
            }

            premiumGenerationProgressRewards[generation.id] = premiumProgressRewards
        }

        // 加载付费全世代进度奖励配置
        if (plugin.premiumRewardConfig.isPremiumOverallProgressEnabled()) {
            val premiumOverallProgressPercentages = plugin.premiumRewardConfig.getPremiumOverallProgressPercentages()

            for (percentage in premiumOverallProgressPercentages) {
                val name = plugin.premiumRewardConfig.getPremiumOverallProgressName(percentage)
                val description = "付费全世代进度达到${percentage}%"
                val commands = plugin.premiumRewardConfig.getPremiumOverallProgressCommands(percentage)
                val descriptions = plugin.premiumRewardConfig.getPremiumOverallProgressDescriptions(percentage)

                if (commands.isNotEmpty()) {
                    premiumOverallProgressRewards.add(
                        OverallProgressReward(
                            requiredPercentage = percentage,
                            name = name,
                            description = description,
                            commands = commands,
                            descriptions = descriptions
                        )
                    )
                }
            }

            // 按进度百分比排序
            premiumOverallProgressRewards.sortBy { it.requiredPercentage }
        }

        plugin.logger.info("奖励配置加载完成 - 世代奖励: ${generationRewards.size}, 进度奖励: ${generationProgressRewards.values.sumOf { it.size }}, 全世代进度奖励: ${overallProgressRewards.size}")
        plugin.logger.info("付费奖励配置加载完成 - 付费世代奖励: ${premiumGenerationRewards.size}, 付费进度奖励: ${premiumGenerationProgressRewards.values.sumOf { it.size }}, 付费全世代进度奖励: ${premiumOverallProgressRewards.size}")
    }
    
    /**
     * 检查并给予世代完成奖励
     */
    fun checkGenerationRewards(player: Player, generationId: String, percentage: Int) {
        if (!plugin.config.enableGenerationRewards) return

        // 检查世代完成奖励（100%）
        val rewards = generationRewards[generationId] ?: emptyList()
        for (reward in rewards) {
            if (percentage >= reward.requiredPercentage) {
                val rewardKey = "gen_${generationId}_completion"

                if (canClaimReward(player, rewardKey)) {
                    giveReward(player, reward.command, reward.description)
                    setRewardCooldown(player, rewardKey)
                }
            }
        }

        // 检查世代进度奖励
        checkGenerationProgressRewards(player, generationId, percentage)
    }

    /**
     * 检查并给予世代进度奖励
     */
    private fun checkGenerationProgressRewards(player: Player, generationId: String, percentage: Int) {
        val progressRewards = generationProgressRewards[generationId] ?: return

        for (reward in progressRewards) {
            if (percentage >= reward.requiredPercentage) {
                val rewardKey = "progress_${generationId}_${reward.requiredPercentage}"

                if (!hasClaimedProgressRewardInternal(player, rewardKey)) {
                    giveReward(player, reward.command, reward.description)
                    markProgressRewardClaimed(player, rewardKey)

                    // 发送特殊的进度奖励消息
                    player.sendMessage("§6§l[进度奖励] §f${plugin.generationManager.getGeneration(generationId)?.name} 达到 ${reward.requiredPercentage}% 进度！")
                }
            }
        }
    }

    /**
     * 异步检查并给予世代完成奖励
     */
    fun checkGenerationRewardsAsync(player: Player, generationId: String, percentage: Int): CompletableFuture<Void> {
        return CompletableFuture.runAsync {
            checkGenerationRewards(player, generationId, percentage)
        }
    }

    /**
     * 检查并给予全世代进度奖励
     */
    fun checkOverallProgressRewards(player: Player, overallPercentage: Int) {
        if (!plugin.config.getConfig().getBoolean("rewards.overall-progress.enabled", false)) return

        for (reward in overallProgressRewards) {
            if (overallPercentage >= reward.requiredPercentage) {
                val rewardKey = "overall_${reward.requiredPercentage}"

                if (!hasClaimedOverallProgressReward(player, rewardKey)) {
                    // 执行所有奖励命令
                    for (command in reward.commands) {
                        giveReward(player, command, reward.name)
                    }

                    markOverallProgressRewardClaimed(player, rewardKey)

                    // 发送特殊的全世代进度奖励消息
                    player.sendMessage("§d§l[全世代进度奖励] §f恭喜达到全世代 ${reward.requiredPercentage}% 进度！")
                    player.sendMessage("§e获得称号: §6${reward.name}")
                }
            }
        }
    }
    



    
    /**
     * 检查玩家是否可以领取奖励
     */
    private fun canClaimReward(player: Player, rewardKey: String): Boolean {
        val playerCooldowns = rewardCooldowns.getOrPut(player.uniqueId) { mutableMapOf() }
        val lastClaim = playerCooldowns[rewardKey] ?: 0
        val cooldown = plugin.config.defaultRewardCooldown

        return System.currentTimeMillis() - lastClaim >= cooldown
    }

    /**
     * 检查玩家是否已领取进度奖励（内部方法）
     */
    private fun hasClaimedProgressRewardInternal(player: Player, rewardKey: String): Boolean {
        return claimedProgressRewards[player.uniqueId]?.contains(rewardKey) ?: false
    }

    /**
     * 标记进度奖励已领取
     */
    private fun markProgressRewardClaimed(player: Player, rewardKey: String) {
        val playerRewards = claimedProgressRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(rewardKey)
        saveRewardData()
    }



    /**
     * 标记全世代进度奖励已领取
     */
    private fun markOverallProgressRewardClaimed(player: Player, rewardKey: String) {
        val playerRewards = claimedOverallProgressRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(rewardKey)
        saveRewardData()
    }
    
    /**
     * 设置奖励冷却时间
     */
    private fun setRewardCooldown(player: Player, rewardKey: String) {
        val playerCooldowns = rewardCooldowns.getOrPut(player.uniqueId) { mutableMapOf() }
        playerCooldowns[rewardKey] = System.currentTimeMillis()
    }
    
    /**
     * 给予奖励
     */
    private fun giveReward(player: Player, command: String, description: String) {
        try {
            // 替换占位符
            val processedCommand = command.replace("{player}", player.name)
            
            // 执行命令
            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
            
            // 发送消息给玩家
            player.sendMessage("§a§l[奖励] §f你获得了奖励: §e$description")
            
            plugin.logger.info("给予玩家 ${player.name} 奖励: $description")
            
        } catch (e: Exception) {
            plugin.logger.severe("给予奖励时发生错误: ${e.message}")
        }
    }
    
    /**
     * 获取玩家的奖励状态
     */
    fun getPlayerRewardStatus(player: Player): RewardStatus {
        // TODO: 实现获取玩家实际的精灵收集数据
        // 这里需要与Cobblemon API交互
        
        return RewardStatus(
            totalGenerationRewards = generationRewards.values.sumOf { it.size },
            claimedGenerationRewards = 0, // TODO: 实现实际的已领取奖励统计
            nextReward = getNextAvailableReward(player)
        )
    }
    
    /**
     * 获取下一个可用奖励
     */
    private fun getNextAvailableReward(player: Player): String? {
        // TODO: 实现获取下一个可领取奖励的逻辑
        return "完成第一世代 25% 可获得钻石奖励"
    }

    /**
     * 检查玩家是否已领取指定世代的完成奖励
     */
    fun hasClaimedGenerationReward(player: Player, generationId: String): Boolean {
        return claimedGenerationRewards[player.uniqueId]?.contains(generationId) ?: false
    }

    /**
     * 检查是否可以领取全世代完成奖励
     */
    fun canClaimAllGenerationsReward(player: Player): Boolean {
        if (!plugin.config.getConfig().getBoolean("rewards.all-generations-completion.enabled", true)) {
            return false
        }

        // 检查是否已经领取过
        if (hasClaimedAllGenerationsReward(player)) {
            return false
        }

        // 检查是否完成了所有1-9世代
        return checkAllGenerationsCompleted(player)
    }

    /**
     * 检查玩家是否已领取全世代完成奖励
     */
    fun hasClaimedAllGenerationsReward(player: Player): Boolean {
        return claimedAllGenerationsRewards[player.uniqueId] ?: false
    }

    /**
     * 检查是否完成了所有1-9世代
     */
    private fun checkAllGenerationsCompleted(player: Player): Boolean {
        val allGenerations = plugin.generationManager.getAllGenerations()

        for (generation in allGenerations) {
            val progress = plugin.pokemonDetector.getGenerationProgress(player, generation.id)
            if (progress.percentage < 100) {
                return false
            }
        }

        return true
    }

    /**
     * 领取全世代完成奖励
     */
    fun claimAllGenerationsReward(player: Player): Boolean {
        if (!canClaimAllGenerationsReward(player)) {
            return false
        }

        // 执行奖励命令
        val rewardCommands = getAllGenerationsRewardCommands()
        rewardCommands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 记录已领取
        claimedAllGenerationsRewards[player.uniqueId] = true

        // 立即保存奖励数据，防止服务器崩溃导致数据丢失
        saveRewardData()

        // 发送消息
        player.sendMessage("§d§l[精灵大师] §f恭喜完成所有1-9世代收集！")
        player.sendMessage("§e精灵大师奖励已发放到你的背包中！")

        plugin.logger.info("玩家 ${player.name} 领取了全世代完成奖励")

        return true
    }

    /**
     * 获取全世代完成奖励命令列表
     */
    private fun getAllGenerationsRewardCommands(): List<String> {
        return plugin.config.getConfig().getStringList("rewards.all-generations-completion.commands").ifEmpty {
            listOf(
                "give {player} minecraft:diamond 100",
                "give {player} minecraft:netherite_ingot 20",
                "give {player} minecraft:beacon 5",
                "give {player} minecraft:dragon_egg 1",
                "tellraw {player} {\"text\":\"恭喜成为精灵大师！\",\"color\":\"light_purple\",\"bold\":true}"
            )
        }
    }





    /**
     * 给予玩家世代完成奖励
     */
    fun giveGenerationReward(player: Player, generationId: String): Boolean {
        // 检查是否已领取
        if (hasClaimedGenerationReward(player, generationId)) {
            return false
        }

        // 检查是否真的完成了
        val progress = plugin.pokemonDetector.getGenerationProgress(player, generationId)
        if (progress.percentage < 100) {
            return false
        }

        // 获取世代信息
        val generation = plugin.generationManager.getGeneration(generationId)
        if (generation == null) {
            return false
        }

        // 执行奖励命令
        val rewardCommands = getGenerationCompletionCommands(generationId)
        rewardCommands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)
                .replace("{generation}", generation.name)
                .replace("{generation_id}", generationId)

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 记录已领取
        val playerRewards = claimedGenerationRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(generationId)

        // 立即保存奖励数据，防止服务器崩溃导致数据丢失
        saveRewardData()

        // 发送消息
        player.sendMessage("§a§l[图鉴奖励] §f恭喜完成${generation.name}！")
        player.sendMessage("§e奖励已发放到你的背包中！")

        plugin.logger.info("玩家 ${player.name} 领取了${generation.name}完成奖励")

        return true
    }

    /**
     * 获取世代完成奖励命令（从配置文件读取）
     */
    private fun getGenerationCompletionCommands(generationId: String): List<String> {
        val config = plugin.config.getConfig()
        return config.getStringList("rewards.generation.$generationId.completion.commands")
    }

    /**
     * 保存已领取奖励数据到文件
     */
    fun saveRewardData() {
        try {
            val dataFile = java.io.File(plugin.dataFolder, "claimed-rewards.yml")
            val config = org.bukkit.configuration.file.YamlConfiguration()

            // 保存世代完成奖励数据
            claimedGenerationRewards.forEach { (uuid, rewards) ->
                config.set("generation-rewards.$uuid", rewards.toList())
            }

            // 保存全世代完成奖励数据
            claimedAllGenerationsRewards.forEach { (uuid, claimed) ->
                config.set("all-generations-rewards.$uuid", claimed)
            }

            // 保存进度奖励数据
            claimedProgressRewards.forEach { (uuid, rewards) ->
                config.set("progress-rewards.$uuid", rewards.toList())
            }

            // 保存全世代进度奖励数据
            claimedOverallProgressRewards.forEach { (uuid, rewards) ->
                config.set("overall-progress-rewards.$uuid", rewards.toList())
            }

            // 保存付费世代完成奖励数据
            claimedPremiumGenerationRewards.forEach { (uuid, rewards) ->
                config.set("premium-generation-rewards.$uuid", rewards.toList())
            }

            // 保存付费全世代完成奖励数据
            claimedPremiumAllGenerationsRewards.forEach { (uuid, claimed) ->
                config.set("premium-all-generations-rewards.$uuid", claimed)
            }

            // 保存付费进度奖励数据
            claimedPremiumProgressRewards.forEach { (uuid, rewards) ->
                config.set("premium-progress-rewards.$uuid", rewards.toList())
            }

            // 保存付费全世代进度奖励数据
            claimedPremiumOverallProgressRewards.forEach { (uuid, rewards) ->
                config.set("premium-overall-progress-rewards.$uuid", rewards.toList())
            }

            config.save(dataFile)
        } catch (e: Exception) {
            plugin.logger.severe("保存奖励数据失败: ${e.message}")
        }
    }

    /**
     * 从文件加载已领取奖励数据
     */
    fun loadRewardData() {
        try {
            val dataFile = java.io.File(plugin.dataFolder, "claimed-rewards.yml")
            if (!dataFile.exists()) {
                return
            }

            val config = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(dataFile)

            // 加载世代奖励数据
            config.getConfigurationSection("generation-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val rewards = config.getStringList("generation-rewards.$uuidString").toMutableSet()
                    claimedGenerationRewards[uuid] = rewards
                } catch (e: Exception) {
                    plugin.logger.warning("加载世代奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载全世代奖励数据
            config.getConfigurationSection("all-generations-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val claimed = config.getBoolean("all-generations-rewards.$uuidString", false)
                    claimedAllGenerationsRewards[uuid] = claimed
                } catch (e: Exception) {
                    plugin.logger.warning("加载全世代奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载进度奖励数据
            config.getConfigurationSection("progress-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val rewards = config.getStringList("progress-rewards.$uuidString").toMutableSet()
                    claimedProgressRewards[uuid] = rewards
                } catch (e: Exception) {
                    plugin.logger.warning("加载进度奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载全世代进度奖励数据
            config.getConfigurationSection("overall-progress-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val rewards = config.getStringList("overall-progress-rewards.$uuidString").toMutableSet()
                    claimedOverallProgressRewards[uuid] = rewards
                } catch (e: Exception) {
                    plugin.logger.warning("加载全世代进度奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载付费世代奖励数据
            config.getConfigurationSection("premium-generation-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val rewards = config.getStringList("premium-generation-rewards.$uuidString").toMutableSet()
                    claimedPremiumGenerationRewards[uuid] = rewards
                } catch (e: Exception) {
                    plugin.logger.warning("加载付费世代奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载付费全世代奖励数据
            config.getConfigurationSection("premium-all-generations-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val claimed = config.getBoolean("premium-all-generations-rewards.$uuidString", false)
                    claimedPremiumAllGenerationsRewards[uuid] = claimed
                } catch (e: Exception) {
                    plugin.logger.warning("加载付费全世代奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载付费进度奖励数据
            config.getConfigurationSection("premium-progress-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val rewards = config.getStringList("premium-progress-rewards.$uuidString").toMutableSet()
                    claimedPremiumProgressRewards[uuid] = rewards
                } catch (e: Exception) {
                    plugin.logger.warning("加载付费进度奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            // 加载付费全世代进度奖励数据
            config.getConfigurationSection("premium-overall-progress-rewards")?.getKeys(false)?.forEach { uuidString ->
                try {
                    val uuid = java.util.UUID.fromString(uuidString)
                    val rewards = config.getStringList("premium-overall-progress-rewards.$uuidString").toMutableSet()
                    claimedPremiumOverallProgressRewards[uuid] = rewards
                } catch (e: Exception) {
                    plugin.logger.warning("加载付费全世代进度奖励数据时出错: $uuidString - ${e.message}")
                }
            }

            plugin.logger.info("已加载奖励数据 - 世代: ${claimedGenerationRewards.size}, 全世代: ${claimedAllGenerationsRewards.size}, 进度: ${claimedProgressRewards.size}, 全世代进度: ${claimedOverallProgressRewards.size}")
            plugin.logger.info("已加载付费奖励数据 - 付费世代: ${claimedPremiumGenerationRewards.size}, 付费全世代: ${claimedPremiumAllGenerationsRewards.size}, 付费进度: ${claimedPremiumProgressRewards.size}, 付费全世代进度: ${claimedPremiumOverallProgressRewards.size}")
        } catch (e: Exception) {
            plugin.logger.severe("加载奖励数据失败: ${e.message}")
        }
    }

    /**
     * 重置玩家的世代奖励记录（管理员命令用）
     */
    fun resetPlayerGenerationRewards(player: org.bukkit.entity.Player) {
        claimedGenerationRewards.remove(player.uniqueId)
        saveRewardData()
        plugin.logger.info("已重置玩家 ${player.name} 的世代奖励记录")
    }

    /**
     * 重置指定世代的奖励记录（管理员命令用）
     */
    fun resetGenerationRewards(generationId: String) {
        claimedGenerationRewards.values.forEach { rewards ->
            rewards.remove(generationId)
        }
        saveRewardData()
        plugin.logger.info("已重置世代 $generationId 的奖励记录")
    }

    /**
     * 获取玩家已领取的世代奖励列表
     */
    fun getClaimedGenerationRewards(player: org.bukkit.entity.Player): Set<String> {
        return claimedGenerationRewards[player.uniqueId]?.toSet() ?: emptySet()
    }

    /**
     * 检查玩家是否已领取指定的全世代进度奖励（公共方法）
     */
    fun hasClaimedOverallProgressReward(player: org.bukkit.entity.Player, rewardKey: String): Boolean {
        return claimedOverallProgressRewards[player.uniqueId]?.contains(rewardKey) ?: false
    }

    /**
     * 检查玩家是否已领取指定的进度奖励（公共方法）
     */
    fun hasClaimedProgressReward(player: org.bukkit.entity.Player, rewardKey: String): Boolean {
        return claimedProgressRewards[player.uniqueId]?.contains(rewardKey) ?: false
    }

    /**
     * 手动领取单个进度奖励
     */
    fun claimProgressReward(player: Player, generationId: String, percentage: Int): Boolean {
        val rewardKey = "progress_${generationId}_${percentage}"

        // 检查是否已经领取
        if (hasClaimedProgressReward(player, rewardKey)) {
            return false
        }

        // 检查当前进度是否达到要求
        val currentProgress = plugin.pokemonDetector.getGenerationProgress(player, generationId)
        if (currentProgress.percentage < percentage) {
            return false
        }

        // 获取奖励配置
        val config = plugin.config.getConfig()
        val commands = config.getStringList("rewards.generation.$generationId.progress.$percentage.commands")
        val descriptions = config.getStringList("rewards.generation.$generationId.progress.$percentage.descriptions")

        if (commands.isEmpty()) {
            return false
        }

        // 执行奖励命令
        val generation = plugin.generationManager.getGeneration(generationId)
        commands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)
                .replace("{generation}", generation?.name ?: generationId)
                .replace("{generation_id}", generationId)
                .replace("{percentage}", percentage.toString())

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 标记为已领取
        val playerRewards = claimedProgressRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(rewardKey)
        saveRewardData()

        // 发送消息
        player.sendMessage("§6§l[进度奖励] §f${generation?.name ?: generationId} 达到 ${percentage}% 进度！")
        descriptions.forEach { description ->
            player.sendMessage("§e获得: $description")
        }

        plugin.logger.info("玩家 ${player.name} 手动领取了${generationId} ${percentage}%进度奖励")

        return true
    }

    /**
     * 批量领取可领取的进度奖励
     */
    fun claimAvailableProgressRewards(player: Player, generationId: String): List<Int> {
        val claimedPercentages = mutableListOf<Int>()
        val currentProgress = plugin.pokemonDetector.getGenerationProgress(player, generationId)

        // 获取所有进度奖励配置
        val config = plugin.config.getConfig()
        val progressSection = config.getConfigurationSection("rewards.generation.$generationId.progress")

        if (progressSection != null) {
            val availableRewards = mutableListOf<Int>()

            // 收集所有可领取的进度奖励
            for (percentageKey in progressSection.getKeys(false)) {
                try {
                    val percentage = percentageKey.toInt()
                    val rewardKey = "progress_${generationId}_${percentage}"

                    // 检查是否达到进度且未领取
                    if (currentProgress.percentage >= percentage && !hasClaimedProgressReward(player, rewardKey)) {
                        availableRewards.add(percentage)
                    }
                } catch (e: NumberFormatException) {
                    // 忽略无效的百分比配置
                }
            }

            // 按百分比排序并领取
            availableRewards.sorted().forEach { percentage ->
                if (claimProgressReward(player, generationId, percentage)) {
                    claimedPercentages.add(percentage)
                }
            }
        }

        return claimedPercentages
    }

    /**
     * 手动领取单个全世代进度奖励
     */
    fun claimOverallProgressReward(player: Player, percentage: Int): Boolean {
        val rewardKey = "overall_$percentage"

        // 检查是否已经领取
        if (hasClaimedOverallProgressReward(player, rewardKey)) {
            return false
        }

        // 检查当前全世代进度是否达到要求
        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
        if (allProgress.overallPercentage < percentage) {
            return false
        }

        // 查找对应的奖励配置
        val reward = overallProgressRewards.find { it.requiredPercentage == percentage }
        if (reward == null) {
            return false
        }

        // 执行奖励命令
        reward.commands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)
                .replace("{percentage}", percentage.toString())

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 标记为已领取
        val playerRewards = claimedOverallProgressRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(rewardKey)
        saveRewardData()

        // 发送消息
        player.sendMessage("§d§l[全世代进度奖励] §f恭喜达到全世代 ${percentage}% 进度！")
        player.sendMessage("§e获得称号: §6${reward.name}")
        reward.descriptions.forEach { description ->
            player.sendMessage("§e获得: $description")
        }

        plugin.logger.info("玩家 ${player.name} 手动领取了全世代 ${percentage}%进度奖励")

        return true
    }

    /**
     * 批量领取可领取的全世代进度奖励
     */
    fun claimAvailableOverallProgressRewards(player: Player): List<Int> {
        val claimedPercentages = mutableListOf<Int>()
        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

        // 检查配置是否启用
        if (!plugin.config.getConfig().getBoolean("rewards.overall-progress.enabled", false)) {
            return claimedPercentages
        }

        val availableRewards = mutableListOf<Int>()

        // 收集所有可领取的全世代进度奖励
        for (reward in overallProgressRewards) {
            val rewardKey = "overall_${reward.requiredPercentage}"

            // 检查是否达到进度且未领取
            if (allProgress.overallPercentage >= reward.requiredPercentage && !hasClaimedOverallProgressReward(player, rewardKey)) {
                availableRewards.add(reward.requiredPercentage)
            }
        }

        // 按百分比排序并领取
        availableRewards.sorted().forEach { percentage ->
            if (claimOverallProgressReward(player, percentage)) {
                claimedPercentages.add(percentage)
            }
        }

        return claimedPercentages
    }

    // === 付费奖励相关方法 ===

    /**
     * 检查玩家是否已领取指定付费世代的完成奖励
     */
    fun hasClaimedPremiumGenerationReward(player: Player, generationId: String): Boolean {
        return claimedPremiumGenerationRewards[player.uniqueId]?.contains(generationId) ?: false
    }

    /**
     * 检查是否可以领取付费全世代完成奖励
     */
    fun canClaimPremiumAllGenerationsReward(player: Player): Boolean {
        if (!plugin.premiumRewardConfig.isPremiumAllGenerationsCompletionEnabled()) {
            return false
        }

        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            return false
        }

        // 检查是否已经领取过
        if (hasClaimedPremiumAllGenerationsReward(player)) {
            return false
        }

        // 检查是否完成了所有1-9世代
        return checkAllGenerationsCompleted(player)
    }

    /**
     * 检查玩家是否已领取付费全世代完成奖励
     */
    fun hasClaimedPremiumAllGenerationsReward(player: Player): Boolean {
        return claimedPremiumAllGenerationsRewards[player.uniqueId] ?: false
    }

    /**
     * 领取付费全世代完成奖励
     */
    fun claimPremiumAllGenerationsReward(player: Player): Boolean {
        if (!canClaimPremiumAllGenerationsReward(player)) {
            return false
        }

        // 执行付费奖励命令
        val rewardCommands = getPremiumAllGenerationsRewardCommands()
        rewardCommands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 记录已领取
        claimedPremiumAllGenerationsRewards[player.uniqueId] = true

        // 立即保存奖励数据，防止服务器崩溃导致数据丢失
        saveRewardData()

        // 发送消息
        player.sendMessage("§6§l[付费精灵大师] §f恭喜完成所有1-9世代收集！")
        player.sendMessage("§e付费精灵大师奖励已发放到你的背包中！")

        plugin.logger.info("玩家 ${player.name} 领取了付费全世代完成奖励")

        return true
    }

    /**
     * 获取付费全世代完成奖励命令列表
     */
    private fun getPremiumAllGenerationsRewardCommands(): List<String> {
        return plugin.premiumRewardConfig.getPremiumAllGenerationsCompletionCommands().ifEmpty {
            listOf(
                "give {player} minecraft:diamond 200",
                "give {player} minecraft:netherite_ingot 50",
                "give {player} minecraft:beacon 10",
                "give {player} minecraft:dragon_egg 3",
                "tellraw {player} {\"text\":\"恭喜成为付费精灵大师！\",\"color\":\"gold\",\"bold\":true}"
            )
        }
    }

    /**
     * 给予玩家付费世代完成奖励
     */
    fun givePremiumGenerationReward(player: Player, generationId: String): Boolean {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            return false
        }

        // 检查是否已领取
        if (hasClaimedPremiumGenerationReward(player, generationId)) {
            return false
        }

        // 检查是否真的完成了
        val progress = plugin.pokemonDetector.getGenerationProgress(player, generationId)
        if (progress.percentage < 100) {
            return false
        }

        // 获取世代信息
        val generation = plugin.generationManager.getGeneration(generationId)
        if (generation == null) {
            return false
        }

        // 执行付费奖励命令
        val rewardCommands = getPremiumGenerationCompletionCommands(generationId)
        rewardCommands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)
                .replace("{generation}", generation.name)
                .replace("{generation_id}", generationId)

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 记录已领取
        val playerRewards = claimedPremiumGenerationRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(generationId)

        // 立即保存奖励数据，防止服务器崩溃导致数据丢失
        saveRewardData()

        // 发送消息
        player.sendMessage("§6§l[付费图鉴奖励] §f恭喜完成${generation.name}！")
        player.sendMessage("§e付费奖励已发放到你的背包中！")

        plugin.logger.info("玩家 ${player.name} 领取了${generation.name}付费完成奖励")

        return true
    }

    /**
     * 获取付费世代完成奖励命令列表
     */
    private fun getPremiumGenerationCompletionCommands(generationId: String): List<String> {
        return plugin.premiumRewardConfig.getPremiumGenerationCompletionCommands(generationId).ifEmpty {
            listOf(
                "give {player} minecraft:diamond 50",
                "give {player} minecraft:netherite_ingot 10",
                "tellraw {player} {\"text\":\"恭喜完成付费${generationId}！\",\"color\":\"gold\",\"bold\":true}"
            )
        }
    }

    /**
     * 检查玩家是否已领取指定的付费进度奖励
     */
    fun hasClaimedPremiumProgressReward(player: Player, rewardKey: String): Boolean {
        return claimedPremiumProgressRewards[player.uniqueId]?.contains(rewardKey) ?: false
    }

    /**
     * 检查玩家是否已领取指定的付费全世代进度奖励
     */
    fun hasClaimedPremiumOverallProgressReward(player: Player, rewardKey: String): Boolean {
        return claimedPremiumOverallProgressRewards[player.uniqueId]?.contains(rewardKey) ?: false
    }

    /**
     * 手动领取单个付费进度奖励
     */
    fun claimPremiumProgressReward(player: Player, generationId: String, percentage: Int): Boolean {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            return false
        }

        val rewardKey = "premium_progress_${generationId}_${percentage}"

        // 检查是否已经领取
        if (hasClaimedPremiumProgressReward(player, rewardKey)) {
            return false
        }

        // 检查当前进度是否达到要求
        val currentProgress = plugin.pokemonDetector.getGenerationProgress(player, generationId)
        if (currentProgress.percentage < percentage) {
            return false
        }

        // 使用新的付费奖励配置
        val commands = plugin.premiumRewardConfig.getPremiumGenerationProgressCommands(generationId, percentage)
        val descriptions = plugin.premiumRewardConfig.getPremiumGenerationProgressDescriptions(generationId, percentage)

        if (commands.isEmpty()) {
            return false
        }

        // 执行付费奖励命令
        val generation = plugin.generationManager.getGeneration(generationId)
        commands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)
                .replace("{generation}", generation?.name ?: generationId)
                .replace("{generation_id}", generationId)
                .replace("{percentage}", percentage.toString())

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 标记为已领取
        val playerRewards = claimedPremiumProgressRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(rewardKey)
        saveRewardData()

        // 发送消息
        player.sendMessage("§6§l[付费进度奖励] §f${generation?.name ?: generationId} 达到 ${percentage}% 进度！")
        descriptions.forEach { description ->
            player.sendMessage("§e获得: $description")
        }

        plugin.logger.info("玩家 ${player.name} 手动领取了${generationId} ${percentage}%付费进度奖励")

        return true
    }

    /**
     * 批量领取可领取的付费进度奖励
     */
    fun claimAvailablePremiumProgressRewards(player: Player, generationId: String): List<Int> {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            return emptyList()
        }

        val claimedPercentages = mutableListOf<Int>()
        val currentProgress = plugin.pokemonDetector.getGenerationProgress(player, generationId)

        // 使用新的付费奖励配置
        val progressPercentages = plugin.premiumRewardConfig.getPremiumGenerationProgressPercentages(generationId)

        if (progressPercentages.isNotEmpty()) {
            val availableRewards = mutableListOf<Int>()

            // 收集所有可领取的付费进度奖励
            for (percentage in progressPercentages) {
                val rewardKey = "premium_progress_${generationId}_${percentage}"

                // 检查是否达到进度且未领取
                if (currentProgress.percentage >= percentage && !hasClaimedPremiumProgressReward(player, rewardKey)) {
                    availableRewards.add(percentage)
                }
            }

            // 按百分比排序并领取
            availableRewards.sorted().forEach { percentage ->
                if (claimPremiumProgressReward(player, generationId, percentage)) {
                    claimedPercentages.add(percentage)
                }
            }
        }

        return claimedPercentages
    }

    /**
     * 手动领取单个付费全世代进度奖励
     */
    fun claimPremiumOverallProgressReward(player: Player, percentage: Int): Boolean {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            return false
        }

        val rewardKey = "premium_overall_$percentage"

        // 检查是否已经领取
        if (hasClaimedPremiumOverallProgressReward(player, rewardKey)) {
            return false
        }

        // 检查当前全世代进度是否达到要求
        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
        if (allProgress.overallPercentage < percentage) {
            return false
        }

        // 查找对应的付费奖励配置
        val reward = premiumOverallProgressRewards.find { it.requiredPercentage == percentage }
        if (reward == null) {
            return false
        }

        // 执行付费奖励命令
        reward.commands.forEach { command ->
            val processedCommand = command.replace("{player}", player.name)
                .replace("{percentage}", percentage.toString())

            Bukkit.getScheduler().runTask(plugin, Runnable {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand)
            })
        }

        // 标记为已领取
        val playerRewards = claimedPremiumOverallProgressRewards.getOrPut(player.uniqueId) { mutableSetOf() }
        playerRewards.add(rewardKey)
        saveRewardData()

        // 发送消息
        player.sendMessage("§6§l[付费全世代进度奖励] §f恭喜达到全世代 ${percentage}% 进度！")
        player.sendMessage("§e获得称号: §6${reward.name}")
        reward.descriptions.forEach { description ->
            player.sendMessage("§e获得: $description")
        }

        plugin.logger.info("玩家 ${player.name} 手动领取了全世代 ${percentage}%付费进度奖励")

        return true
    }

    /**
     * 批量领取可领取的付费全世代进度奖励
     */
    fun claimAvailablePremiumOverallProgressRewards(player: Player): List<Int> {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            return emptyList()
        }

        val claimedPercentages = mutableListOf<Int>()
        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

        // 检查付费全世代进度奖励是否启用
        if (!plugin.premiumRewardConfig.isPremiumOverallProgressEnabled()) {
            return claimedPercentages
        }

        val availableRewards = mutableListOf<Int>()

        // 收集所有可领取的付费全世代进度奖励
        for (reward in premiumOverallProgressRewards) {
            val rewardKey = "premium_overall_${reward.requiredPercentage}"

            // 检查是否达到进度且未领取
            if (allProgress.overallPercentage >= reward.requiredPercentage && !hasClaimedPremiumOverallProgressReward(player, rewardKey)) {
                availableRewards.add(reward.requiredPercentage)
            }
        }

        // 按百分比排序并领取
        availableRewards.sorted().forEach { percentage ->
            if (claimPremiumOverallProgressReward(player, percentage)) {
                claimedPercentages.add(percentage)
            }
        }

        return claimedPercentages
    }
}

/**
 * 世代奖励数据类
 */
data class GenerationReward(
    val generationId: String,
    val requiredPercentage: Int,
    val command: String,
    val description: String
)

/**
 * 进度奖励数据类
 */
data class ProgressReward(
    val generationId: String,
    val requiredPercentage: Int,
    val command: String,
    val description: String
)

/**
 * 全世代进度奖励数据类
 */
data class OverallProgressReward(
    val requiredPercentage: Int,
    val name: String,
    val description: String,
    val commands: List<String>,
    val descriptions: List<String>
)

/**
 * 奖励状态数据类
 */
data class RewardStatus(
    val totalGenerationRewards: Int,
    val claimedGenerationRewards: Int,
    val nextReward: String?
)
