/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第五世代精灵名称映射 (494-649)
 * 包含合众地区的所有精灵
 */
object PokemonGen5Mapping {
    
    /**
     * 获取第五世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第五世代 (494-649)
            "victini" to "比克提尼",
            "snivy" to "藤藤蛇",
            "servine" to "青藤蛇",
            "serperior" to "君主蛇",
            "tepig" to "暖暖猪",
            "pignite" to "炒炒猪",
            "emboar" to "炎武王",
            "oshawott" to "水水獭",
            "dewott" to "双刃丸",
            "samurott" to "大剑鬼",
            "patrat" to "探探鼠",
            "watchog" to "步哨鼠",
            "lillipup" to "小约克",
            "herdier" to "哈约克",
            "stoutland" to "长毛狗",
            "purrloin" to "扒手猫",
            "liepard" to "酷豹",
            "pansage" to "花椰猴",
            "simisage" to "花椰猿",
            "pansear" to "爆香猴",
            "simisear" to "爆香猿",
            "panpour" to "冷水猴",
            "simipour" to "冷水猿",
            "munna" to "食梦梦",
            "musharna" to "梦梦蚀",
            "pidove" to "豆豆鸽",
            "tranquill" to "咕咕鸽",
            "unfezant" to "高傲雉鸡",
            "blitzle" to "斑斑马",
            "zebstrika" to "雷电斑马",
            "roggenrola" to "石丸子",
            "boldore" to "地幔岩",
            "gigalith" to "庞岩怪",
            "woobat" to "滚滚蝙蝠",
            "swoobat" to "心蝙蝠",
            "drilbur" to "螺钉地鼠",
            "excadrill" to "龙头地鼠",
            "audino" to "差不多娃娃",
            "timburr" to "搬运小匠",
            "gurdurr" to "铁骨土人",
            "conkeldurr" to "修建老匠",
            "tympole" to "圆蝌蚪",
            "palpitoad" to "蓝蟾蜍",
            "seismitoad" to "蟾蜍王",
            "throh" to "投摔鬼",
            "sawk" to "打击鬼",
            "sewaddle" to "虫宝包",
            "swadloon" to "宝包茧",
            "leavanny" to "保姆虫",
            "venipede" to "百足蜈蚣",
            "whirlipede" to "车轮球",
            "scolipede" to "蜈蚣王",
            "cottonee" to "木棉球",
            "whimsicott" to "风妖精",
            "petilil" to "百合根娃娃",
            "lilligant" to "裙儿小姐",
            "basculin" to "野蛮鲈鱼",
            "sandile" to "黑眼鳄",
            "krokorok" to "混混鳄",
            "krookodile" to "流氓鳄",
            "darumaka" to "火红不倒翁",
            "darmanitan" to "达摩狒狒",
            "maractus" to "沙铃仙人掌",
            "dwebble" to "石居蟹",
            "crustle" to "岩殿居蟹",
            "scraggy" to "滑滑小蜥",
            "scrafty" to "头巾混混",
            "sigilyph" to "象征鸟",
            "yamask" to "哭哭面具",
            "cofagrigus" to "死神棺",
            "tirtouga" to "原盖海龟",
            "carracosta" to "肋骨海龟",
            "archen" to "始祖小鸟",
            "archeops" to "始祖大鸟",
            "trubbish" to "破破袋",
            "garbodor" to "灰尘山",
            "zorua" to "索罗亚",
            "zoroark" to "索罗亚克",
            "minccino" to "泡沫栗鼠",
            "cinccino" to "奇诺栗鼠",
            "gothita" to "哥德宝宝",
            "gothorita" to "哥德小童",
            "gothitelle" to "哥德小姐",
            "solosis" to "单卵细胞球",
            "duosion" to "双卵细胞球",
            "reuniclus" to "人造细胞卵",
            "ducklett" to "鸭宝宝",
            "swanna" to "舞天鹅",
            "vanillite" to "迷你冰",
            "vanillish" to "多多冰",
            "vanilluxe" to "双倍多多冰",
            "deerling" to "四季鹿",
            "sawsbuck" to "萌芽鹿",
            "emolga" to "电飞鼠",
            "karrablast" to "盖盖虫",
            "escavalier" to "骑士蜗牛",
            "foongus" to "哎呀球菇",
            "amoonguss" to "败露球菇",
            "frillish" to "轻飘飘",
            "jellicent" to "胖嘟嘟",
            "alomomola" to "保姆曼波",
            "joltik" to "电电虫",
            "galvantula" to "电蜘蛛",
            "ferroseed" to "种子铁球",
            "ferrothorn" to "坚果哑铃",
            "klink" to "齿轮儿",
            "klang" to "齿轮组",
            "klinklang" to "齿轮怪",
            "tynamo" to "麻麻小鱼",
            "eelektrik" to "麻麻鳗",
            "eelektross" to "麻麻鳗鱼王",
            "elgyem" to "小灰怪",
            "beheeyem" to "大宇怪",
            "litwick" to "烛光灵",
            "lampent" to "灯火幽灵",
            "chandelure" to "水晶灯火灵",
            "axew" to "牙牙",
            "fraxure" to "斧牙龙",
            "haxorus" to "双斧战龙",
            "cubchoo" to "喷嚏熊",
            "beartic" to "冻原熊",
            "cryogonal" to "几何雪花",
            "shelmet" to "小嘴蜗",
            "accelgor" to "敏捷虫",
            "stunfisk" to "泥巴鱼",
            "mienfoo" to "功夫鼬",
            "mienshao" to "师父鼬",
            "druddigon" to "赤面龙",
            "golett" to "泥偶小人",
            "golurk" to "泥偶巨人",
            "pawniard" to "驹刀小兵",
            "bisharp" to "劈斩司令",
            "bouffalant" to "爆炸头水牛",
            "rufflet" to "毛头小鹰",
            "braviary" to "勇士雄鹰",
            "vullaby" to "秃鹰丫头",
            "mandibuzz" to "秃鹰娜",
            "heatmor" to "熔蚁兽",
            "durant" to "铁蚁",
            "deino" to "单首龙",
            "zweilous" to "双首暴龙",
            "hydreigon" to "三首恶龙",
            "larvesta" to "燃烧虫",
            "volcarona" to "火神蛾",
            "cobalion" to "勾帕路翁",
            "terrakion" to "代拉基翁",
            "virizion" to "毕力吉翁",
            "tornadus" to "龙卷云",
            "thundurus" to "雷电云",
            "reshiram" to "莱希拉姆",
            "zekrom" to "捷克罗姆",
            "landorus" to "土地云",
            "kyurem" to "酋雷姆",
            "keldeo" to "凯路迪欧",
            "meloetta" to "美洛耶塔",
            "genesect" to "盖诺赛克特",
    )
}
