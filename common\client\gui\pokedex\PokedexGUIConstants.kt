/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.client.gui.pokedex

object PokedexGUIConstants {
    const val BASE_WIDTH = 345
    const val BASE_HEIGHT = 207
    const val HALF_OVERLAY_WIDTH = 139
    const val HALF_OVERLAY_HEIGHT = 163
    const val HEADER_BAR_HEIGHT = 11
    const val POKEMON_DESCRIPTION_HEIGHT = 42
    const val POKEMON_DESCRIPTION_PADDING = 9

    const val TAB_ICON_SIZE = 16

    const val POKEMON_FORMS_WIDTH = 80
    const val POKEMON_FORMS_HEIGHT = 70

    const val SCALE = 0.5F

    //PokemonInfoWidget
    const val POKEMON_PORTRAIT_WIDTH = 137
    const val POKEMON_PORTRAIT_HEIGHT = 68
    const val PORTRAIT_POKE_BALL_WIDTH = 109
    const val PORTRAIT_POKE_BALL_HEIGHT = 68
    const val SCROLL_BAR_WIDTH = 5
    const val TAB_DESCRIPTION = 0
    const val TAB_ABILITIES = 1
    const val TAB_SIZE = 2
    const val TAB_STATS = 3
    const val TAB_DROPS = 4

    // RowScrollingWidget
    const val SCROLL_BASE_HEIGHT = 141
    const val SCROLL_SLOT_SIZE = 25
    const val SCROLL_SLOT_SPACING = 2
}