/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.api.pokemon.effect

import com.cobblemon.mod.common.pokemon.Pokemon
import net.minecraft.server.level.ServerPlayer

/**
 * Interface for all ShoulderEffects
 *
 * <AUTHOR>
 * @since 2022-01-26
 */
interface ShoulderEffect {
    fun applyEffect(pokemon: <PERSON><PERSON><PERSON>, player: ServerPlayer, isLeft: <PERSON>olean)
    fun removeEffect(pokemon: <PERSON><PERSON><PERSON>, player: ServerPlayer, isLeft: <PERSON><PERSON><PERSON>)
}