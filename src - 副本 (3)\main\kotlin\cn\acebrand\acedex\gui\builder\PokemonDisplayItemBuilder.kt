/**
 * AceDex - 精灵图鉴插件
 * 精灵显示物品构建器
 */

package cn.acebrand.acedex.gui.builder

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.util.LGMenuIntegration
import com.cobblemon.mod.common.pokemon.Pokemon
import com.cobblemon.mod.common.pokemon.Species
import org.bukkit.enchantments.Enchantment
import org.bukkit.inventory.ItemFlag
import org.bukkit.inventory.ItemStack
import org.bukkit.inventory.meta.ItemMeta

/**
 * 精灵显示物品构建器
 * 类似于 LGPokemonMenu 的 PokemonDisplayItemBuilder
 */
class PokemonDisplayItemBuilder(
    private val plugin: AceDex,
    private val nameTemplate: String,
    private val loreTemplate: List<String>
) {
    
    /**
     * 从 Pokemon 对象构建物品
     */
    fun build(pokemon: Pokemon): ItemStack? {
        return try {
            // 使用 LGMenuIntegration 创建基础物品
            val baseItem = LGMenuIntegration.createPokemonItem(pokemon)
            if (baseItem != null) {
                // 应用自定义显示信息
                applyCustomDisplay(baseItem, pokemon)
                return baseItem
            }
            null
        } catch (e: Exception) {
            plugin.logger.warning("构建精灵物品失败: ${e.message}")
            null
        }
    }
    
    /**
     * 从精灵名称构建物品
     */
    fun build(pokemonName: String, hasCaught: Boolean = true): ItemStack? {
        return try {
            val species = LGMenuIntegration.getSpeciesByName(pokemonName)
            if (species != null) {
                val pokemon = LGMenuIntegration.createPokemon(species, false, 1)
                val baseItem = LGMenuIntegration.createPokemonItem(pokemon)
                if (baseItem != null) {
                    // 应用自定义显示信息
                    applyCustomDisplay(baseItem, pokemon, hasCaught)
                    return baseItem
                }
            }
            null
        } catch (e: Exception) {
            plugin.logger.warning("构建精灵物品失败: ${e.message}")
            null
        }
    }
    
    /**
     * 应用自定义显示信息
     */
    private fun applyCustomDisplay(item: ItemStack, pokemon: Pokemon, hasCaught: Boolean = true) {
        val meta = item.itemMeta ?: return
        
        try {
            // 构建占位符映射
            val placeholders = buildPlaceholders(pokemon, hasCaught)
            
            // 应用名称模板
            meta.setDisplayName(replacePlaceholders(nameTemplate, placeholders))
            
            // 应用描述模板
            val processedLore = loreTemplate.map { line ->
                replacePlaceholders(line, placeholders)
            }
            meta.lore = processedLore
            
            // 添加附魔效果（如果已捕获）
            if (hasCaught) {
                meta.addEnchant(Enchantment.LURE, 1, true)
                // 不隐藏附魔效果，让玩家能看到闪光
                plugin.logger.info("✓ PokemonDisplayItemBuilder: 为已收集精灵添加附魔效果 (LURE, level 1)")
            }

            // 隐藏所有不必要的信息（但不隐藏附魔）
            meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
            meta.addItemFlags(ItemFlag.HIDE_DESTROYS)
            meta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
            meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
            meta.addItemFlags(ItemFlag.HIDE_DYE)
            meta.addItemFlags(ItemFlag.HIDE_ADDITIONAL_TOOLTIP)
            
            item.itemMeta = meta
        } catch (e: Exception) {
            plugin.logger.warning("应用自定义显示信息失败: ${e.message}")
        }
    }
    
    /**
     * 构建占位符映射
     */
    private fun buildPlaceholders(pokemon: Pokemon, hasCaught: Boolean): Map<String, String> {
        val placeholders = mutableMapOf<String, String>()
        
        try {
            val species = pokemon.species
            
            // 基础信息
            placeholders["{pokemon_name}"] = LGMenuIntegration.getPokemonDisplayName(species)
            placeholders["{pokemon_name_en}"] = species.name
            placeholders["{national_dex}"] = String.format("%03d", species.nationalPokedexNumber)
            placeholders["{level}"] = pokemon.level.toString()
            placeholders["{shiny}"] = if (pokemon.shiny) "是" else "否"

            // 类型信息
            val types = LGMenuIntegration.getPokemonTypes(species)
            placeholders["{primary_type}"] = types.first
            placeholders["{secondary_type}"] = types.second ?: ""
            placeholders["{types}"] = if (types.second != null) {
                "${types.first}/${types.second}"
            } else {
                types.first
            }
            
            // 状态信息
            placeholders["{caught_status}"] = if (hasCaught) "已收集" else "尚未收集"
            placeholders["{caught_color}"] = if (hasCaught) "§a" else "§7"
            
            // 其他信息
            placeholders["{gender}"] = when (pokemon.gender.name.lowercase()) {
                "male" -> "♂"
                "female" -> "♀"
                else -> "○"
            }
            
        } catch (e: Exception) {
            plugin.logger.warning("构建占位符映射失败: ${e.message}")
        }
        
        return placeholders
    }
    
    /**
     * 替换占位符
     */
    private fun replacePlaceholders(template: String, placeholders: Map<String, String>): String {
        var result = template
        placeholders.forEach { (placeholder, value) ->
            result = result.replace(placeholder, value)
        }
        return result
    }
    
    companion object {
        /**
         * 创建默认的精灵显示构建器
         */
        fun createDefault(plugin: AceDex): PokemonDisplayItemBuilder {
            val nameTemplate = "{caught_color}{shiny_indicator}#{national_dex} {pokemon_name}"
            val loreTemplate = listOf(
                "§f类型: §e{types}",
                "§f等级: §e{level}",
                "§f性别: §e{gender}",
                "§f异色: §e{shiny}",
                "",
                "{caught_color}{caught_status}",
                "§e点击查看详情"
            )
            return PokemonDisplayItemBuilder(plugin, nameTemplate, loreTemplate)
        }
        
        /**
         * 创建简化的精灵显示构建器
         */
        fun createSimple(plugin: AceDex): PokemonDisplayItemBuilder {
            val nameTemplate = "{caught_color}#{national_dex} {pokemon_name}"
            val loreTemplate = listOf(
                "§f类型: §e{types}",
                "",
                "{caught_color}{caught_status}"
            )
            return PokemonDisplayItemBuilder(plugin, nameTemplate, loreTemplate)
        }
    }
}
