/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第四世代精灵名称映射 (387-493)
 * 包含神奥地区的所有精灵
 */
object PokemonGen4Mapping {
    
    /**
     * 获取第四世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第四世代 (387-493)
            "turtwig" to "草苗龟",
            "grotle" to "树林龟",
            "torterra" to "土台龟",
            "chimchar" to "小火焰猴",
            "monferno" to "猛火猴",
            "infernape" to "烈焰猴",
            "piplup" to "波加曼",
            "prinplup" to "波皇子",
            "empoleon" to "帝王拿波",
            "starly" to "姆克儿",
            "staravia" to "姆克鸟",
            "staraptor" to "姆克鹰",
            "bidoof" to "大牙狸",
            "bibarel" to "大尾狸",
            "kricketot" to "圆法师",
            "kricketune" to "音箱蟀",
            "shinx" to "小猫怪",
            "luxio" to "勒克猫",
            "luxray" to "伦琴猫",
            "budew" to "含羞苞",
            "roserade" to "罗丝雷朵",
            "cranidos" to "头盖龙",
            "rampardos" to "战槌龙",
            "shieldon" to "盾甲龙",
            "bastiodon" to "护城龙",
            "burmy" to "结草儿",
            "wormadam" to "结草贵妇",
            "mothim" to "绅士蛾",
            "combee" to "三蜜蜂",
            "vespiquen" to "蜂女王",
            "pachirisu" to "帕奇利兹",
            "buizel" to "泳圈鼬",
            "floatzel" to "浮潜鼬",
            "cherubi" to "樱花宝",
            "cherrim" to "樱花儿",
            "shellos" to "无壳海兔",
            "gastrodon" to "海兔兽",
            "ambipom" to "双尾怪手",
            "drifloon" to "飘飘球",
            "drifblim" to "随风球",
            "buneary" to "卷卷耳",
            "lopunny" to "长耳兔",
            "mismagius" to "梦妖魔",
            "honchkrow" to "乌鸦头头",
            "glameow" to "魅力喵",
            "purugly" to "东施喵",
            "chingling" to "铃铛响",
            "stunky" to "臭鼬噗",
            "skuntank" to "坦克臭鼬",
            "bronzor" to "铜镜怪",
            "bronzong" to "青铜钟",
            "bonsly" to "盆才怪",
            "mimejr" to "魔尼尼",
            "happiny" to "小福蛋",
            "chatot" to "聒噪鸟",
            "spiritomb" to "花岩怪",
            "gible" to "圆陆鲨",
            "gabite" to "尖牙陆鲨",
            "garchomp" to "烈咬陆鲨",
            "munchlax" to "小卡比兽",
            "riolu" to "利欧路",
            "lucario" to "路卡利欧",
            "hippopotas" to "沙河马",
            "hippowdon" to "河马兽",
            "skorupi" to "钳尾蝎",
            "drapion" to "龙王蝎",
            "croagunk" to "不良蛙",
            "toxicroak" to "毒骷蛙",
            "carnivine" to "尖牙笼",
            "finneon" to "荧光鱼",
            "lumineon" to "霓虹鱼",
            "mantyke" to "小球飞鱼",
            "snover" to "雪笠怪",
            "abomasnow" to "暴雪王",
            "weavile" to "玛狃拉",
            "magnezone" to "自爆磁怪",
            "lickilicky" to "大舌舔",
            "rhyperior" to "超甲狂犀",
            "tangrowth" to "巨蔓藤",
            "electivire" to "电击魔兽",
            "magmortar" to "鸭嘴焰龙",
            "togekiss" to "波克基斯",
            "yanmega" to "远古巨蜓",
            "leafeon" to "叶伊布",
            "glaceon" to "冰伊布",
            "gliscor" to "天蝎王",
            "mamoswine" to "象牙猪",
            "porygonz" to "多边兽Z",
            "gallade" to "艾路雷朵",
            "probopass" to "大朝北鼻",
            "dusknoir" to "黑夜魔灵",
            "froslass" to "雪妖女",
            "rotom" to "洛托姆",
            "uxie" to "由克希",
            "mesprit" to "艾姆利多",
            "azelf" to "亚克诺姆",
            "dialga" to "帝牙卢卡",
            "palkia" to "帕路奇亚",
            "heatran" to "席多蓝恩",
            "regigigas" to "雷吉奇卡斯",
            "giratina" to "骑拉帝纳",
            "cresselia" to "克雷色利亚",
            "phione" to "霏欧纳",
            "manaphy" to "玛纳霏",
            "darkrai" to "达克莱伊",
            "shaymin" to "谢米",
            "arceus" to "阿尔宙斯",
    )
}
