/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第二代精灵数据 (城都地区)
 * 包含全国图鉴编号 152-251 的精灵数据
 */
object Gen2PokemonData {
    val data = mapOf(
        // 152-160 (城都御三家及其进化)
        "chikorita" to PokemonData(152, "草", "叶子精灵", "male", "花草草原"),
        "bayleef" to PokemonData(153, "草", "叶子精灵", "male", "花草草原"),
        "meganium" to PokemonData(154, "草", "香草精灵", "male", "花草草原"),
        "cyndaquil" to PokemonData(155, "火", "火鼠精灵", "male", "温带, 针叶林"),
        "quilava" to PokemonData(156, "火", "火山精灵", "male", "温带, 针叶林"),
        "typhlosion" to PokemonData(157, "火", "火山精灵", "male", "温带, 针叶林"),
        "totodile" to PokemonData(158, "水", "大颚精灵", "male", "沼泽"),
        "croconaw" to PokemonData(159, "水", "大颚精灵", "male", "沼泽"),
        "feraligatr" to PokemonData(160, "水", "大颚精灵", "male", "沼泽"),

        // 161-170
        "sentret" to PokemonData(161, "一般", "侦察精灵", "male", "针叶林, 温带, 苔原"),
        "furret" to PokemonData(162, "一般", "长身精灵", "male", "针叶林, 温带, 苔原"),
        "hoothoot" to PokemonData(163, "一般/飞行", "猫头鹰精灵", "male", "森林, 阴森森林"),
        "noctowl" to PokemonData(164, "一般/飞行", "猫头鹰精灵", "male", "森林, 阴森森林"),
        "ledyba" to PokemonData(165, "虫/飞行", "五星精灵", "male", "温带, 花草草原"),
        "ledian" to PokemonData(166, "虫/飞行", "五星精灵", "male", "温带, 花草草原"),
        "spinarak" to PokemonData(167, "虫/毒", "吐丝精灵", "male", "森林, 丛林, 阴森森林"),
        "ariados" to PokemonData(168, "虫/毒", "长腿精灵", "male", "森林, 丛林, 阴森森林"),
        "crobat" to PokemonData(169, "毒/飞行", "蝙蝠精灵", "male", "森林, 沼泽, 阴森森林"),
        "chinchou" to PokemonData(170, "水/电", "琵琶鱼精灵", "male", "深海, 海洋"),

        // 171-180
        "lanturn" to PokemonData(171, "水/电", "灯精灵", "male", "深海, 海洋"),
        "pichu" to PokemonData(172, "电", "小鼠精灵", "male", "森林"),
        "cleffa" to PokemonData(173, "妖精", "星形精灵", "female", "溶洞, 丘陵"),
        "igglybuff" to PokemonData(174, "一般/妖精", "气球精灵", "female", "花草草原, 平原"),
        "togepi" to PokemonData(175, "妖精", "针球精灵", "male", "城市, 村庄"),
        "togetic" to PokemonData(176, "妖精/飞行", "幸福精灵", "male", "城市, 村庄"),
        "natu" to PokemonData(177, "超能力/飞行", "小鸟精灵", "male", "恶地, 高原"),
        "xatu" to PokemonData(178, "超能力/飞行", "神秘精灵", "male", "恶地, 高原"),
        "mareep" to PokemonData(179, "电", "绵羊精灵", "male", "高地, 平原"),
        "flaaffy" to PokemonData(180, "电", "绵羊精灵", "male", "高地, 平原"),

        // 181-190
        "ampharos" to PokemonData(181, "电", "灯塔精灵", "male", "高地, 平原"),
        "bellossom" to PokemonData(182, "草", "花精灵", "female", "花草草原, 丛林"),
        "marill" to PokemonData(183, "水/妖精", "水鼠精灵", "male", "淡水, 河流"),
        "azumarill" to PokemonData(184, "水/妖精", "水兔精灵", "male", "淡水, 河流"),
        "sudowoodo" to PokemonData(185, "岩石", "模仿精灵", "male", "森林"),
        "politoed" to PokemonData(186, "水", "蛙精灵", "male", "沼泽, 淡水"),
        "hoppip" to PokemonData(187, "草/飞行", "棉草精灵", "male", "草原, 平原"),
        "skiploom" to PokemonData(188, "草/飞行", "棉草精灵", "male", "草原, 平原"),
        "jumpluff" to PokemonData(189, "草/飞行", "棉花精灵", "male", "草原, 平原"),
        "aipom" to PokemonData(190, "一般", "长尾精灵", "male", "丛林, 热带岛屿"),

        // 191-200
        "sunkern" to PokemonData(191, "草", "种子精灵", "male", "花草草原, 热带草原"),
        "sunflora" to PokemonData(192, "草", "太阳精灵", "female", "花草草原, 热带草原"),
        "yanma" to PokemonData(193, "虫/飞行", "蜻蜓精灵", "male", "沼泽, 丛林"),
        "wooper" to PokemonData(194, "水/地面", "水鱼精灵", "male", "沼泽, 淡水"),
        "quagsire" to PokemonData(195, "水/地面", "水鱼精灵", "male", "沼泽, 淡水"),
        "espeon" to PokemonData(196, "超能力", "太阳精灵", "male", "城市, 村庄"),
        "umbreon" to PokemonData(197, "恶", "月亮精灵", "male", "城市, 村庄"),
        "murkrow" to PokemonData(198, "恶/飞行", "黑暗精灵", "male", "阴森森林, 黑暗森林"),
        "slowking" to PokemonData(199, "水/超能力", "王者精灵", "male", "海滩, 河流"),
        "misdreavus" to PokemonData(200, "幽灵", "尖叫精灵", "female", "阴森森林, 黑暗森林"),

        // 201-210
        "unown" to PokemonData(201, "超能力", "象征精灵", "genderless", "溶洞, 地下"),
        "wobbuffet" to PokemonData(202, "超能力", "忍耐精灵", "male", "溶洞, 地下"),
        "girafarig" to PokemonData(203, "一般/超能力", "长颈精灵", "male", "热带草原"),
        "pineco" to PokemonData(204, "虫", "蓑衣虫精灵", "male", "森林, 针叶林"),
        "forretress" to PokemonData(205, "虫/钢", "蓑衣虫精灵", "male", "森林, 针叶林"),
        "dunsparce" to PokemonData(206, "一般", "地蛇精灵", "male", "溶洞, 地下"),
        "gligar" to PokemonData(207, "地面/飞行", "飞蝎精灵", "male", "山地, 丘陵"),
        "steelix" to PokemonData(208, "钢/地面", "铁蛇精灵", "male", "地下, 山地"),
        "snubbull" to PokemonData(209, "妖精", "妖精精灵", "female", "城市, 村庄"),
        "granbull" to PokemonData(210, "妖精", "妖精精灵", "female", "城市, 村庄"),

        // 211-220
        "qwilfish" to PokemonData(211, "水/毒", "气球精灵", "male", "温暖海洋"),
        "scizor" to PokemonData(212, "虫/钢", "钳子精灵", "male", "森林, 丘陵"),
        "shuckle" to PokemonData(213, "虫/岩石", "发酵精灵", "male", "山地, 溶洞"),
        "heracross" to PokemonData(214, "虫/格斗", "独角仙精灵", "male", "丛林, 热带岛屿"),
        "sneasel" to PokemonData(215, "恶/冰", "钩爪精灵", "male", "冰冻地区, 雪山"),
        "teddiursa" to PokemonData(216, "一般", "小熊精灵", "male", "森林, 针叶林"),
        "ursaring" to PokemonData(217, "一般", "冬眠精灵", "male", "森林, 针叶林"),
        "slugma" to PokemonData(218, "火", "熔岩精灵", "male", "火山, 恶地"),
        "magcargo" to PokemonData(219, "火/岩石", "熔岩精灵", "male", "火山, 恶地"),
        "swinub" to PokemonData(220, "冰/地面", "小猪精灵", "male", "冰冻地区, 雪山"),

        // 221-230
        "piloswine" to PokemonData(221, "冰/地面", "野猪精灵", "male", "冰冻地区, 雪山"),
        "corsola" to PokemonData(222, "水/岩石", "珊瑚精灵", "female", "温暖海洋"),
        "remoraid" to PokemonData(223, "水", "喷射精灵", "male", "温暖海洋"),
        "octillery" to PokemonData(224, "水", "喷射精灵", "male", "温暖海洋"),
        "delibird" to PokemonData(225, "冰/飞行", "配送精灵", "male", "冰冻地区, 雪山"),
        "mantine" to PokemonData(226, "水/飞行", "风筝精灵", "male", "温暖海洋"),
        "skarmory" to PokemonData(227, "钢/飞行", "装甲鸟精灵", "male", "山地, 恶地"),
        "houndour" to PokemonData(228, "恶/火", "黑暗精灵", "male", "恶地, 干旱地区"),
        "houndoom" to PokemonData(229, "恶/火", "黑暗精灵", "male", "恶地, 干旱地区"),
        "kingdra" to PokemonData(230, "水/龙", "龙精灵", "male", "深海, 海洋"),

        // 231-240
        "phanpy" to PokemonData(231, "地面", "长鼻精灵", "male", "热带草原"),
        "donphan" to PokemonData(232, "地面", "装甲精灵", "male", "热带草原"),
        "porygon2" to PokemonData(233, "一般", "虚拟精灵", "genderless", "城市"),
        "stantler" to PokemonData(234, "一般", "大角精灵", "male", "森林, 针叶林"),
        "smeargle" to PokemonData(235, "一般", "画家精灵", "male", "城市, 村庄"),
        "tyrogue" to PokemonData(236, "格斗", "打斗精灵", "male", "丘陵"),
        "hitmontop" to PokemonData(237, "格斗", "倒立精灵", "male", "丘陵"),
        "smoochum" to PokemonData(238, "冰/超能力", "亲吻精灵", "female", "冰冻地区, 城市"),
        "elekid" to PokemonData(239, "电", "电击精灵", "male", "丘陵, 平原"),
        "magby" to PokemonData(240, "火", "活炭精灵", "male", "丘陵, 火山"),

        // 241-251 (传说精灵)
        "miltank" to PokemonData(241, "一般", "奶牛精灵", "female", "草原, 平原"),
        "blissey" to PokemonData(242, "一般", "幸福精灵", "female", "城市, 村庄"),
        "raikou" to PokemonData(243, "电", "雷精灵", "genderless", "草原, 平原"),
        "entei" to PokemonData(244, "火", "火山精灵", "genderless", "火山, 恶地"),
        "suicune" to PokemonData(245, "水", "极光精灵", "genderless", "淡水, 河流"),
        "larvitar" to PokemonData(246, "岩石/地面", "岩肌精灵", "male", "山地, 地下"),
        "pupitar" to PokemonData(247, "岩石/地面", "硬壳精灵", "male", "山地, 地下"),
        "tyranitar" to PokemonData(248, "岩石/恶", "装甲精灵", "male", "山地, 地下"),
        "lugia" to PokemonData(249, "超能力/飞行", "潜水精灵", "genderless", "深海"),
        "hooh" to PokemonData(250, "火/飞行", "虹色精灵", "genderless", "火山"),
        "celebi" to PokemonData(251, "超能力/草", "时空穿越精灵", "genderless", "森林")
    )
}
