/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第五代精灵数据 (合众地区)
 * 包含全国图鉴编号 494-649 的精灵数据
 */
object Gen5PokemonData {
    val data = mapOf(
        // 494-502 (合众御三家及其进化)
        "victini" to PokemonData(494, "超能力/火", "胜利精灵", "genderless", "传说区域"),
        "snivy" to PokemonData(495, "草", "草蛇精灵", "male", "丛林"),
        "servine" to PokemonData(496, "草", "草蛇精灵", "male", "丛林"),
        "serperior" to PokemonData(497, "草", "君主蛇精灵", "male", "丛林"),
        "tepig" to PokemonData(498, "火", "火猪精灵", "male", "平原"),
        "pignite" to <PERSON>kemonData(499, "火/格斗", "火猪精灵", "male", "平原"),
        "emboar" to PokemonData(500, "火/格斗", "炎武王精灵", "male", "平原"),
        "oshawott" to PokemonData(501, "水", "海獭精灵", "male", "海滩, 冰冻海洋"),
        "dewott" to PokemonData(502, "水", "训练精灵", "male", "海滩, 冰冻海洋"),

        // 503-512
        "samurott" to PokemonData(503, "水", "大剑鬼精灵", "male", "海滩, 冰冻海洋"),
        "patrat" to PokemonData(504, "一般", "侦察精灵", "male", "草原"),
        "watchog" to PokemonData(505, "一般", "警戒精灵", "male", "草原"),
        "lillipup" to PokemonData(506, "一般", "小狗精灵", "male", "城市, 村庄"),
        "herdier" to PokemonData(507, "一般", "忠犬精灵", "male", "城市, 村庄"),
        "stoutland" to PokemonData(508, "一般", "大犬精灵", "male", "城市, 村庄"),
        "purrloin" to PokemonData(509, "恶", "恶猫精灵", "male", "丛林, 沼泽, 温带, 热带草原, 城市, 村庄"),
        "liepard" to PokemonData(510, "恶", "冷酷精灵", "male", "丛林, 沼泽, 温带, 热带草原, 城市, 村庄"),
        "pansage" to PokemonData(511, "草", "草猴精灵", "male", "森林"),
        "simisage" to PokemonData(512, "草", "刺草猴精灵", "male", "森林"),

        // 513-522
        "pansear" to PokemonData(513, "火", "高温猴精灵", "male", "沙漠"),
        "simisear" to PokemonData(514, "火", "爆香猴精灵", "male", "沙漠"),
        "panpour" to PokemonData(515, "水", "泼泼猴精灵", "male", "淡水"),
        "simipour" to PokemonData(516, "水", "冷水猴精灵", "male", "淡水"),
        "munna" to PokemonData(517, "超能力", "食梦精灵", "female", "花草草原"),
        "musharna" to PokemonData(518, "超能力", "引梦精灵", "female", "花草草原"),
        "pidove" to PokemonData(519, "一般/飞行", "幼鸽精灵", "male", "山地, 城市, 村庄"),
        "tranquill" to PokemonData(520, "一般/飞行", "野鸽精灵", "male", "山地, 城市, 村庄"),
        "unfezant" to PokemonData(521, "一般/飞行", "高傲精灵", "male", "山地, 城市, 村庄"),
        "blitzle" to PokemonData(522, "电", "带电精灵", "male", "平原"),

        // 523-532
        "zebstrika" to PokemonData(523, "电", "雷斑马精灵", "male", "平原"),
        "roggenrola" to PokemonData(524, "岩石", "地幔精灵", "male", "地下"),
        "boldore" to PokemonData(525, "岩石", "矿石精灵", "male", "地下"),
        "gigalith" to PokemonData(526, "岩石", "高压精灵", "male", "地下"),
        "woobat" to PokemonData(527, "超能力/飞行", "蝙蝠精灵", "male", "丛林, 热带草原, 地下"),
        "swoobat" to PokemonData(528, "超能力/飞行", "求爱精灵", "male", "丛林, 热带草原, 地下"),
        "drilbur" to PokemonData(529, "地面", "鼹鼠精灵", "male", "地下"),
        "excadrill" to PokemonData(530, "地面/钢", "地底精灵", "male", "地下"),
        "audino" to PokemonData(531, "一般", "听觉精灵", "female", "森林"),
        "timburr" to PokemonData(532, "格斗", "肌肉精灵", "male", "城市, 村庄"),

        // 533-542
        "gurdurr" to PokemonData(533, "格斗", "肌肉精灵", "male", "城市, 村庄"),
        "conkeldurr" to PokemonData(534, "格斗", "铁骨精灵", "male", "城市, 村庄"),
        "tympole" to PokemonData(535, "水", "音符精灵", "male", "沼泽"),
        "palpitoad" to PokemonData(536, "水/地面", "震动精灵", "male", "沼泽"),
        "seismitoad" to PokemonData(537, "水/地面", "震动精灵", "male", "沼泽"),
        "throh" to PokemonData(538, "格斗", "柔道精灵", "male", "丘陵"),
        "sawk" to PokemonData(539, "格斗", "空手道精灵", "male", "丘陵"),
        "sewaddle" to PokemonData(540, "虫/草", "缝叶精灵", "male", "森林"),
        "swadloon" to PokemonData(541, "虫/草", "育成精灵", "male", "森林"),
        "leavanny" to PokemonData(542, "虫/草", "育成精灵", "male", "森林"),

        // 543-552
        "venipede" to PokemonData(543, "虫/毒", "百足精灵", "male", "森林, 丛林"),
        "whirlipede" to PokemonData(544, "虫/毒", "旋转精灵", "male", "森林, 丛林"),
        "scolipede" to PokemonData(545, "虫/毒", "百足精灵", "male", "森林, 丛林"),
        "cottonee" to PokemonData(546, "草/妖精", "棉花球精灵", "male", "平原"),
        "whimsicott" to PokemonData(547, "草/妖精", "风妖精精灵", "male", "平原"),
        "petilil" to PokemonData(548, "草", "根茎精灵", "female", "花草草原, 森林, 热带岛屿, 丘陵"),
        "lilligant" to PokemonData(549, "草", "花饰精灵", "female", "花草草原, 森林, 热带岛屿, 丘陵"),
        "basculin" to PokemonData(550, "水", "激烈精灵", "male", "淡水"),
        "sandile" to PokemonData(551, "地面/恶", "沙漠鳄精灵", "male", "沙漠"),
        "krokorok" to PokemonData(552, "地面/恶", "沙漠鳄精灵", "male", "沙漠"),

        // 553-562
        "krookodile" to PokemonData(553, "地面/恶", "威吓精灵", "male", "沙漠"),
        "darumaka" to PokemonData(554, "火", "达摩精灵", "male", "沙漠, 丛林"),
        "darmanitan" to PokemonData(555, "火", "达摩精灵", "male", "沙漠, 丛林"),
        "maractus" to PokemonData(556, "草", "仙人掌精灵", "male", "恶地, 沙漠"),
        "dwebble" to PokemonData(557, "虫/岩石", "岩居蟹精灵", "male", "恶地"),
        "crustle" to PokemonData(558, "虫/岩石", "岩居蟹精灵", "male", "恶地"),
        "scraggy" to PokemonData(559, "恶/格斗", "脱皮精灵", "male", "恶地, 城市, 村庄"),
        "scrafty" to PokemonData(560, "恶/格斗", "头巾精灵", "male", "恶地, 城市, 村庄"),
        "sigilyph" to PokemonData(561, "超能力/飞行", "鸟神精灵", "male", "恶地, 沙漠"),
        "yamask" to PokemonData(562, "幽灵", "灵魂精灵", "male", "沙漠, 地下"),

        // 563-572
        "cofagrigus" to PokemonData(563, "幽灵", "棺材精灵", "male", "沙漠, 地下"),
        "tirtouga" to PokemonData(564, "水/岩石", "原盖龟精灵", "male", "地下"),
        "carracosta" to PokemonData(565, "水/岩石", "原盖龟精灵", "male", "地下"),
        "archen" to PokemonData(566, "岩石/飞行", "古鸟精灵", "male", "地下"),
        "archeops" to PokemonData(567, "岩石/飞行", "古鸟精灵", "male", "地下"),
        "trubbish" to PokemonData(568, "毒", "垃圾袋精灵", "male", "城市"),
        "garbodor" to PokemonData(569, "毒", "垃圾堆精灵", "male", "城市"),
        "zorua" to PokemonData(570, "恶", "恶狐精灵", "male", "森林, 雪山, 针叶林"),
        "zoroark" to PokemonData(571, "恶", "幻狐精灵", "male", "森林, 雪山, 针叶林"),
        "minccino" to PokemonData(572, "一般", "栗鼠精灵", "male", "森林"),

        // 573-582
        "cinccino" to PokemonData(573, "一般", "围巾精灵", "male", "森林"),
        "gothita" to PokemonData(574, "超能力", "凝视精灵", "female", "城市"),
        "gothorita" to PokemonData(575, "超能力", "操纵精灵", "female", "城市"),
        "gothitelle" to PokemonData(576, "超能力", "天体精灵", "female", "城市"),
        "solosis" to PokemonData(577, "超能力", "细胞精灵", "male", "洞穴"),
        "duosion" to PokemonData(578, "超能力", "分裂精灵", "male", "洞穴"),
        "reuniclus" to PokemonData(579, "超能力", "增殖精灵", "male", "洞穴"),
        "ducklett" to PokemonData(580, "水/飞行", "水鸟精灵", "male", "淡水"),
        "swanna" to PokemonData(581, "水/飞行", "白鸟精灵", "male", "淡水"),
        "vanillite" to PokemonData(582, "冰", "新雪精灵", "male", "冰冻地区"),

        // 583-592
        "vanillish" to PokemonData(583, "冰", "冰淇淋精灵", "male", "冰冻地区"),
        "vanilluxe" to PokemonData(584, "冰", "暴雪精灵", "male", "冰冻地区"),
        "deerling" to PokemonData(585, "一般/草", "季节精灵", "male", "花草草原, 森林, 丘陵, 平原, 雪山, 针叶林"),
        "sawsbuck" to PokemonData(586, "一般/草", "季节精灵", "male", "花草草原, 森林, 丘陵, 平原, 雪山, 针叶林"),
        "emolga" to PokemonData(587, "电/飞行", "飞鼠精灵", "male", "针叶林"),
        "karrablast" to PokemonData(588, "虫", "骑兵精灵", "male", "沼泽"),
        "escavalier" to PokemonData(589, "虫/钢", "骑士精灵", "male", "沼泽"),
        "foongus" to PokemonData(590, "草/毒", "蘑菇精灵", "male", "蘑菇岛"),
        "amoonguss" to PokemonData(591, "草/毒", "蘑菇精灵", "male", "蘑菇岛"),
        "frillish" to PokemonData(592, "水/幽灵", "浮游精灵", "male", "海洋"),

        // 593-602
        "jellicent" to PokemonData(593, "水/幽灵", "浮游精灵", "male", "海洋"),
        "alomomola" to PokemonData(594, "水", "护理精灵", "female", "深海, 冰冻海洋"),
        "joltik" to PokemonData(595, "虫/电", "附着精灵", "male", "恶地, 丛林, 热带草原, 地下"),
        "galvantula" to PokemonData(596, "虫/电", "电蜘蛛精灵", "male", "恶地, 丛林, 热带草原, 地下"),
        "ferroseed" to PokemonData(597, "草/钢", "刺种精灵", "male", "地下"),
        "ferrothorn" to PokemonData(598, "草/钢", "刺球精灵", "male", "地下"),
        "klink" to PokemonData(599, "钢", "齿轮精灵", "genderless", "地下, 城市"),
        "klang" to PokemonData(600, "钢", "齿轮精灵", "genderless", "地下, 城市"),
        "klinklang" to PokemonData(601, "钢", "齿轮精灵", "genderless", "地下, 城市"),
        "tynamo" to PokemonData(602, "电", "电鱼精灵", "male", "洞穴"),

        // 603-612
        "eelektrik" to PokemonData(603, "电", "电鳗精灵", "male", "洞穴"),
        "eelektross" to PokemonData(604, "电", "电鳗精灵", "male", "洞穴"),
        "elgyem" to PokemonData(605, "超能力", "大脑精灵", "male", "城市"),
        "beheeyem" to PokemonData(606, "超能力", "大脑精灵", "male", "城市"),
        "litwick" to PokemonData(607, "幽灵/火", "蜡烛精灵", "male", "城市"),
        "lampent" to PokemonData(608, "幽灵/火", "灯火精灵", "male", "城市"),
        "chandelure" to PokemonData(609, "幽灵/火", "诱导精灵", "male", "城市"),
        "axew" to PokemonData(610, "龙", "牙精灵", "male", "洞穴"),
        "fraxure" to PokemonData(611, "龙", "斧颚精灵", "male", "洞穴"),
        "haxorus" to PokemonData(612, "龙", "斧颚精灵", "male", "洞穴"),

        // 613-622
        "cubchoo" to PokemonData(613, "冰", "结冰精灵", "male", "冰冻海洋"),
        "beartic" to PokemonData(614, "冰", "冻结精灵", "male", "冰冻海洋"),
        "cryogonal" to PokemonData(615, "冰", "结晶精灵", "genderless", "冰冻地区"),
        "shelmet" to PokemonData(616, "虫", "蜗牛精灵", "male", "沼泽"),
        "accelgor" to PokemonData(617, "虫", "脱壳精灵", "male", "沼泽"),
        "stunfisk" to PokemonData(618, "地面/电", "陷阱精灵", "male", "沼泽"),
        "mienfoo" to PokemonData(619, "格斗", "武术精灵", "male", "山地"),
        "mienshao" to PokemonData(620, "格斗", "武术精灵", "male", "山地"),
        "druddigon" to PokemonData(621, "龙", "洞穴精灵", "male", "洞穴"),
        "golett" to PokemonData(622, "地面/幽灵", "魔像精灵", "genderless", "地下, 沙漠, 丛林"),

        // 623-632
        "golurk" to PokemonData(623, "地面/幽灵", "魔像精灵", "genderless", "地下, 沙漠, 丛林"),
        "pawniard" to PokemonData(624, "恶/钢", "利刃精灵", "male", "山地"),
        "bisharp" to PokemonData(625, "恶/钢", "利刃精灵", "male", "山地"),
        "bouffalant" to PokemonData(626, "一般", "头冲精灵", "male", "平原"),
        "rufflet" to PokemonData(627, "一般/飞行", "雏鹰精灵", "male", "恶地, 丘陵"),
        "braviary" to PokemonData(628, "一般/飞行", "勇士精灵", "male", "恶地, 丘陵"),
        "vullaby" to PokemonData(629, "恶/飞行", "尿布精灵", "female", "恶地"),
        "mandibuzz" to PokemonData(630, "恶/飞行", "骨鹰精灵", "female", "恶地"),
        "heatmor" to PokemonData(631, "火", "食蚁精灵", "male", "恶地, 丘陵"),
        "durant" to PokemonData(632, "虫/钢", "铁蚁精灵", "male", "恶地, 丘陵, 地下"),

        // 633-642
        "deino" to PokemonData(633, "恶/龙", "粗暴精灵", "male", "地下"),
        "zweilous" to PokemonData(634, "恶/龙", "敌意精灵", "male", "地下"),
        "hydreigon" to PokemonData(635, "恶/龙", "凶暴精灵", "male", "地下"),
        "larvesta" to PokemonData(636, "虫/火", "火炬精灵", "male", "沙漠, 丛林"),
        "volcarona" to PokemonData(637, "虫/火", "太阳精灵", "male", "沙漠, 丛林"),
        "cobalion" to PokemonData(638, "钢/格斗", "铁心精灵", "genderless", "森林"),
        "terrakion" to PokemonData(639, "岩石/格斗", "岩窟精灵", "genderless", "洞穴"),
        "virizion" to PokemonData(640, "草/格斗", "草原精灵", "genderless", "森林"),
        "tornadus" to PokemonData(641, "飞行", "旋风精灵", "male", "平原"),
        "thundurus" to PokemonData(642, "电/飞行", "雷电精灵", "male", "平原"),

        // 643-649 (传说精灵)
        "reshiram" to PokemonData(643, "龙/火", "白阳精灵", "genderless", "传说区域"),
        "zekrom" to PokemonData(644, "龙/电", "黑阴精灵", "genderless", "传说区域"),
        "landorus" to PokemonData(645, "地面/飞行", "丰饶精灵", "male", "平原"),
        "kyurem" to PokemonData(646, "龙/冰", "境界精灵", "genderless", "传说区域"),
        "keldeo" to PokemonData(647, "水/格斗", "幻之精灵", "genderless", "传说区域"),
        "meloetta" to PokemonData(648, "一般/超能力", "旋律精灵", "genderless", "传说区域"),
        "genesect" to PokemonData(649, "虫/钢", "古生代精灵", "genderless", "传说区域")
    )
}
