package cn.acebrand.acebiomespawner.config

import org.bukkit.configuration.ConfigurationSection
import org.bukkit.configuration.file.YamlConfiguration
import kotlin.random.Random

/**
 * 生物群系配置
 */
data class BiomeConfig(
    var enabled: Boolean = true,
    var spawnChance: Double = 0.5, // 生物群系刷新概率 (0.0-1.0)
    var pokemonList: List<PokemonEntry> = emptyList()
) {

    companion object {
        /**
         * 从YAML配置加载
         */
        fun fromYaml(section: ConfigurationSection): BiomeConfig {
            val enabled = section.getBoolean("enabled", true)
            val spawnChance = section.getDouble("spawn_chance", 0.5)
            val pokemonList = mutableListOf<PokemonEntry>()

            // 支持两种格式：列表格式和对象格式
            if (section.isList("pokemon")) {
                // 新的简化列表格式，使用默认权重1.0
                val pokemonNames = section.getStringList("pokemon")
                for (pokemonName in pokemonNames) {
                    val entry = PokemonEntry(pokemonName, 1.0)
                    pokemonList.add(entry)
                }
            } else {
                // 旧的对象格式（向后兼容）
                val pokemonSection = section.getConfigurationSection("pokemon")
                if (pokemonSection != null) {
                    for (pokemonName in pokemonSection.getKeys(false)) {
                        val pokemonData = pokemonSection.getConfigurationSection(pokemonName)
                        if (pokemonData != null) {
                            val entry = PokemonEntry.fromYaml(pokemonName, pokemonData)
                            pokemonList.add(entry)
                        } else {
                            // 如果没有子配置，则创建简单条目，使用默认权重
                            val entry = PokemonEntry(pokemonName, 1.0)
                            pokemonList.add(entry)
                        }
                    }
                }
            }

            return BiomeConfig(enabled, spawnChance, pokemonList)
        }
    }

    /**
     * 保存到YAML配置
     */
    fun saveToYaml(yaml: YamlConfiguration, path: String) {
        yaml.set("$path.enabled", enabled)
        yaml.set("$path.spawn_chance", spawnChance)

        for (pokemon in pokemonList) {
            pokemon.saveToYaml(yaml, "$path.pokemon.${pokemon.name}")
        }
    }

    /**
     * 检查是否应该在此生物群系生成精灵
     */
    fun shouldSpawn(): Boolean {
        return enabled && pokemonList.isNotEmpty() && Random.nextDouble() < spawnChance
    }

    /**
     * 获取总权重
     */
    fun getTotalWeight(): Double {
        return pokemonList.sumOf { it.weight }
    }

    /**
     * 根据权重随机选择一个精灵
     */
    fun getRandomPokemon(): PokemonEntry? {
        if (pokemonList.isEmpty()) return null

        val totalWeight = getTotalWeight()
        if (totalWeight <= 0) return null

        val random = Random.nextDouble() * totalWeight
        var currentWeight = 0.0

        for (entry in pokemonList) {
            currentWeight += entry.weight
            if (random <= currentWeight) {
                return entry
            }
        }

        return pokemonList.firstOrNull()
    }

    /**
     * 检查是否有有效的精灵可以生成
     */
    fun hasValidPokemon(): Boolean {
        return enabled && pokemonList.isNotEmpty() && getTotalWeight() > 0
    }
}

/**
 * 精灵条目
 */
data class PokemonEntry(
    var name: String,
    var weight: Double = 1.0,
    var minLevel: Int? = null,
    var maxLevel: Int? = null,
    var canBeShiny: Boolean? = null,
    var shinyChance: Double? = null,
    var properties: Map<String, String> = emptyMap()
) {

    companion object {
        /**
         * 从YAML配置加载
         */
        fun fromYaml(name: String, section: ConfigurationSection): PokemonEntry {
            val weight = section.getDouble("weight", 1.0)
            val minLevel = if (section.contains("min_level")) section.getInt("min_level") else null
            val maxLevel = if (section.contains("max_level")) section.getInt("max_level") else null
            val canBeShiny = if (section.contains("can_be_shiny")) section.getBoolean("can_be_shiny") else null
            val shinyChance = if (section.contains("shiny_chance")) section.getDouble("shiny_chance") else null

            val properties = mutableMapOf<String, String>()
            val propertiesSection = section.getConfigurationSection("properties")
            if (propertiesSection != null) {
                for (key in propertiesSection.getKeys(false)) {
                    val value = propertiesSection.getString(key)
                    if (value != null) {
                        properties[key] = value
                    }
                }
            }

            return PokemonEntry(name, weight, minLevel, maxLevel, canBeShiny, shinyChance, properties)
        }
    }

    /**
     * 保存到YAML配置
     */
    fun saveToYaml(yaml: YamlConfiguration, path: String) {
        yaml.set("$path.weight", weight)
        minLevel?.let { yaml.set("$path.min_level", it) }
        maxLevel?.let { yaml.set("$path.max_level", it) }
        canBeShiny?.let { yaml.set("$path.can_be_shiny", it) }
        shinyChance?.let { yaml.set("$path.shiny_chance", it) }

        if (properties.isNotEmpty()) {
            for ((key, value) in properties) {
                yaml.set("$path.properties.$key", value)
            }
        }
    }

    /**
     * 获取有效的等级范围
     */
    fun getEffectiveLevelRange(globalMin: Int, globalMax: Int): IntRange {
        val min = minLevel ?: globalMin
        val max = maxLevel ?: globalMax
        return min..max
    }

    /**
     * 获取有效的闪光设置
     */
    fun getEffectiveShinySettings(globalCanBeShiny: Boolean, globalShinyChance: Double): Pair<Boolean, Double> {
        val canShiny = canBeShiny ?: globalCanBeShiny
        val chance = shinyChance ?: globalShinyChance
        return canShiny to chance
    }

    /**
     * 生成精灵属性字符串
     */
    fun generatePropertiesString(level: Int, isShiny: Boolean): String {
        val props = mutableListOf<String>()

        props.add("species=$name")
        props.add("level=$level")

        if (isShiny) {
            props.add("shiny=true")
        }

        for ((key, value) in properties) {
            props.add("$key=$value")
        }

        return props.joinToString(" ")
    }

    /**
     * 验证精灵条目是否有效
     */
    fun isValid(): Boolean {
        return name.isNotBlank() && weight > 0
    }
}
