/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第三代精灵数据 (丰缘地区)
 * 包含全国图鉴编号 252-386 的精灵数据
 */
object Gen3PokemonData {
    val data = mapOf(
        // 252-260 (丰缘御三家及其进化)
        "treecko" to PokemonData(252, "草", "森林蜥蜴精灵", "male", "丛林"),
        "grovyle" to PokemonData(253, "草", "森林蜥蜴精灵", "male", "丛林"),
        "sceptile" to PokemonData(254, "草", "森林精灵", "male", "丛林"),
        "torchic" to PokemonData(255, "火", "雏鸡精灵", "male", "平原"),
        "combusken" to PokemonData(256, "火/格斗", "幼火鸡精灵", "male", "平原"),
        "blaziken" to PokemonData(257, "火/格斗", "火焰鸡精灵", "male", "平原"),
        "mudkip" to PokemonData(258, "水", "沼跃鱼精灵", "male", "沼泽"),
        "marshtomp" to PokemonData(259, "水/地面", "沼鱼精灵", "male", "沼泽"),
        "swampert" to PokemonData(260, "水/地面", "沼王精灵", "male", "沼泽"),

        // 261-270
        "poochyena" to PokemonData(261, "恶", "咬咬狗精灵", "male", "恶地, 热带草原"),
        "mightyena" to PokemonData(262, "恶", "咬咬狗精灵", "male", "恶地, 热带草原"),
        "zigzagoon" to PokemonData(263, "一般", "直冲熊精灵", "male", "温带"),
        "linoone" to PokemonData(264, "一般", "直冲熊精灵", "male", "温带"),
        "wurmple" to PokemonData(265, "虫", "刺尾虫精灵", "male", "森林"),
        "silcoon" to PokemonData(266, "虫", "甲蛹精灵", "male", "森林"),
        "beautifly" to PokemonData(267, "虫/飞行", "狩猎凤蝶精灵", "male", "森林"),
        "cascoon" to PokemonData(268, "虫", "盾甲茧精灵", "male", "森林"),
        "dustox" to PokemonData(269, "虫/毒", "毒粉蛾精灵", "male", "森林"),
        "lotad" to PokemonData(270, "水/草", "莲叶童子精灵", "male", "沼泽"),

        // 271-280
        "lombre" to PokemonData(271, "水/草", "莲帽小童精灵", "male", "沼泽"),
        "ludicolo" to PokemonData(272, "水/草", "乐天河童精灵", "male", "沼泽"),
        "seedot" to PokemonData(273, "草", "橡实果精灵", "male", "森林"),
        "nuzleaf" to PokemonData(274, "草/恶", "长鼻叶精灵", "male", "森林"),
        "shiftry" to PokemonData(275, "草/恶", "狡猾天狗精灵", "male", "森林"),
        "taillow" to PokemonData(276, "一般/飞行", "傲骨燕精灵", "male", "温带"),
        "swellow" to PokemonData(277, "一般/飞行", "大王燕精灵", "male", "温带"),
        "wingull" to PokemonData(278, "水/飞行", "长翅鸥精灵", "male", "海岸, 海洋, 热带岛屿"),
        "pelipper" to PokemonData(279, "水/飞行", "大嘴鸥精灵", "male", "海岸, 海洋, 热带岛屿"),
        "ralts" to PokemonData(280, "超能力/妖精", "拉鲁拉丝精灵", "male", "花草草原, 魔法森林"),

        // 281-290
        "kirlia" to PokemonData(281, "超能力/妖精", "奇鲁莉安精灵", "male", "花草草原, 魔法森林"),
        "gardevoir" to PokemonData(282, "超能力/妖精", "沙奈朵精灵", "female", "花草草原, 魔法森林"),
        "surskit" to PokemonData(283, "虫/水", "溜溜糖球精灵", "male", "淡水"),
        "masquerain" to PokemonData(284, "虫/飞行", "雨翅蛾精灵", "male", "淡水"),
        "shroomish" to PokemonData(285, "草", "蘑蘑菇精灵", "male", "森林, 蘑菇岛"),
        "breloom" to PokemonData(286, "草/格斗", "斗笠菇精灵", "male", "森林, 蘑菇岛"),
        "slakoth" to PokemonData(287, "一般", "懒人獭精灵", "male", "丛林"),
        "vigoroth" to PokemonData(288, "一般", "过动猿精灵", "male", "丛林"),
        "slaking" to PokemonData(289, "一般", "请假王精灵", "male", "丛林"),
        "nincada" to PokemonData(290, "虫/地面", "土居忍士精灵", "male", "森林, 丛林"),

        // 291-300
        "ninjask" to PokemonData(291, "虫/飞行", "铁面忍者精灵", "male", "森林, 丛林"),
        "shedinja" to PokemonData(292, "虫/幽灵", "脱壳忍者精灵", "genderless", "森林, 丛林"),
        "whismur" to PokemonData(293, "一般", "咕妞妞精灵", "male", "地下"),
        "loudred" to PokemonData(294, "一般", "吼爆弹精灵", "male", "地下"),
        "exploud" to PokemonData(295, "一般", "爆音怪精灵", "male", "地下"),
        "makuhita" to PokemonData(296, "格斗", "幕下力士精灵", "male", "山地"),
        "hariyama" to PokemonData(297, "格斗", "铁掌力士精灵", "male", "山地"),
        "azurill" to PokemonData(298, "一般/妖精", "露力丽精灵", "female", "淡水, 河流"),
        "nosepass" to PokemonData(299, "岩石", "朝北鼻精灵", "male", "地下"),
        "skitty" to PokemonData(300, "一般", "向尾喵精灵", "female", "城市, 村庄"),

        // 301-310
        "delcatty" to PokemonData(301, "一般", "优雅猫精灵", "female", "城市, 村庄"),
        "sableye" to PokemonData(302, "恶/幽灵", "勾魂眼精灵", "male", "地下"),
        "mawile" to PokemonData(303, "钢/妖精", "大嘴娃精灵", "female", "地下"),
        "aron" to PokemonData(304, "钢/岩石", "可可多拉精灵", "male", "山地, 地下"),
        "lairon" to PokemonData(305, "钢/岩石", "可多拉精灵", "male", "山地, 地下"),
        "aggron" to PokemonData(306, "钢/岩石", "波士可多拉精灵", "male", "山地, 地下"),
        "meditite" to PokemonData(307, "格斗/超能力", "玛沙那精灵", "male", "山地"),
        "medicham" to PokemonData(308, "格斗/超能力", "恰雷姆精灵", "male", "山地"),
        "electrike" to PokemonData(309, "电", "落雷兽精灵", "male", "平原"),
        "manectric" to PokemonData(310, "电", "雷电兽精灵", "male", "平原"),

        // 311-320
        "plusle" to PokemonData(311, "电", "正电拍拍精灵", "male", "平原"),
        "minun" to PokemonData(312, "电", "负电拍拍精灵", "male", "平原"),
        "volbeat" to PokemonData(313, "虫", "电萤虫精灵", "male", "淡水"),
        "illumise" to PokemonData(314, "虫", "甜甜萤精灵", "female", "淡水"),
        "roselia" to PokemonData(315, "草/毒", "毒蔷薇精灵", "female", "花草草原"),
        "gulpin" to PokemonData(316, "毒", "溶食兽精灵", "male", "沼泽"),
        "swalot" to PokemonData(317, "毒", "吞食兽精灵", "male", "沼泽"),
        "carvanha" to PokemonData(318, "水/恶", "利牙鱼精灵", "male", "海洋, 沼泽"),
        "sharpedo" to PokemonData(319, "水/恶", "巨牙鲨精灵", "male", "海洋"),
        "wailmer" to PokemonData(320, "水", "吼吼鲸精灵", "male", "深海, 海洋"),

        // 321-330
        "wailord" to PokemonData(321, "水", "吼鲸王精灵", "male", "深海, 海洋"),
        "numel" to PokemonData(322, "火/地面", "呆火驼精灵", "male", "恶地, 火山"),
        "camerupt" to PokemonData(323, "火/地面", "喷火驼精灵", "male", "恶地, 火山"),
        "torkoal" to PokemonData(324, "火", "煤炭龟精灵", "male", "山地"),
        "spoink" to PokemonData(325, "超能力", "跳跳猪精灵", "male", "丘陵"),
        "grumpig" to PokemonData(326, "超能力", "噗噗猪精灵", "male", "丘陵"),
        "spinda" to PokemonData(327, "一般", "晃晃斑精灵", "male", "丘陵"),
        "trapinch" to PokemonData(328, "地面", "大颚蚁精灵", "male", "沙漠"),
        "vibrava" to PokemonData(329, "地面/龙", "超音波幼虫精灵", "male", "沙漠"),
        "flygon" to PokemonData(330, "地面/龙", "沙漠蜻蜓精灵", "male", "沙漠"),

        // 331-340
        "cacnea" to PokemonData(331, "草", "刺球仙人掌精灵", "male", "恶地, 沙漠"),
        "cacturne" to PokemonData(332, "草/恶", "梦歌仙人掌精灵", "male", "恶地, 沙漠"),
        "swablu" to PokemonData(333, "一般/飞行", "青绵鸟精灵", "male", "山地"),
        "altaria" to PokemonData(334, "龙/飞行", "七夕青鸟精灵", "male", "山地"),
        "zangoose" to PokemonData(335, "一般", "猫鼬斩精灵", "male", "草原"),
        "seviper" to PokemonData(336, "毒", "饭匙蛇精灵", "male", "草原"),
        "lunatone" to PokemonData(337, "岩石/超能力", "月石精灵", "genderless", "溶洞, 山地"),
        "solrock" to PokemonData(338, "岩石/超能力", "太阳岩精灵", "genderless", "溶洞, 山地"),
        "barboach" to PokemonData(339, "水/地面", "泥泥鳅精灵", "male", "沼泽"),
        "whiscash" to PokemonData(340, "水/地面", "鲶鱼王精灵", "male", "沼泽"),

        // 341-350
        "corphish" to PokemonData(341, "水", "龙虾小兵精灵", "male", "淡水"),
        "crawdaunt" to PokemonData(342, "水/恶", "铁螯龙虾精灵", "male", "淡水"),
        "baltoy" to PokemonData(343, "地面/超能力", "天秤偶精灵", "genderless", "恶地, 沙漠"),
        "claydol" to PokemonData(344, "地面/超能力", "念力土偶精灵", "genderless", "恶地, 沙漠"),
        "lileep" to PokemonData(345, "岩石/草", "触手百合精灵", "male", "海洋"),
        "cradily" to PokemonData(346, "岩石/草", "摇篮百合精灵", "male", "海洋"),
        "anorith" to PokemonData(347, "岩石/虫", "太古羽虫精灵", "male", "海洋"),
        "armaldo" to PokemonData(348, "岩石/虫", "太古盔甲精灵", "male", "海洋"),
        "feebas" to PokemonData(349, "水", "丑丑鱼精灵", "male", "海岸, 海洋"),
        "milotic" to PokemonData(350, "水", "美纳斯精灵", "female", "海岸, 海洋"),

        // 351-360
        "castform" to PokemonData(351, "一般", "飘浮泡泡精灵", "male", "平原"),
        "kecleon" to PokemonData(352, "一般", "变隐龙精灵", "male", "丛林, 热带岛屿"),
        "shuppet" to PokemonData(353, "幽灵", "怨影娃娃精灵", "male", "阴森森林"),
        "banette" to PokemonData(354, "幽灵", "诅咒娃娃精灵", "male", "阴森森林"),
        "duskull" to PokemonData(355, "幽灵", "夜巡灵精灵", "male", "阴森森林"),
        "dusclops" to PokemonData(356, "幽灵", "彷徨夜灵精灵", "male", "阴森森林"),
        "tropius" to PokemonData(357, "草/飞行", "热带龙精灵", "male", "丛林"),
        "chimecho" to PokemonData(358, "超能力", "风铃铃精灵", "male", "森林, 山地, 阴森森林, 针叶林"),
        "absol" to PokemonData(359, "恶", "阿勃梭鲁精灵", "male", "山地, 雪山, 针叶林"),
        "wynaut" to PokemonData(360, "超能力", "小果然精灵", "male", "地下"),

        // 361-370
        "snorunt" to PokemonData(361, "冰", "雪童子精灵", "male", "冰冻地区"),
        "glalie" to PokemonData(362, "冰", "冰鬼护精灵", "male", "冰冻地区"),
        "spheal" to PokemonData(363, "冰/水", "海豹球精灵", "male", "冰冻海洋"),
        "sealeo" to PokemonData(364, "冰/水", "海魔狮精灵", "male", "冰冻海洋"),
        "walrein" to PokemonData(365, "冰/水", "帝牙海狮精灵", "male", "冰冻海洋"),
        "clamperl" to PokemonData(366, "水", "珍珠贝精灵", "male", "深海, 温暖海洋"),
        "huntail" to PokemonData(367, "水", "猎斑鱼精灵", "male", "深海, 温暖海洋"),
        "gorebyss" to PokemonData(368, "水", "樱花鱼精灵", "female", "深海, 温暖海洋"),
        "relicanth" to PokemonData(369, "水/岩石", "古空棘鱼精灵", "male", "深海, 海洋"),
        "luvdisc" to PokemonData(370, "水", "爱心鱼精灵", "female", "温暖海洋"),

        // 371-380
        "bagon" to PokemonData(371, "龙", "宝贝龙精灵", "male", "山峰, 溶洞"),
        "shelgon" to PokemonData(372, "龙", "甲壳龙精灵", "male", "溶洞"),
        "salamence" to PokemonData(373, "龙/飞行", "暴飞龙精灵", "male", "山峰, 溶洞"),
        "beldum" to PokemonData(374, "钢/超能力", "铁哑铃精灵", "genderless", "溶洞, 山峰"),
        "metang" to PokemonData(375, "钢/超能力", "金属怪精灵", "genderless", "溶洞, 山峰"),
        "metagross" to PokemonData(376, "钢/超能力", "巨金怪精灵", "genderless", "溶洞, 山峰"),
        "regirock" to PokemonData(377, "岩石", "雷吉洛克精灵", "genderless", "沙漠"),
        "regice" to PokemonData(378, "冰", "雷吉艾斯精灵", "genderless", "冰冻地区"),
        "registeel" to PokemonData(379, "钢", "雷吉斯奇鲁精灵", "genderless", "山地"),
        "latias" to PokemonData(380, "龙/超能力", "拉帝亚斯精灵", "female", "城市"),

        // 381-386 (传说精灵)
        "latios" to PokemonData(381, "龙/超能力", "拉帝欧斯精灵", "male", "城市"),
        "kyogre" to PokemonData(382, "水", "盖欧卡精灵", "genderless", "深海"),
        "groudon" to PokemonData(383, "地面", "固拉多精灵", "genderless", "火山"),
        "rayquaza" to PokemonData(384, "龙/飞行", "烈空坐精灵", "genderless", "山峰"),
        "jirachi" to PokemonData(385, "钢/超能力", "基拉祈精灵", "genderless", "山峰"),
        "deoxys" to PokemonData(386, "超能力", "代欧奇希斯精灵", "genderless", "山峰")
    )
}
