# 放生保留图鉴功能说明

## 功能概述

新增了一个配置选项 `pokemon.keep-dex-on-release`，用于控制当玩家放生精灵时是否保留图鉴记录。

## 配置选项

### 配置文件位置
- 主配置文件：`plugins/AceDex/config.yml`

### 配置项
```yaml
pokemon:
  keep-dex-on-release: false  # 默认值：false
```

### 配置说明
- `true`：启用保留图鉴记录功能
  - 放生精灵时，图鉴记录将被保留
  - 玩家会收到消息："§e精灵 §f[精灵名] §e已放生（图鉴记录已保留）"
  
- `false`：禁用保留图鉴记录功能（默认行为）
  - 放生精灵时，如果玩家没有其他相同精灵，图鉴记录将被移除
  - 如果玩家仍有其他相同精灵，图鉴记录保留
  - 玩家会收到相应的消息提示

## 命令使用

### 管理员命令
```
/acedx config keep-dex-on-release <true|false|status>
```

#### 子命令说明
- `true` / `on` / `enable`：启用保留图鉴记录功能
- `false` / `off` / `disable`：禁用保留图鉴记录功能
- `status` / `check`：查看当前设置状态

#### 示例
```
# 启用保留图鉴记录功能
/acedx config keep-dex-on-release true

# 禁用保留图鉴记录功能
/acedx config keep-dex-on-release false

# 查看当前状态
/acedx config keep-dex-on-release status

# 查看所有配置选项
/acedx config list
```

### 权限要求
- 使用配置命令需要 `acedx.admin` 权限

## 功能行为

### 启用保留图鉴记录时
1. 玩家放生精灵
2. 系统不会重新检测玩家的精灵数据
3. 图鉴记录保持不变
4. 清除相关缓存并刷新GUI
5. 发送保留图鉴记录的提示消息

### 禁用保留图鉴记录时（默认）
1. 玩家放生精灵
2. 系统重新检测玩家的精灵数据
3. 如果玩家不再拥有该种类精灵，从图鉴中移除记录
4. 如果玩家仍有其他相同精灵，保留图鉴记录
5. 清除相关缓存并刷新GUI
6. 发送相应的提示消息

## 技术实现

### 配置管理
- 在 `AceDexConfig.kt` 中添加了 `keepDexOnRelease` 属性
- 在 `setDefaults()` 方法中设置默认值
- 在 `loadConfigValues()` 方法中加载配置值

### 放生处理逻辑
- 修改了 `PokemonCaptureListener.kt` 中的 `handlePokemonReleased` 方法
- 根据配置选项决定是否重新检测玩家精灵数据
- 提供不同的消息反馈

### 命令系统
- 在 `AceDexCommand.kt` 中添加了 `handleConfig` 方法
- 支持动态修改配置并保存到文件
- 提供完整的Tab补全支持

## 注意事项

1. 修改配置后会自动重新加载插件配置
2. 该功能不影响精灵捕获时的图鉴记录行为
3. 配置更改会立即生效，无需重启服务器
4. 建议在服务器维护时间进行配置更改，以避免影响玩家体验
