package cn.acebrand.acedex.listener

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.data.PlayerPokemonData
import cn.acebrand.acedex.util.PokemonInfo
import cn.acebrand.acedex.util.CobblemonItemHelper
import com.cobblemon.mod.common.api.events.CobblemonEvents
import com.cobblemon.mod.common.api.events.pokemon.PokemonCapturedEvent
import com.cobblemon.mod.common.api.events.storage.ReleasePokemonEvent
import com.cobblemon.mod.common.pokemon.Pokemon
import net.minecraft.server.level.ServerPlayer
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener

/**
 * 精灵捕获事件监听器
 * 自动将捕获的精灵保存到玩家本地数据
 */
class PokemonCaptureListener(private val plugin: AceDex) : Listener {

    init {
        // 注册Cobblemon事件监听器
        registerCobblemonEvents()
    }

    /**
     * 注册Cobblemon事件
     */
    private fun registerCobblemonEvents() {
        try {
            // 监听精灵捕获事件
            CobblemonEvents.POKEMON_CAPTURED.subscribe { event ->
                handlePokemonCaptured(event)
            }

            // 监听精灵放生事件
            CobblemonEvents.POKEMON_RELEASED_EVENT_POST.subscribe { event ->
                handlePokemonReleased(event)
            }

            plugin.logger.info("精灵捕获和放生事件监听器已注册")
            
        } catch (e: Exception) {
            plugin.logger.warning("注册精灵捕获事件监听器失败: ${e.message}")
        }
    }

    /**
     * 处理精灵放生事件
     */
    private fun handlePokemonReleased(event: ReleasePokemonEvent.Post) {
        try {
            val serverPlayer = event.player
            val pokemon = event.pokemon

            // 获取Bukkit玩家对象
            val playerUUID = try {
                // 通过gameProfile获取UUID
                serverPlayer.gameProfile.id
            } catch (e: Exception) {
                plugin.logger.warning("无法获取玩家UUID: ${e.message}")
                return
            }

            val bukkitPlayer = Bukkit.getPlayer(playerUUID)
            if (bukkitPlayer == null || !bukkitPlayer.isOnline) {
                return
            }

            if (plugin.config.enableDebug) {
                plugin.logger.info("玩家 ${bukkitPlayer.name} 放生了精灵: ${pokemon.species.name}")
            }

            // 延迟更新数据，确保放生操作完成
            plugin.server.scheduler.runTaskLater(plugin, Runnable {
                // 重新检测玩家精灵数据并保存
                val updatedData = plugin.pokemonDetector.detectAndSavePlayerPokemon(bukkitPlayer)

                // 清除精灵相关的缓存
                val pokemonName = pokemon.species.name
                val nationalDex = pokemon.species.nationalPokedexNumber
                plugin.pokemonItemCreator.clearPokemonCache(pokemonName, nationalDex)

                // 清除GUI缓存以确保进度条更新
                plugin.mainGui.clearMainInventoryCache(bukkitPlayer)
                plugin.mainGui.clearGenerationInventoryCache(bukkitPlayer)

                // 通知GUI更新进度条
                if (plugin.config.enableDebug) {
                    plugin.logger.info("放生精灵后刷新GUI: ${bukkitPlayer.name}")
                }
                plugin.mainGui.refreshCurrentGui(bukkitPlayer)

                // 检查是否还有相同的精灵
                val chineseName = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonName)

                if (updatedData != null) {
                    val allPokemon = updatedData.partyPokemon + updatedData.pcPokemon
                    val stillHasSamePokemon = allPokemon.any { it.name == pokemonName }

                    if (stillHasSamePokemon) {
                        bukkitPlayer.sendMessage("§e精灵 §f$chineseName §e已放生（你仍拥有其他相同精灵）")
                    } else {
                        bukkitPlayer.sendMessage("§c✗ 精灵 §e$chineseName §c已从图鉴中移除！")
                    }
                } else {
                    bukkitPlayer.sendMessage("§c✗ 精灵 §e$chineseName §c已放生")
                }

            }, 20L) // 延迟1秒确保数据同步

        } catch (e: Exception) {
            plugin.logger.warning("处理精灵放生事件失败: ${e.message}")
        }
    }

    /**
     * 处理精灵捕获事件
     */
    private fun handlePokemonCaptured(event: PokemonCapturedEvent) {
        try {
            val pokemon = event.pokemon
            val player = event.player
            
            if (pokemon != null && player != null) {
                // 在主线程中处理数据保存
                Bukkit.getScheduler().runTask(plugin, Runnable {
                    saveCapturedPokemonToLocalData(pokemon, player)
                })
            }
            
        } catch (e: Exception) {
            plugin.logger.warning("处理精灵捕获事件失败: ${e.message}")
        }
    }

    /**
     * 将捕获的精灵保存到本地数据
     */
    private fun saveCapturedPokemonToLocalData(pokemon: Pokemon, serverPlayer: ServerPlayer) {
        try {
            // 获取Bukkit玩家对象
            val playerUUID = try {
                // 通过gameProfile获取UUID
                serverPlayer.gameProfile.id
            } catch (e: Exception) {
                plugin.logger.warning("无法获取玩家UUID: ${e.message}")
                return
            }

            val bukkitPlayer = Bukkit.getPlayer(playerUUID)
            if (bukkitPlayer == null || !bukkitPlayer.isOnline) {
                return
            }

            // 创建精灵信息
            val pokemonInfo = createPokemonInfo(pokemon)
            
            // 获取现有的玩家数据
            val existingData = plugin.dataStorage.loadPlayerData(bukkitPlayer.uniqueId, bukkitPlayer.name)
            
            // 支持重复精灵 - 每次捕获都添加到本地文件
            // 这样放生一个重复精灵时，其他相同精灵仍然显示为已收集

            // 检查是否是首次捕获这种精灵
            val allExistingPokemon = existingData.partyPokemon + existingData.pcPokemon
            val isFirstCapture = !allExistingPokemon.any { it.name == pokemonInfo.name && it.nationalDex == pokemonInfo.nationalDex }

            // 添加到队伍精灵列表（新捕获的通常在队伍中）
            val updatedPartyPokemon = existingData.partyPokemon.toMutableList()
            updatedPartyPokemon.add(pokemonInfo)
                
            // 创建更新后的数据
            val updatedData = PlayerPokemonData(
                partyPokemon = updatedPartyPokemon,
                pcPokemon = existingData.pcPokemon,
                totalCaught = updatedPartyPokemon.size + existingData.pcPokemon.size
            )
                
            // 保存到本地文件
            plugin.dataStorage.savePlayerData(bukkitPlayer.uniqueId, bukkitPlayer.name, updatedData)

            if (plugin.config.enableDebug) {
                plugin.logger.info("已保存捕获的精灵到本地数据: ${pokemonInfo.name} (玩家: ${bukkitPlayer.name}, 首次捕获: $isFirstCapture)")
            }

            // 发送提示消息给玩家 - 使用中文名称
            val chineseName = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonInfo.name)
            if (isFirstCapture) {
                bukkitPlayer.sendMessage("§a✓ 精灵 §e$chineseName §a已添加到图鉴！")
            } else {
                bukkitPlayer.sendMessage("§a✓ 精灵 §e$chineseName §a已捕获（重复精灵）")
            }

            // 通知GUI更新进度条并检查奖励
            plugin.server.scheduler.runTaskLater(plugin, Runnable {
                // 清除精灵相关的缓存
                plugin.pokemonItemCreator.clearPokemonCache(pokemonInfo.name, pokemonInfo.nationalDex)

                // 清除GUI缓存以确保进度条更新
                plugin.mainGui.clearMainInventoryCache(bukkitPlayer)
                plugin.mainGui.clearGenerationInventoryCache(bukkitPlayer)

                // 检查世代进度奖励和全世代进度奖励
                checkProgressRewards(bukkitPlayer, pokemonInfo)

                // 如果玩家当前打开的是图鉴界面，刷新界面
                plugin.mainGui.refreshCurrentGui(bukkitPlayer)
            }, 1L) // 延迟1tick确保数据已保存
            
        } catch (e: Exception) {
            plugin.logger.warning("保存捕获精灵到本地数据失败: ${e.message}")
        }
    }

    /**
     * 检查进度奖励
     */
    private fun checkProgressRewards(player: Player, pokemonInfo: PokemonInfo) {
        try {
            // 获取精灵所属世代
            val generation = plugin.generationManager.getGenerationByPokemon(pokemonInfo.name)
            if (generation != null) {
                // 获取当前世代进度
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationProgress = allProgress.generationProgresses[generation.id]

                if (generationProgress != null) {
                    // 检查世代进度奖励
                    plugin.rewardManager.checkGenerationRewards(player, generation.id, generationProgress.percentage)
                }
            }

            // 检查全世代进度奖励
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            plugin.rewardManager.checkOverallProgressRewards(player, allProgress.overallPercentage)

        } catch (e: Exception) {
            plugin.logger.warning("检查进度奖励失败: ${e.message}")
        }
    }

    /**
     * 创建精灵信息对象
     */
    private fun createPokemonInfo(pokemon: Pokemon): PokemonInfo {
        return try {
            val species = pokemon.species
            val nationalDex = species.nationalPokedexNumber
            
            PokemonInfo(
                name = species.name,
                nationalDex = nationalDex,
                generation = getGenerationFromDexNumber(nationalDex)
            )
            
        } catch (e: Exception) {
            // 如果获取失败，创建一个基本的信息对象
            PokemonInfo(
                name = pokemon.species.name,
                nationalDex = 0,
                generation = "unknown"
            )
        }
    }

    /**
     * 根据图鉴编号获取世代信息
     */
    private fun getGenerationFromDexNumber(dexNumber: Int): String {
        return when (dexNumber) {
            in 1..151 -> "gen1"
            in 152..251 -> "gen2"
            in 252..386 -> "gen3"
            in 387..493 -> "gen4"
            in 494..649 -> "gen5"
            in 650..721 -> "gen6"
            in 722..809 -> "gen7"
            in 810..905 -> "gen8"
            in 906..1025 -> "gen9"
            else -> "unknown"
        }
    }

    /**
     * 手动添加精灵到玩家数据（用于指令）
     */
    fun addPokemonToPlayerData(player: Player, pokemonName: String): Boolean {
        return try {
            // 获取精灵种类信息
            val species = CobblemonItemHelper.getSpeciesByName(pokemonName)
            if (species == null) {
                player.sendMessage("§c未找到精灵: $pokemonName")
                return false
            }

            // 创建精灵信息
            val pokemonInfo = PokemonInfo(
                name = species.name,
                nationalDex = species.nationalPokedexNumber,
                generation = getGenerationFromDexNumber(species.nationalPokedexNumber)
            )

            // 获取现有数据
            val existingData = plugin.dataStorage.loadPlayerData(player.uniqueId, player.name)
            
            // 检查重复
            val isDuplicate = existingData.partyPokemon.any { it.name == pokemonInfo.name && it.nationalDex == pokemonInfo.nationalDex } ||
                             existingData.pcPokemon.any { it.name == pokemonInfo.name && it.nationalDex == pokemonInfo.nationalDex }
            
            if (isDuplicate) {
                val chineseName = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonInfo.name)
                player.sendMessage("§e精灵 §f$chineseName §e已存在于图鉴中，跳过添加")
                return false
            }

            // 添加到队伍精灵
            val updatedPartyPokemon = existingData.partyPokemon.toMutableList()
            updatedPartyPokemon.add(pokemonInfo)
            
            val updatedData = PlayerPokemonData(
                partyPokemon = updatedPartyPokemon,
                pcPokemon = existingData.pcPokemon,
                totalCaught = updatedPartyPokemon.size + existingData.pcPokemon.size
            )
            
            // 保存数据
            plugin.dataStorage.savePlayerData(player.uniqueId, player.name, updatedData)

            val chineseName = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonInfo.name)
            player.sendMessage("§a✓ 精灵 §e$chineseName §a已添加到图鉴！")

            // 通知GUI更新进度条
            plugin.server.scheduler.runTaskLater(plugin, Runnable {
                plugin.mainGui.refreshCurrentGui(player)
            }, 1L)
            
            if (plugin.config.enableDebug) {
                plugin.logger.info("通过指令添加精灵到本地数据: ${pokemonInfo.name} (玩家: ${player.name})")
            }
            
            return true
            
        } catch (e: Exception) {
            plugin.logger.warning("通过指令添加精灵失败: ${e.message}")
            player.sendMessage("§c添加精灵时发生错误: ${e.message}")
            return false
        }
    }

    /**
     * 批量添加精灵到玩家数据
     */
    fun addMultiplePokemonToPlayerData(player: Player, pokemonNames: List<String>): Int {
        var successCount = 0
        
        for (pokemonName in pokemonNames) {
            if (addPokemonToPlayerData(player, pokemonName)) {
                successCount++
            }
        }
        
        if (successCount > 0) {
            player.sendMessage("§a成功添加 §e$successCount §a个精灵到图鉴！")
        }
        
        return successCount
    }
}
