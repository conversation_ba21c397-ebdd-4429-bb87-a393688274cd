/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.util

/**
 * 精灵信息数据类
 * 统一的精灵信息表示
 */
data class PokemonInfo(
    val name: String,
    val nationalDex: Int,
    val generation: String
)
