/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.command

import cn.acebrand.acedex.AceDex
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack

/**
 * AceDex 命令处理器
 */
class AceDexCommand(private val plugin: AceDex) : CommandExecutor, TabCompleter {
    
    override fun onCommand(
        sender: CommandSender,
        command: Command,
        label: String,
        args: Array<out String>
    ): Boolean {
        
        when (args.getOrNull(0)?.lowercase()) {
            "info" -> return handleInfo(sender)
            "reload" -> return handleReload(sender)
            "status" -> return handleStatus(sender)
            "cache" -> return handleCache(sender, args)
            "refresh" -> return handleRefresh(sender)
            "check" -> return handleCheck(sender, args)
            "test-enchant" -> return handleTestEnchant(sender, args)
            "give" -> return handleGivePokemon(sender, args)
            "add" -> return handleAddPokemon(sender, args)
            "sync" -> return handleSyncPlayer(sender, args)
            "premium" -> return handlePremium(sender)
            "help" -> return handleHelp(sender)
            null -> return handleMain(sender)
            else -> return handleHelp(sender)
        }
    }
    
    /**
     * 处理主命令
     */
    private fun handleMain(sender: CommandSender): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c这个命令只能由玩家执行！")
            return true
        }

        if (!sender.hasPermission("acedex.use")) {
            sender.sendMessage("§c你没有权限使用这个命令！")
            return true
        }

        // 直接打开主GUI界面 - 从本地文件读取数据，不再进行实时检测
        plugin.mainGui.openMainMenu(sender)

        return true
    }
    
    /**
     * 处理信息命令
     */
    private fun handleInfo(sender: CommandSender): Boolean {
        sender.sendMessage("§6§l=== AceDex 详细信息 ===")
        sender.sendMessage("§e插件名称: §f${AceDex.PLUGIN_NAME}")
        sender.sendMessage("§e版本: §f${AceDex.VERSION}")
        sender.sendMessage("§e作者: §fAceBrand")
        sender.sendMessage("§e描述: §f精灵图鉴插件 - 基于Cobblemon的精灵收集和奖励系统")
        sender.sendMessage("§e支持版本: §fMinecraft 1.21.1 + Cobblemon 1.6.1")
        sender.sendMessage("")
        sender.sendMessage("§e功能特色:")
        sender.sendMessage("§7- §f九个世代的精灵分类")
        sender.sendMessage("§7- §f基于完成度的奖励系统")
        sender.sendMessage("§7- §f使用Cobblemon内部材质ID")
        sender.sendMessage("§7- §f完整的API支持")
        
        return true
    }
    
    /**
     * 处理重载命令
     */
    private fun handleReload(sender: CommandSender): Boolean {
        if (!sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§c你没有权限执行这个命令！")
            return true
        }
        
        try {
            sender.sendMessage("§e正在重载 AceDex...")
            
            plugin.reload()
            
            sender.sendMessage("§aAceDex 重载完成！")
            
        } catch (e: Exception) {
            sender.sendMessage("§c重载失败: ${e.message}")
            plugin.logger.severe("重载失败: ${e.message}")
        }
        
        return true
    }
    
    /**
     * 处理状态命令
     */
    private fun handleStatus(sender: CommandSender): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c这个命令只能由玩家执行！")
            return true
        }

        if (!sender.hasPermission("acedex.use")) {
            sender.sendMessage("§c你没有权限使用这个命令！")
            return true
        }

        // 异步显示玩家状态，避免阻塞主线程
        sender.sendMessage("§e正在获取你的图鉴状态...")

        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            try {
                // 在异步线程中获取数据
                val playerData = plugin.pokemonDetector.getPlayerPokemon(sender)
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(sender)

                // 回到主线程发送消息
                plugin.server.scheduler.runTask(plugin, Runnable {
                    sender.sendMessage("§6§l=== 你的图鉴状态 ===")
                    sender.sendMessage("§7总收集数: §a${playerData.totalCaught}")
                    sender.sendMessage("§7队伍精灵: §b${playerData.partyPokemon.size}")
                    sender.sendMessage("§7PC精灵: §d${playerData.pcPokemon.size}")
                    sender.sendMessage("")

                    // 显示世代进度
                    sender.sendMessage("§e世代完成情况:")
                    for ((generationId, progress) in allProgress.generationProgresses) {
                        val generation = plugin.generationManager.getGeneration(generationId)
                        if (generation != null) {
                            sender.sendMessage("§7- ${generation.displayName}: §f${progress.caught}§7/§f${progress.total} §7(§a${progress.percentage}%§7)")
                        }
                    }

                    sender.sendMessage("")
                    sender.sendMessage("§7总体完成度: §a${allProgress.overallPercentage}%")
                })
            } catch (e: Exception) {
                plugin.server.scheduler.runTask(plugin, Runnable {
                    sender.sendMessage("§c获取图鉴状态时发生错误，请稍后重试")
                })
            }
        })

        return true
    }

    /**
     * 处理刷新命令
     */
    private fun handleRefresh(sender: CommandSender): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c这个命令只能由玩家执行！")
            return true
        }

        if (!sender.hasPermission("acedex.use")) {
            sender.sendMessage("§c你没有权限使用这个命令！")
            return true
        }

        // 检查玩家是否在图鉴界面
        val currentGui = plugin.mainGui.openGuis[sender]
        if (currentGui == null) {
            sender.sendMessage("§c请先打开图鉴界面再使用此命令！")
            return true
        }

        sender.sendMessage("§e正在刷新图鉴数据...")

        // 手动触发数据更新检测
        plugin.mainGui.triggerDataUpdateCheck(sender)

        return true
    }

    /**
     * 处理缓存命令
     */
    private fun handleCache(sender: CommandSender, args: Array<out String>): Boolean {
        if (!sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§c你没有权限执行这个命令！")
            return true
        }

        val subCommand = args.getOrNull(1)?.lowercase()

        when (subCommand) {
            "stats", "status" -> {
                // 显示缓存统计
                sender.sendMessage("§6§l=== 缓存状态统计 ===")

                // 精灵物品缓存统计
                val itemStats = plugin.pokemonItemCreator.getItemCacheStats()
                sender.sendMessage("§e精灵物品缓存:")
                sender.sendMessage("§7$itemStats")

                // 预加载管理器统计
                try {
                    val preloadStats = plugin.pokemonModelPreloader.getCacheStats()
                    sender.sendMessage("§e精灵模型预加载:")
                    sender.sendMessage("§7$preloadStats")
                } catch (e: UninitializedPropertyAccessException) {
                    sender.sendMessage("§7精灵模型预加载: 未初始化")
                }

                // GUI按钮缓存统计
                try {
                    val guiButtonStats = plugin.guiButtonCacheManager.getCacheStats()
                    sender.sendMessage("§eGUI按钮缓存:")
                    sender.sendMessage("§7$guiButtonStats")
                } catch (e: UninitializedPropertyAccessException) {
                    sender.sendMessage("§7GUI按钮缓存: 未初始化")
                }

                sender.sendMessage("§7缓存用于避免重复创建已收集精灵的物品模型")
                sender.sendMessage("§7这样可以保持附魔效果不丢失")
            }
            "clear" -> {
                // 清理缓存
                plugin.pokemonItemCreator.clearItemCache()
                try {
                    plugin.pokemonModelPreloader.clearCache()
                } catch (e: UninitializedPropertyAccessException) {
                    // 预加载管理器未初始化，跳过
                }
                try {
                    plugin.guiButtonCacheManager.clearCache()
                } catch (e: UninitializedPropertyAccessException) {
                    // GUI按钮缓存管理器未初始化，跳过
                }
                sender.sendMessage("§a已清理所有缓存（精灵物品、模型预加载、GUI按钮）")
                sender.sendMessage("§7下次打开菜单时将重新创建所有物品")
            }
            "refresh" -> {
                // 刷新玩家数据文件
                if (sender is Player) {
                    sender.sendMessage("§e正在重新检测你的精灵数据...")
                    plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                        val data = plugin.pokemonDetector.detectAndSavePlayerPokemon(sender)
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (data != null) {
                                sender.sendMessage("§a已更新你的精灵数据文件")
                                sender.sendMessage("§7检测到 ${data.totalCaught} 只精灵")

                                // 刷新图鉴界面
                                plugin.mainGui.refreshCurrentGui(sender)
                                sender.sendMessage("§7图鉴进度已更新！")
                            } else {
                                sender.sendMessage("§c更新失败，请稍后重试")
                            }
                        })
                    })
                } else {
                    sender.sendMessage("§c这个子命令只能由玩家执行！")
                }
            }
            "cleanup" -> {
                // 显示数据文件统计
                val files = plugin.pokemonDetector.getAllPlayerDataFiles()
                sender.sendMessage("§a当前有 ${files.size} 个玩家数据文件")
                sender.sendMessage("§7数据文件存储在: ${plugin.dataFolder}/playerdata/")
            }
            else -> {
                sender.sendMessage("§c用法: /acedex cache <stats|clear|refresh|cleanup>")
                sender.sendMessage("§7- stats: 显示缓存统计信息")
                sender.sendMessage("§7- clear: 清理所有物品缓存")
                sender.sendMessage("§7- refresh: 刷新你的精灵数据缓存")
                sender.sendMessage("§7- cleanup: 清理过期的数据缓存")
            }
        }

        return true
    }

    /**
     * 处理给予精灵命令
     */
    private fun handleGivePokemon(sender: CommandSender, args: Array<out String>): Boolean {
        if (!sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§c你没有权限使用这个命令！")
            return true
        }

        if (args.size < 2) {
            sender.sendMessage("§c用法: /acedex give <玩家> <精灵名称> [精灵名称2] ...")
            return true
        }

        val targetPlayerName = args[1]
        val targetPlayer = plugin.server.getPlayer(targetPlayerName)

        if (targetPlayer == null || !targetPlayer.isOnline) {
            sender.sendMessage("§c玩家 $targetPlayerName 不在线或不存在！")
            return true
        }

        val pokemonNames = args.drop(2)
        if (pokemonNames.isEmpty()) {
            sender.sendMessage("§c请指定至少一个精灵名称！")
            return true
        }

        // 使用捕获监听器的方法添加精灵
        val successCount = plugin.pokemonCaptureListener.addMultiplePokemonToPlayerData(targetPlayer, pokemonNames)

        sender.sendMessage("§a成功为玩家 §e${targetPlayer.name} §a添加了 §e$successCount §a个精灵到图鉴！")

        return true
    }

    /**
     * 处理添加精灵命令（给自己添加）
     */
    private fun handleAddPokemon(sender: CommandSender, args: Array<out String>): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c这个命令只能由玩家执行！")
            return true
        }

        if (!sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§c你没有权限使用这个命令！")
            return true
        }

        if (args.size < 2) {
            sender.sendMessage("§c用法: /acedex add <精灵名称> [精灵名称2] ...")
            return true
        }

        val pokemonNames = args.drop(1)
        val successCount = plugin.pokemonCaptureListener.addMultiplePokemonToPlayerData(sender, pokemonNames)

        return true
    }

    /**
     * 处理同步玩家数据命令
     */
    private fun handleSyncPlayer(sender: CommandSender, args: Array<out String>): Boolean {
        if (!sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§c你没有权限使用这个命令！")
            return true
        }

        if (args.size < 2) {
            sender.sendMessage("§c用法: /acedex sync <玩家名称>")
            sender.sendMessage("§7说明: 同步指定玩家的PC和背包精灵数据到本地")
            return true
        }

        val targetPlayerName = args[1]
        val targetPlayer = plugin.server.getPlayer(targetPlayerName)

        if (targetPlayer == null || !targetPlayer.isOnline) {
            sender.sendMessage("§c玩家 $targetPlayerName 不在线或不存在！")
            return true
        }

        sender.sendMessage("§e正在同步玩家 §f${targetPlayer.name} §e的精灵数据...")

        // 异步执行同步操作
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            try {
                // 使用PokemonDetector检测并保存玩家数据
                val playerData = plugin.pokemonDetector.detectAndSavePlayerPokemon(targetPlayer)

                // 回到主线程发送结果
                plugin.server.scheduler.runTask(plugin, Runnable {
                    if (playerData != null) {
                        sender.sendMessage("§a§l=== 同步完成 ===")
                        sender.sendMessage("§e玩家: §f${targetPlayer.name}")
                        sender.sendMessage("§e队伍精灵: §f${playerData.partyPokemon.size} 只")
                        sender.sendMessage("§e PC精灵: §f${playerData.pcPokemon.size} 只")
                        sender.sendMessage("§e总计: §f${playerData.totalCaught} 只")
                        sender.sendMessage("§a数据已保存到本地文件！")

                        // 刷新玩家的图鉴界面（如果正在查看）
                        plugin.mainGui.refreshCurrentGui(targetPlayer)

                        // 通知目标玩家
                        targetPlayer.sendMessage("§a管理员已同步了你的精灵数据到图鉴！")
                        targetPlayer.sendMessage("§7图鉴进度已更新，如果正在查看图鉴界面会自动刷新")

                    } else {
                        sender.sendMessage("§c同步失败，请确保Cobblemon正常运行")
                    }
                })

            } catch (e: Exception) {
                plugin.server.scheduler.runTask(plugin, Runnable {
                    sender.sendMessage("§c同步过程中发生错误: ${e.message}")
                })
                plugin.logger.warning("同步玩家 ${targetPlayer.name} 数据失败: ${e.message}")
            }
        })

        return true
    }

    /**
     * 处理精灵检查命令
     */
    private fun handleCheck(sender: CommandSender, args: Array<out String>): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c这个命令只能由玩家执行！")
            return true
        }

        val pokemonName = args.getOrNull(1)
        if (pokemonName == null) {
            sender.sendMessage("§c用法: /acedex check <精灵名称>")
            sender.sendMessage("§7例如: /acedex check pikachu")
            return true
        }

        val hasPokemon = plugin.pokemonDetector.checkPokemon(sender, pokemonName)
        if (hasPokemon) {
            sender.sendMessage("§a你拥有精灵: §f$pokemonName")
        } else {
            sender.sendMessage("§c你没有精灵: §f$pokemonName")
        }

        return true
    }

    /**
     * 处理附魔测试命令
     */
    private fun handleTestEnchant(sender: CommandSender, args: Array<out String>): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c这个命令只能由玩家执行！")
            return true
        }

        if (!sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§c你没有权限执行这个命令！")
            return true
        }

        val pokemonName = args.getOrNull(1) ?: "pikachu"

        sender.sendMessage("§6=== 附魔测试：$pokemonName ===")

        try {
            // 创建已收集的精灵物品
            val caughtItem = plugin.pokemonItemCreator.createPokemonItem(pokemonName, 25, true)
            sender.sendMessage("§a已收集精灵物品创建成功")

            // 输出调试信息
            val debugInfo = getItemDebugInfo(caughtItem, pokemonName)
            debugInfo.split("\n").forEach { line ->
                if (line.isNotBlank()) {
                    sender.sendMessage("§7$line")
                }
            }

            // 给玩家物品以便检查
            sender.inventory.addItem(caughtItem)
            sender.sendMessage("§e已收集版本已添加到你的背包")
            sender.sendMessage("§a请检查物品是否有附魔闪光效果！")

            // 创建未收集的精灵物品作为对比
            val uncaughtItem = plugin.pokemonItemCreator.createPokemonItem(pokemonName, 25, false)
            sender.inventory.addItem(uncaughtItem)
            sender.sendMessage("§7未收集版本也已添加作为对比")

            // 创建一个普通的附魔物品作为参考（使用相同的附魔类型）
            val referenceItem = org.bukkit.inventory.ItemStack(org.bukkit.Material.DIAMOND_SWORD)
            referenceItem.addUnsafeEnchantment(org.bukkit.enchantments.Enchantment.UNBREAKING, 1)
            val referenceMeta = referenceItem.itemMeta
            referenceMeta?.setDisplayName("§b参考物品 - 耐久附魔")
            referenceMeta?.lore = listOf("§7这个物品使用了和精灵相同的附魔", "§7如果这个有光效但精灵没有，说明还有其他问题")
            referenceItem.itemMeta = referenceMeta
            sender.inventory.addItem(referenceItem)
            sender.sendMessage("§b参考物品（耐久附魔钻石剑）已添加，用于对比光效")

        } catch (e: Exception) {
            sender.sendMessage("§c测试失败: ${e.message}")
            plugin.logger.severe("附魔测试失败: ${e.message}")
            e.printStackTrace()
        }

        return true
    }

    /**
     * 处理帮助命令
     */
    private fun handleHelp(sender: CommandSender): Boolean {
        sender.sendMessage("§6§l=== AceDex 命令帮助 ===")
        sender.sendMessage("§e/acedex §7- 显示主界面")
        sender.sendMessage("§e/acedex info §7- 显示插件详细信息")
        sender.sendMessage("§e/acedex status §7- 显示你的图鉴状态")
        sender.sendMessage("§e/acedex refresh §7- 刷新当前图鉴界面数据")
        sender.sendMessage("§e/acedex check <精灵名> §7- 检查是否拥有指定精灵")
        sender.sendMessage("§e/acedex premium §7- 打开付费奖励菜单")
        sender.sendMessage("§e/acedex help §7- 显示这个帮助信息")

        if (sender.hasPermission("acedex.admin")) {
            sender.sendMessage("§e/acedex reload §7- 重载插件配置 §c(管理员)")
            sender.sendMessage("§e/acedex cache <stats|clear|refresh> §7- 管理数据文件 §c(管理员)")
            sender.sendMessage("§e/acedex give <玩家> <精灵名> §7- 给玩家添加精灵到图鉴 §c(管理员)")
            sender.sendMessage("§e/acedex add <精灵名> §7- 给自己添加精灵到图鉴 §c(管理员)")
            sender.sendMessage("§e/acedex sync <玩家> §7- 同步玩家PC和背包数据 §c(管理员)")
            sender.sendMessage("§e/acedex test-enchant [精灵名] §7- 测试附魔效果 §c(管理员)")
        }
        
        sender.sendMessage("")
        sender.sendMessage("§e别名: §f/dex")
        sender.sendMessage("§7提示: 捕获精灵会自动保存到图鉴，无需手动检测！")
        
        return true
    }
    
    override fun onTabComplete(
        sender: CommandSender,
        command: Command,
        alias: String,
        args: Array<out String>
    ): List<String> {
        
        if (args.size == 1) {
            val subcommands = mutableListOf("info", "status", "refresh", "check", "help")

            if (sender.hasPermission("acedex.admin")) {
                subcommands.add("reload")
                subcommands.add("cache")
                subcommands.add("give")
                subcommands.add("add")
                subcommands.add("sync")
                subcommands.add("test-enchant")
            }

            return subcommands.filter { it.startsWith(args[0].lowercase()) }
        }

        if (args.size == 2 && args[0].lowercase() == "cache") {
            val cacheSubcommands = listOf("stats", "clear", "refresh", "cleanup")
            return cacheSubcommands.filter { it.startsWith(args[1].lowercase()) }
        }

        return emptyList()
    }

    /**
     * 获取物品调试信息
     */
    private fun getItemDebugInfo(item: ItemStack, pokemonName: String): String {
        val builder = StringBuilder()
        builder.append("=== 物品调试信息 ===\n")
        builder.append("精灵名称: $pokemonName\n")
        builder.append("物品类型: ${item.type}\n")
        builder.append("物品数量: ${item.amount}\n")

        // 附魔信息
        val enchantments = item.enchantments
        if (enchantments.isNotEmpty()) {
            builder.append("附魔效果:\n")
            enchantments.forEach { (enchant, level) ->
                builder.append("  - ${enchant.key}: $level\n")
            }
        } else {
            builder.append("无附魔效果\n")
        }

        // ItemMeta信息
        val meta = item.itemMeta
        if (meta != null) {
            builder.append("显示名称: ${meta.displayName ?: "无"}\n")

            val flags = meta.itemFlags
            if (flags.isNotEmpty()) {
                builder.append("物品标志:\n")
                flags.forEach { flag ->
                    builder.append("  - $flag\n")
                }
            } else {
                builder.append("无物品标志\n")
            }

            val lore = meta.lore
            if (lore != null && lore.isNotEmpty()) {
                builder.append("描述行数: ${lore.size}\n")
            } else {
                builder.append("无描述信息\n")
            }
        } else {
            builder.append("无ItemMeta信息\n")
        }

        return builder.toString()
    }

    /**
     * 处理付费奖励命令
     */
    private fun handlePremium(sender: CommandSender): Boolean {
        if (sender !is Player) {
            sender.sendMessage("§c此命令只能由玩家执行！")
            return true
        }

        // 检查权限
        if (!sender.hasPermission("acedex.premium.view")) {
            sender.sendMessage("§c你没有权限使用付费奖励功能！")
            sender.sendMessage("§7需要权限: acedex.premium.view")
            return true
        }

        try {
            // 打开付费奖励菜单
            plugin.premiumRewardGui.openPremiumRewardGui(sender)
            sender.sendMessage("§6正在打开付费奖励菜单...")
        } catch (e: Exception) {
            sender.sendMessage("§c打开付费奖励菜单失败: ${e.message}")
            plugin.logger.warning("玩家 ${sender.name} 打开付费奖励菜单失败: ${e.message}")
        }

        return true
    }
}
