/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon

import cn.acebrand.acedex.pokemon.generation.*

/**
 * 精灵名称映射工具类
 * 负责管理精灵英文名到中文名的映射
 * 将大量数据按世代分离到单独文件中，避免混淆问题
 */
object PokemonNameMapping {

    /**
     * 精灵英文名到中文名的映射表（1-9世代完整版）
     * 通过合并各世代的映射表构建
     */
    private val pokemonChineseNames = buildMap {
        putAll(PokemonGen1Mapping.getMapping())
        putAll(PokemonGen2Mapping.getMapping())
        putAll(PokemonGen3Mapping.getMapping())
        putAll(PokemonGen4Mapping.getMapping())
        putAll(PokemonGen5Mapping.getMapping())
        putAll(PokemonGen6Mapping.getMapping())
        putAll(PokemonGen7Mapping.getMapping())
        putAll(PokemonGen8Mapping.getMapping())
        putAll(PokemonGen9Mapping.getMapping())
        putAll(PokemonSpecialMapping.getMapping())
    }

    /**
     * 获取精灵的中文名称（输入已标准化的名称）
     */
    fun getPokemonChineseName(normalizedName: String): String {
        return pokemonChineseNames[normalizedName] ?: normalizedName
    }

    /**
     * 获取精灵的中文名称（输入原始英文名称）
     */
    fun getPokemonChineseNameFromEnglish(englishName: String): String {
        val normalizedName = englishName.lowercase().replace(Regex("[^a-z0-9-]+"), "")
        return pokemonChineseNames[normalizedName] ?: englishName
    }

    /**
     * 检查是否有中文名称映射（输入已标准化的名称）
     */
    fun hasChineseName(normalizedName: String): Boolean {
        return pokemonChineseNames.containsKey(normalizedName)
    }

    /**
     * 检查是否有中文名称映射（输入原始英文名称）
     */
    fun hasChineseNameFromEnglish(englishName: String): Boolean {
        val normalizedName = englishName.lowercase().replace(Regex("[^a-z0-9-]+"), "")
        return pokemonChineseNames.containsKey(normalizedName)
    }

    /**
     * 获取所有支持的精灵名称
     */
    fun getAllSupportedPokemon(): Set<String> {
        return pokemonChineseNames.keys
    }

    /**
     * 获取映射表大小
     */
    fun getMappingSize(): Int {
        return pokemonChineseNames.size
    }
}