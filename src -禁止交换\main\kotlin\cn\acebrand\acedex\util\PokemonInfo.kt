/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.util

/**
 * 精灵获得方式枚举
 */
enum class PokemonAcquisitionMethod {
    CAUGHT,    // 捕获获得
    TRADED,    // 交换获得
    EVOLVED,   // 进化获得
    UNKNOWN    // 未知方式
}

/**
 * 精灵信息数据类
 * 统一的精灵信息表示
 */
data class PokemonInfo(
    val name: String,
    val nationalDex: Int,
    val generation: String,
    val acquisitionMethod: PokemonAcquisitionMethod = PokemonAcquisitionMethod.CAUGHT
)
