package cn.acebrand.acebiomespawner.spawner

import cn.acebrand.acebiomespawner.AceBiomeSpawner
import cn.acebrand.acebiomespawner.config.PokemonEntry
import cn.acebrand.acebiomespawner.config.SpawnerConfig
import cn.acebrand.acebiomespawner.util.AnnouncementUtil
import com.cobblemon.mod.common.api.pokemon.PokemonProperties
import org.bukkit.Bukkit
import org.bukkit.Location
import org.bukkit.Material
import org.bukkit.World
import org.bukkit.block.Biome
import org.bukkit.entity.Player
import kotlin.random.Random

/**
 * 生物群系生成器管理器
 */
class BiomeSpawnerManager {

    private val logger = AceBiomeSpawner.INSTANCE.logger

    /**
     * 执行一次完整的生成周期
     */
    fun executeSpawnCycle() {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        if (!config.enabled) {
            return
        }

        try {
            val onlinePlayers = Bukkit.getOnlinePlayers().toList()

            if (onlinePlayers.size < config.minOnlinePlayers) {
                logger.info("在线玩家数量不足，跳过生成周期")
                return
            }

            val targetPlayers = onlinePlayers.filter { player ->
                config.targetWorlds.contains(player.world.name)
            }

            if (targetPlayers.isEmpty()) {
                logger.info("目标世界中没有玩家，跳过生成周期")
                return
            }

            val selectedPlayers = selectRandomPlayers(targetPlayers, config.playersPerSpawn)

            for (player in selectedPlayers) {
                executeSpawnForPlayer(player)
            }

        } catch (e: Exception) {
            logger.severe("执行生成周期时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 随机选择玩家
     */
    private fun selectRandomPlayers(players: List<Player>, count: Int): List<Player> {
        return if (players.size <= count) {
            players
        } else {
            players.shuffled().take(count)
        }
    }

    /**
     * 为指定玩家执行生成
     */
    private fun executeSpawnForPlayer(player: Player) {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        try {
            val spawnLocation = findRandomSpawnLocation(player) ?: return
            val biome = getBiomeAt(spawnLocation)
            val biomeName = getBiomeName(biome)

            val biomeConfig = config.biomeConfigs[biomeName]
            if (biomeConfig == null || !biomeConfig.hasValidPokemon()) {
                logger.info("生物群系 $biomeName 没有配置或无有效精灵，跳过生成")
                return
            }

            // 检查生物群系是否应该生成精灵
            if (!biomeConfig.shouldSpawn()) {
                logger.info("生物群系 $biomeName 本次未触发生成（概率: ${biomeConfig.spawnChance}）")
                return
            }

            val spawnCount = Random.nextInt(config.spawnCountMin, config.spawnCountMax + 1)
            val spawnedPokemon = mutableListOf<SpawnResult>()

            for (i in 0 until spawnCount) {
                val pokemonEntry = biomeConfig.getRandomPokemon() ?: continue
                val spawnResult = spawnPokemon(spawnLocation, pokemonEntry)

                if (spawnResult != null) {
                    spawnedPokemon.add(spawnResult)
                }
            }

            if (spawnedPokemon.isNotEmpty() && config.enableAnnouncement) {
                AnnouncementUtil.sendSpawnAnnouncement(
                    spawnedPokemon,
                    biomeName,
                    spawnLocation,
                    player.name
                )
            }

            logger.info("在 $biomeName 生物群系为玩家 ${player.name} 生成了 ${spawnedPokemon.size} 只精灵")

        } catch (e: Exception) {
            logger.severe("为玩家 ${player.name} 执行生成时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 寻找随机生成位置
     */
    private fun findRandomSpawnLocation(player: Player): Location? {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        val playerLoc = player.location
        val range = config.detectionRange

        repeat(10) {
            val randomX = playerLoc.blockX + Random.nextInt(-range, range + 1)
            val randomZ = playerLoc.blockZ + Random.nextInt(-range, range + 1)

            val surfaceY = findSurfaceY(playerLoc.world, randomX, randomZ)

            if (surfaceY != null) {
                val location = Location(playerLoc.world, randomX.toDouble(), surfaceY.toDouble(), randomZ.toDouble())
                if (isValidSpawnLocation(location)) {
                    return location
                }
            }
        }

        return null
    }

    /**
     * 寻找地表Y坐标
     */
    private fun findSurfaceY(world: World?, x: Int, z: Int): Int? {
        if (world == null) return null

        for (y in world.maxHeight downTo world.minHeight) {
            val currentBlock = world.getBlockAt(x, y, z)
            val belowBlock = world.getBlockAt(x, y - 1, z)

            if (currentBlock.type == Material.AIR && belowBlock.type.isSolid) {
                return y
            }
        }

        return null
    }

    /**
     * 检查是否是有效的生成位置
     */
    private fun isValidSpawnLocation(location: Location): Boolean {
        val world = location.world ?: return false

        if (!world.worldBorder.isInside(location)) {
            return false
        }

        val currentBlock = world.getBlockAt(location)
        val aboveBlock = world.getBlockAt(location.blockX, location.blockY + 1, location.blockZ)
        val belowBlock = world.getBlockAt(location.blockX, location.blockY - 1, location.blockZ)

        return currentBlock.type == Material.AIR &&
               aboveBlock.type == Material.AIR &&
               belowBlock.type.isSolid
    }

    /**
     * 获取指定位置的生物群系
     */
    private fun getBiomeAt(location: Location): Biome {
        return location.block.biome
    }

    /**
     * 获取生物群系名称
     * 处理不同格式的生物群系名称，包括重复前缀格式
     */
    private fun getBiomeName(biome: Biome): String {
        val biomeName = biome.key.toString().lowercase()

        // 处理重复前缀格式和斜杠格式的Terralith生物群系
        val duplicatePrefixMappings = mapOf(
            "terralith:terralith_brushland" to "terralith:brushland",
            "terralith:terralith_cavegranite_caves" to "terralith:granite_caves",
            "terralith:terralith_cavedeep_caves" to "terralith:deep_caves",
            "terralith:terralith_caveandesite_caves" to "terralith:andesite_caves",
            "terralith:terralith_cavediorite_caves" to "terralith:diorite_caves",
            "terralith:terralith_cavetuff_caves" to "terralith:tuff_caves",
            "terralith:terralith_cavethermal_caves" to "terralith:thermal_caves",
            "terralith:terralith_cavefrostfire_caves" to "terralith:frostfire_caves",
            "terralith:terralith_cavemantle_caves" to "terralith:mantle_caves",
            "terralith:terralith_caveinfested_caves" to "terralith:infested_caves",
            "terralith:terralith_cavefungal_caves" to "terralith:fungal_caves",
            "terralith:terralith_hot_shrubland" to "terralith:hot_shrubland",
            "terralith:terralith_cold_shrubland" to "terralith:cold_shrubland",
            "terralith:terralith_rocky_shrubland" to "terralith:rocky_shrubland",
            "terralith:terralith_arid_highlands" to "terralith:arid_highlands",
            "terralith:terralith_alpine_grove" to "terralith:alpine_grove",
            "terralith:terralith_sakura_grove" to "terralith:sakura_grove",
            "terralith:terralith_moonlight_valley" to "terralith:moonlight_valley",
            "terralith:terralith_volcanic_crater" to "terralith:volcanic_crater",
            "terralith:terralith_ancient_sands" to "terralith:ancient_sands",
            "terralith:terralith_desert_spires" to "terralith:desert_spires",
            "terralith:terralith_tropical_jungle" to "terralith:tropical_jungle",
            "terralith:terralith_temperate_highlands" to "terralith:temperate_highlands",
            "terralith:terralith_skylands_autumn" to "terralith:skylands_autumn",
            "terralith:terralith_skylands_spring" to "terralith:skylands_spring",
            "terralith:terralith_skylands_summer" to "terralith:skylands_summer",
            "terralith:terralith_skylands_winter" to "terralith:skylands_winter",
            // 斜杠格式的洞穴生物群系
            "terralith:cave/mantle_caves" to "terralith:cave/mantle_caves", // 保持原样，因为配置中就是这个格式
            "terralith:cave/granite_caves" to "terralith:granite_caves",
            "terralith:cave/deep_caves" to "terralith:deep_caves",
            "terralith:cave/andesite_caves" to "terralith:andesite_caves",
            "terralith:cave/diorite_caves" to "terralith:diorite_caves",
            "terralith:cave/tuff_caves" to "terralith:tuff_caves",
            "terralith:cave/thermal_caves" to "terralith:thermal_caves",
            "terralith:cave/frostfire_caves" to "terralith:frostfire_caves",
            "terralith:cave/infested_caves" to "terralith:infested_caves",
            "terralith:cave/fungal_caves" to "terralith:fungal_caves"
        )

        // 检查是否是重复前缀格式
        if (duplicatePrefixMappings.containsKey(biomeName)) {
            return duplicatePrefixMappings[biomeName]!!
        }

        // 如果已经有正确的命名空间前缀，直接返回
        if (biomeName.startsWith("minecraft:") || biomeName.startsWith("terralith:")) {
            return biomeName
        }

        // 检查是否是Terralith生物群系
        val terralithKeywords = listOf(
            "alpine", "ancient", "arid", "ashen", "basalt", "blooming", "bryce", "caldera",
            "cloud", "emerald", "fractured", "frozen", "glacial", "granite", "gravel",
            "haze", "highlands", "lavender", "lush", "mirage", "moonlight", "orchid",
            "painted", "rocky", "sakura", "scarlet", "shield", "siberian", "skylands",
            "stony", "temperate", "thermal", "tropical", "volcanic", "warped", "windswept",
            "wintry", "yellowstone", "yosemite", "amethyst", "andesite", "diorite",
            "frostfire", "fungal", "infested", "mantle", "tuff", "underground", "cave",
            "brushland", "shrubland", "steppe", "mesa", "spires", "cliffs", "peaks",
            "valley", "canyon", "oasis", "marsh", "swamp", "beach", "river", "ocean"
        )

        // 检查生物群系名称是否包含Terralith关键词
        val isTerralithBiome = terralithKeywords.any { keyword ->
            biomeName.contains(keyword)
        }

        return if (isTerralithBiome) {
            "terralith:$biomeName"
        } else {
            "minecraft:$biomeName"
        }
    }

    /**
     * 生成精灵
     */
    private fun spawnPokemon(location: Location, pokemonEntry: PokemonEntry): SpawnResult? {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        try {

            val levelRange = pokemonEntry.getEffectiveLevelRange(config.levelMin, config.levelMax)
            val pokemonLevel = Random.nextInt(levelRange.first, levelRange.last + 1)

            val (canBeShiny, shinyChance) = pokemonEntry.getEffectiveShinySettings(config.allowShiny, config.shinyChance)
            val isShiny = canBeShiny && Random.nextDouble() < shinyChance

            val propertiesString = pokemonEntry.generatePropertiesString(pokemonLevel, isShiny)

            // 使用Cobblemon API生成精灵
            try {
                // 检查Cobblemon API是否可用
                if (isCobblemonApiAvailable()) {
                    val properties = PokemonProperties.parse(propertiesString)
                    if (config.showSpawnProcess) {
                        logger.info("成功解析精灵属性: $propertiesString")
                    }

                    // 获取Bukkit世界对应的Minecraft世界
                    val world = location.world
                    if (world == null) {
                        logger.warning("无法获取世界对象")
                        return null
                    }

                    // 使用主线程执行生成操作
                    Bukkit.getScheduler().runTask(AceBiomeSpawner.INSTANCE, Runnable {
                        try {
                            spawnPokemonEntity(location, properties, propertiesString)
                        } catch (e: Exception) {
                            logger.severe("生成Pokemon实体时发生错误: ${e.message}")
                            e.printStackTrace()
                        }
                    })

                    if (config.showSpawnProcess) {
                        logger.info("尝试生成精灵: $propertiesString 在位置 ${location.blockX}, ${location.blockY}, ${location.blockZ}")
                    }
                } else {
                    logger.warning("Cobblemon API不可用，无法生成精灵")
                    return null
                }

            } catch (e: Exception) {
                logger.severe("Cobblemon API调用失败: ${e.message}")
                e.printStackTrace()
                return null
            }

            return SpawnResult(
                pokemonName = pokemonEntry.name,
                level = pokemonLevel,
                isShiny = isShiny,
                location = location
            )

        } catch (e: Exception) {
            logger.severe("生成精灵 ${pokemonEntry.name} 时发生错误: ${e.message}")
            e.printStackTrace()
            return null
        }
    }

    /**
     * 为指定玩家强制执行生成（忽略生物群系概率）
     */
    fun executeSpawnForPlayer(player: Player, ignoreChance: Boolean = true, triggerPlayerName: String? = null): Boolean {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        try {
            val spawnLocation = findRandomSpawnLocation(player) ?: return false
            val biome = getBiomeAt(spawnLocation)
            val biomeName = getBiomeName(biome)

            var actualBiomeConfig = config.biomeConfigs[biomeName]
            var actualBiomeName = biomeName

            // 如果当前生物群系没有配置，随机选择一个已配置的生物群系
            if (actualBiomeConfig == null || !actualBiomeConfig.hasValidPokemon()) {
                val availableBiomes = config.biomeConfigs.filter { it.value.hasValidPokemon() }
                if (availableBiomes.isEmpty()) {
                    if (config.verboseLogging) {
                        logger.info("没有任何已配置的生物群系，跳过生成")
                    }
                    return false
                }

                // 随机选择一个已配置的生物群系
                val randomBiome = availableBiomes.keys.random()
                actualBiomeConfig = availableBiomes[randomBiome]!!
                actualBiomeName = randomBiome

                if (config.verboseLogging) {
                    logger.info("生物群系 $biomeName 没有配置，随机选择 $actualBiomeName 进行生成")
                }
            }

            // 如果是手动触发，可以选择忽略概率检查
            if (!ignoreChance && !actualBiomeConfig.shouldSpawn()) {
                if (config.verboseLogging) {
                    logger.info("生物群系 $actualBiomeName 本次未触发生成（概率: ${actualBiomeConfig.spawnChance}）")
                }
                return false
            }

            val spawnCount = Random.nextInt(config.spawnCountMin, config.spawnCountMax + 1)
            val spawnedPokemon = mutableListOf<SpawnResult>()

            for (i in 0 until spawnCount) {
                val pokemonEntry = actualBiomeConfig.getRandomPokemon() ?: continue
                val spawnResult = spawnPokemon(spawnLocation, pokemonEntry)

                if (spawnResult != null) {
                    spawnedPokemon.add(spawnResult)
                }
            }

            if (spawnedPokemon.isNotEmpty() && config.enableAnnouncement) {
                AnnouncementUtil.sendSpawnAnnouncement(
                    spawnedPokemon,
                    actualBiomeName,
                    spawnLocation,
                    player.name,
                    isManualTrigger = triggerPlayerName != null,
                    triggerPlayerName = triggerPlayerName
                )
            }

            if (config.verboseLogging) {
                logger.info("在 $actualBiomeName 生物群系为玩家 ${player.name} 生成了 ${spawnedPokemon.size} 只精灵")
            }
            return spawnedPokemon.isNotEmpty()

        } catch (e: Exception) {
            logger.severe("为玩家 ${player.name} 执行生成时发生错误: ${e.message}")
            e.printStackTrace()
            return false
        }
    }



    /**
     * 实际生成Pokemon实体的方法（使用正确的Cobblemon API）
     */
    private fun spawnPokemonEntity(location: Location, properties: PokemonProperties, propertiesString: String) {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        try {
            if (config.verboseLogging) {
                logger.info("尝试使用Cobblemon API生成Pokemon: $propertiesString")
            }

            // 获取世界对象
            val world = location.world
            if (world == null) {
                logger.severe("世界对象为null")
                return
            }

            // 获取ServerLevel
            val serverLevel = getServerLevel(world)
            if (config.showReflectionDetails) {
                logger.info("获取到ServerLevel: ${serverLevel::class.java.name}")
            }

            // 动态查找正确的Level类名（处理混淆）
            val levelClass = findLevelClass()
            if (config.showReflectionDetails) {
                logger.info("找到Level类: ${levelClass.name}")
            }

            // 使用PokemonProperties.createEntity方法创建实体
            val pokemonPropertiesClass = Class.forName("com.cobblemon.mod.common.api.pokemon.PokemonProperties")
            val createEntityMethod = pokemonPropertiesClass.getMethod("createEntity", levelClass)
            val pokemonEntity = createEntityMethod.invoke(properties, serverLevel)
            if (config.showSpawnProcess) {
                logger.info("成功创建Pokemon实体: ${pokemonEntity::class.java.name}")
            }

            // 使用Pokemon实体的实际类来查找方法
            val actualEntityClass = pokemonEntity.javaClass
            if (config.showReflectionDetails) {
                logger.info("Pokemon实体的实际类: ${actualEntityClass.name}")
            }

            // 查找并调用位置设置方法
            val success = setPokemonPosition(pokemonEntity, actualEntityClass, location, config)
            if (!success) {
                logger.severe("设置Pokemon位置失败")
                return
            }

            // 设置随机朝向
            setRandomRotation(pokemonEntity, actualEntityClass, config)

            // 添加到世界 - 使用Bukkit方式
            val addSuccess = addPokemonToBukkitWorld(location.world!!, pokemonEntity, config)
            if (!addSuccess) {
                logger.severe("添加Pokemon到世界失败")
                return
            }

            if (config.verboseLogging) {
                logger.info("✓ 成功通过API生成Pokemon: $propertiesString 在位置 ${location.blockX}, ${location.blockY}, ${location.blockZ}")
            }

        } catch (e: Exception) {
            logger.severe("使用API生成Pokemon失败: ${e.message}")
            e.printStackTrace()
        }
    }



    /**
     * 设置Pokemon位置
     */
    private fun setPokemonPosition(pokemonEntity: Any, entityClass: Class<*>, location: Location, config: SpawnerConfig): Boolean {
        // 根据日志，只有method_5814有效
        try {
            val method = entityClass.getMethod("method_5814", Double::class.javaPrimitiveType, Double::class.javaPrimitiveType, Double::class.javaPrimitiveType)
            method.invoke(pokemonEntity, location.x, location.y, location.z)
            if (config.showSpawnProcess) {
                logger.info("✓ 成功使用方法 method_5814 设置位置")
            }
            return true
        } catch (e: Exception) {
            logger.severe("设置Pokemon位置失败: ${e.message}")
            return false
        }
    }

    /**
     * 设置随机朝向
     */
    private fun setRandomRotation(pokemonEntity: Any, entityClass: Class<*>, config: SpawnerConfig) {
        val randomYaw = kotlin.random.Random.nextFloat() * 360F

        // 根据日志，field_6031在父类Entity中有效
        var currentClass = entityClass.superclass
        while (currentClass != null) {
            try {
                val field = currentClass.getDeclaredField("field_6031")
                field.isAccessible = true
                field.setFloat(pokemonEntity, randomYaw)
                if (config.showSpawnProcess) {
                    logger.info("✓ 成功在父类 ${currentClass.name} 中使用字段 field_6031 设置朝向")
                }
                return
            } catch (e: Exception) {
                // 继续查找父类
            }
            currentClass = currentClass.superclass
        }

        if (config.showSpawnProcess) {
            logger.warning("无法设置Pokemon朝向")
        }
    }

    /**
     * 使用Bukkit方式添加Pokemon到世界
     */
    private fun addPokemonToBukkitWorld(world: org.bukkit.World, pokemonEntity: Any, config: SpawnerConfig): Boolean {
        try {
            if (config.showSpawnProcess) {
                logger.info("尝试使用Bukkit方式添加Pokemon到世界...")
            }

            // 方法1: 通过CraftWorld的getHandle()直接添加到ServerLevel
            try {
                val craftWorldClass = world.javaClass
                if (config.showReflectionDetails) {
                    logger.info("World类: ${craftWorldClass.name}")
                }

                val getHandleMethod = craftWorldClass.getMethod("getHandle")
                val serverLevel = getHandleMethod.invoke(world)
                if (config.showReflectionDetails) {
                    logger.info("获取到ServerLevel: ${serverLevel.javaClass.name}")
                }

                // 查找正确的Entity基类
                val entityBaseClass = findEntityBaseClass(pokemonEntity, config)
                if (config.showReflectionDetails) {
                    logger.info("找到Entity基类: ${entityBaseClass.name}")
                }

                // 尝试addFreshEntity方法
                val addFreshEntityMethod = serverLevel.javaClass.getMethod("addFreshEntity", entityBaseClass)
                val result = addFreshEntityMethod.invoke(serverLevel, pokemonEntity)
                if (config.showSpawnProcess) {
                    logger.info("✓ 通过CraftWorld.getHandle().addFreshEntity成功，结果: $result")
                }
                return result as? Boolean ?: true

            } catch (e: Exception) {
                if (config.showReflectionDetails) {
                    logger.info("CraftWorld方式失败: ${e.message}")
                }
            }

            // 方法2: 使用反射查找正确的方法
            return addEntityToWorld(getServerLevel(world), pokemonEntity, findLevelClass(), pokemonEntity.javaClass, config)

        } catch (e: Exception) {
            logger.severe("Bukkit方式添加Pokemon失败: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 查找Entity的正确基类
     */
    private fun findEntityBaseClass(pokemonEntity: Any, config: SpawnerConfig): Class<*> {
        // 查找Pokemon实体的继承链，找到合适的Entity基类
        var currentClass = pokemonEntity.javaClass

        if (config.showReflectionDetails) {
            logger.info("Pokemon实体继承链:")
        }
        while (currentClass != null && currentClass != Any::class.java) {
            if (config.showReflectionDetails) {
                logger.info("  - ${currentClass.name}")
            }

            // 如果找到包含Entity的类名，且不是Pokemon特定的类
            if (currentClass.name.contains("Entity") && !currentClass.name.contains("Pokemon")) {
                if (config.showReflectionDetails) {
                    logger.info("找到合适的Entity基类: ${currentClass.name}")
                }
                return currentClass
            }

            currentClass = currentClass.superclass
        }

        // 如果没找到，返回Object类
        if (config.showReflectionDetails) {
            logger.warning("未找到合适的Entity基类，使用Object")
        }
        return Any::class.java
    }



    /**
     * 添加实体到世界（原方法，作为备用）
     */
    private fun addEntityToWorld(serverLevel: Any, pokemonEntity: Any, levelClass: Class<*>, entityClass: Class<*>, config: SpawnerConfig): Boolean {
        if (config.showReflectionDetails) {
            logger.info("尝试添加实体到世界...")
            logger.info("Level类: ${levelClass.name}")
            logger.info("Entity类: ${entityClass.name}")
            logger.info("Pokemon实体类: ${pokemonEntity.javaClass.name}")
        }

        // 根据日志，只有method_8649(net.minecraft.world.entity.Entity)有效
        try {
            val entityClass = Class.forName("net.minecraft.world.entity.Entity")
            val method = levelClass.getMethod("method_8649", entityClass)
            val result = method.invoke(serverLevel, pokemonEntity)
            if (config.showSpawnProcess) {
                logger.info("✓ 成功使用方法 method_8649 添加实体，结果: $result")
            }
            return true
        } catch (e: Exception) {
            logger.severe("使用method_8649添加实体失败: ${e.message}")
        }

        logger.severe("所有添加实体方法都失败了")
        return false
    }

    /**
     * 动态查找Level类（处理混淆）
     */
    private fun findLevelClass(): Class<*> {
        // 根据日志，我们知道正确的类名是net.minecraft.class_1937
        try {
            return Class.forName("net.minecraft.class_1937")
        } catch (e: ClassNotFoundException) {
            // 备用方案
            return Class.forName("net.minecraft.world.level.Level")
        }


    }

    /**
     * 动态查找Entity类（处理混淆）
     */
    private fun findEntityClass(): Class<*> {
        // 根据日志，我们知道正确的类名是net.minecraft.world.entity.Entity
        try {
            return Class.forName("net.minecraft.world.entity.Entity")
        } catch (e: ClassNotFoundException) {
            // 备用方案
            return Class.forName("net.minecraft.class_1297")
        }
    }

    /**
     * 获取ServerLevel对象
     */
    private fun getServerLevel(world: org.bukkit.World): Any {
        try {
            return try {
                // 方式1: 直接通过CraftWorld获取
                val craftWorldClass = Class.forName("org.bukkit.craftbukkit.v1_21_R1.CraftWorld")
                val getHandleMethod = craftWorldClass.getMethod("getHandle")
                getHandleMethod.invoke(world)
            } catch (e: ClassNotFoundException) {
                try {
                    // 方式2: 通过Arclight的CraftWorld
                    val arclightCraftWorldClass = Class.forName("org.bukkit.craftbukkit.CraftWorld")
                    val getHandleMethod = arclightCraftWorldClass.getMethod("getHandle")
                    getHandleMethod.invoke(world)
                } catch (e2: ClassNotFoundException) {
                    // 方式3: 直接使用反射获取world字段
                    val worldField = world.javaClass.getDeclaredField("world")
                    worldField.isAccessible = true
                    worldField.get(world)
                }
            }
        } catch (e: Exception) {
            logger.severe("无法获取ServerLevel对象: ${e.message}")
            throw RuntimeException("无法获取ServerLevel对象: ${e.message}", e)
        }
    }

    /**
     * 检查Cobblemon API是否可用
     */
    private fun isCobblemonApiAvailable(): Boolean {
        return try {
            Class.forName("com.cobblemon.mod.common.api.pokemon.PokemonProperties")
            true
        } catch (e: ClassNotFoundException) {
            false
        }
    }

    /**
     * 生成结果数据类
     */
    data class SpawnResult(
        val pokemonName: String,
        val level: Int,
        val isShiny: Boolean,
        val location: Location
    )
}
