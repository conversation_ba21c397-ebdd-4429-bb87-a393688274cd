/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第四代精灵数据 (神奥地区)
 * 包含全国图鉴编号 387-493 的精灵数据
 */
object Gen4PokemonData {
    val data = mapOf(
        // 387-395 (神奥御三家及其进化)
        "turtwig" to PokemonData(387, "草", "嫩苗龟精灵", "male", "森林"),
        "grotle" to PokemonData(388, "草", "树林龟精灵", "male", "森林"),
        "torterra" to PokemonData(389, "草/地面", "大陆精灵", "male", "森林"),
        "chimchar" to PokemonData(390, "火", "小火猴精灵", "male", "山地"),
        "monferno" to PokemonData(391, "火/格斗", "顽皮精灵", "male", "山地"),
        "infernape" to PokemonData(392, "火/格斗", "烈焰猴精灵", "male", "山地"),
        "piplup" to PokemonData(393, "水", "企鹅精灵", "male", "冰冻海洋"),
        "prinplup" to PokemonData(394, "水", "企鹅精灵", "male", "冰冻海洋"),
        "empoleon" to PokemonData(395, "水/钢", "皇帝精灵", "male", "冰冻海洋"),

        // 396-405
        "starly" to PokemonData(396, "一般/飞行", "椋鸟精灵", "male", "丘陵, 雪山, 针叶林"),
        "staravia" to PokemonData(397, "一般/飞行", "椋鸟精灵", "male", "丘陵, 雪山, 针叶林"),
        "staraptor" to PokemonData(398, "一般/飞行", "猛禽精灵", "male", "丘陵, 雪山, 针叶林"),
        "bidoof" to PokemonData(399, "一般", "圆鼠精灵", "male", "淡水, 森林, 针叶林"),
        "bibarel" to PokemonData(400, "一般/水", "河狸精灵", "male", "淡水, 森林, 针叶林"),
        "kricketot" to PokemonData(401, "虫", "蟋蟀精灵", "male", "森林, 沼泽"),
        "kricketune" to PokemonData(402, "虫", "蟋蟀精灵", "male", "森林, 沼泽"),
        "shinx" to PokemonData(403, "电", "闪光精灵", "male", "沙漠, 热带草原"),
        "luxio" to PokemonData(404, "电", "电光精灵", "male", "沙漠, 热带草原"),
        "luxray" to PokemonData(405, "电", "眼光精灵", "male", "沙漠, 热带草原"),

        // 406-415
        "budew" to PokemonData(406, "草/毒", "花苞精灵", "male", "花草草原"),
        "roserade" to PokemonData(407, "草/毒", "花束精灵", "female", "花草草原"),
        "cranidos" to PokemonData(408, "岩石", "头锤龙精灵", "male", "海洋"),
        "rampardos" to PokemonData(409, "岩石", "头锤龙精灵", "male", "海洋"),
        "shieldon" to PokemonData(410, "岩石/钢", "盾甲龙精灵", "male", "海洋"),
        "bastiodon" to PokemonData(411, "岩石/钢", "盾甲龙精灵", "male", "海洋"),
        "burmy" to PokemonData(412, "虫", "结草儿精灵", "male", "森林"),
        "wormadam" to PokemonData(413, "虫/草", "结草贵妇精灵", "female", "森林"),
        "mothim" to PokemonData(414, "虫/飞行", "绅士蛾精灵", "male", "森林"),
        "combee" to PokemonData(415, "虫/飞行", "三蜜蜂精灵", "male", "温带"),

        // 416-425
        "vespiquen" to PokemonData(416, "虫/飞行", "蜂女王精灵", "female", "温带"),
        "pachirisu" to PokemonData(417, "电", "电松鼠精灵", "male", "森林"),
        "buizel" to PokemonData(418, "水", "泳圈鼬精灵", "male", "淡水"),
        "floatzel" to PokemonData(419, "水", "浮潜鼬精灵", "male", "淡水"),
        "cherubi" to PokemonData(420, "草", "樱花宝精灵", "male", "森林"),
        "cherrim" to PokemonData(421, "草", "樱花儿精灵", "male", "森林"),
        "shellos" to PokemonData(422, "水", "无壳海兔精灵", "male", "海滩"),
        "gastrodon" to PokemonData(423, "水/地面", "海兔兽精灵", "male", "海滩"),
        "ambipom" to PokemonData(424, "一般", "双尾怪手精灵", "male", "丛林, 热带岛屿"),
        "drifloon" to PokemonData(425, "幽灵/飞行", "气球精灵", "male", "平原"),

        // 426-435
        "drifblim" to PokemonData(426, "幽灵/飞行", "随风球精灵", "male", "平原"),
        "buneary" to PokemonData(427, "一般", "卷卷耳精灵", "female", "森林, 丘陵, 雪山, 针叶林"),
        "lopunny" to PokemonData(428, "一般", "长耳兔精灵", "female", "森林, 丘陵, 雪山, 针叶林"),
        "mismagius" to PokemonData(429, "幽灵", "梦妖魔精灵", "female", "阴森森林, 沼泽"),
        "honchkrow" to PokemonData(430, "恶/飞行", "乌鸦头头精灵", "male", "阴森森林, 沼泽, 针叶林"),
        "glameow" to PokemonData(431, "一般", "魅力猫精灵", "female", "城市, 村庄"),
        "purugly" to PokemonData(432, "一般", "东施猫精灵", "female", "城市, 村庄"),
        "chingling" to PokemonData(433, "超能力", "铃铛响精灵", "male", "森林, 山地, 阴森森林, 针叶林"),
        "stunky" to PokemonData(434, "毒/恶", "臭鼬噗精灵", "male", "森林"),
        "skuntank" to PokemonData(435, "毒/恶", "坦克臭鼬精灵", "male", "森林"),

        // 436-445
        "bronzor" to PokemonData(436, "钢/超能力", "铜镜怪精灵", "genderless", "山地"),
        "bronzong" to PokemonData(437, "钢/超能力", "青铜钟精灵", "genderless", "山地"),
        "bonsly" to PokemonData(438, "岩石", "盆才怪精灵", "male", "森林"),
        "mimejr" to PokemonData(439, "超能力/妖精", "魔尼尼精灵", "male", "城市, 村庄"),
        "happiny" to PokemonData(440, "一般", "小福蛋精灵", "female", "城市, 村庄"),
        "chatot" to PokemonData(441, "一般/飞行", "聒噪鸟精灵", "male", "丛林, 热带岛屿"),
        "spiritomb" to PokemonData(442, "幽灵/恶", "花岩怪精灵", "male", "地下, 沙漠, 丛林"),
        "gible" to PokemonData(443, "龙/地面", "圆陆鲨精灵", "male", "恶地, 山地"),
        "gabite" to PokemonData(444, "龙/地面", "尖牙陆鲨精灵", "male", "恶地, 山地"),
        "garchomp" to PokemonData(445, "龙/地面", "烈咬陆鲨精灵", "male", "恶地, 山地"),

        // 446-455
        "munchlax" to PokemonData(446, "一般", "小卡比兽精灵", "male", "森林, 丘陵"),
        "riolu" to PokemonData(447, "格斗", "利欧路精灵", "male", "山地"),
        "lucario" to PokemonData(448, "格斗/钢", "路卡利欧精灵", "male", "山地"),
        "hippopotas" to PokemonData(449, "地面", "沙河马精灵", "male", "沙漠"),
        "hippowdon" to PokemonData(450, "地面", "河马兽精灵", "male", "沙漠"),
        "skorupi" to PokemonData(451, "毒/虫", "钳尾蝎精灵", "male", "沙漠"),
        "drapion" to PokemonData(452, "毒/恶", "龙王蝎精灵", "male", "沙漠"),
        "croagunk" to PokemonData(453, "毒/格斗", "不良蛙精灵", "male", "沼泽"),
        "toxicroak" to PokemonData(454, "毒/格斗", "毒骷蛙精灵", "male", "沼泽"),
        "carnivine" to PokemonData(455, "草", "尖牙笼精灵", "male", "沼泽"),

        // 456-465
        "finneon" to PokemonData(456, "水", "荧光鱼精灵", "male", "深海, 海洋"),
        "lumineon" to PokemonData(457, "水", "霓虹鱼精灵", "male", "深海, 海洋"),
        "mantyke" to PokemonData(458, "水/飞行", "小球飞鱼精灵", "male", "温暖海洋"),
        "snover" to PokemonData(459, "草/冰", "雪笠怪精灵", "male", "冰冻地区"),
        "abomasnow" to PokemonData(460, "草/冰", "暴雪王精灵", "male", "冰冻地区"),
        "weavile" to PokemonData(461, "恶/冰", "玛狃拉精灵", "male", "针叶林"),
        "magnezone" to PokemonData(462, "电/钢", "自爆磁怪精灵", "genderless", "地下"),
        "lickilicky" to PokemonData(463, "一般", "大舌舔精灵", "male", "草原"),
        "rhyperior" to PokemonData(464, "地面/岩石", "超甲狂犀精灵", "male", "山地, 热带草原"),
        "tangrowth" to PokemonData(465, "草", "巨蔓藤精灵", "male", "丛林"),

        // 466-475
        "electivire" to PokemonData(466, "电", "电击魔兽精灵", "male", "丘陵, 平原"),
        "magmortar" to PokemonData(467, "火", "鸭嘴炎兽精灵", "male", "丘陵, 火山"),
        "togekiss" to PokemonData(468, "妖精/飞行", "波克基斯精灵", "male", "城市, 村庄"),
        "yanmega" to PokemonData(469, "虫/飞行", "远古巨蜓精灵", "male", "淡水"),
        "leafeon" to PokemonData(470, "草", "叶伊布精灵", "male", "森林, 丛林"),
        "glaceon" to PokemonData(471, "冰", "冰伊布精灵", "male", "冰冻地区"),
        "gliscor" to PokemonData(472, "地面/飞行", "天蝎王精灵", "male", "恶地"),
        "mamoswine" to PokemonData(473, "冰/地面", "象牙猪精灵", "male", "苔原"),
        "porygonz" to PokemonData(474, "一般", "多边兽Z精灵", "genderless", "城市"),
        "gallade" to PokemonData(475, "超能力/格斗", "艾路雷朵精灵", "male", "花草草原, 魔法森林"),

        // 476-485
        "probopass" to PokemonData(476, "岩石/钢", "大朝北鼻精灵", "male", "地下"),
        "dusknoir" to PokemonData(477, "幽灵", "黑夜魔灵精灵", "male", "阴森森林"),
        "froslass" to PokemonData(478, "冰/幽灵", "雪妖女精灵", "female", "冰冻地区"),
        "rotom" to PokemonData(479, "电/幽灵", "洛托姆精灵", "genderless", "城市"),
        "uxie" to PokemonData(480, "超能力", "由克希精灵", "genderless", "湖泊"),
        "mesprit" to PokemonData(481, "超能力", "艾姆利多精灵", "genderless", "湖泊"),
        "azelf" to PokemonData(482, "超能力", "亚克诺姆精灵", "genderless", "湖泊"),
        "dialga" to PokemonData(483, "钢/龙", "帝牙卢卡精灵", "genderless", "山峰"),
        "palkia" to PokemonData(484, "水/龙", "帕路奇犽精灵", "genderless", "山峰"),
        "heatran" to PokemonData(485, "火/钢", "席多蓝恩精灵", "genderless", "火山"),

        // 486-493 (传说精灵)
        "regigigas" to PokemonData(486, "一般", "雷吉奇卡斯精灵", "genderless", "雪山"),
        "giratina" to PokemonData(487, "幽灵/龙", "骑拉帝纳精灵", "genderless", "山峰"),
        "cresselia" to PokemonData(488, "超能力", "克雷色利亚精灵", "female", "花草草原"),
        "phione" to PokemonData(489, "水", "霏欧纳精灵", "genderless", "温暖海洋"),
        "manaphy" to PokemonData(490, "水", "玛纳霏精灵", "genderless", "温暖海洋"),
        "darkrai" to PokemonData(491, "恶", "达克莱伊精灵", "genderless", "阴森森林"),
        "shaymin" to PokemonData(492, "草", "谢米精灵", "genderless", "花草草原"),
        "arceus" to PokemonData(493, "一般", "阿尔宙斯精灵", "genderless", "山峰")
    )
}
