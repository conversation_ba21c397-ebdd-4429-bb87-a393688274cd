/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.event

import cn.acebrand.acedex.AceDex
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.bukkit.event.Event
import org.bukkit.event.HandlerList
import java.util.*

/**
 * Cobblemon事件包装器基类
 * 将Cobblemon的Forge事件转换为Bukkit事件
 */
abstract class CobblemonEventWrapper : Event() {
    companion object {
        private val handlers = HandlerList()
        
        @JvmStatic
        fun getHandlerList(): HandlerList = handlers
    }
    
    override fun getHandlers(): HandlerList = Companion.handlers
}

/**
 * 精灵捕获事件
 */
class PokemonCaptureEvent(
    val player: Player,
    val pokemonName: String,
    val pokemonDex: Int,
    val isShiny: Boolean
) : CobblemonEventWrapper()

/**
 * 战斗胜利事件
 */
class BattleVictoryEvent(
    val player: Player,
    val battleType: String,
    val experienceGained: Int
) : CobblemonEventWrapper()

/**
 * 精灵进化事件
 */
class PokemonEvolutionEvent(
    val player: Player,
    val fromPokemon: String,
    val toPokemon: String,
    val fromDex: Int,
    val toDex: Int
) : CobblemonEventWrapper()

/**
 * 精灵交换事件
 */
class PokemonTradeEvent(
    val player: Player,
    val tradedPokemon: String,
    val receivedPokemon: String
) : CobblemonEventWrapper()

/**
 * 精灵释放事件
 */
class PokemonReleaseEvent(
    val player: Player,
    val pokemonName: String,
    val pokemonDex: Int
) : CobblemonEventWrapper()

/**
 * 精灵孵化事件
 */
class PokemonHatchEvent(
    val player: Player,
    val pokemonName: String,
    val pokemonDex: Int,
    val isShiny: Boolean
) : CobblemonEventWrapper()

/**
 * 精灵存储变更事件
 */
class PokemonStorageChangeEvent(
    val player: Player,
    val changeType: StorageChangeType,
    val pokemonName: String?,
    val pokemonDex: Int?
) : CobblemonEventWrapper()

enum class StorageChangeType {
    PARTY_TO_PC,
    PC_TO_PARTY,
    POKEMON_ADDED,
    POKEMON_REMOVED
}

/**
 * 图鉴更新事件
 */
class PokedexUpdateEvent(
    val player: Player,
    val pokemonName: String,
    val pokemonDex: Int,
    val updateType: PokedexUpdateType
) : CobblemonEventWrapper()

enum class PokedexUpdateType {
    FIRST_CATCH,
    SHINY_CATCH,
    EVOLUTION_SEEN,
    BATTLE_SEEN
}
