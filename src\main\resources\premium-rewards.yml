# ========================================
# 付费奖励配置文件
# Premium Rewards Configuration
# ========================================

# 付费奖励系统总开关
enabled: true

# 付费权限设置
permissions:
  # 查看付费奖励菜单的权限
  view: "acedex.premium.view"
  # 领取付费奖励的权限
  claim: "acedex.premium.claim"

# 付费世代完成奖励 (100%完成度奖励)
generation:
  gen1:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen1.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6关都地区付费大师称号"
    # 付费进度奖励
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen2:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen2.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6城都地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen3:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen3.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6丰缘地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen4:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen4.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6神奥地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen5:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen5.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6合众地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen6:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen6.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6卡洛斯地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen7:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen7.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6阿罗拉地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen8:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen8.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6伽勒尔地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

  gen9:
    completion:
      enabled: true
      commands:
        - "give {player} minecraft:diamond 100"
        - "give {player} minecraft:netherite_ingot 20"
        - "give {player} minecraft:beacon 2"
        - "lp user {player} permission set acedex.premium.gen9.master true"
      descriptions:
        - "§7• §b钻石 x100"
        - "§7• §5下界合金锭 x20"
        - "§7• §e信标 x2"
        - "§7• §6帕底亚地区付费大师称号"
    progress:
      25:
        commands:
          - "give {player} minecraft:gold_ingot 50"
          - "give {player} minecraft:experience_bottle 20"
        descriptions:
          - "§7• §6金锭 x50"
          - "§7• §a经验瓶 x20"
      50:
        commands:
          - "give {player} minecraft:diamond 25"
          - "give {player} minecraft:emerald 30"
        descriptions:
          - "§7• §b钻石 x25"
          - "§7• §a绿宝石 x30"
      75:
        commands:
          - "give {player} minecraft:netherite_ingot 5"
          - "give {player} minecraft:totem_of_undying 1"
        descriptions:
          - "§7• §5下界合金锭 x5"
          - "§7• §a不死图腾 x1"

# 付费全世代完成奖励 (完成所有1-9世代)
all-generations-completion:
  enabled: true
  commands:
    - "give {player} minecraft:diamond 500"
    - "give {player} minecraft:netherite_ingot 100"
    - "give {player} minecraft:beacon 10"
    - "give {player} minecraft:dragon_egg 5"
    - "give {player} minecraft:enchanted_golden_apple 20"
    - "give {player} minecraft:totem_of_undying 10"
    - "lp user {player} permission set acedex.premium.grandmaster true"
    - "tellraw @a {\"text\":\"恭喜 {player} 成为付费精灵大师！\",\"color\":\"gold\",\"bold\":true}"
  descriptions:
    - "§7• §b钻石 x500"
    - "§7• §5下界合金锭 x100"
    - "§7• §e信标 x10"
    - "§7• §d龙蛋 x5"
    - "§7• §6附魔金苹果 x20"
    - "§7• §a不死图腾 x10"
    - "§7• §6付费精灵大师称号"
    - "§7• §c全服公告"

# 付费全世代进度奖励
overall-progress:
  enabled: true

  25:
    name: "付费收集新手"
    commands:
      - "give {player} minecraft:diamond 50"
      - "give {player} minecraft:gold_ingot 100"
      - "lp user {player} permission set acedex.premium.novice true"
    descriptions:
      - "§7• §b钻石 x50"
      - "§7• §6金锭 x100"
      - "§7• §e付费收集新手称号"

  50:
    name: "付费收集专家"
    commands:
      - "give {player} minecraft:diamond 100"
      - "give {player} minecraft:emerald 150"
      - "give {player} minecraft:beacon 1"
      - "lp user {player} permission set acedex.premium.expert true"
    descriptions:
      - "§7• §b钻石 x100"
      - "§7• §a绿宝石 x150"
      - "§7• §e信标 x1"
      - "§7• §e付费收集专家称号"

  75:
    name: "付费收集大师"
    commands:
      - "give {player} minecraft:diamond 200"
      - "give {player} minecraft:netherite_ingot 25"
      - "give {player} minecraft:beacon 3"
      - "give {player} minecraft:totem_of_undying 3"
      - "lp user {player} permission set acedex.premium.master true"
    descriptions:
      - "§7• §b钻石 x200"
      - "§7• §5下界合金锭 x25"
      - "§7• §e信标 x3"
      - "§7• §a不死图腾 x3"
      - "§7• §e付费收集大师称号"

# 付费奖励显示设置
display:
  # 付费奖励菜单标题
  menu-title: "§6§l付费奖励菜单"

  # 付费奖励按钮显示名称
  button-names:
    completed-premium: "§6§l{generation} §a✓ §6[付费]"
    available-premium: "§6§l{generation} §6★ §6[付费]"
    locked-premium: "§6§l{generation} §6[付费]"

  # 付费奖励状态文本
  status-text:
    completed: "§a已完成并领取付费奖励！"
    available: "§6恭喜完成！可以领取付费奖励"
    locked: "§7还需收集更多精灵"
    no-permission: "§c需要付费权限才能查看"

  # 付费奖励描述前缀
  reward-prefix:
    completion: "§6付费完成奖励:"
    progress: "§6付费进度奖励:"
    overall: "§d付费全世代奖励:"

# 付费奖励消息设置
messages:
  # 成功消息
  success:
    generation-completion: "§6§l[付费图鉴奖励] §f恭喜完成{generation}！"
    progress-reward: "§6§l[付费进度奖励] §f{generation} 达到 {percentage}% 进度！"
    overall-completion: "§6§l[付费精灵大师] §f恭喜完成所有1-9世代收集！"
    overall-progress: "§6§l[付费全世代进度奖励] §f恭喜达到全世代 {percentage}% 进度！"
    batch-claim: "§6§l[批量领取] §f同时领取了以下付费奖励："

  # 错误消息
  error:
    no-permission-view: "§c你没有权限查看付费奖励菜单！"
    no-permission-claim: "§c你没有权限领取付费奖励！"
    already-claimed: "§e你已经领取过{reward}了！"
    not-completed: "§c你还需要收集 {remaining} 只精灵才能完成{generation}！"
    reward-failed: "§c付费奖励发放失败，请联系管理员！"
    data-failed: "§c读取进度数据失败: {error}"

  # 信息消息
  info:
    opening-menu: "§6正在打开付费奖励菜单..."
    current-progress: "§7当前进度: §a{caught}§7/§f{total}"
    remaining-generations: "§c你还需要完成 {remaining} 个世代才能领取付费精灵大师奖励！"
    no-rewards-available: "§7暂无可领取的付费奖励"

# ========================================
# 付费菜单界面配置
# Premium Menu Interface Configuration
# ========================================

# 付费菜单界面设置
premium-menu:
  # 付费精灵大师按钮材质配置（根据进度显示不同精灵球）
  progress-button:
    materials:
      0-25: COBBLEMON_POKE_BALL      # 0-25% 完成度 - 普通精灵球
      25-50: COBBLEMON_GREAT_BALL    # 25-50% 完成度 - 超级球
      50-75: COBBLEMON_ULTRA_BALL    # 50-75% 完成度 - 高级球
      75-99: COBBLEMON_MASTER_BALL   # 75-99% 完成度 - 大师球
      '100': COBBLEMON_PREMIER_BALL  # 100% 完成度 - 纪念球

  # 关闭按钮材质配置
  close-button:
    material: PAPER:10030            # 关闭按钮材质（支持自定义模型数据）

  # 付费菜单标题配置
  title: "§6§l✦ 付费奖励菜单 ✦"

  # 付费菜单大小配置
  size: 54                           # 菜单大小（9的倍数，最大54）

# ========================================
# 付费菜单按钮配置说明
# Premium Menu Button Configuration Guide
# ========================================

# 材质配置说明:
# 1. 付费世代按钮: 使用固定的世代材质（和普通菜单相同）
#    - gen1: COBBLEMON_POKE_BALL (关都 - 红色精灵球)
#    - gen2: COBBLEMON_GREAT_BALL (城都 - 金色精灵球)
#    - gen3: COBBLEMON_ULTRA_BALL (丰缘 - 蓝色精灵球)
#    - gen4: COBBLEMON_MASTER_BALL (神奥 - 紫色精灵球)
#    - gen5: COBBLEMON_TIMER_BALL (合众 - 黑白精灵球)
#    - gen6: COBBLEMON_LUXURY_BALL (卡洛斯 - 粉色精灵球)
#    - gen7: COBBLEMON_PREMIER_BALL (阿罗拉 - 橙色精灵球)
#    - gen8: COBBLEMON_DUSK_BALL (伽勒尔 - 蓝紫精灵球)
#    - gen9: COBBLEMON_QUICK_BALL (帕底亚 - 彩色精灵球)
#
# 2. 付费精灵大师按钮: 根据总进度显示不同材质
#    - 0-25%: 普通精灵球 (刚开始收集)
#    - 25-50%: 超级球 (初级收集者)
#    - 50-75%: 高级球 (中级收集者)
#    - 75-99%: 大师球 (高级收集者/可领取状态)
#    - 100%: 纪念球 (已完成/已领取状态)
#
# 3. 关闭按钮: 可自定义材质
#    - 支持普通材质: BARRIER, PAPER 等
#    - 支持自定义模型数据: PAPER:10030 格式
#
# 4. 材质优先级:
#    - 付费菜单优先使用 premium-menu 配置
#    - 如果没有配置，则使用默认材质
#    - 世代按钮始终使用 gui.yml 中的世代材质配置
