{"input": "build/libs/AceDex-1.0.0.jar", "output": "obfuscated/AceDex-1.0.0-obfuscated.jar", "libraries": ["libs/", "%JAVA_HOME%/jmods"], "exempts": ["cn.acebrand.acedex.AceDex", "cn.acebrand.acedex.AceDex.onEnable()", "cn.acebrand.acedex.AceDex.onDisable()", "cn.acebrand.acedex.command.**", "cn.acebrand.acedex.listener.**", "cn.acebrand.acedex.integration.PlaceholderAPIExpansion", "cn.acebrand.acedex.license.LicenseManager.validateLicenseKey(*)", "cn.acebrand.acedex.license.LicenseManager.isValid()", "cn.acebrand.acedex.license.LicenseManager.shutdown()"], "transformers": {"flow": {"enabled": true, "intensity": 3}, "string": {"enabled": true, "intensity": 2}, "number": {"enabled": true, "intensity": 2}, "reference": {"enabled": true, "intensity": 3}, "condition": {"enabled": true, "intensity": 2}, "outlining": {"enabled": true, "intensity": 2}, "ahegao": {"enabled": true, "intensity": 1}, "driver": {"enabled": false}, "native": {"enabled": false}}, "renamer": {"enabled": true, "dictionary": "alphabet", "repackage": "obf.acedex"}, "runtime": {"jvm": ["-Xmx4G", "-Xms1G"]}, "debug": true, "phantom": true}