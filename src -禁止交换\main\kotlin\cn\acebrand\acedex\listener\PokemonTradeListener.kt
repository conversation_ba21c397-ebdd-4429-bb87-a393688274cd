package cn.acebrand.acedex.listener

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.util.PokemonInfo
import cn.acebrand.acedex.util.PokemonAcquisitionMethod
import com.cobblemon.mod.common.api.events.CobblemonEvents
import com.cobblemon.mod.common.api.events.pokemon.TradeCompletedEvent
import com.cobblemon.mod.common.util.getPlayer
import org.bukkit.entity.Player
import java.util.*

/**
 * 精灵交换事件监听器
 * 处理精灵交换相关的逻辑，包括是否将交换获得的精灵计入图鉴完成
 */
class PokemonTradeListener(private val plugin: AceDex) {

    // 存储交换获得的精灵信息，持久化记录
    private val tradedPokemonRecords = mutableMapOf<UUID, MutableSet<String>>()

    init {
        // 注册Cobblemon交换完成事件监听器
        CobblemonEvents.TRADE_COMPLETED.subscribe { event ->
            handleTradeCompleted(event)
        }
    }

    /**
     * 处理交换完成事件
     */
    private fun handleTradeCompleted(event: TradeCompletedEvent) {
        try {
            if (plugin.config.enableDebug) {
                plugin.logger.info("检测到精灵交换事件")
                plugin.logger.info("玩家1: ${event.tradeParticipant1.uuid}")
                plugin.logger.info("玩家1的精灵: ${event.tradeParticipant1Pokemon.species.name}")
                plugin.logger.info("玩家2: ${event.tradeParticipant2.uuid}")
                plugin.logger.info("玩家2的精灵: ${event.tradeParticipant2Pokemon.species.name}")
            }

            // 正确的交换逻辑：玩家1获得玩家2的精灵，玩家2获得玩家1的精灵
            val player1UUID = event.tradeParticipant1.uuid
            val bukkitPlayer1 = plugin.server.getPlayer(player1UUID)
            if (bukkitPlayer1 != null) {
                handlePlayerReceivedPokemon(
                    bukkitPlayer1,
                    event.tradeParticipant2Pokemon.species.name,
                    event.tradeParticipant2Pokemon.species.nationalPokedexNumber
                )
                if (plugin.config.enableDebug) {
                    plugin.logger.info("玩家1 ${bukkitPlayer1.name} 获得精灵: ${event.tradeParticipant2Pokemon.species.name}")
                }
            }

            val player2UUID = event.tradeParticipant2.uuid
            val bukkitPlayer2 = plugin.server.getPlayer(player2UUID)
            if (bukkitPlayer2 != null) {
                handlePlayerReceivedPokemon(
                    bukkitPlayer2,
                    event.tradeParticipant1Pokemon.species.name,
                    event.tradeParticipant1Pokemon.species.nationalPokedexNumber
                )
                if (plugin.config.enableDebug) {
                    plugin.logger.info("玩家2 ${bukkitPlayer2.name} 获得精灵: ${event.tradeParticipant1Pokemon.species.name}")
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("处理精灵交换事件失败: ${e.message}")
        }
    }

    /**
     * 处理玩家通过交换获得精灵
     */
    private fun handlePlayerReceivedPokemon(player: Player, pokemonName: String, nationalDex: Int) {
        val chineseName = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonName)

        if (plugin.config.countTradedPokemon) {
            // 如果配置允许交换精灵计入图鉴，正常处理
            player.sendMessage("§a✓ 通过交换获得精灵 §e$chineseName §a已添加到图鉴！")

            if (plugin.config.enableDebug) {
                plugin.logger.info("玩家 ${player.name} 通过交换获得精灵 $pokemonName，已计入图鉴")
            }
        } else {
            // 如果配置不允许交换精灵计入图鉴，记录这个精灵为交换获得
            val playerUUID = player.uniqueId
            val tradedPokemonSet = tradedPokemonRecords.getOrPut(playerUUID) { mutableSetOf() }
            tradedPokemonSet.add(pokemonName)

            player.sendMessage("§e通过交换获得精灵 §f$chineseName §e（不计入图鉴完成）")

            if (plugin.config.enableDebug) {
                plugin.logger.info("玩家 ${player.name} 通过交换获得精灵 $pokemonName，不计入图鉴完成")
            }
        }

        // 延迟刷新GUI，确保数据已更新
        plugin.server.scheduler.runTaskLater(plugin, Runnable {
            // 清除精灵相关的缓存
            plugin.pokemonItemCreator.clearPokemonCache(pokemonName, nationalDex)

            // 清除GUI缓存
            plugin.mainGui.clearMainInventoryCache(player)
            plugin.mainGui.clearGenerationInventoryCache(player)

            // 刷新当前GUI
            plugin.mainGui.refreshCurrentGui(player)
        }, 20L) // 1秒后刷新
    }


    /**
     * 检查指定精灵是否是玩家通过交换获得的
     * 用于在数据检测时标记交换获得的精灵
     */
    fun isTradedPokemon(playerUUID: UUID, pokemonName: String): Boolean {
        return tradedPokemonRecords[playerUUID]?.contains(pokemonName) == true
    }

    /**
     * 清理玩家的交换记录（玩家退出时调用）
     */
    fun clearPlayerTradeRecords(playerUUID: UUID) {
        tradedPokemonRecords.remove(playerUUID)
    }

    /**
     * 根据全国图鉴编号确定世代
     */
    private fun determineGeneration(nationalDex: Int): String {
        return when (nationalDex) {
            in 1..151 -> "第一世代"
            in 152..251 -> "第二世代"
            in 252..386 -> "第三世代"
            in 387..493 -> "第四世代"
            in 494..649 -> "第五世代"
            in 650..721 -> "第六世代"
            in 722..809 -> "第七世代"
            in 810..905 -> "第八世代"
            in 906..1025 -> "第九世代"
            else -> "未知世代"
        }
    }
}
