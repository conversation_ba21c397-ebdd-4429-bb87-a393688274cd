/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.api.riding

/**
 * A general medium for riding. Mainly used for categorizing which stats to pull from and for
 * presenting a simplified view of riding to users.
 *
 * <AUTHOR>
 * @since February 17th, 2025
 */
enum class RidingStyle {
    LAND,
    LIQUID,
    AIR
}
