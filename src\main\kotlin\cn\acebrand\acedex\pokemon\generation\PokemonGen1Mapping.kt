/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第一世代精灵名称映射 (1-151)
 * 包含关都地区的所有精灵
 */
object PokemonGen1Mapping {
    
    /**
     * 获取第一世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第一世代 (1-151)
            "bulbasaur" to "妙蛙种子",
            "ivysaur" to "妙蛙草",
            "venusaur" to "妙蛙花",
            "charmander" to "小火龙",
            "charmeleon" to "火恐龙",
            "charizard" to "喷火龙",
            "squirtle" to "杰尼龟",
            "wartortle" to "卡咪龟",
            "blastoise" to "水箭龟",
            "caterpie" to "绿毛虫",
            "metapod" to "铁甲蛹",
            "butterfree" to "巴大蝶",
            "weedle" to "独角虫",
            "kakuna" to "铁壳蛹",
            "beedrill" to "大针蜂",
            "pidgey" to "波波",
            "pidgeotto" to "比比鸟",
            "pidgeot" to "大比鸟",
            "rattata" to "小拉达",
            "raticate" to "拉达",
            "spearow" to "烈雀",
            "fearow" to "大嘴雀",
            "ekans" to "阿柏蛇",
            "arbok" to "阿柏怪",
            "pikachu" to "皮卡丘",
            "raichu" to "雷丘",
            "sandshrew" to "穿山鼠",
            "sandslash" to "穿山王",
            "nidoranf" to "尼多兰",
            "nidorina" to "尼多娜",
            "nidoqueen" to "尼多后",
            "nidoranm" to "尼多朗",
            "nidorino" to "尼多力诺",
            "nidoking" to "尼多王",
            "clefairy" to "皮皮",
            "clefable" to "皮可西",
            "vulpix" to "六尾",
            "ninetales" to "九尾",
            "jigglypuff" to "胖丁",
            "wigglytuff" to "胖可丁",
            "zubat" to "超音蝠",
            "golbat" to "大嘴蝠",
            "oddish" to "走路草",
            "gloom" to "臭臭花",
            "vileplume" to "霸王花",
            "paras" to "派拉斯",
            "parasect" to "派拉斯特",
            "venonat" to "毛球",
            "venomoth" to "摩鲁蛾",
            "diglett" to "地鼠",
            "dugtrio" to "三地鼠",
            "meowth" to "喵喵",
            "persian" to "猫老大",
            "psyduck" to "可达鸭",
            "golduck" to "哥达鸭",
            "mankey" to "猴怪",
            "primeape" to "火爆猴",
            "growlithe" to "卡蒂狗",
            "arcanine" to "风速狗",
            "poliwag" to "蚊香蝌蚪",
            "poliwhirl" to "蚊香君",
            "poliwrath" to "蚊香泳士",
            "abra" to "凯西",
            "kadabra" to "勇基拉",
            "alakazam" to "胡地",
            "machop" to "腕力",
            "machoke" to "豪力",
            "machamp" to "怪力",
            "bellsprout" to "喇叭芽",
            "weepinbell" to "口呆花",
            "victreebel" to "大食花",
            "tentacool" to "玛瑙水母",
            "tentacruel" to "毒刺水母",
            "geodude" to "小拳石",
            "graveler" to "隆隆石",
            "golem" to "隆隆岩",
            "ponyta" to "小火马",
            "rapidash" to "烈焰马",
            "slowpoke" to "呆呆兽",
            "slowbro" to "呆壳兽",
            "magnemite" to "小磁怪",
            "magneton" to "三合一磁怪",
            "farfetchd" to "大葱鸭",
            "doduo" to "嘟嘟",
            "dodrio" to "嘟嘟利",
            "seel" to "小海狮",
            "dewgong" to "白海狮",
            "grimer" to "臭泥",
            "muk" to "臭臭泥",
            "shellder" to "大舌贝",
            "cloyster" to "刺甲贝",
            "gastly" to "鬼斯",
            "haunter" to "鬼斯通",
            "gengar" to "耿鬼",
            "onix" to "大岩蛇",
            "drowzee" to "催眠貘",
            "hypno" to "引梦貘人",
            "krabby" to "大钳蟹",
            "kingler" to "巨钳蟹",
            "voltorb" to "霹雳电球",
            "electrode" to "顽皮雷弹",
            "exeggcute" to "蛋蛋",
            "exeggutor" to "椰蛋树",
            "cubone" to "卡拉卡拉",
            "marowak" to "嘎啦嘎啦",
            "hitmonlee" to "飞腿郎",
            "hitmonchan" to "快拳郎",
            "lickitung" to "大舌头",
            "koffing" to "瓦斯弹",
            "weezing" to "双弹瓦斯",
            "rhyhorn" to "独角犀牛",
            "rhydon" to "钻角犀兽",
            "chansey" to "吉利蛋",
            "tangela" to "蔓藤怪",
            "kangaskhan" to "袋兽",
            "horsea" to "墨海马",
            "seadra" to "海刺龙",
            "goldeen" to "角金鱼",
            "seaking" to "金鱼王",
            "staryu" to "海星星",
            "starmie" to "宝石海星",
            "mrmime" to "魔墙人偶",
            "scyther" to "飞天螳螂",
            "jynx" to "迷唇姐",
            "electabuzz" to "电击兽",
            "magmar" to "鸭嘴火兽",
            "pinsir" to "凯罗斯",
            "tauros" to "肯泰罗",
            "magikarp" to "鲤鱼王",
            "gyarados" to "暴鲤龙",
            "lapras" to "拉普拉斯",
            "ditto" to "百变怪",
            "eevee" to "伊布",
            "vaporeon" to "水伊布",
            "jolteon" to "雷伊布",
            "flareon" to "火伊布",
            "porygon" to "多边兽",
            "omanyte" to "菊石兽",
            "omastar" to "多刺菊石兽",
            "kabuto" to "化石盔",
            "kabutops" to "镰刀盔",
            "aerodactyl" to "化石翼龙",
            "snorlax" to "卡比兽",
            "articuno" to "急冻鸟",
            "zapdos" to "闪电鸟",
            "moltres" to "火焰鸟",
            "dratini" to "迷你龙",
            "dragonair" to "哈克龙",
            "dragonite" to "快龙",
            "mewtwo" to "超梦",
            "mew" to "梦幻",
    )
}
