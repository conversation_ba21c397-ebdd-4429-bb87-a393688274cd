package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.EventPriority
import org.bukkit.event.Listener
import org.bukkit.event.player.PlayerJoinEvent
import org.bukkit.event.player.PlayerQuitEvent
import org.bukkit.inventory.Inventory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 异步GUI菜单管理器
 * 管理玩家进服时的异步GUI实例化，在图鉴变化时刷新GUI菜单
 */
class AsyncGuiManager(private val plugin: AceDex) : Listener {
    
    // 玩家GUI实例缓存
    private val playerGuiCache = ConcurrentHashMap<Player, PlayerGuiData>()
    
    // 异步执行器
    private val executor: ScheduledExecutorService = Executors.newScheduledThreadPool(2)
    
    // GUI刷新队列
    private val refreshQueue = ConcurrentHashMap<Player, Long>()
    
    init {
        // 注册事件监听器
        plugin.server.pluginManager.registerEvents(this, plugin)
        
        // 启动定期刷新任务
        startPeriodicRefreshTask()
    }
    
    /**
     * 玩家GUI数据类
     */
    data class PlayerGuiData(
        var mainInventory: Inventory? = null,
        var generationInventories: MutableMap<String, Inventory> = mutableMapOf(),
        var lastUpdateTime: Long = System.currentTimeMillis(),
        var isInitialized: Boolean = false
    )
    
    /**
     * 玩家进服事件处理
     */
    @EventHandler(priority = EventPriority.MONITOR)
    fun onPlayerJoin(event: PlayerJoinEvent) {
        val player = event.player
        
        // 延迟初始化GUI，避免进服时的卡顿
        executor.schedule({
            initializePlayerGui(player)
        }, 3, TimeUnit.SECONDS)
    }
    
    /**
     * 玩家离服事件处理
     */
    @EventHandler(priority = EventPriority.MONITOR)
    fun onPlayerQuit(event: PlayerQuitEvent) {
        val player = event.player
        
        // 清理玩家GUI缓存
        playerGuiCache.remove(player)
        refreshQueue.remove(player)
        
        if (plugin.config.enableDebug) {
            plugin.logger.info("已清理玩家 ${player.name} 的GUI缓存")
        }
    }
    
    /**
     * 异步初始化玩家GUI
     */
    private fun initializePlayerGui(player: Player) {
        if (!player.isOnline) {
            return
        }
        
        CompletableFuture.runAsync({
            try {
                // 检查预加载是否完成
                if (!plugin.pokemonModelPreloader.isPreloadComplete()) {
                    plugin.logger.info("等待精灵模型预加载完成...")
                    // 等待预加载完成，最多等待30秒
                    var waitTime = 0
                    while (!plugin.pokemonModelPreloader.isPreloadComplete() && waitTime < 30000) {
                        Thread.sleep(1000)
                        waitTime += 1000
                    }
                }
                
                if (!player.isOnline) {
                    return@runAsync
                }
                
                // 创建玩家GUI数据
                val guiData = PlayerGuiData()
                
                // 预创建主菜单
                guiData.mainInventory = createMainInventoryAsync(player)
                
                // 预创建世代菜单
                val generations = plugin.generationManager.getAllGenerations()
                for (generation in generations) {
                    val generationInventory = createGenerationInventoryAsync(player, generation.id)
                    if (generationInventory != null) {
                        guiData.generationInventories[generation.id] = generationInventory
                    }
                }
                
                guiData.isInitialized = true
                playerGuiCache[player] = guiData
                
                if (plugin.config.enableDebug) {
                    plugin.logger.info("玩家 ${player.name} 的GUI已异步初始化完成")
                }
                
            } catch (e: Exception) {
                plugin.logger.warning("初始化玩家 ${player.name} 的GUI失败: ${e.message}")
            }
        }, executor)
    }
    
    /**
     * 异步创建主菜单
     */
    private fun createMainInventoryAsync(player: Player): Inventory? {
        return try {
            // 使用现有的主GUI创建方法，但不直接打开
            plugin.mainGui.createMainInventoryForPlayer(player)
        } catch (e: Exception) {
            plugin.logger.warning("创建玩家 ${player.name} 的主菜单失败: ${e.message}")
            null
        }
    }
    
    /**
     * 异步创建世代菜单
     */
    private fun createGenerationInventoryAsync(player: Player, generationId: String): Inventory? {
        return try {
            // 使用现有的世代GUI创建方法，但不直接打开
            plugin.mainGui.createGenerationInventoryForPlayer(player, generationId)
        } catch (e: Exception) {
            plugin.logger.warning("创建玩家 ${player.name} 的世代菜单 $generationId 失败: ${e.message}")
            null
        }
    }
    
    /**
     * 获取玩家的主菜单
     */
    fun getPlayerMainInventory(player: Player): Inventory? {
        val guiData = playerGuiCache[player]
        return if (guiData?.isInitialized == true) {
            guiData.mainInventory
        } else {
            // 如果未初始化，触发初始化并返回null
            initializePlayerGui(player)
            null
        }
    }
    
    /**
     * 获取玩家的世代菜单
     */
    fun getPlayerGenerationInventory(player: Player, generationId: String): Inventory? {
        val guiData = playerGuiCache[player]
        return if (guiData?.isInitialized == true) {
            guiData.generationInventories[generationId]
        } else {
            // 如果未初始化，触发初始化并返回null
            initializePlayerGui(player)
            null
        }
    }
    
    /**
     * 标记玩家GUI需要刷新
     */
    fun markPlayerGuiForRefresh(player: Player) {
        refreshQueue[player] = System.currentTimeMillis()
        
        if (plugin.config.enableDebug) {
            plugin.logger.info("标记玩家 ${player.name} 的GUI需要刷新")
        }
    }
    
    /**
     * 立即刷新玩家GUI
     */
    fun refreshPlayerGuiImmediate(player: Player) {
        CompletableFuture.runAsync({
            try {
                val guiData = playerGuiCache[player]
                if (guiData?.isInitialized == true) {
                    // 重新创建主菜单
                    guiData.mainInventory = createMainInventoryAsync(player)
                    
                    // 重新创建世代菜单
                    val generations = plugin.generationManager.getAllGenerations()
                    for (generation in generations) {
                        val generationInventory = createGenerationInventoryAsync(player, generation.id)
                        if (generationInventory != null) {
                            guiData.generationInventories[generation.id] = generationInventory
                        }
                    }
                    
                    guiData.lastUpdateTime = System.currentTimeMillis()
                    
                    // 如果玩家当前打开的是图鉴界面，刷新显示
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        if (player.isOnline) {
                            plugin.mainGui.refreshCurrentGui(player)
                        }
                    })
                    
                    if (plugin.config.enableDebug) {
                        plugin.logger.info("玩家 ${player.name} 的GUI已刷新")
                    }
                }
            } catch (e: Exception) {
                plugin.logger.warning("刷新玩家 ${player.name} 的GUI失败: ${e.message}")
            }
        }, executor)
    }
    
    /**
     * 启动定期刷新任务
     */
    private fun startPeriodicRefreshTask() {
        executor.scheduleAtFixedRate({
            try {
                val currentTime = System.currentTimeMillis()
                val playersToRefresh = mutableListOf<Player>()
                
                // 检查需要刷新的玩家
                refreshQueue.entries.removeIf { (player, markTime) ->
                    if (currentTime - markTime > 1000) { // 1秒后执行刷新
                        if (player.isOnline) {
                            playersToRefresh.add(player)
                        }
                        true
                    } else {
                        false
                    }
                }
                
                // 批量刷新
                for (player in playersToRefresh) {
                    refreshPlayerGuiImmediate(player)
                }
                
            } catch (e: Exception) {
                plugin.logger.warning("定期刷新任务执行失败: ${e.message}")
            }
        }, 5, 5, TimeUnit.SECONDS)
    }
    
    /**
     * 检查玩家GUI是否已初始化
     */
    fun isPlayerGuiInitialized(player: Player): Boolean {
        return playerGuiCache[player]?.isInitialized == true
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): String {
        return buildString {
            appendLine("在线玩家GUI缓存数量: ${playerGuiCache.size}")
            appendLine("等待刷新的玩家数量: ${refreshQueue.size}")
            
            var initializedCount = 0
            var totalMainInventories = 0
            var totalGenerationInventories = 0
            
            playerGuiCache.values.forEach { guiData ->
                if (guiData.isInitialized) {
                    initializedCount++
                }
                if (guiData.mainInventory != null) {
                    totalMainInventories++
                }
                totalGenerationInventories += guiData.generationInventories.size
            }
            
            appendLine("已初始化的玩家数量: $initializedCount")
            appendLine("主菜单缓存数量: $totalMainInventories")
            appendLine("世代菜单缓存数量: $totalGenerationInventories")
        }
    }
    
    /**
     * 清理所有缓存
     */
    fun clearAllCache() {
        playerGuiCache.clear()
        refreshQueue.clear()
        plugin.logger.info("已清理所有GUI缓存")
    }
    
    /**
     * 关闭管理器
     */
    fun shutdown() {
        executor.shutdown()
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            executor.shutdownNow()
        }
    }
}
