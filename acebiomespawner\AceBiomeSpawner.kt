package cn.acebrand.acebiomespawner

import cn.acebrand.acebiomespawner.config.ConfigManager
import cn.acebrand.acebiomespawner.listener.PlayerEventListener
import cn.acebrand.acebiomespawner.placeholder.BiomeSpawnerPlaceholder
import cn.acebrand.acebiomespawner.spawner.BiomeSpawnerManager
import cn.acebrand.acebiomespawner.command.AceSpawnerCommand
import org.bukkit.Bukkit
import org.bukkit.plugin.java.JavaPlugin
import org.bukkit.scheduler.BukkitRunnable
import java.util.logging.Logger

/**
 * AceBiomeSpawner - 生物群系随机精灵生成插件
 *
 * 功能：
 * - 随机选择在线玩家附近的生物群系
 * - 根据配置在指定生物群系生成精灵
 * - 发送公告通知所有玩家
 * - 支持概率配置和时间间隔设置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
class AceBiomeSpawner : JavaPlugin() {

    companion object {
        const val PLUGIN_ID = "acebiomespawner"
        const val PLUGIN_NAME = "AceBiomeSpawner"
        const val VERSION = "1.0.0"

        lateinit var INSTANCE: AceBiomeSpawner
            private set

        lateinit var CONFIG_MANAGER: ConfigManager
            private set
    }

    private lateinit var spawnerManager: BiomeSpawnerManager
    private var spawnerTask: BukkitRunnable? = null
    private var placeholderExpansion: BiomeSpawnerPlaceholder? = null

    override fun onEnable() {
        INSTANCE = this

        logger.info("$PLUGIN_NAME v$VERSION 正在启用...")

        // 检查Cobblemon依赖
        if (!checkCobblemonDependency()) {
            logger.severe("未找到Cobblemon插件，插件将被禁用！")
            server.pluginManager.disablePlugin(this)
            return
        }

        // 初始化配置管理器
        CONFIG_MANAGER = ConfigManager(this)
        CONFIG_MANAGER.loadConfig()

        // 注册事件监听器
        server.pluginManager.registerEvents(PlayerEventListener(), this)

        // 注册命令
        getCommand("acespawner")?.setExecutor(AceSpawnerCommand())
        getCommand("acespawner")?.tabCompleter = AceSpawnerCommand()

        // 初始化生成器管理器
        spawnerManager = BiomeSpawnerManager()

        // 启动定时任务
        startSpawnerTask()

        // 注册PlaceholderAPI扩展
        registerPlaceholderAPI()

        logger.info("$PLUGIN_NAME 启用完成！")
    }

    override fun onDisable() {
        // 停止定时任务
        spawnerTask?.cancel()
        spawnerTask = null

        // 注销PlaceholderAPI扩展
        placeholderExpansion?.unregister()
        placeholderExpansion = null

        logger.info("$PLUGIN_NAME 插件已禁用")
    }

    /**
     * 检查Cobblemon依赖
     */
    private fun checkCobblemonDependency(): Boolean {
        return try {
            // 尝试加载Cobblemon的核心类
            Class.forName("com.cobblemon.mod.common.api.pokemon.PokemonProperties")
            logger.info("检测到Cobblemon mod")
            true
        } catch (e: ClassNotFoundException) {
            logger.warning("未检测到Cobblemon mod的类: ${e.message}")
            try {
                // 尝试其他可能的Cobblemon类
                Class.forName("com.cobblemon.mod.common.Cobblemon")
                logger.info("检测到Cobblemon mod (备用检测)")
                true
            } catch (e2: ClassNotFoundException) {
                logger.warning("未检测到Cobblemon mod的备用类: ${e2.message}")
                false
            }
        } catch (e: Exception) {
            logger.warning("检测Cobblemon依赖时发生错误: ${e.message}")
            false
        }
    }

    /**
     * 启动生成器定时任务
     */
    private fun startSpawnerTask() {
        val config = CONFIG_MANAGER.getConfig()

        if (!config.enabled) {
            logger.info("插件已禁用，不启动生成器任务")
            return
        }

        spawnerTask = object : BukkitRunnable() {
            override fun run() {
                try {
                    if (CONFIG_MANAGER.getConfig().enabled) {
                        spawnerManager.executeSpawnCycle()
                    }
                } catch (e: Exception) {
                    logger.severe("执行生成周期时发生错误: ${e.message}")
                    e.printStackTrace()
                }
            }
        }

        // 延迟启动，然后按间隔执行
        spawnerTask?.runTaskTimer(
            this,
            (config.initialDelay * 20).toLong(), // 转换为ticks
            (config.spawnInterval * 20).toLong()  // 转换为ticks
        )

        logger.info("生成器任务已启动，间隔: ${config.spawnInterval}秒")
    }

    /**
     * 重启生成器任务
     */
    fun restartSpawnerTask() {
        spawnerTask?.cancel()
        startSpawnerTask()
    }

    /**
     * 获取生成器管理器
     */
    fun getSpawnerManager(): BiomeSpawnerManager {
        return spawnerManager
    }

    /**
     * 重新加载配置
     */
    fun reloadPluginConfig() {
        CONFIG_MANAGER.loadConfig()
        restartSpawnerTask()
        logger.info("配置已重新加载")
    }

    /**
     * 注册PlaceholderAPI扩展
     */
    private fun registerPlaceholderAPI() {
        if (server.pluginManager.getPlugin("PlaceholderAPI") != null) {
            try {
                placeholderExpansion = BiomeSpawnerPlaceholder(this)
                if (placeholderExpansion!!.register()) {
                    logger.info("PlaceholderAPI 扩展注册成功")
                } else {
                    logger.warning("PlaceholderAPI 扩展注册失败")
                }
            } catch (e: Exception) {
                logger.warning("注册PlaceholderAPI扩展时发生错误: ${e.message}")
            }
        } else {
            logger.info("未检测到PlaceholderAPI插件，跳过变量注册")
        }
    }

    /**
     * 获取配置管理器
     */
    fun getConfigManager(): ConfigManager {
        return CONFIG_MANAGER
    }
}
