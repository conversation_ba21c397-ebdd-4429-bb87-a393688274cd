/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.data

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.util.PokemonInfo
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.bukkit.entity.Player
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.util.*

/**
 * 玩家数据管理器
 * 负责玩家数据的加载、保存和管理
 */
class PlayerDataManager(private val plugin: AceDex) {
    
    private val gson: Gson = GsonBuilder()
        .setPrettyPrinting()
        .create()
    
    private val dataFolder: File = File(plugin.dataFolder, "playerdata")
    
    init {
        // 确保数据文件夹存在
        if (!dataFolder.exists()) {
            dataFolder.mkdirs()
        }
    }
    
    /**
     * 加载玩家数据
     */
    fun loadPlayerData(playerId: UUID, playerName: String): PlayerPokemonData {
        return loadPlayerDataFromFile(playerId) ?: run {
            // 如果文件不存在，创建默认数据
            val defaultData = PlayerPokemonData(
                partyPokemon = emptyList(),
                pcPokemon = emptyList(),
                totalCaught = 0
            )
            savePlayerData(playerId, playerName, defaultData)
            defaultData
        }
    }
    
    /**
     * 保存玩家数据
     */
    fun savePlayerData(playerId: UUID, playerName: String, data: PlayerPokemonData) {
        try {
            val file = File(dataFolder, "${playerId}.json")
            val storageData = PlayerDataStorage.fromPlayerPokemonData(
                playerId,
                playerName,
                data
            )
            
            FileWriter(file).use { writer ->
                gson.toJson(storageData, writer)
            }
            
            if (plugin.config.enableDebug) {
                plugin.logger.info("已保存玩家 $playerName 的数据")
            }
        } catch (e: Exception) {
            plugin.logger.warning("保存玩家数据失败: ${e.message}")
        }
    }
    
    /**
     * 从文件加载玩家数据
     */
    private fun loadPlayerDataFromFile(playerId: UUID): PlayerPokemonData? {
        return try {
            val file = File(dataFolder, "${playerId}.json")
            if (!file.exists()) {
                return null
            }
            
            FileReader(file).use { reader ->
                val storageData = gson.fromJson(reader, PlayerDataStorage::class.java)
                storageData?.toPlayerPokemonData()
            }
        } catch (e: Exception) {
            plugin.logger.warning("加载玩家数据失败: ${e.message}")
            null
        }
    }
    
    /**
     * 删除玩家数据文件
     */
    fun deletePlayerData(playerId: UUID) {
        try {
            val file = File(dataFolder, "${playerId}.json")
            if (file.exists()) {
                file.delete()
                plugin.logger.info("已删除玩家数据文件: $playerId")
            }
        } catch (e: Exception) {
            plugin.logger.warning("删除玩家数据文件失败: ${e.message}")
        }
    }
    
    /**
     * 检查玩家数据文件是否存在
     */
    fun hasPlayerData(playerId: UUID): Boolean {
        val file = File(dataFolder, "${playerId}.json")
        return file.exists()
    }
    
    /**
     * 获取所有玩家数据文件
     */
    fun getAllPlayerDataFiles(): List<File> {
        return try {
            dataFolder.listFiles { file -> file.extension == "json" }?.toList() ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 添加精灵到玩家数据
     */
    fun addPokemonToPlayerData(playerId: UUID, playerName: String, pokemonInfo: PokemonInfo): Boolean {
        return try {
            val currentData = loadPlayerData(playerId, playerName)
            
            // 检查是否已经拥有这个精灵
            val allPokemon = currentData.partyPokemon + currentData.pcPokemon
            val alreadyHas = allPokemon.any { 
                it.name == pokemonInfo.name && it.nationalDex == pokemonInfo.nationalDex 
            }
            
            if (!alreadyHas) {
                // 添加到PC精灵列表
                val updatedData = currentData.copy(
                    pcPokemon = currentData.pcPokemon + pokemonInfo,
                    totalCaught = currentData.totalCaught + 1
                )
                
                savePlayerData(playerId, playerName, updatedData)
                true
            } else {
                false // 已经拥有，跳过
            }
        } catch (e: Exception) {
            plugin.logger.warning("添加精灵到玩家数据失败: ${e.message}")
            false
        }
    }
}
