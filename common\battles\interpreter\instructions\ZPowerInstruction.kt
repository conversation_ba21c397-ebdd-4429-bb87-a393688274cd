/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.battles.interpreter.instructions

import com.cobblemon.mod.common.api.battles.interpreter.BattleMessage
import com.cobblemon.mod.common.api.battles.model.PokemonBattle
import com.cobblemon.mod.common.api.events.CobblemonEvents
import com.cobblemon.mod.common.api.events.battles.instruction.ZMoveUsedEvent
import com.cobblemon.mod.common.api.text.yellow
import com.cobblemon.mod.common.battles.dispatch.InterpreterInstruction
import com.cobblemon.mod.common.util.battleLang

/**
 * Format: |-zpower|POKEMON
 *
 * POKEMON has used the z-move variant of its move.
 * <AUTHOR> <PERSON>
 * @since September 10th, 2023
 */
class ZPowerInstruction(val message: BattleMessage): InterpreterInstruction {

    override fun invoke(battle: <PERSON>kemonBattle) {
        val battlePokemon = message.battlePokemon(0, battle) ?: return
        battle.dispatchWaiting {
            val pokemonName = battlePokemon.getName()
            battle.broadcastChatMessage(battleLang("zpower", pokemonName).yellow())
            CobblemonEvents.ZPOWER_USED.post(ZMoveUsedEvent(battle, battlePokemon))
            battle.minorBattleActions[battlePokemon.uuid] = message
        }
    }
}