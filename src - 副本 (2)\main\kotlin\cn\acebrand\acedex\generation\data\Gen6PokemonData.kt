/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第六代精灵数据 (卡洛斯地区)
 * 包含全国图鉴编号 650-721 的精灵数据
 */
object Gen6PokemonData {
    val data = mapOf(
        // 650-658 (卡洛斯御三家及其进化)
        "chespin" to PokemonData(650, "草", "栗鼠精灵", "male", "森林"),
        "quilladin" to PokemonData(651, "草", "栗鼠精灵", "male", "森林"),
        "chesnaught" to PokemonData(652, "草/格斗", "铠甲精灵", "male", "森林"),
        "fennekin" to PokemonData(653, "火", "狐狸精灵", "male", "沙漠"),
        "braixen" to PokemonData(654, "火", "狐狸精灵", "male", "沙漠"),
        "delphox" to PokemonData(655, "火/超能力", "狐狸精灵", "male", "沙漠"),
        "froakie" to PokemonData(656, "水", "泡沫蛙精灵", "male", "淡水, 丛林"),
        "frogadier" to PokemonData(657, "水", "泡沫蛙精灵", "male", "淡水, 丛林"),
        "greninja" to PokemonData(658, "水/恶", "忍者精灵", "male", "淡水, 丛林"),

        // 659-668
        "bunnelby" to PokemonData(659, "一般", "挖掘精灵", "male", "丘陵, 温带"),
        "diggersby" to PokemonData(660, "一般/地面", "挖掘精灵", "male", "丘陵, 温带"),
        "fletchling" to PokemonData(661, "一般/飞行", "知更鸟精灵", "male", "森林, 针叶林"),
        "fletchinder" to PokemonData(662, "火/飞行", "烬火鸟精灵", "male", "森林, 针叶林"),
        "talonflame" to PokemonData(663, "火/飞行", "烈箭鹰精灵", "male", "森林, 针叶林"),
        "scatterbug" to PokemonData(664, "虫", "鳞粉精灵", "male", "花草草原, 平原, 热带草原"),
        "spewpa" to PokemonData(665, "虫", "鳞粉精灵", "male", "花草草原, 平原, 热带草原"),
        "vivillon" to PokemonData(666, "虫/飞行", "鳞粉精灵", "male", "花草草原, 平原, 热带草原, 村庄"),
        "litleo" to PokemonData(667, "火/一般", "幼狮精灵", "male", "热带草原"),
        "pyroar" to PokemonData(668, "火/一般", "王者精灵", "male", "热带草原"),

        // 669-678
        "flabebe" to PokemonData(669, "妖精", "单朵花精灵", "female", "花草草原"),
        "floette" to PokemonData(670, "妖精", "单朵花精灵", "female", "花草草原"),
        "florges" to PokemonData(671, "妖精", "花园精灵", "female", "花草草原"),
        "skiddo" to PokemonData(672, "草", "坐骑精灵", "male", "山地"),
        "gogoat" to PokemonData(673, "草", "坐骑精灵", "male", "山地"),
        "pancham" to PokemonData(674, "格斗", "顽皮精灵", "male", "森林"),
        "pangoro" to PokemonData(675, "格斗/恶", "霸道精灵", "male", "森林"),
        "furfrou" to PokemonData(676, "一般", "贵宾犬精灵", "male", "城市, 村庄"),
        "espurr" to PokemonData(677, "超能力", "抑制精灵", "male", "城市, 村庄"),
        "meowstic" to PokemonData(678, "超能力", "抑制精灵", "male", "城市, 村庄"),

        // 679-688
        "honedge" to PokemonData(679, "钢/幽灵", "剑精灵", "male", "地下"),
        "doublade" to PokemonData(680, "钢/幽灵", "剑精灵", "male", "地下"),
        "aegislash" to PokemonData(681, "钢/幽灵", "王剑精灵", "male", "地下"),
        "spritzee" to PokemonData(682, "妖精", "香水精灵", "female", "花草草原"),
        "aromatisse" to PokemonData(683, "妖精", "芳香精灵", "female", "花草草原"),
        "swirlix" to PokemonData(684, "妖精", "棉花糖精灵", "male", "花草草原"),
        "slurpuff" to PokemonData(685, "妖精", "生奶油精灵", "male", "花草草原"),
        "inkay" to PokemonData(686, "恶/超能力", "回转精灵", "male", "海洋"),
        "malamar" to PokemonData(687, "恶/超能力", "回转精灵", "male", "海洋"),
        "binacle" to PokemonData(688, "岩石/水", "两手精灵", "male", "海岸, 海洋"),

        // 689-698
        "barbaracle" to PokemonData(689, "岩石/水", "集合精灵", "male", "海岸, 海洋"),
        "skrelp" to PokemonData(690, "毒/水", "仿生精灵", "male", "海洋"),
        "dragalge" to PokemonData(691, "毒/龙", "仿生精灵", "male", "海洋"),
        "clauncher" to PokemonData(692, "水", "水枪精灵", "male", "海岸, 海洋"),
        "clawitzer" to PokemonData(693, "水", "发射器精灵", "male", "海洋"),
        "helioptile" to PokemonData(694, "电/一般", "发电蜥精灵", "male", "沙漠"),
        "heliolisk" to PokemonData(695, "电/一般", "发电蜥精灵", "male", "沙漠"),
        "tyrunt" to PokemonData(696, "岩石/龙", "幼君精灵", "male", "地下"),
        "tyrantrum" to PokemonData(697, "岩石/龙", "暴君精灵", "male", "地下"),
        "amaura" to PokemonData(698, "岩石/冰", "冻原精灵", "male", "地下"),

        // 699-708
        "aurorus" to PokemonData(699, "岩石/冰", "冻原精灵", "male", "地下"),
        "sylveon" to PokemonData(700, "妖精", "缎带精灵", "male", "花草草原, 魔法森林, 城市, 村庄"),
        "hawlucha" to PokemonData(701, "格斗/飞行", "摔跤精灵", "male", "山地"),
        "dedenne" to PokemonData(702, "电/妖精", "天线精灵", "male", "花草草原"),
        "carbink" to PokemonData(703, "岩石/妖精", "宝石精灵", "genderless", "地下"),
        "goomy" to PokemonData(704, "龙", "软体精灵", "male", "沼泽"),
        "sliggoo" to PokemonData(705, "龙", "软体精灵", "male", "沼泽"),
        "goodra" to PokemonData(706, "龙", "龙精灵", "male", "沼泽"),
        "klefki" to PokemonData(707, "钢/妖精", "钥匙圈精灵", "male", "城市, 村庄"),
        "phantump" to PokemonData(708, "幽灵/草", "树桩精灵", "male", "森林, 针叶林, 阴森森林"),

        // 709-718
        "trevenant" to PokemonData(709, "幽灵/草", "老树精灵", "male", "森林, 针叶林, 阴森森林"),
        "pumpkaboo" to PokemonData(710, "幽灵/草", "南瓜精灵", "male", "平原"),
        "gourgeist" to PokemonData(711, "幽灵/草", "南瓜精灵", "male", "平原"),
        "bergmite" to PokemonData(712, "冰", "冰块精灵", "male", "冰冻海洋"),
        "avalugg" to PokemonData(713, "冰", "冰山精灵", "male", "冰冻海洋"),
        "noibat" to PokemonData(714, "飞行/龙", "音波精灵", "male", "洞穴"),
        "noivern" to PokemonData(715, "飞行/龙", "音波精灵", "male", "洞穴"),
        "xerneas" to PokemonData(716, "妖精", "生命精灵", "genderless", "传说区域"),
        "yveltal" to PokemonData(717, "恶/飞行", "破坏精灵", "genderless", "传说区域"),
        "zygarde" to PokemonData(718, "龙/地面", "秩序精灵", "genderless", "传说区域"),

        // 719-721 (传说精灵)
        "diancie" to PokemonData(719, "岩石/妖精", "宝石精灵", "genderless", "传说区域"),
        "hoopa" to PokemonData(720, "超能力/幽灵", "恶作剧精灵", "genderless", "传说区域"),
        "volcanion" to PokemonData(721, "火/水", "蒸汽精灵", "genderless", "传说区域")
    )
}
