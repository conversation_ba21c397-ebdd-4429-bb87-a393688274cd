/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.block.grower

import com.cobblemon.mod.common.world.feature.CobblemonConfiguredFeatures
import net.minecraft.world.level.block.grower.TreeGrower
import java.util.*

class SaccharineTreeGrower() : TreeGrower(
        "saccharine_tree",
        Optional.empty(),
        Optional.of(CobblemonConfiguredFeatures.SACCHARINE_TREE_KEY),
        Optional.empty()
)