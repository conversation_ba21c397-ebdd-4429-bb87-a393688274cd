/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.util

import com.cobblemon.mod.common.Cobblemon
import com.cobblemon.mod.common.api.pokemon.PokemonSpecies
import com.cobblemon.mod.common.api.storage.party.PlayerPartyStore
import com.cobblemon.mod.common.api.storage.pc.PCStore
import com.cobblemon.mod.common.item.PokemonItem
import com.cobblemon.mod.common.pokemon.Pokemon
import com.cobblemon.mod.common.pokemon.Species
import net.minecraft.core.RegistryAccess
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack
import java.util.*

/**
 * Cobblemon API辅助工具类
 * 处理混淆类访问和API调用
 */
object CobblemonApiHelper {
    
    /**
     * 安全地获取玩家的队伍存储
     */
    fun getPlayerParty(player: Player): PlayerPartyStore? {
        return try {
            // 方法1：尝试通过EntityPlayer获取（从调试信息看到有这个方法）
            try {
                val serverPlayer = getServerPlayer(player)
                if (serverPlayer != null) {
                    val storageManager = Cobblemon.storage
                    val getPartyMethod = storageManager.javaClass.methods.find {
                        it.name == "getParty" && it.parameterCount == 1 &&
                        it.parameterTypes[0].simpleName.contains("EntityPlayer")
                    }
                    if (getPartyMethod != null) {
                        return getPartyMethod.invoke(storageManager, serverPlayer) as? PlayerPartyStore
                    }
                }
            } catch (e: Exception) {
                // 方法失败，继续尝试其他方法
            }

            // 方法2：尝试通过UUID和IRegistryCustom获取
            try {
                val iRegistryCustom = getIRegistryCustom()
                if (iRegistryCustom != null) {
                    val storageManager = Cobblemon.storage
                    val getPartyMethod = storageManager.javaClass.methods.find {
                        it.name == "getParty" && it.parameterCount == 2 &&
                        it.parameterTypes[0] == java.util.UUID::class.java
                    }
                    if (getPartyMethod != null) {
                        return getPartyMethod.invoke(storageManager, player.uniqueId, iRegistryCustom) as? PlayerPartyStore
                    }
                }
            } catch (e: Exception) {
                // 方法失败，继续尝试其他方法
            }

            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 安全地获取玩家的PC存储
     */
    fun getPlayerPC(player: Player): PCStore? {
        return try {
            // 方法1：尝试通过EntityPlayer获取
            try {
                val serverPlayer = getServerPlayer(player)
                if (serverPlayer != null) {
                    val storageManager = Cobblemon.storage
                    val getPCMethod = storageManager.javaClass.methods.find {
                        it.name == "getPC" && it.parameterCount == 1 &&
                        it.parameterTypes[0].simpleName.contains("EntityPlayer")
                    }
                    if (getPCMethod != null) {
                        return getPCMethod.invoke(storageManager, serverPlayer) as? PCStore
                    }
                }
            } catch (e: Exception) {
                // 方法失败，继续尝试其他方法
            }

            // 方法2：尝试通过UUID和IRegistryCustom获取
            try {
                val iRegistryCustom = getIRegistryCustom()
                if (iRegistryCustom != null) {
                    val storageManager = Cobblemon.storage
                    val getPCMethod = storageManager.javaClass.methods.find {
                        it.name == "getPC" && it.parameterCount == 2 &&
                        it.parameterTypes[0] == java.util.UUID::class.java
                    }
                    if (getPCMethod != null) {
                        return getPCMethod.invoke(storageManager, player.uniqueId, iRegistryCustom) as? PCStore
                    }
                }
            } catch (e: Exception) {
                // 方法失败，继续尝试其他方法
            }

            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取ServerPlayer
     */
    private fun getServerPlayer(player: Player): Any? {
        return try {
            // 通过反射获取ServerPlayer
            val craftPlayerClass = player.javaClass
            val getHandleMethod = craftPlayerClass.getMethod("getHandle")
            getHandleMethod.invoke(player)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取IRegistryCustom
     */
    private fun getIRegistryCustom(): Any? {
        return try {
            // 尝试通过MinecraftServer获取IRegistryCustom
            val bukkitServer = Bukkit.getServer()
            val craftServerClass = bukkitServer.javaClass

            // 获取MinecraftServer
            val getServerMethod = craftServerClass.getMethod("getServer")
            val minecraftServer = getServerMethod.invoke(bukkitServer)

            // 尝试获取IRegistryCustom
            val possibleMethods = listOf(
                "registryAccess",
                "getRegistryAccess",
                "registries",
                "getRegistries"
            )

            for (methodName in possibleMethods) {
                try {
                    val method = minecraftServer.javaClass.getMethod(methodName)
                    val result = method.invoke(minecraftServer)
                    if (result != null) {
                        return result
                    }
                } catch (e: Exception) {
                    // 继续尝试下一个方法
                }
            }

            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取RegistryAccess (保留原方法)
     */
    fun getRegistryAccess(): RegistryAccess? {
        return try {
            // 方法1：尝试通过Cobblemon获取
            try {
                val cobblemonClass = Cobblemon::class.java
                val registryAccessField = cobblemonClass.declaredFields.find {
                    it.type.simpleName.contains("RegistryAccess")
                }
                if (registryAccessField != null) {
                    registryAccessField.isAccessible = true
                    return registryAccessField.get(null) as? RegistryAccess
                }
            } catch (e: Exception) {
                // 继续尝试其他方法
            }

            // 方法2：通过反射获取MinecraftServer
            val bukkitServer = Bukkit.getServer()
            val craftServerClass = bukkitServer.javaClass

            // 尝试不同的获取服务器的方法
            val serverMethods = listOf("getServer", "getHandle", "getMinecraftServer")
            var minecraftServer: Any? = null

            for (methodName in serverMethods) {
                try {
                    val method = craftServerClass.getMethod(methodName)
                    minecraftServer = method.invoke(bukkitServer)
                    break
                } catch (e: Exception) {
                    // 继续尝试下一个方法
                }
            }

            if (minecraftServer != null) {
                // 尝试不同的方法名（包括混淆名）
                val possibleMethods = listOf(
                    "registryAccess",
                    "getRegistryAccess",
                    "registries",
                    "getRegistries",
                    "m_206755_",  // 可能的混淆名
                    "f_129815_"   // 另一个可能的混淆名
                )

                for (methodName in possibleMethods) {
                    try {
                        val method = minecraftServer.javaClass.getMethod(methodName)
                        val result = method.invoke(minecraftServer)
                        if (result != null && result.javaClass.simpleName.contains("RegistryAccess")) {
                            return result as RegistryAccess
                        }
                    } catch (e: Exception) {
                        // 继续尝试下一个方法
                    }
                }
            }

            null

        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 使用 LGPokemonMenu 的方法创建精灵物品
     */
    fun createPokemonItemByLGMenu(pokemon: Pokemon): ItemStack? {
        return try {
            // 尝试调用 LGPokemonMenu.getPokemonItem(pokemon)
            val lgMenuClass = Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
            val getPokemonItemMethod = lgMenuClass.getMethod("getPokemonItem", Pokemon::class.java)
            getPokemonItemMethod.invoke(null, pokemon) as ItemStack
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据精灵名称获取 Cobblemon Species
     */
    fun getSpeciesByName(pokemonName: String): Species? {
        return try {
            // 尝试通过 PokemonSpecies API 获取精灵种类
            val resourceIdentifier = com.cobblemon.mod.common.util.cobblemonResource(pokemonName.lowercase())
            PokemonSpecies.getByIdentifier(resourceIdentifier)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据全国图鉴编号获取 Species
     */
    fun getSpeciesByDexNumber(dexNumber: Int): Species? {
        return try {
            PokemonSpecies.getByPokedexNumber(dexNumber)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 创建精灵对象
     */
    fun createPokemon(species: Species, isShiny: Boolean = false, level: Int = 1): Pokemon {
        val pokemon = Pokemon()
        pokemon.species = species
        pokemon.shiny = isShiny
        pokemon.level = level
        pokemon.initialize()
        return pokemon
    }

    /**
     * 从精灵对象创建物品（优先使用 LGPokemonMenu 的方法）
     */
    fun createPokemonItem(pokemon: Pokemon): ItemStack? {
        return try {
            // 方法1：尝试使用 LGPokemonMenu 的方法
            val lgMenuItem = createPokemonItemByLGMenu(pokemon)
            if (lgMenuItem != null) {
                return lgMenuItem
            }

            // 方法2：直接使用 Cobblemon API（可能会有版本兼容问题）
            val nmsItem = PokemonItem.from(pokemon)
            convertToItemStack(nmsItem)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据精灵名称直接创建精灵物品
     */
    fun createPokemonItemByName(pokemonName: String, level: Int = 1): ItemStack? {
        return try {
            val species = getSpeciesByName(pokemonName) ?: return null
            val pokemon = createPokemon(species, false, level)
            createPokemonItem(pokemon)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据全国图鉴编号创建精灵物品
     */
    fun createPokemonItemByDexNumber(dexNumber: Int, level: Int = 1): ItemStack? {
        return try {
            val species = getSpeciesByDexNumber(dexNumber) ?: return null
            val pokemon = createPokemon(species, false, level)
            createPokemonItem(pokemon)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 将 NMS ItemStack 转换为 Bukkit ItemStack
     * 使用反射来避免版本依赖问题
     */
    private fun convertToItemStack(nmsItem: Any): ItemStack? {
        return try {
            // 尝试使用 LGPokemonMenu 的转换方法
            val lgMenuClass = Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
            val toItemStackMethod = lgMenuClass.getMethod("toItemStack", Class.forName("class_1799"))
            toItemStackMethod.invoke(null, nmsItem) as ItemStack
        } catch (e: Exception) {
            try {
                // 备用方案：直接使用反射调用 CraftItemStack.asBukkitCopy
                val craftItemStackClass = Class.forName("org.bukkit.craftbukkit.inventory.CraftItemStack")
                val asBukkitCopyMethod = craftItemStackClass.getMethod("asBukkitCopy", Class.forName("net.minecraft.world.item.ItemStack"))
                asBukkitCopyMethod.invoke(null, nmsItem) as ItemStack
            } catch (e2: Exception) {
                // 如果都失败，返回 null
                null
            }
        }
    }

    /**
     * 检查精灵是否存在于 Cobblemon 中
     */
    fun isPokemonExists(pokemonName: String): Boolean {
        return getSpeciesByName(pokemonName) != null
    }

    /**
     * 检查指定图鉴编号的精灵是否存在
     */
    fun isPokemonExists(dexNumber: Int): Boolean {
        return getSpeciesByDexNumber(dexNumber) != null
    }

    /**
     * 获取精灵的本地化名称
     */
    fun getPokemonDisplayName(species: Species): String {
        return try {
            species.translatedName.string
        } catch (e: Exception) {
            species.name
        }
    }

    /**
     * 获取精灵的类型信息
     */
    fun getPokemonTypes(species: Species): Pair<String, String?> {
        return try {
            val primaryType = species.primaryType.displayName.string
            val secondaryType = species.secondaryType?.displayName?.string
            Pair(primaryType, secondaryType)
        } catch (e: Exception) {
            Pair("未知", null)
        }
    }

    /**
     * 安全地获取精灵信息
     */
    fun getPokemonInfo(pokemon: Pokemon): PokemonInfo? {
        return try {
            val species = pokemon.species
            val nationalDex = species.nationalPokedexNumber
            val generation = determineGeneration(nationalDex)

            PokemonInfo(
                name = species.name,
                nationalDex = nationalDex,
                generation = generation
            )
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据全国图鉴编号确定世代
     */
    private fun determineGeneration(nationalDex: Int): String {
        return when (nationalDex) {
            in 1..151 -> "第一世代"
            in 152..251 -> "第二世代"
            in 252..386 -> "第三世代"
            in 387..493 -> "第四世代"
            in 494..649 -> "第五世代"
            in 650..721 -> "第六世代"
            in 722..809 -> "第七世代"
            in 810..905 -> "第八世代"
            in 906..1025 -> "第九世代"
            else -> "未知世代"
        }
    }
    
    /**
     * 通过反射安全地获取对象的UUID
     */
    fun getUUIDFromObject(obj: Any): UUID? {
        return try {
            // 尝试多种可能的方法名
            val possibleMethods = listOf("getUUID", "uuid", "getId", "getGameProfile")
            
            for (methodName in possibleMethods) {
                try {
                    val method = obj.javaClass.getMethod(methodName)
                    val result = method.invoke(obj)
                    if (result is UUID) {
                        return result
                    }
                    // 如果是GameProfile，尝试获取其UUID
                    if (result != null && result.javaClass.simpleName == "GameProfile") {
                        val idMethod = result.javaClass.getMethod("getId")
                        return idMethod.invoke(result) as UUID
                    }
                } catch (e: NoSuchMethodException) {
                    // 继续尝试下一个方法
                }
            }
            
            // 尝试通过字段获取
            val possibleFields = listOf("uuid", "id", "gameProfile")
            for (fieldName in possibleFields) {
                try {
                    val field = obj.javaClass.getDeclaredField(fieldName)
                    field.isAccessible = true
                    val result = field.get(obj)
                    if (result is UUID) {
                        return result
                    }
                } catch (e: NoSuchFieldException) {
                    // 继续尝试下一个字段
                }
            }
            
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 检查Cobblemon是否可用
     */
    fun isCobblemonAvailable(): Boolean {
        return try {
            val storage = Cobblemon.storage
            storage != null
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 调试方法：打印Cobblemon存储信息
     */
    fun debugCobblemonStorage() {
        // 调试方法已禁用，避免控制台日志
    }
}


