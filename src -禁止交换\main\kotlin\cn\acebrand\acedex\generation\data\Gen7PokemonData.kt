/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第七代精灵数据 (阿罗拉地区)
 * 包含全国图鉴编号 722-809 的精灵数据
 */
object Gen7PokemonData {
    val data = mapOf(
        // 722-730 (阿罗拉御三家及其进化)
        "rowlet" to PokemonData(722, "草/飞行", "草羽精灵", "male", "森林, 针叶林, 雪山"),
        "dartrix" to PokemonData(723, "草/飞行", "刃羽精灵", "male", "森林, 针叶林, 雪山"),
        "decidueye" to PokemonData(724, "草/幽灵", "箭羽精灵", "male", "森林, 针叶林, 雪山"),
        "litten" to PokemonData(725, "火", "火猫精灵", "male", "丛林"),
        "torracat" to PokemonData(726, "火", "火猫精灵", "male", "丛林"),
        "incineroar" to PokemonData(727, "火/恶", "虎斑精灵", "male", "丛林"),
        "popplio" to PokemonData(728, "水", "海狮精灵", "male", "海岸, 海洋, 热带岛屿"),
        "brionne" to PokemonData(729, "水", "海狮精灵", "male", "海岸, 海洋, 热带岛屿"),
        "primarina" to PokemonData(730, "水/妖精", "歌声精灵", "male", "海岸, 海洋, 热带岛屿"),

        // 731-740
        "pikipek" to PokemonData(731, "一般/飞行", "啄木鸟精灵", "male", "丛林, 热带岛屿"),
        "trumbeak" to PokemonData(732, "一般/飞行", "喇叭鸟精灵", "male", "丛林, 热带岛屿"),
        "toucannon" to PokemonData(733, "一般/飞行", "炮嘴鸟精灵", "male", "丛林, 热带岛屿"),
        "yungoos" to PokemonData(734, "一般", "巡守精灵", "male", "草原"),
        "gumshoos" to PokemonData(735, "一般", "侦探精灵", "male", "草原"),
        "grubbin" to PokemonData(736, "虫", "幼虫精灵", "male", "森林"),
        "charjabug" to PokemonData(737, "虫/电", "蓄电池精灵", "male", "森林"),
        "vikavolt" to PokemonData(738, "虫/电", "锹形虫精灵", "male", "森林"),
        "crabrawler" to PokemonData(739, "格斗", "拳击蟹精灵", "male", "海岸"),
        "crabominable" to PokemonData(740, "格斗/冰", "毛蟹精灵", "male", "山峰"),

        // 741-750
        "oricorio" to PokemonData(741, "火/飞行", "舞蹈鸟精灵", "male", "花草草原"),
        "cutiefly" to PokemonData(742, "虫/妖精", "蜂虻精灵", "male", "花草草原"),
        "ribombee" to PokemonData(743, "虫/妖精", "蜂球精灵", "male", "花草草原"),
        "rockruff" to PokemonData(744, "岩石", "岩狗精灵", "male", "恶地, 山地"),
        "lycanroc" to PokemonData(745, "岩石", "狼人精灵", "male", "恶地, 山地"),
        "wishiwashi" to PokemonData(746, "水", "弱丁鱼精灵", "male", "海岸, 海洋"),
        "mareanie" to PokemonData(747, "毒/水", "好坏星精灵", "male", "海岸, 温暖海洋"),
        "toxapex" to PokemonData(748, "毒/水", "超坏星精灵", "male", "海岸, 温暖海洋"),
        "mudbray" to PokemonData(749, "地面", "泥驴仔精灵", "male", "恶地, 草原"),
        "mudsdale" to PokemonData(750, "地面", "重泥挽马精灵", "male", "恶地, 草原"),

        // 751-760
        "dewpider" to PokemonData(751, "水/虫", "滴蛛精灵", "male", "森林, 淡水, 草原, 丛林"),
        "araquanid" to PokemonData(752, "水/虫", "滴蛛霸精灵", "male", "森林, 淡水, 草原, 丛林"),
        "fomantis" to PokemonData(753, "草", "伪螳草精灵", "male", "花草草原, 丛林, 热带岛屿"),
        "lurantis" to PokemonData(754, "草", "兰螳花精灵", "male", "花草草原, 丛林, 热带岛屿"),
        "morelull" to PokemonData(755, "草/妖精", "睡菇精灵", "male", "丛林, 魔法森林, 蘑菇岛"),
        "shiinotic" to PokemonData(756, "草/妖精", "灯罩夜菇精灵", "male", "丛林, 魔法森林, 蘑菇岛"),
        "salandit" to PokemonData(757, "毒/火", "夜盗蜥精灵", "male", "火山, 恶地, 山地"),
        "salazzle" to PokemonData(758, "毒/火", "焰后蜥精灵", "female", "火山, 恶地, 山地"),
        "stufful" to PokemonData(759, "一般/格斗", "童偶熊精灵", "male", "竹林"),
        "bewear" to PokemonData(760, "一般/格斗", "穿着熊精灵", "male", "竹林"),

        // 761-770
        "bounsweet" to PokemonData(761, "草", "甜竹竹精灵", "female", "丛林, 热带岛屿"),
        "steenee" to PokemonData(762, "草", "甜舞妮精灵", "female", "丛林, 热带岛屿"),
        "tsareena" to PokemonData(763, "草", "甜冷美后精灵", "female", "丛林, 热带岛屿"),
        "comfey" to PokemonData(764, "妖精", "花疗环环精灵", "female", "花草草原, 热带岛屿"),
        "oranguru" to PokemonData(765, "一般/超能力", "智挥猩精灵", "male", "丛林"),
        "passimian" to PokemonData(766, "格斗", "投掷猴精灵", "male", "丛林"),
        "wimpod" to PokemonData(767, "虫/水", "胆小虫精灵", "male", "海岸, 热带岛屿"),
        "golisopod" to PokemonData(768, "虫/水", "具甲武者精灵", "male", "海岸, 热带岛屿"),
        "sandygast" to PokemonData(769, "幽灵/地面", "沙丘娃精灵", "male", "海滩"),
        "palossand" to PokemonData(770, "幽灵/地面", "噬沙堡爷精灵", "male", "海滩"),

        // 771-780
        "pyukumuku" to PokemonData(771, "水", "拳海参精灵", "male", "海滩, 海洋, 温暖海洋"),
        "typenull" to PokemonData(772, "一般", "属性：空精灵", "genderless", "城市"),
        "silvally" to PokemonData(773, "一般", "银伴战兽精灵", "genderless", "城市"),
        "minior" to PokemonData(774, "岩石/飞行", "小陨星精灵", "genderless", "山峰"),
        "komala" to PokemonData(775, "一般", "树枕尾熊精灵", "male", "热带草原"),
        "turtonator" to PokemonData(776, "火/龙", "爆焰龟兽精灵", "male", "火山"),
        "togedemaru" to PokemonData(777, "电/钢", "托戈德玛尔精灵", "male", "平原"),
        "mimikyu" to PokemonData(778, "幽灵/妖精", "谜拟Q精灵", "male", "阴森森林"),
        "bruxish" to PokemonData(779, "水/超能力", "磨牙彩皮鱼精灵", "male", "温暖海洋"),
        "drampa" to PokemonData(780, "一般/龙", "老翁龙精灵", "male", "山地"),

        // 781-790
        "dhelmise" to PokemonData(781, "幽灵/草", "破破舵轮精灵", "genderless", "海洋"),
        "jangmoo" to PokemonData(782, "龙", "心鳞宝精灵", "male", "丛林, 热带岛屿"),
        "hakamoo" to PokemonData(783, "龙/格斗", "鳞甲龙精灵", "male", "丛林, 热带岛屿"),
        "kommoo" to PokemonData(784, "龙/格斗", "杖尾鳞甲龙精灵", "male", "丛林, 热带岛屿"),
        "tapukoko" to PokemonData(785, "电/妖精", "卡璞·鸣鸣精灵", "genderless", "传说区域"),
        "tapulele" to PokemonData(786, "超能力/妖精", "卡璞·蝶蝶精灵", "genderless", "传说区域"),
        "tapubulu" to PokemonData(787, "草/妖精", "卡璞·哞哞精灵", "genderless", "传说区域"),
        "tapufini" to PokemonData(788, "水/妖精", "卡璞·鳍鳍精灵", "genderless", "传说区域"),
        "cosmog" to PokemonData(789, "超能力", "科斯莫古精灵", "genderless", "传说区域"),
        "cosmoem" to PokemonData(790, "超能力", "科斯莫姆精灵", "genderless", "传说区域"),

        // 791-800
        "solgaleo" to PokemonData(791, "超能力/钢", "索尔迦雷欧精灵", "genderless", "传说区域"),
        "lunala" to PokemonData(792, "超能力/幽灵", "露奈雅拉精灵", "genderless", "传说区域"),
        "necrozma" to PokemonData(793, "超能力", "奈克洛兹玛精灵", "genderless", "传说区域"),
        "magearna" to PokemonData(794, "钢/妖精", "玛机雅娜精灵", "genderless", "传说区域"),
        "marshadow" to PokemonData(795, "格斗/幽灵", "玛夏多精灵", "genderless", "传说区域"),
        "poipole" to PokemonData(796, "毒", "毒贝比精灵", "genderless", "传说区域"),
        "naganadel" to PokemonData(797, "毒/龙", "四颚针龙精灵", "genderless", "传说区域"),
        "stakataka" to PokemonData(798, "岩石/钢", "垒磊石精灵", "genderless", "传说区域"),
        "blacephalon" to PokemonData(799, "火/幽灵", "爆头小丑精灵", "genderless", "传说区域"),
        "zeraora" to PokemonData(800, "电", "捷拉奥拉精灵", "genderless", "传说区域"),

        // 801-809 (阿罗拉形态和其他)
        "meltan" to PokemonData(808, "钢", "美录坦精灵", "genderless", "传说区域"),
        "melmetal" to PokemonData(809, "钢", "美录梅塔精灵", "genderless", "传说区域")
    )
}
