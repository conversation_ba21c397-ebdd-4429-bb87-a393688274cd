/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第八世代精灵名称映射 (810-905)
 * 包含伽勒尔地区的所有精灵和传说阿尔宙斯新增精灵
 */
object PokemonGen8Mapping {
    
    /**
     * 获取第八世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第八世代 (810-905)
            "grookey" to "敲音猴",
            "thwackey" to "啪咚猴",
            "rillaboom" to "轰擂金刚猩",
            "scorbunny" to "炎兔儿",
            "raboot" to "腾蹴小将",
            "cinderace" to "闪焰王牌",
            "sobble" to "泪眼蜥",
            "drizzile" to "变涩蜥",
            "inteleon" to "千面避役",
            "skwovet" to "贪心栗鼠",
            "greedent" to "藏饱栗鼠",
            "rookidee" to "稚山雀",
            "corvisquire" to "蓝鸦",
            "corviknight" to "钢铠鸦",
            "blipbug" to "索侦虫",
            "dottler" to "天罩虫",
            "orbeetle" to "以欧路普",
            "nickit" to "偷儿狐",
            "thievul" to "狐大盗",
            "gossifleur" to "幼棉棉",
            "eldegoss" to "白蓬蓬",
            "wooloo" to "毛辫羊",
            "dubwool" to "毛毛角羊",
            "chewtle" to "咬咬龟",
            "drednaw" to "暴噬龟",
            "yamper" to "来电汪",
            "boltund" to "逐电犬",
            "rolycoly" to "小炭仔",
            "carkol" to "大炭车",
            "coalossal" to "巨炭山",
            "applin" to "啃果虫",
            "flapple" to "苹裹龙",
            "appletun" to "丰蜜龙",
            "silicobra" to "沙包蛇",
            "sandaconda" to "沙螺蟒",
            "cramorant" to "古月鸟",
            "arrokuda" to "刺梭鱼",
            "barraskewda" to "戽斗尖梭",
            "toxel" to "毒电婴",
            "toxtricity" to "颤弦蝾螈",
            "sizzlipede" to "烧火蚣",
            "centiskorch" to "焚焰蚣",
            "clobbopus" to "拳拳蛸",
            "grapploct" to "八爪武师",
            "sinistea" to "来悲茶",
            "polteageist" to "怖思壶",
            "hatenna" to "迷布莉姆",
            "hattrem" to "提布莉姆",
            "hatterene" to "布莉姆温",
            "impidimp" to "捣蛋小妖",
            "morgrem" to "诈唬魔",
            "grimmsnarl" to "长毛巨魔",
            "obstagoon" to "堵拦熊",
            "perrserker" to "喵头目",
            "cursola" to "魔灵珊瑚",
            "sirfetchd" to "葱游兵",
            "mrrime" to "踏冰人偶",
            "runerigus" to "死神板",
            "milcery" to "小仙奶",
            "alcremie" to "霜奶仙",
            "falinks" to "列阵兵",
            "pincurchin" to "啪嚓海胆",
            "snom" to "雪吞虫",
            "frosmoth" to "雪绒蛾",
            "stonjourner" to "巨石丁",
            "eiscue" to "冰砌鹅",
            "indeedee" to "爱管侍",
            "morpeko" to "莫鲁贝可",
            "cufant" to "铜象",
            "copperajah" to "大王铜象",
            "dracozolt" to "雷鸟龙",
            "arctozolt" to "雷鸟海兽",
            "dracovish" to "鳃鱼龙",
            "arctovish" to "鳃鱼海兽",
            "duraludon" to "铝钢龙",
            "dreepy" to "多龙梅西亚",
            "drakloak" to "多龙奇",
            "dragapult" to "多龙巴鲁托",
            "zacian" to "苍响",
            "zamazenta" to "藏玛然特",
            "eternatus" to "无极汰那",
            "kubfu" to "熊徒弟",
            "urshifu" to "武道熊师",
            "zarude" to "萨戮德",
            "regieleki" to "雷吉艾勒奇",
            "regidrago" to "雷吉铎拉戈",
            "glastrier" to "雪暴马",
            "spectrier" to "灵幽马",
            "calyrex" to "蕾冠王",

            // 传说阿尔宙斯新增精灵 (899-905)
            "wyrdeer" to "诡角鹿",
            "kleavor" to "劈斧螳螂",
            "ursaluna" to "月月熊",
            "basculegion" to "幽尾玄鱼",
            "sneasler" to "大狃拉",
            "overqwil" to "万针鱼",
            "enamorus" to "眷恋云",
    )
}
