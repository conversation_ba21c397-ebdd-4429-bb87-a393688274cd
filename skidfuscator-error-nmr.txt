handler=Block #CR, types=[Ljava/io/IOException;], range=[Block #CQ, Block #CP]
handler=Block #CU, types=[Ljava/lang/RuntimeException;], range=[Block #CT, Block #CS]
handler=Block #CX, types=[Ljava/lang/IllegalAccessException;], range=[Block #CW, Block #CV]
handler=Block #DA, types=[Ljava/lang/IllegalAccessException;], range=[Block #CZ, Block #CY]
handler=Block #DD, types=[Ljava/lang/IllegalAccessException;], range=[Block #DC, Block #DB]
handler=Block #DG, types=[Ljava/lang/IllegalAccessException;], range=[Block #DF, Block #DE]
handler=Block #DJ, types=[Ljava/lang/IllegalAccessException;], range=[Block #DI, Block #DH]
handler=Block #DM, types=[Ljava/io/IOException;], range=[Block #DL, Block #DK]
handler=Block #DP, types=[Ljava/io/IOException;], range=[Block #DO, Block #DN]
handler=Block #DS, types=[Ljava/lang/RuntimeException;], range=[Block #DR, Block #DQ]
handler=Block #DV, types=[Ljava/lang/IllegalAccessException;], range=[Block #DU, Block #DT]
handler=Block #DY, types=[Ljava/lang/RuntimeException;], range=[Block #DX, Block #DW]
handler=Block #EB, types=[Ljava/io/IOException;], range=[Block #EA, Block #DZ]
handler=Block #EE, types=[Ljava/lang/IllegalAccessException;], range=[Block #ED, Block #EC]
handler=Block #EH, types=[Ljava/lang/RuntimeException;], range=[Block #EG, Block #EF]
handler=Block #EK, types=[Ljava/lang/RuntimeException;], range=[Block #EJ, Block #EI]
handler=Block #EN, types=[Ljava/lang/IllegalAccessException;], range=[Block #EM, Block #EL]
handler=Block #EQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #EP, Block #EO]
handler=Block #ET, types=[Ljava/lang/RuntimeException;], range=[Block #ES, Block #ER]
handler=Block #EW, types=[Ljava/lang/RuntimeException;], range=[Block #EV, Block #EU]
handler=Block #EZ, types=[Ljava/lang/IllegalAccessException;], range=[Block #EY, Block #EX]
handler=Block #FC, types=[Ljava/io/IOException;], range=[Block #FB, Block #FA]
handler=Block #FF, types=[Ljava/lang/RuntimeException;], range=[Block #FE, Block #FD]
handler=Block #FI, types=[Ljava/io/IOException;], range=[Block #FH, Block #FG]
handler=Block #FL, types=[Ljava/lang/RuntimeException;], range=[Block #FK, Block #FJ]
handler=Block #FO, types=[Ljava/lang/RuntimeException;], range=[Block #FN, Block #FM]
handler=Block #FR, types=[Ljava/lang/RuntimeException;], range=[Block #FQ, Block #FP]
handler=Block #FU, types=[Ljava/lang/IllegalAccessException;], range=[Block #FT, Block #FS]
handler=Block #FX, types=[Ljava/lang/RuntimeException;], range=[Block #FW, Block #FV]
handler=Block #GA, types=[Ljava/lang/RuntimeException;], range=[Block #FZ, Block #FY]
handler=Block #GD, types=[Ljava/lang/IllegalAccessException;], range=[Block #GC, Block #GB]
handler=Block #GG, types=[Ljava/io/IOException;], range=[Block #GF, Block #GE]
handler=Block #GJ, types=[Ljava/lang/IllegalAccessException;], range=[Block #GI, Block #GH]
handler=Block #GM, types=[Ljava/lang/RuntimeException;], range=[Block #GL, Block #GK]
handler=Block #GP, types=[Ljava/io/IOException;], range=[Block #GO, Block #GN]
handler=Block #GS, types=[Ljava/lang/RuntimeException;], range=[Block #GR, Block #GQ]
handler=Block #GV, types=[Ljava/lang/RuntimeException;], range=[Block #GU, Block #GT]
handler=Block #GY, types=[Ljava/lang/RuntimeException;], range=[Block #GX, Block #GW]
handler=Block #HB, types=[Ljava/lang/RuntimeException;], range=[Block #HA, Block #GZ]
handler=Block #HE, types=[Ljava/lang/IllegalAccessException;], range=[Block #HD, Block #HC]
handler=Block #HH, types=[Ljava/lang/IllegalAccessException;], range=[Block #HG, Block #HF]
handler=Block #HK, types=[Ljava/lang/IllegalAccessException;], range=[Block #HJ, Block #HI]
handler=Block #HN, types=[Ljava/io/IOException;], range=[Block #HM, Block #HL]
handler=Block #HQ, types=[Ljava/io/IOException;], range=[Block #HP, Block #HO]
handler=Block #HT, types=[Ljava/lang/IllegalAccessException;], range=[Block #HS, Block #HR]
handler=Block #HW, types=[Ljava/io/IOException;], range=[Block #HV, Block #HU]
handler=Block #HZ, types=[Ljava/io/IOException;], range=[Block #HY, Block #HX]
handler=Block #IC, types=[Ljava/io/IOException;], range=[Block #IB, Block #IA]
handler=Block #IF, types=[Ljava/lang/IllegalAccessException;], range=[Block #IE, Block #ID]
handler=Block #II, types=[Ljava/lang/IllegalAccessException;], range=[Block #IH, Block #IG]
handler=Block #IL, types=[Ljava/lang/IllegalAccessException;], range=[Block #IK, Block #IJ]
handler=Block #IO, types=[Ljava/io/IOException;], range=[Block #IN, Block #IM]
handler=Block #IR, types=[Ljava/lang/IllegalAccessException;], range=[Block #IQ, Block #IP]
handler=Block #IU, types=[Ljava/io/IOException;], range=[Block #IT, Block #IS]
handler=Block #IX, types=[Ljava/lang/IllegalAccessException;], range=[Block #IW, Block #IV]
handler=Block #JA, types=[Ljava/io/IOException;], range=[Block #IZ, Block #IY]
===#Block A(size=4, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
   2. synth(lvar2 = lvar2);
   3. synth(lvar3 = lvar3);
      -> Immediate #A -> #B
===#Block B(size=0, flags=0)===
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=2, flags=0)===
   0. lvar5 = lvar3;
   1. if (lvar5 == {513769956 ^ lvar105})
      goto JI
      -> ConditionalJump[IF_ICMPEQ] #C -> #JI
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=6, flags=0)===
   0. lvar10 = lvar1;
   1. lvar9 = lvar10;
   2. lvar11 = lvar9;
   3. lvar12 = lvar11.hashCode();
   4. svar107 = {lvar12 ^ lvar105};
   5. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(svar107)) {
      case 40729537:
      	 goto	#Z
      case 40729539:
      	 goto	#AC
      case 40729545:
      	 goto	#N
      case 40729547:
      	 goto	#T
      case 40729555:
      	 goto	#W
      case 40729557:
      	 goto	#Q
      case 40729559:
      	 goto	#H
      case 40729565:
      	 goto	#E
      case 40729567:
      	 goto	#K
      default:
      	 goto	#AE
   }
      -> Switch[40729539] #D -> #AC
      -> Switch[40729537] #D -> #Z
      -> DefaultSwitch #D -> #AE
      -> Switch[40729555] #D -> #W
      -> Switch[40729547] #D -> #T
      -> Switch[40729557] #D -> #Q
      -> Switch[40729545] #D -> #N
      -> Switch[40729567] #D -> #K
      -> Switch[40729559] #D -> #H
      -> Switch[40729565] #D -> #E
      <- Immediate #C -> #D
===#Block E(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar13 = lvar9;
   2. lvar6 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.riibyjjmwjkrcqe(), lvar105);
   3. lvar14 = lvar13.equals(lvar6);
   4. if (lvar14 != {580546123 ^ lvar105})
      goto JT
      -> Immediate #E -> #G
      -> ConditionalJump[IF_ICMPNE] #E -> #JT
      <- Switch[40729565] #D -> #E
===#Block JT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -906151901)
      goto F
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JT -> #F
      -> UnconditionalJump[GOTO] #JT -> #JC
      <- ConditionalJump[IF_ICMPNE] #E -> #JT
===#Block F(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.LAPIS_BLOCK;
   2. goto ED
      -> UnconditionalJump[GOTO] #F -> #ED
      <- ConditionalJump[IF_ICMPEQ] #JT -> #F
===#Block ED(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 4504997)
      goto EC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #ED -> #EC
      -> TryCatch range: [ED...EC] -> EE ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #F -> #ED
===#Block EC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [ED...EC] -> EE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #ED -> #EC
===#Block EE(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EE -> #CN
      <- TryCatch range: [ED...EC] -> EE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [ED...EC] -> EE ([Ljava/lang/IllegalAccessException;])
===#Block G(size=1, flags=0)===
   0. goto CW
      -> UnconditionalJump[GOTO] #G -> #CW
      <- Immediate #E -> #G
===#Block CW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 231804070)
      goto CV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CW -> #CV
      -> TryCatch range: [CW...CV] -> CX ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #G -> #CW
===#Block CV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CW...CV] -> CX ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CW -> #CV
===#Block CX(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #CX -> #AE
      <- TryCatch range: [CW...CV] -> CX ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CW...CV] -> CX ([Ljava/lang/IllegalAccessException;])
===#Block H(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar9;
   2. lvar76 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.cpzmfuuwdwwirvj(), lvar105);
   3. lvar17 = lvar16.equals(lvar76);
   4. if (lvar17 != {580142758 ^ lvar105})
      goto JN
      -> Immediate #H -> #J
      -> ConditionalJump[IF_ICMPNE] #H -> #JN
      <- Switch[40729559] #D -> #H
===#Block JN(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -2101074352)
      goto I
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JN -> #I
      -> UnconditionalJump[GOTO] #JN -> #JC
      <- ConditionalJump[IF_ICMPNE] #H -> #JN
===#Block I(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.QUARTZ_BLOCK;
   2. goto FZ
      -> UnconditionalJump[GOTO] #I -> #FZ
      <- ConditionalJump[IF_ICMPEQ] #JN -> #I
===#Block FZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 230255253)
      goto FY
   1. throw nullconst;
      -> TryCatch range: [FZ...FY] -> GA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FZ -> #FY
      <- UnconditionalJump[GOTO] #I -> #FZ
===#Block FY(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FZ...FY] -> GA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FZ -> #FY
===#Block GA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GA -> #CN
      <- TryCatch range: [FZ...FY] -> GA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FZ...FY] -> GA ([Ljava/lang/RuntimeException;])
===#Block J(size=1, flags=0)===
   0. goto DO
      -> UnconditionalJump[GOTO] #J -> #DO
      <- Immediate #H -> #J
===#Block DO(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 233607538)
      goto DN
   1. throw nullconst;
      -> TryCatch range: [DO...DN] -> DP ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DO -> #DN
      <- UnconditionalJump[GOTO] #J -> #DO
===#Block DN(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DO...DN] -> DP ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DO -> #DN
===#Block DP(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #DP -> #AE
      <- TryCatch range: [DO...DN] -> DP ([Ljava/io/IOException;])
      <- TryCatch range: [DO...DN] -> DP ([Ljava/io/IOException;])
===#Block K(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar18 = lvar9;
   2. lvar77 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ijyfdgbmhitezqj(), lvar105);
   3. lvar19 = lvar18.equals(lvar77);
   4. if (lvar19 != {322624000 ^ lvar105})
      goto KC
      -> ConditionalJump[IF_ICMPNE] #K -> #KC
      -> Immediate #K -> #L
      <- Switch[40729567] #D -> #K
===#Block L(size=1, flags=0)===
   0. goto GI
      -> UnconditionalJump[GOTO] #L -> #GI
      <- Immediate #K -> #L
===#Block GI(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 155184986)
      goto GH
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GI -> #GH
      -> TryCatch range: [GI...GH] -> GJ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #L -> #GI
===#Block GH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GI...GH] -> GJ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GI -> #GH
===#Block GJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #GJ -> #AE
      <- TryCatch range: [GI...GH] -> GJ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GI...GH] -> GJ ([Ljava/lang/IllegalAccessException;])
===#Block KC(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -648564830)
      goto M
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KC -> #M
      -> UnconditionalJump[GOTO] #KC -> #JC
      <- ConditionalJump[IF_ICMPNE] #K -> #KC
===#Block M(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GOLD_BLOCK;
   2. goto IZ
      -> UnconditionalJump[GOTO] #M -> #IZ
      <- ConditionalJump[IF_ICMPEQ] #KC -> #M
===#Block IZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 126969144)
      goto IY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IZ -> #IY
      -> TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #M -> #IZ
===#Block IY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IZ -> #IY
===#Block JA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #JA -> #CN
      <- TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
      <- TryCatch range: [IZ...IY] -> JA ([Ljava/io/IOException;])
===#Block N(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar9;
   2. lvar78 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ipsleyxqvziocdb(), lvar105);
   3. lvar21 = lvar20.equals(lvar78);
   4. if (lvar21 != {1123715161 ^ lvar105})
      goto KE
      -> ConditionalJump[IF_ICMPNE] #N -> #KE
      -> Immediate #N -> #P
      <- Switch[40729545] #D -> #N
===#Block P(size=1, flags=0)===
   0. goto EA
      -> UnconditionalJump[GOTO] #P -> #EA
      <- Immediate #N -> #P
===#Block EA(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 175959139)
      goto DZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EA -> #DZ
      -> TryCatch range: [EA...DZ] -> EB ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #P -> #EA
===#Block DZ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EA...DZ] -> EB ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EA -> #DZ
===#Block EB(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #EB -> #AE
      <- TryCatch range: [EA...DZ] -> EB ([Ljava/io/IOException;])
      <- TryCatch range: [EA...DZ] -> EB ([Ljava/io/IOException;])
===#Block KE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -259464853)
      goto O
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KE -> #O
      -> UnconditionalJump[GOTO] #KE -> #JC
      <- ConditionalJump[IF_ICMPNE] #N -> #KE
===#Block O(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.DIAMOND_BLOCK;
   2. goto FE
      -> UnconditionalJump[GOTO] #O -> #FE
      <- ConditionalJump[IF_ICMPEQ] #KE -> #O
===#Block FE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 204338098)
      goto FD
   1. throw nullconst;
      -> TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FE -> #FD
      <- UnconditionalJump[GOTO] #O -> #FE
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FE -> #FD
===#Block FF(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FF -> #CN
      <- TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FE...FD] -> FF ([Ljava/lang/RuntimeException;])
===#Block Q(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar9;
   2. lvar79 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.nkjogvxbwjmsalr(), lvar105);
   3. lvar23 = lvar22.equals(lvar79);
   4. if (lvar23 != {2047618457 ^ lvar105})
      goto JV
      -> Immediate #Q -> #S
      -> ConditionalJump[IF_ICMPNE] #Q -> #JV
      <- Switch[40729557] #D -> #Q
===#Block JV(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 2060528864)
      goto R
   1. goto JC
      -> UnconditionalJump[GOTO] #JV -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JV -> #R
      <- ConditionalJump[IF_ICMPNE] #Q -> #JV
===#Block R(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.EMERALD_BLOCK;
   2. goto IN
      -> UnconditionalJump[GOTO] #R -> #IN
      <- ConditionalJump[IF_ICMPEQ] #JV -> #R
===#Block IN(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 214181147)
      goto IM
   1. throw nullconst;
      -> TryCatch range: [IN...IM] -> IO ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #IN -> #IM
      <- UnconditionalJump[GOTO] #R -> #IN
===#Block IM(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IN...IM] -> IO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IN -> #IM
===#Block IO(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IO -> #CN
      <- TryCatch range: [IN...IM] -> IO ([Ljava/io/IOException;])
      <- TryCatch range: [IN...IM] -> IO ([Ljava/io/IOException;])
===#Block S(size=1, flags=0)===
   0. goto DX
      -> UnconditionalJump[GOTO] #S -> #DX
      <- Immediate #Q -> #S
===#Block DX(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 127633729)
      goto DW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DX -> #DW
      -> TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #S -> #DX
===#Block DW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DX -> #DW
===#Block DY(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #DY -> #AE
      <- TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
===#Block T(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar24 = lvar9;
   2. lvar80 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.mukafzievulzqwg(), lvar105);
   3. lvar25 = lvar24.equals(lvar80);
   4. if (lvar25 != {2032347766 ^ lvar105})
      goto JS
      -> ConditionalJump[IF_ICMPNE] #T -> #JS
      -> Immediate #T -> #U
      <- Switch[40729547] #D -> #T
===#Block U(size=1, flags=0)===
   0. goto FK
      -> UnconditionalJump[GOTO] #U -> #FK
      <- Immediate #T -> #U
===#Block FK(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 167067897)
      goto FJ
   1. throw nullconst;
      -> TryCatch range: [FK...FJ] -> FL ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FK -> #FJ
      <- UnconditionalJump[GOTO] #U -> #FK
===#Block FJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FK...FJ] -> FL ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FK -> #FJ
===#Block FL(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #FL -> #AE
      <- TryCatch range: [FK...FJ] -> FL ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FK...FJ] -> FL ([Ljava/lang/RuntimeException;])
===#Block JS(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1581595901)
      goto V
   1. goto JC
      -> UnconditionalJump[GOTO] #JS -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JS -> #V
      <- ConditionalJump[IF_ICMPNE] #T -> #JS
===#Block V(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.OBSIDIAN;
   2. goto CZ
      -> UnconditionalJump[GOTO] #V -> #CZ
      <- ConditionalJump[IF_ICMPEQ] #JS -> #V
===#Block CZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 47016940)
      goto CY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CZ -> #CY
      -> TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #V -> #CZ
===#Block CY(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CZ -> #CY
===#Block DA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DA -> #CN
      <- TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CZ...CY] -> DA ([Ljava/lang/IllegalAccessException;])
===#Block W(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar26 = lvar9;
   2. lvar81 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.otbgujqqdsjgcff(), lvar105);
   3. lvar27 = lvar26.equals(lvar81);
   4. if (lvar27 != {1139136092 ^ lvar105})
      goto JQ
      -> ConditionalJump[IF_ICMPNE] #W -> #JQ
      -> Immediate #W -> #Y
      <- Switch[40729555] #D -> #W
===#Block Y(size=1, flags=0)===
   0. goto EV
      -> UnconditionalJump[GOTO] #Y -> #EV
      <- Immediate #W -> #Y
===#Block EV(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 27868522)
      goto EU
   1. throw nullconst;
      -> TryCatch range: [EV...EU] -> EW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EV -> #EU
      <- UnconditionalJump[GOTO] #Y -> #EV
===#Block EU(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EV...EU] -> EW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EV -> #EU
===#Block EW(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #EW -> #AE
      <- TryCatch range: [EV...EU] -> EW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EV...EU] -> EW ([Ljava/lang/RuntimeException;])
===#Block JQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1871210750)
      goto X
   1. goto JC
      -> UnconditionalJump[GOTO] #JQ -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JQ -> #X
      <- ConditionalJump[IF_ICMPNE] #W -> #JQ
===#Block X(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.REDSTONE_BLOCK;
   2. goto GX
      -> UnconditionalJump[GOTO] #X -> #GX
      <- ConditionalJump[IF_ICMPEQ] #JQ -> #X
===#Block GX(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 255318130)
      goto GW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GX -> #GW
      -> TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #X -> #GX
===#Block GW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GX -> #GW
===#Block GY(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GY -> #CN
      <- TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GX...GW] -> GY ([Ljava/lang/RuntimeException;])
===#Block Z(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar28 = lvar9;
   2. lvar82 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.qhbewokxemwjhlw(), lvar105);
   3. lvar29 = lvar28.equals(lvar82);
   4. if (lvar29 != {1760368201 ^ lvar105})
      goto JJ
      -> ConditionalJump[IF_ICMPNE] #Z -> #JJ
      -> Immediate #Z -> #AA
      <- Switch[40729537] #D -> #Z
===#Block AA(size=1, flags=0)===
   0. goto GU
      -> UnconditionalJump[GOTO] #AA -> #GU
      <- Immediate #Z -> #AA
===#Block GU(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 88535310)
      goto GT
   1. throw nullconst;
      -> TryCatch range: [GU...GT] -> GV ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GU -> #GT
      <- UnconditionalJump[GOTO] #AA -> #GU
===#Block GT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GU...GT] -> GV ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GU -> #GT
===#Block GV(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #GV -> #AE
      <- TryCatch range: [GU...GT] -> GV ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GU...GT] -> GV ([Ljava/lang/RuntimeException;])
===#Block JJ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1168539755)
      goto AB
   1. goto JC
      -> UnconditionalJump[GOTO] #JJ -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JJ -> #AB
      <- ConditionalJump[IF_ICMPNE] #Z -> #JJ
===#Block AB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPUR_BLOCK;
   2. goto IB
      -> UnconditionalJump[GOTO] #AB -> #IB
      <- ConditionalJump[IF_ICMPEQ] #JJ -> #AB
===#Block IB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 114876229)
      goto IA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IB -> #IA
      -> TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AB -> #IB
===#Block IA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IB -> #IA
===#Block IC(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IC -> #CN
      <- TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
      <- TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
===#Block AC(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar30 = lvar9;
   2. lvar83 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.kpvwgbqpiuchqre(), lvar105);
   3. lvar31 = lvar30.equals(lvar83);
   4. if (lvar31 != {1571557276 ^ lvar105})
      goto JD
      -> ConditionalJump[IF_ICMPNE] #AC -> #JD
      -> Immediate #AC -> #AD
      <- Switch[40729539] #D -> #AC
===#Block AD(size=1, flags=0)===
   0. goto DC
      -> UnconditionalJump[GOTO] #AD -> #DC
      <- Immediate #AC -> #AD
===#Block DC(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 16042207)
      goto DB
   1. throw nullconst;
      -> TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DC -> #DB
      <- UnconditionalJump[GOTO] #AD -> #DC
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DC -> #DB
===#Block DD(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #DD -> #AE
      <- TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
===#Block AE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.IRON_BLOCK;
   2. goto HY
      -> UnconditionalJump[GOTO] #AE -> #HY
      <- UnconditionalJump[GOTO] #DD -> #AE
      <- UnconditionalJump[GOTO] #DY -> #AE
      <- UnconditionalJump[GOTO] #EB -> #AE
      <- DefaultSwitch #D -> #AE
      <- UnconditionalJump[GOTO] #EW -> #AE
      <- UnconditionalJump[GOTO] #GJ -> #AE
      <- UnconditionalJump[GOTO] #CX -> #AE
      <- UnconditionalJump[GOTO] #GV -> #AE
      <- UnconditionalJump[GOTO] #DP -> #AE
      <- UnconditionalJump[GOTO] #FL -> #AE
===#Block HY(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 64095137)
      goto HX
   1. throw nullconst;
      -> TryCatch range: [HY...HX] -> HZ ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HY -> #HX
      <- UnconditionalJump[GOTO] #AE -> #HY
===#Block HX(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HY...HX] -> HZ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HY -> #HX
===#Block HZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HZ -> #CN
      <- TryCatch range: [HY...HX] -> HZ ([Ljava/io/IOException;])
      <- TryCatch range: [HY...HX] -> HZ ([Ljava/io/IOException;])
===#Block JD(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -167025520)
      goto AF
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JD -> #AF
      -> UnconditionalJump[GOTO] #JD -> #JC
      <- ConditionalJump[IF_ICMPNE] #AC -> #JD
===#Block AF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PRISMARINE;
   2. goto EM
      -> UnconditionalJump[GOTO] #AF -> #EM
      <- ConditionalJump[IF_ICMPEQ] #JD -> #AF
===#Block EM(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 63417640)
      goto EL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EM -> #EL
      -> TryCatch range: [EM...EL] -> EN ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AF -> #EM
===#Block EL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EM...EL] -> EN ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EM -> #EL
===#Block EN(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EN -> #CN
      <- TryCatch range: [EM...EL] -> EN ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EM...EL] -> EN ([Ljava/lang/IllegalAccessException;])
===#Block JI(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1979864142)
      goto AG
   1. goto JC
      -> UnconditionalJump[GOTO] #JI -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JI -> #AG
      <- ConditionalJump[IF_ICMPEQ] #C -> #JI
===#Block AG(size=3, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar32 = lvar2;
   2. if (lvar32 == {1042278404 ^ lvar105})
      goto JZ
      -> ConditionalJump[IF_ICMPEQ] #AG -> #JZ
      -> Immediate #AG -> #AH
      <- ConditionalJump[IF_ICMPEQ] #JI -> #AG
===#Block AH(size=6, flags=0)===
   0. lvar33 = lvar1;
   1. lvar102 = lvar33;
   2. lvar34 = lvar102;
   3. lvar35 = lvar34.hashCode();
   4. svar107 = {lvar35 ^ lvar105};
   5. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(svar107)) {
      case 36440743:
      	 goto	#BG
      case 36440750:
      	 goto	#AI
      case 36440751:
      	 goto	#AL
      case 36440912:
      	 goto	#AR
      case 36440913:
      	 goto	#BA
      case 36440914:
      	 goto	#AU
      case 36440915:
      	 goto	#AX
      case 36440917:
      	 goto	#BD
      default:
      	 goto	#BI
   }
      -> Switch[36440743] #AH -> #BG
      -> Switch[36440917] #AH -> #BD
      -> Switch[36440913] #AH -> #BA
      -> Switch[36440915] #AH -> #AX
      -> Switch[36440914] #AH -> #AU
      -> Switch[36440912] #AH -> #AR
      -> DefaultSwitch #AH -> #BI
      -> Switch[36440751] #AH -> #AL
      -> Switch[36440750] #AH -> #AI
      <- Immediate #AG -> #AH
===#Block AI(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar36 = lvar102;
   2. lvar84 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bnutizvbzkuurtn(), lvar105);
   3. lvar37 = lvar36.equals(lvar84);
   4. if (lvar37 != {1630965012 ^ lvar105})
      goto JG
      -> Immediate #AI -> #AK
      -> ConditionalJump[IF_ICMPNE] #AI -> #JG
      <- Switch[36440750] #AH -> #AI
===#Block JG(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 67611179)
      goto AJ
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JG -> #AJ
      -> UnconditionalJump[GOTO] #JG -> #JC
      <- ConditionalJump[IF_ICMPNE] #AI -> #JG
===#Block AJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLUE_GLAZED_TERRACOTTA;
   2. goto GF
      -> UnconditionalJump[GOTO] #AJ -> #GF
      <- ConditionalJump[IF_ICMPEQ] #JG -> #AJ
===#Block GF(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 21138865)
      goto GE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GF -> #GE
      -> TryCatch range: [GF...GE] -> GG ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AJ -> #GF
===#Block GE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [GF...GE] -> GG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #GF -> #GE
===#Block GG(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GG -> #CN
      <- TryCatch range: [GF...GE] -> GG ([Ljava/io/IOException;])
      <- TryCatch range: [GF...GE] -> GG ([Ljava/io/IOException;])
===#Block AK(size=1, flags=0)===
   0. goto DR
      -> UnconditionalJump[GOTO] #AK -> #DR
      <- Immediate #AI -> #AK
===#Block DR(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 60791143)
      goto DQ
   1. throw nullconst;
      -> TryCatch range: [DR...DQ] -> DS ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #DR -> #DQ
      <- UnconditionalJump[GOTO] #AK -> #DR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DR...DQ] -> DS ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DR -> #DQ
===#Block DS(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #DS -> #BI
      <- TryCatch range: [DR...DQ] -> DS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DR...DQ] -> DS ([Ljava/lang/RuntimeException;])
===#Block AL(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar38 = lvar102;
   2. lvar85 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.spaclzqpxinkeug(), lvar105);
   3. lvar39 = lvar38.equals(lvar85);
   4. if (lvar39 != {1215229759 ^ lvar105})
      goto KD
      -> Immediate #AL -> #AM
      -> ConditionalJump[IF_ICMPNE] #AL -> #KD
      <- Switch[36440751] #AH -> #AL
===#Block KD(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1463715842)
      goto AN
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KD -> #AN
      -> UnconditionalJump[GOTO] #KD -> #JC
      <- ConditionalJump[IF_ICMPNE] #AL -> #KD
===#Block AN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_GLAZED_TERRACOTTA;
   2. goto EP
      -> UnconditionalJump[GOTO] #AN -> #EP
      <- ConditionalJump[IF_ICMPEQ] #KD -> #AN
===#Block EP(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 131300124)
      goto EO
   1. throw nullconst;
      -> TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EP -> #EO
      <- UnconditionalJump[GOTO] #AN -> #EP
===#Block EO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EP -> #EO
===#Block EQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EQ -> #CN
      <- TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
===#Block AM(size=1, flags=0)===
   0. goto IW
      -> UnconditionalJump[GOTO] #AM -> #IW
      <- Immediate #AL -> #AM
===#Block IW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 186267076)
      goto IV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IW -> #IV
      -> TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AM -> #IW
===#Block IV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IW -> #IV
===#Block IX(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #IX -> #BI
      <- TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
===#Block AR(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar42 = lvar102;
   2. lvar87 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.vvwvuangnhppqjm(), lvar105);
   3. lvar43 = lvar42.equals(lvar87);
   4. if (lvar43 != {1585746559 ^ lvar105})
      goto JE
      -> ConditionalJump[IF_ICMPNE] #AR -> #JE
      -> Immediate #AR -> #AT
      <- Switch[36440912] #AH -> #AR
===#Block AT(size=1, flags=0)===
   0. goto DI
      -> UnconditionalJump[GOTO] #AT -> #DI
      <- Immediate #AR -> #AT
===#Block DI(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 156897518)
      goto DH
   1. throw nullconst;
      -> TryCatch range: [DI...DH] -> DJ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DI -> #DH
      <- UnconditionalJump[GOTO] #AT -> #DI
===#Block DH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DI...DH] -> DJ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DI -> #DH
===#Block DJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #DJ -> #BI
      <- TryCatch range: [DI...DH] -> DJ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DI...DH] -> DJ ([Ljava/lang/IllegalAccessException;])
===#Block JE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1024256325)
      goto AS
   1. goto JC
      -> UnconditionalJump[GOTO] #JE -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JE -> #AS
      <- ConditionalJump[IF_ICMPNE] #AR -> #JE
===#Block AS(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_GLAZED_TERRACOTTA;
   2. goto HS
      -> UnconditionalJump[GOTO] #AS -> #HS
      <- ConditionalJump[IF_ICMPEQ] #JE -> #AS
===#Block HS(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 95765420)
      goto HR
   1. throw nullconst;
      -> TryCatch range: [HS...HR] -> HT ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HS -> #HR
      <- UnconditionalJump[GOTO] #AS -> #HS
===#Block HR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HS...HR] -> HT ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HS -> #HR
===#Block HT(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HT -> #CN
      <- TryCatch range: [HS...HR] -> HT ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HS...HR] -> HT ([Ljava/lang/IllegalAccessException;])
===#Block AU(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar44 = lvar102;
   2. lvar88 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.oszvjpwpvgvmngu(), lvar105);
   3. lvar45 = lvar44.equals(lvar88);
   4. if (lvar45 != {796355998 ^ lvar105})
      goto JY
      -> Immediate #AU -> #AW
      -> ConditionalJump[IF_ICMPNE] #AU -> #JY
      <- Switch[36440914] #AH -> #AU
===#Block JY(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 568735338)
      goto AV
   1. goto JC
      -> UnconditionalJump[GOTO] #JY -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JY -> #AV
      <- ConditionalJump[IF_ICMPNE] #AU -> #JY
===#Block AV(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_GLAZED_TERRACOTTA;
   2. goto HV
      -> UnconditionalJump[GOTO] #AV -> #HV
      <- ConditionalJump[IF_ICMPEQ] #JY -> #AV
===#Block HV(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 134828961)
      goto HU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HV -> #HU
      -> TryCatch range: [HV...HU] -> HW ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AV -> #HV
===#Block HU(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HV...HU] -> HW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HV -> #HU
===#Block HW(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HW -> #CN
      <- TryCatch range: [HV...HU] -> HW ([Ljava/io/IOException;])
      <- TryCatch range: [HV...HU] -> HW ([Ljava/io/IOException;])
===#Block AW(size=1, flags=0)===
   0. goto FH
      -> UnconditionalJump[GOTO] #AW -> #FH
      <- Immediate #AU -> #AW
===#Block FH(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 197198308)
      goto FG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FH -> #FG
      -> TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AW -> #FH
===#Block FG(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FH -> #FG
===#Block FI(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #FI -> #BI
      <- TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
      <- TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
===#Block AX(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar46 = lvar102;
   2. lvar89 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.sctozekszthyqxt(), lvar105);
   3. lvar47 = lvar46.equals(lvar89);
   4. if (lvar47 != {173044848 ^ lvar105})
      goto KA
      -> Immediate #AX -> #AZ
      -> ConditionalJump[IF_ICMPNE] #AX -> #KA
      <- Switch[36440915] #AH -> #AX
===#Block KA(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1203995671)
      goto AY
   1. goto JC
      -> UnconditionalJump[GOTO] #KA -> #JC
      -> ConditionalJump[IF_ICMPEQ] #KA -> #AY
      <- ConditionalJump[IF_ICMPNE] #AX -> #KA
===#Block AY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_GLAZED_TERRACOTTA;
   2. goto DU
      -> UnconditionalJump[GOTO] #AY -> #DU
      <- ConditionalJump[IF_ICMPEQ] #KA -> #AY
===#Block DU(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 66050098)
      goto DT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DU -> #DT
      -> TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AY -> #DU
===#Block DT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DU -> #DT
===#Block DV(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DV -> #CN
      <- TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
===#Block AZ(size=1, flags=0)===
   0. goto HA
      -> UnconditionalJump[GOTO] #AZ -> #HA
      <- Immediate #AX -> #AZ
===#Block HA(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 114709456)
      goto GZ
   1. throw nullconst;
      -> TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #HA -> #GZ
      <- UnconditionalJump[GOTO] #AZ -> #HA
===#Block GZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HA -> #GZ
===#Block HB(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #HB -> #BI
      <- TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HA...GZ] -> HB ([Ljava/lang/RuntimeException;])
===#Block BA(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar48 = lvar102;
   2. lvar90 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ypfiglmvzfugrhq(), lvar105);
   3. lvar49 = lvar48.equals(lvar90);
   4. if (lvar49 != {1714174902 ^ lvar105})
      goto JR
      -> Immediate #BA -> #BC
      -> ConditionalJump[IF_ICMPNE] #BA -> #JR
      <- Switch[36440913] #AH -> #BA
===#Block JR(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1822809081)
      goto BB
   1. goto JC
      -> UnconditionalJump[GOTO] #JR -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JR -> #BB
      <- ConditionalJump[IF_ICMPNE] #BA -> #JR
===#Block BB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_GLAZED_TERRACOTTA;
   2. goto IE
      -> UnconditionalJump[GOTO] #BB -> #IE
      <- ConditionalJump[IF_ICMPEQ] #JR -> #BB
===#Block IE(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 212918693)
      goto ID
   1. throw nullconst;
      -> TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IE -> #ID
      <- UnconditionalJump[GOTO] #BB -> #IE
===#Block ID(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IE -> #ID
===#Block IF(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IF -> #CN
      <- TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
===#Block BC(size=1, flags=0)===
   0. goto HJ
      -> UnconditionalJump[GOTO] #BC -> #HJ
      <- Immediate #BA -> #BC
===#Block HJ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 12095536)
      goto HI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HJ -> #HI
      -> TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BC -> #HJ
===#Block HI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HJ -> #HI
===#Block HK(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #HK -> #BI
      <- TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
===#Block BD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar50 = lvar102;
   2. lvar91 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.lchdawfkdlrigzy(), lvar105);
   3. lvar51 = lvar50.equals(lvar91);
   4. if (lvar51 != {163328578 ^ lvar105})
      goto JF
      -> Immediate #BD -> #BF
      -> ConditionalJump[IF_ICMPNE] #BD -> #JF
      <- Switch[36440917] #AH -> #BD
===#Block JF(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 2071834947)
      goto BE
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JF -> #BE
      -> UnconditionalJump[GOTO] #JF -> #JC
      <- ConditionalJump[IF_ICMPNE] #BD -> #JF
===#Block BE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_GLAZED_TERRACOTTA;
   2. goto FN
      -> UnconditionalJump[GOTO] #BE -> #FN
      <- ConditionalJump[IF_ICMPEQ] #JF -> #BE
===#Block FN(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 175542601)
      goto FM
   1. throw nullconst;
      -> TryCatch range: [FN...FM] -> FO ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FN -> #FM
      <- UnconditionalJump[GOTO] #BE -> #FN
===#Block FM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FN...FM] -> FO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FN -> #FM
===#Block FO(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FO -> #CN
      <- TryCatch range: [FN...FM] -> FO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FN...FM] -> FO ([Ljava/lang/RuntimeException;])
===#Block BF(size=1, flags=0)===
   0. goto EY
      -> UnconditionalJump[GOTO] #BF -> #EY
      <- Immediate #BD -> #BF
===#Block EY(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 59135096)
      goto EX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EY -> #EX
      -> TryCatch range: [EY...EX] -> EZ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BF -> #EY
===#Block EX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EY...EX] -> EZ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EY -> #EX
===#Block EZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #EZ -> #BI
      <- TryCatch range: [EY...EX] -> EZ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EY...EX] -> EZ ([Ljava/lang/IllegalAccessException;])
===#Block BG(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar52 = lvar102;
   2. lvar92 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.kkxvwiveqaegeol(), lvar105);
   3. lvar53 = lvar52.equals(lvar92);
   4. if (lvar53 != {667798196 ^ lvar105})
      goto JO
      -> ConditionalJump[IF_ICMPNE] #BG -> #JO
      -> Immediate #BG -> #BH
      <- Switch[36440743] #AH -> #BG
===#Block BH(size=1, flags=0)===
   0. goto IT
      -> UnconditionalJump[GOTO] #BH -> #IT
      <- Immediate #BG -> #BH
===#Block IT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 95614154)
      goto IS
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IT -> #IS
      -> TryCatch range: [IT...IS] -> IU ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BH -> #IT
===#Block IS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IT...IS] -> IU ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IT -> #IS
===#Block IU(size=2, flags=0)===
   0. _consume(catch());
   1. goto BI
      -> UnconditionalJump[GOTO] #IU -> #BI
      <- TryCatch range: [IT...IS] -> IU ([Ljava/io/IOException;])
      <- TryCatch range: [IT...IS] -> IU ([Ljava/io/IOException;])
===#Block BI(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_GLAZED_TERRACOTTA;
   2. goto IQ
      -> UnconditionalJump[GOTO] #BI -> #IQ
      <- UnconditionalJump[GOTO] #IU -> #BI
      <- UnconditionalJump[GOTO] #FI -> #BI
      <- UnconditionalJump[GOTO] #HK -> #BI
      <- UnconditionalJump[GOTO] #IX -> #BI
      <- UnconditionalJump[GOTO] #ET -> #BI
      <- UnconditionalJump[GOTO] #DS -> #BI
      <- DefaultSwitch #AH -> #BI
      <- UnconditionalJump[GOTO] #EZ -> #BI
      <- UnconditionalJump[GOTO] #DJ -> #BI
      <- UnconditionalJump[GOTO] #HB -> #BI
===#Block IQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 138613427)
      goto IP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IQ -> #IP
      -> TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BI -> #IQ
===#Block IP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IQ -> #IP
===#Block IR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IR -> #CN
      <- TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IQ...IP] -> IR ([Ljava/lang/IllegalAccessException;])
===#Block JO(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1475065426)
      goto BJ
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JO -> #BJ
      -> UnconditionalJump[GOTO] #JO -> #JC
      <- ConditionalJump[IF_ICMPNE] #BG -> #JO
===#Block BJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_GLAZED_TERRACOTTA;
   2. goto GO
      -> UnconditionalJump[GOTO] #BJ -> #GO
      <- ConditionalJump[IF_ICMPEQ] #JO -> #BJ
===#Block GO(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 121602531)
      goto GN
   1. throw nullconst;
      -> TryCatch range: [GO...GN] -> GP ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #GO -> #GN
      <- UnconditionalJump[GOTO] #BJ -> #GO
===#Block GN(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [GO...GN] -> GP ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #GO -> #GN
===#Block GP(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GP -> #CN
      <- TryCatch range: [GO...GN] -> GP ([Ljava/io/IOException;])
      <- TryCatch range: [GO...GN] -> GP ([Ljava/io/IOException;])
===#Block JZ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 937027521)
      goto BK
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JZ -> #BK
      -> UnconditionalJump[GOTO] #JZ -> #JC
      <- ConditionalJump[IF_ICMPEQ] #AG -> #JZ
===#Block BK(size=7, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar54 = lvar1;
   2. lvar103 = lvar54;
   3. lvar55 = lvar103;
   4. lvar56 = lvar55.hashCode();
   5. svar107 = {lvar56 ^ lvar105};
   6. switch (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(svar107)) {
      case 13855524:
      	 goto	#CA
      case 13855547:
      	 goto	#BL
      case 13855681:
      	 goto	#CD
      case 13855683:
      	 goto	#BR
      case 13855687:
      	 goto	#BX
      case 13855689:
      	 goto	#CJ
      case 13855691:
      	 goto	#BU
      case 13855693:
      	 goto	#CG
      case 13855695:
      	 goto	#BO
      default:
      	 goto	#CM
   }
      -> Switch[13855689] #BK -> #CJ
      -> Switch[13855693] #BK -> #CG
      -> Switch[13855681] #BK -> #CD
      -> Switch[13855524] #BK -> #CA
      -> Switch[13855687] #BK -> #BX
      -> DefaultSwitch #BK -> #CM
      -> Switch[13855691] #BK -> #BU
      -> Switch[13855683] #BK -> #BR
      -> Switch[13855695] #BK -> #BO
      -> Switch[13855547] #BK -> #BL
      <- ConditionalJump[IF_ICMPEQ] #JZ -> #BK
===#Block BL(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar57 = lvar103;
   2. lvar93 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.mblioqoctzcrvpb(), lvar105);
   3. lvar58 = lvar57.equals(lvar93);
   4. if (lvar58 != {1263420077 ^ lvar105})
      goto JK
      -> Immediate #BL -> #BN
      -> ConditionalJump[IF_ICMPNE] #BL -> #JK
      <- Switch[13855547] #BK -> #BL
===#Block JK(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1627696575)
      goto BM
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JK -> #BM
      -> UnconditionalJump[GOTO] #JK -> #JC
      <- ConditionalJump[IF_ICMPNE] #BL -> #JK
===#Block BM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_CONCRETE_POWDER;
   2. goto EG
      -> UnconditionalJump[GOTO] #BM -> #EG
      <- ConditionalJump[IF_ICMPEQ] #JK -> #BM
===#Block EG(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 114117268)
      goto EF
   1. throw nullconst;
      -> TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EG -> #EF
      <- UnconditionalJump[GOTO] #BM -> #EG
===#Block EF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EG -> #EF
===#Block EH(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EH -> #CN
      <- TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EG...EF] -> EH ([Ljava/lang/RuntimeException;])
===#Block BN(size=1, flags=0)===
   0. goto EJ
      -> UnconditionalJump[GOTO] #BN -> #EJ
      <- Immediate #BL -> #BN
===#Block EJ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 152000843)
      goto EI
   1. throw nullconst;
      -> TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EJ -> #EI
      <- UnconditionalJump[GOTO] #BN -> #EJ
===#Block EI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EJ -> #EI
===#Block EK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #EK -> #CM
      <- TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EJ...EI] -> EK ([Ljava/lang/RuntimeException;])
===#Block BO(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar59 = lvar103;
   2. lvar94 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.meyungshfaklqzh(), lvar105);
   3. lvar60 = lvar59.equals(lvar94);
   4. if (lvar60 != {2049404324 ^ lvar105})
      goto JX
      -> Immediate #BO -> #BP
      -> ConditionalJump[IF_ICMPNE] #BO -> #JX
      <- Switch[13855695] #BK -> #BO
===#Block JX(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 981566339)
      goto BQ
   1. goto JC
      -> UnconditionalJump[GOTO] #JX -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JX -> #BQ
      <- ConditionalJump[IF_ICMPNE] #BO -> #JX
===#Block BQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_CONCRETE_POWDER;
   2. goto FT
      -> UnconditionalJump[GOTO] #BQ -> #FT
      <- ConditionalJump[IF_ICMPEQ] #JX -> #BQ
===#Block FT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 107117943)
      goto FS
   1. throw nullconst;
      -> TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #FT -> #FS
      <- UnconditionalJump[GOTO] #BQ -> #FT
===#Block FS(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FT -> #FS
===#Block FU(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FU -> #CN
      <- TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
===#Block BP(size=1, flags=0)===
   0. goto GL
      -> UnconditionalJump[GOTO] #BP -> #GL
      <- Immediate #BO -> #BP
===#Block GL(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 104135798)
      goto GK
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GL -> #GK
      -> TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BP -> #GL
===#Block GK(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GL -> #GK
===#Block GM(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #GM -> #CM
      <- TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
===#Block BR(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar61 = lvar103;
   2. lvar95 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.ywtlzczccfwtbsy(), lvar105);
   3. lvar62 = lvar61.equals(lvar95);
   4. if (lvar62 != {603881595 ^ lvar105})
      goto JM
      -> ConditionalJump[IF_ICMPNE] #BR -> #JM
      -> Immediate #BR -> #BS
      <- Switch[13855683] #BK -> #BR
===#Block BS(size=1, flags=0)===
   0. goto GR
      -> UnconditionalJump[GOTO] #BS -> #GR
      <- Immediate #BR -> #BS
===#Block GR(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 178016286)
      goto GQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GR -> #GQ
      -> TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BS -> #GR
===#Block GQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GR -> #GQ
===#Block GS(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #GS -> #CM
      <- TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
===#Block JM(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -212645638)
      goto BT
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JM -> #BT
      -> UnconditionalJump[GOTO] #JM -> #JC
      <- ConditionalJump[IF_ICMPNE] #BR -> #JM
===#Block BT(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_CONCRETE_POWDER;
   2. goto HD
      -> UnconditionalJump[GOTO] #BT -> #HD
      <- ConditionalJump[IF_ICMPEQ] #JM -> #BT
===#Block HD(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 135412927)
      goto HC
   1. throw nullconst;
      -> TryCatch range: [HD...HC] -> HE ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HD -> #HC
      <- UnconditionalJump[GOTO] #BT -> #HD
===#Block HC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HD...HC] -> HE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HD -> #HC
===#Block HE(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HE -> #CN
      <- TryCatch range: [HD...HC] -> HE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HD...HC] -> HE ([Ljava/lang/IllegalAccessException;])
===#Block BU(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar63 = lvar103;
   2. lvar96 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bpphycvdavmhjeo(), lvar105);
   3. lvar64 = lvar63.equals(lvar96);
   4. if (lvar64 != {1807562275 ^ lvar105})
      goto JL
      -> Immediate #BU -> #BV
      -> ConditionalJump[IF_ICMPNE] #BU -> #JL
      <- Switch[13855691] #BK -> #BU
===#Block JL(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1982270933)
      goto BW
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JL -> #BW
      -> UnconditionalJump[GOTO] #JL -> #JC
      <- ConditionalJump[IF_ICMPNE] #BU -> #JL
===#Block BW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_CONCRETE_POWDER;
   2. goto DL
      -> UnconditionalJump[GOTO] #BW -> #DL
      <- ConditionalJump[IF_ICMPEQ] #JL -> #BW
===#Block DL(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 137856370)
      goto DK
   1. throw nullconst;
      -> TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DL -> #DK
      <- UnconditionalJump[GOTO] #BW -> #DL
===#Block DK(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DL -> #DK
===#Block DM(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DM -> #CN
      <- TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
      <- TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
===#Block BV(size=1, flags=0)===
   0. goto HG
      -> UnconditionalJump[GOTO] #BV -> #HG
      <- Immediate #BU -> #BV
===#Block HG(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 252561569)
      goto HF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HG -> #HF
      -> TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BV -> #HG
===#Block HF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HG -> #HF
===#Block HH(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #HH -> #CM
      <- TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
===#Block BX(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar65 = lvar103;
   2. lvar97 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bhxrstgudtyznzn(), lvar105);
   3. lvar66 = lvar65.equals(lvar97);
   4. if (lvar66 != {1300852871 ^ lvar105})
      goto JB
      -> Immediate #BX -> #BZ
      -> ConditionalJump[IF_ICMPNE] #BX -> #JB
      <- Switch[13855687] #BK -> #BX
===#Block JB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1828178673)
      goto BY
   1. goto JC
      -> UnconditionalJump[GOTO] #JB -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JB -> #BY
      <- ConditionalJump[IF_ICMPNE] #BX -> #JB
===#Block BY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_CONCRETE_POWDER;
   2. goto CT
      -> UnconditionalJump[GOTO] #BY -> #CT
      <- ConditionalJump[IF_ICMPEQ] #JB -> #BY
===#Block CT(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 166599615)
      goto CS
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CT -> #CS
      -> TryCatch range: [CT...CS] -> CU ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BY -> #CT
===#Block CS(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CT...CS] -> CU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CT -> #CS
===#Block CU(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #CU -> #CN
      <- TryCatch range: [CT...CS] -> CU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CT...CS] -> CU ([Ljava/lang/RuntimeException;])
===#Block BZ(size=1, flags=0)===
   0. goto IK
      -> UnconditionalJump[GOTO] #BZ -> #IK
      <- Immediate #BX -> #BZ
===#Block IK(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 252919562)
      goto IJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IK -> #IJ
      -> TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BZ -> #IK
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IK -> #IJ
===#Block IL(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #IL -> #CM
      <- TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
===#Block CA(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar67 = lvar103;
   2. lvar98 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.bnkreeconiugdhg(), lvar105);
   3. lvar68 = lvar67.equals(lvar98);
   4. if (lvar68 != {946070035 ^ lvar105})
      goto JW
      -> ConditionalJump[IF_ICMPNE] #CA -> #JW
      -> Immediate #CA -> #CB
      <- Switch[13855524] #BK -> #CA
===#Block CB(size=1, flags=0)===
   0. goto HP
      -> UnconditionalJump[GOTO] #CB -> #HP
      <- Immediate #CA -> #CB
===#Block HP(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 56673413)
      goto HO
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HP -> #HO
      -> TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #CB -> #HP
===#Block HO(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HP -> #HO
===#Block HQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #HQ -> #CM
      <- TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
      <- TryCatch range: [HP...HO] -> HQ ([Ljava/io/IOException;])
===#Block JW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -684915167)
      goto CC
   1. goto JC
      -> UnconditionalJump[GOTO] #JW -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JW -> #CC
      <- ConditionalJump[IF_ICMPNE] #CA -> #JW
===#Block CC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.MAGENTA_CONCRETE_POWDER;
   2. goto CQ
      -> UnconditionalJump[GOTO] #CC -> #CQ
      <- ConditionalJump[IF_ICMPEQ] #JW -> #CC
===#Block CQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 113116441)
      goto CP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CQ -> #CP
      -> TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #CC -> #CQ
===#Block CP(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CQ -> #CP
===#Block CR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #CR -> #CN
      <- TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
      <- TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
===#Block CD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar69 = lvar103;
   2. lvar99 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.jocevainbklktid(), lvar105);
   3. lvar70 = lvar69.equals(lvar99);
   4. if (lvar70 != {400957447 ^ lvar105})
      goto JU
      -> ConditionalJump[IF_ICMPNE] #CD -> #JU
      -> Immediate #CD -> #CE
      <- Switch[13855681] #BK -> #CD
===#Block CE(size=1, flags=0)===
   0. goto FQ
      -> UnconditionalJump[GOTO] #CE -> #FQ
      <- Immediate #CD -> #CE
===#Block FQ(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 97412612)
      goto FP
   1. throw nullconst;
      -> TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FQ -> #FP
      <- UnconditionalJump[GOTO] #CE -> #FQ
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FQ -> #FP
===#Block FR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #FR -> #CM
      <- TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
===#Block JU(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -1236193405)
      goto CF
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JU -> #CF
      -> UnconditionalJump[GOTO] #JU -> #JC
      <- ConditionalJump[IF_ICMPNE] #CD -> #JU
===#Block CF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_CONCRETE_POWDER;
   2. goto FB
      -> UnconditionalJump[GOTO] #CF -> #FB
      <- ConditionalJump[IF_ICMPEQ] #JU -> #CF
===#Block FB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 34343702)
      goto FA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FB -> #FA
      -> TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #CF -> #FB
===#Block FA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FB -> #FA
===#Block FC(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FC -> #CN
      <- TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
      <- TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
===#Block CG(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar71 = lvar103;
   2. lvar100 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.fmfhzcdzyfbvjmu(), lvar105);
   3. lvar72 = lvar71.equals(lvar100);
   4. if (lvar72 != {83177030 ^ lvar105})
      goto JH
      -> ConditionalJump[IF_ICMPNE] #CG -> #JH
      -> Immediate #CG -> #CI
      <- Switch[13855693] #BK -> #CG
===#Block CI(size=1, flags=0)===
   0. goto FW
      -> UnconditionalJump[GOTO] #CI -> #FW
      <- Immediate #CG -> #CI
===#Block FW(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 86389476)
      goto FV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FW -> #FV
      -> TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CI -> #FW
===#Block FV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FW -> #FV
===#Block FX(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #FX -> #CM
      <- TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
===#Block JH(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == 1358656162)
      goto CH
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JH -> #CH
      -> UnconditionalJump[GOTO] #JH -> #JC
      <- ConditionalJump[IF_ICMPNE] #CG -> #JH
===#Block CH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_CONCRETE_POWDER;
   2. goto IH
      -> UnconditionalJump[GOTO] #CH -> #IH
      <- ConditionalJump[IF_ICMPEQ] #JH -> #CH
===#Block IH(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 98080135)
      goto IG
   1. throw nullconst;
      -> TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IH -> #IG
      <- UnconditionalJump[GOTO] #CH -> #IH
===#Block IG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IH -> #IG
===#Block II(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #II -> #CN
      <- TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
===#Block CJ(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar73 = lvar103;
   2. lvar101 = cn.acebrand.acedex.gui.PokeBallItemCreator.epofosqemq(cn.acebrand.acedex.gui.PokeBallItemCreator.fvwfnwmwdldmmri(), lvar105);
   3. lvar74 = lvar73.equals(lvar101);
   4. if (lvar74 != {1072861611 ^ lvar105})
      goto KB
      -> ConditionalJump[IF_ICMPNE] #CJ -> #KB
      -> Immediate #CJ -> #CL
      <- Switch[13855689] #BK -> #CJ
===#Block CL(size=1, flags=0)===
   0. goto HM
      -> UnconditionalJump[GOTO] #CL -> #HM
      <- Immediate #CJ -> #CL
===#Block HM(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 121482592)
      goto HL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HM -> #HL
      -> TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #CL -> #HM
===#Block HL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HM -> #HL
===#Block HN(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #HN -> #CM
      <- TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
===#Block CM(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_CONCRETE_POWDER;
      -> Immediate #CM -> #CN
      <- UnconditionalJump[GOTO] #HQ -> #CM
      <- UnconditionalJump[GOTO] #GS -> #CM
      <- UnconditionalJump[GOTO] #GM -> #CM
      <- UnconditionalJump[GOTO] #HH -> #CM
      <- DefaultSwitch #BK -> #CM
      <- UnconditionalJump[GOTO] #IL -> #CM
      <- UnconditionalJump[GOTO] #EK -> #CM
      <- UnconditionalJump[GOTO] #FX -> #CM
      <- UnconditionalJump[GOTO] #FR -> #CM
      <- UnconditionalJump[GOTO] #HN -> #CM
===#Block KB(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.ytkbvurtcvtvoofm(lvar105) == -594194575)
      goto CK
   1. goto JC
      -> UnconditionalJump[GOTO] #KB -> #JC
      -> ConditionalJump[IF_ICMPEQ] #KB -> #CK
      <- ConditionalJump[IF_ICMPNE] #CJ -> #KB
===#Block CK(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLUE_CONCRETE_POWDER;
   2. goto GC
      -> UnconditionalJump[GOTO] #CK -> #GC
      <- ConditionalJump[IF_ICMPEQ] #KB -> #CK
===#Block GC(size=2, flags=0)===
   0. if (okuntvmualxvdczp.jozqmqpnctbsnlkr.rvwgxmtzvjjgjyah(lvar105) == 1077628)
      goto GB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GC -> #GB
      -> TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CK -> #GC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GC -> #GB
===#Block GD(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GD -> #CN
      <- TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
===#Block CN(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[1] [org/bukkit/Material]
   1. lvar8 = lvar75;
      -> Immediate #CN -> #CO
      <- UnconditionalJump[GOTO] #DV -> #CN
      <- UnconditionalJump[GOTO] #EH -> #CN
      <- UnconditionalJump[GOTO] #FO -> #CN
      <- UnconditionalJump[GOTO] #JA -> #CN
      <- UnconditionalJump[GOTO] #DA -> #CN
      <- UnconditionalJump[GOTO] #IO -> #CN
      <- UnconditionalJump[GOTO] #HE -> #CN
      <- UnconditionalJump[GOTO] #FF -> #CN
      <- UnconditionalJump[GOTO] #EE -> #CN
      <- UnconditionalJump[GOTO] #GA -> #CN
      <- UnconditionalJump[GOTO] #IR -> #CN
      <- UnconditionalJump[GOTO] #HT -> #CN
      <- UnconditionalJump[GOTO] #GG -> #CN
      <- UnconditionalJump[GOTO] #DM -> #CN
      <- UnconditionalJump[GOTO] #EN -> #CN
      <- UnconditionalJump[GOTO] #II -> #CN
      <- UnconditionalJump[GOTO] #HZ -> #CN
      <- UnconditionalJump[GOTO] #GY -> #CN
      <- UnconditionalJump[GOTO] #FU -> #CN
      <- UnconditionalJump[GOTO] #GP -> #CN
      <- UnconditionalJump[GOTO] #EQ -> #CN
      <- UnconditionalJump[GOTO] #CU -> #CN
      <- UnconditionalJump[GOTO] #GD -> #CN
      <- UnconditionalJump[GOTO] #DG -> #CN
      <- UnconditionalJump[GOTO] #HW -> #CN
      <- Immediate #CM -> #CN
      <- UnconditionalJump[GOTO] #IF -> #CN
      <- UnconditionalJump[GOTO] #FC -> #CN
      <- UnconditionalJump[GOTO] #CR -> #CN
      <- UnconditionalJump[GOTO] #IC -> #CN
===#Block CO(size=4, flags=0)===
   0. lvar15 = new org.bukkit.inventory.ItemStack;
   1. lvar7 = lvar8;
   2. _consume(lvar15.<init>(lvar7));
   3. return lvar15;
      <- Immediate #CN -> #CO
===#Block JC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      <- UnconditionalJump[GOTO] #JR -> #JC
      <- UnconditionalJump[GOTO] #JX -> #JC
      <- UnconditionalJump[GOTO] #JJ -> #JC
      <- UnconditionalJump[GOTO] #KB -> #JC
      <- UnconditionalJump[GOTO] #KD -> #JC
      <- UnconditionalJump[GOTO] #JZ -> #JC
      <- UnconditionalJump[GOTO] #JF -> #JC
      <- UnconditionalJump[GOTO] #JL -> #JC
      <- UnconditionalJump[GOTO] #JT -> #JC
      <- UnconditionalJump[GOTO] #JW -> #JC
      <- UnconditionalJump[GOTO] #KA -> #JC
      <- UnconditionalJump[GOTO] #JI -> #JC
      <- UnconditionalJump[GOTO] #JU -> #JC
      <- UnconditionalJump[GOTO] #KE -> #JC
      <- UnconditionalJump[GOTO] #JG -> #JC
      <- UnconditionalJump[GOTO] #JM -> #JC
      <- UnconditionalJump[GOTO] #JS -> #JC
      <- UnconditionalJump[GOTO] #JE -> #JC
      <- UnconditionalJump[GOTO] #JB -> #JC
      <- UnconditionalJump[GOTO] #JD -> #JC
      <- UnconditionalJump[GOTO] #JK -> #JC
      <- UnconditionalJump[GOTO] #JO -> #JC
      <- UnconditionalJump[GOTO] #JH -> #JC
      <- UnconditionalJump[GOTO] #JP -> #JC
      <- UnconditionalJump[GOTO] #JQ -> #JC
      <- UnconditionalJump[GOTO] #JY -> #JC
      <- UnconditionalJump[GOTO] #JV -> #JC
      <- UnconditionalJump[GOTO] #KC -> #JC
      <- UnconditionalJump[GOTO] #JN -> #JC
