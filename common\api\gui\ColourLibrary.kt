/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.api.gui

object ColourLibrary {
    const val WHITE = 0xFFFFFF
    const val BUTTON_HOVER_COLOUR = 0xB5C42F
    const val BUTTON_NORMAL_COLOUR = 0xFFFFFF

    const val SIDE_1_BATTLE_COLOUR = 0x2993F9
    const val SIDE_1_ALLY_BATTLE_COLOUR = 0x34C2AC
    const val SIDE_2_BATTLE_COLOUR = 0xE43838
    const val SIDE_2_ALLY_BATTLE_COLOUR = 0xD55ABA

    const val BATTLE_COMMAND_COLOUR = 0xEBBA15
}