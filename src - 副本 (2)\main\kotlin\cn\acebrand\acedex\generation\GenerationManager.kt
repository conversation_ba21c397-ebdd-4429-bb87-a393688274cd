/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.data.GenerationProgress
import cn.acebrand.acedex.generation.data.*
// import com.cobblemon.mod.common.api.pokemon.PokemonSpecies
// import com.cobblemon.mod.common.pokemon.Species
import org.bukkit.entity.Player
import java.util.*

/**
 * 精灵数据类
 */
data class PokemonData(
    val dex: Int,
    val type: String,
    val category: String,
    val gender: String,
    val habitat: String // 栖息地/刷新地形信息
) {
    companion object {
        /**
         * 创建带有默认栖息地的精灵数据
         */
        fun create(dex: Int, type: String, category: String, gender: String, habitat: String = ""): PokemonData {
            return PokemonData(dex, type, category, gender, habitat)
        }
    }
}

/**
 * 世代管理器
 * 负责管理精灵的世代分类和相关数据
 */
class GenerationManager(private val plugin: AceDex) {
    
    // 世代数据存储
    private val generations = mutableMapOf<String, Generation>()
    private val pokemonToGeneration = mutableMapOf<String, String>()
    
    /**
     * 初始化世代管理器
     */
    fun initialize() {
        plugin.logger.info("正在初始化世代管理器...")
        
        // 初始化世代数据
        initializeGenerations()
        
        // 加载精灵数据
        loadPokemonData()
        
        plugin.logger.info("世代管理器初始化完成，共加载 ${generations.size} 个世代")
    }
    
    /**
     * 重新加载世代数据
     */
    fun reload() {
        generations.clear()
        pokemonToGeneration.clear()
        initialize()
    }
    
    /**
     * 初始化世代数据
     */
    private fun initializeGenerations() {
        // 第一世代 (关都地区)
        generations["gen1"] = Generation(
            id = "gen1",
            name = "第一世代",
            displayName = "§c第一世代 §7(关都地区)",
            description = "最初的151只精灵",
            region = "关都",
            pokemonRange = 1..151,
            iconMaterial = "cobblemon:poke_ball", // 使用Cobblemon的精灵球材质ID
            color = "§c"
        )
        
        // 第二世代 (城都地区)
        generations["gen2"] = Generation(
            id = "gen2",
            name = "第二世代",
            displayName = "§6第二世代 §7(城都地区)",
            description = "金银版本的精灵",
            region = "城都",
            pokemonRange = 152..251,
            iconMaterial = "cobblemon:great_ball",
            color = "§6"
        )
        
        // 第三世代 (丰缘地区)
        generations["gen3"] = Generation(
            id = "gen3",
            name = "第三世代",
            displayName = "§e第三世代 §7(丰缘地区)",
            description = "红宝石蓝宝石版本的精灵",
            region = "丰缘",
            pokemonRange = 252..386,
            iconMaterial = "cobblemon:ultra_ball",
            color = "§e"
        )
        
        // 第四世代 (神奥地区)
        generations["gen4"] = Generation(
            id = "gen4",
            name = "第四世代",
            displayName = "§b第四世代 §7(神奥地区)",
            description = "钻石珍珠版本的精灵",
            region = "神奥",
            pokemonRange = 387..493,
            iconMaterial = "cobblemon:master_ball",
            color = "§b"
        )
        
        // 第五世代 (合众地区)
        generations["gen5"] = Generation(
            id = "gen5",
            name = "第五世代",
            displayName = "§d第五世代 §7(合众地区)",
            description = "黑白版本的精灵",
            region = "合众",
            pokemonRange = 494..649,
            iconMaterial = "cobblemon:timer_ball",
            color = "§d"
        )
        
        // 第六世代 (卡洛斯地区)
        generations["gen6"] = Generation(
            id = "gen6",
            name = "第六世代",
            displayName = "§a第六世代 §7(卡洛斯地区)",
            description = "XY版本的精灵",
            region = "卡洛斯",
            pokemonRange = 650..721,
            iconMaterial = "cobblemon:luxury_ball",
            color = "§a"
        )
        
        // 第七世代 (阿罗拉地区)
        generations["gen7"] = Generation(
            id = "gen7",
            name = "第七世代",
            displayName = "§9第七世代 §7(阿罗拉地区)",
            description = "日月版本的精灵",
            region = "阿罗拉",
            pokemonRange = 722..809,
            iconMaterial = "cobblemon:premier_ball",
            color = "§9"
        )
        
        // 第八世代 (伽勒尔地区)
        generations["gen8"] = Generation(
            id = "gen8",
            name = "第八世代",
            displayName = "§5第八世代 §7(伽勒尔地区)",
            description = "剑盾版本的精灵",
            region = "伽勒尔",
            pokemonRange = 810..905,
            iconMaterial = "cobblemon:dusk_ball",
            color = "§5"
        )
        
        // 第九世代 (帕底亚地区)
        generations["gen9"] = Generation(
            id = "gen9",
            name = "第九世代",
            displayName = "§f第九世代 §7(帕底亚地区)",
            description = "朱紫版本的精灵",
            region = "帕底亚",
            pokemonRange = 906..1025,
            iconMaterial = "cobblemon:quick_ball",
            color = "§f"
        )
    }
    
    /**
     * 加载精灵数据
     */
    private fun loadPokemonData() {
        try {
            // TODO: 实现Cobblemon API集成
            // 由于API兼容性问题，暂时使用模拟数据
            loadMockPokemonData()

            plugin.logger.info("精灵数据加载完成，共加载 ${pokemonToGeneration.size} 只精灵")
        } catch (e: Exception) {
            plugin.logger.warning("加载精灵数据时发生错误: ${e.message}")
        }
    }

    /**
     * 加载模拟精灵数据
     */
    private fun loadMockPokemonData() {
        // 第一世代完整精灵数据
        for ((name, data) in Gen1PokemonData.data) {
            pokemonToGeneration[name] = "gen1"
            generations["gen1"]?.addPokemon(name, data.dex)
        }

        // 第二世代完整精灵数据
        for ((name, data) in Gen2PokemonData.data) {
            pokemonToGeneration[name] = "gen2"
            generations["gen2"]?.addPokemon(name, data.dex)
        }

        // 第三世代完整精灵数据
        for ((name, data) in Gen3PokemonData.data) {
            pokemonToGeneration[name] = "gen3"
            generations["gen3"]?.addPokemon(name, data.dex)
        }

        // 第四世代完整精灵数据
        for ((name, data) in Gen4PokemonData.data) {
            pokemonToGeneration[name] = "gen4"
            generations["gen4"]?.addPokemon(name, data.dex)
        }

        // 第五世代完整精灵数据
        for ((name, data) in Gen5PokemonData.data) {
            pokemonToGeneration[name] = "gen5"
            generations["gen5"]?.addPokemon(name, data.dex)
        }

        // 第六世代完整精灵数据
        for ((name, data) in Gen6PokemonData.data) {
            pokemonToGeneration[name] = "gen6"
            generations["gen6"]?.addPokemon(name, data.dex)
        }

        // 第七世代完整精灵数据
        for ((name, data) in Gen7PokemonData.data) {
            pokemonToGeneration[name] = "gen7"
            generations["gen7"]?.addPokemon(name, data.dex)
        }

        // 第八世代完整精灵数据
        for ((name, data) in Gen8PokemonData.data) {
            pokemonToGeneration[name] = "gen8"
            generations["gen8"]?.addPokemon(name, data.dex)
        }

        // 第九世代完整精灵数据
        for ((name, data) in Gen9PokemonData.data) {
            pokemonToGeneration[name] = "gen9"
            generations["gen9"]?.addPokemon(name, data.dex)
        }
    }
    
    /**
     * 根据全国图鉴编号确定世代
     */
    private fun determineGeneration(nationalDex: Int): String? {
        return generations.values.find { nationalDex in it.pokemonRange }?.id
    }
    
    /**
     * 获取所有世代
     */
    fun getAllGenerations(): Collection<Generation> = generations.values
    
    /**
     * 根据ID获取世代
     */
    fun getGeneration(id: String): Generation? = generations[id]
    
    /**
     * 根据精灵名称获取世代
     */
    fun getGenerationByPokemon(pokemonName: String): Generation? {
        val generationId = pokemonToGeneration[pokemonName]
        return if (generationId != null) generations[generationId] else null
    }

    /**
     * 根据精灵名称获取世代ID
     */
    fun getPokemonGeneration(pokemonName: String): String? {
        return pokemonToGeneration[pokemonName.lowercase()]
    }

    /**
     * 获取精灵数据
     */
    fun getPokemonData(pokemonName: String): PokemonData? {
        val name = pokemonName.lowercase()
        // 先从第一代数据中查找
        Gen1PokemonData.data[name]?.let { return it }
        // 再从第二代数据中查找
        Gen2PokemonData.data[name]?.let { return it }
        // 再从第三代数据中查找
        Gen3PokemonData.data[name]?.let { return it }
        // 再从第四代数据中查找
        Gen4PokemonData.data[name]?.let { return it }
        // 再从第五代数据中查找
        Gen5PokemonData.data[name]?.let { return it }
        // 再从第六代数据中查找
        Gen6PokemonData.data[name]?.let { return it }
        // 再从第七代数据中查找
        Gen7PokemonData.data[name]?.let { return it }
        // 再从第八代数据中查找
        Gen8PokemonData.data[name]?.let { return it }
        // 再从第九代数据中查找
        Gen9PokemonData.data[name]?.let { return it }
        return null
    }



    /**
     * 获取玩家在指定世代的收集进度
     */
    fun getPlayerProgress(player: Player, generationId: String): GenerationProgress? {
        val generation = generations[generationId] ?: return null
        
        // 这里需要实现获取玩家精灵收集数据的逻辑
        // 由于需要与Cobblemon API交互，这里先返回模拟数据
        val totalPokemon = generation.getTotalPokemon()
        val caughtPokemon = 0 // TODO: 实现实际的收集数据获取
        
        return GenerationProgress(
            total = totalPokemon,
            caught = caughtPokemon,
            percentage = if (totalPokemon > 0) (caughtPokemon * 100) / totalPokemon else 0
        )
    }
}
