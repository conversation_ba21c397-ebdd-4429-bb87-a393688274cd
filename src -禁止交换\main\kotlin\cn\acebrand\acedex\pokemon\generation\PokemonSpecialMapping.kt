/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 特殊精灵名称映射
 * 包含Let's Go系列和其他特殊精灵
 */
object PokemonSpecialMapping {
    
    /**
     * 获取特殊精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
        // 特殊精灵 (Let's Go)
        "meltan" to "美录坦",
        "melmetal" to "美录梅塔"
    )
}
