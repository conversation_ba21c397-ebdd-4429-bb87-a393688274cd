/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第八代精灵数据 (伽勒尔地区)
 * 包含全国图鉴编号 810-905 的精灵数据
 */
object Gen8PokemonData {
    val data = mapOf(
        // 810-818 (伽勒尔御三家及其进化)
        "grookey" to PokemonData(810, "草", "敲音猴精灵", "male", "丛林, 热带岛屿"),
        "thwackey" to PokemonData(811, "草", "啪咚猴精灵", "male", "丛林, 热带岛屿"),
        "rillaboom" to PokemonData(812, "草", "轰擂金刚猩精灵", "male", "丛林, 热带岛屿"),
        "scorbunny" to PokemonD<PERSON>(813, "火", "炎兔儿精灵", "male", "草原, 苔原"),
        "raboot" to PokemonData(814, "火", "腾蹴小将精灵", "male", "草原, 苔原"),
        "cinderace" to PokemonData(815, "火", "闪焰王牌精灵", "male", "草原, 苔原"),
        "sobble" to PokemonData(816, "水", "泪眼蜥精灵", "male", "沼泽, 丛林, 森林"),
        "drizzile" to PokemonData(817, "水", "变涩蜥精灵", "male", "沼泽, 丛林, 森林"),
        "inteleon" to PokemonData(818, "水", "千面避役精灵", "male", "沼泽, 丛林, 森林"),

        // 819-828
        "skwovet" to PokemonData(819, "一般", "贪心栗鼠精灵", "male", "森林"),
        "greedent" to PokemonData(820, "一般", "藏饱栗鼠精灵", "male", "森林"),
        "rookidee" to PokemonData(821, "飞行", "稚山雀精灵", "male", "丘陵, 阴森森林, 针叶林"),
        "corvisquire" to PokemonData(822, "飞行", "蓝鸦精灵", "male", "丘陵, 阴森森林, 针叶林"),
        "corviknight" to PokemonData(823, "飞行/钢", "钢铠鸦精灵", "male", "丘陵, 阴森森林, 针叶林"),
        "blipbug" to PokemonData(824, "虫", "索侦虫精灵", "male", "森林"),
        "dottler" to PokemonData(825, "虫/超能力", "天罩虫精灵", "male", "森林"),
        "orbeetle" to PokemonData(826, "虫/超能力", "以欧路普精灵", "male", "森林"),
        "nickit" to PokemonData(827, "恶", "偷儿狐精灵", "male", "森林, 针叶林, 平原"),
        "thievul" to PokemonData(828, "恶", "狐大盗精灵", "male", "森林, 针叶林, 平原"),

        // 829-838
        "gossifleur" to PokemonData(829, "草", "幼棉棉精灵", "male", "花草草原"),
        "eldegoss" to PokemonData(830, "草", "白蓬蓬精灵", "male", "花草草原"),
        "wooloo" to PokemonData(831, "一般", "毛辫羊精灵", "male", "山地, 平原"),
        "dubwool" to PokemonData(832, "一般", "毛毛角羊精灵", "male", "山地, 平原"),
        "chewtle" to PokemonData(833, "水", "咬咬龟精灵", "male", "淡水"),
        "drednaw" to PokemonData(834, "水/岩石", "暴噬龟精灵", "male", "淡水"),
        "yamper" to PokemonData(835, "电", "来电汪精灵", "male", "城市, 村庄"),
        "boltund" to PokemonData(836, "电", "逐电犬精灵", "male", "城市, 村庄"),
        "rolycoly" to PokemonData(837, "岩石", "小炭仔精灵", "male", "地下"),
        "carkol" to PokemonData(838, "岩石/火", "大炭车精灵", "male", "地下"),

        // 839-848
        "coalossal" to PokemonData(839, "岩石/火", "巨炭山精灵", "male", "地下"),
        "applin" to PokemonData(840, "草/龙", "啃果虫精灵", "male", "森林"),
        "flapple" to PokemonData(841, "草/龙", "苹裹龙精灵", "male", "森林"),
        "appletun" to PokemonData(842, "草/龙", "丰蜜龙精灵", "male", "森林"),
        "silicobra" to PokemonData(843, "地面", "沙包蛇精灵", "male", "沙漠"),
        "sandaconda" to PokemonData(844, "地面", "沙螺蟒精灵", "male", "沙漠"),
        "cramorant" to PokemonData(845, "飞行/水", "古月鸟精灵", "male", "海岸, 热带岛屿"),
        "arrokuda" to PokemonData(846, "水", "刺梭鱼精灵", "male", "海洋, 沼泽, 海岸"),
        "barraskewda" to PokemonData(847, "水", "戽斗尖梭精灵", "male", "海洋, 沼泽, 海岸"),
        "toxel" to PokemonData(848, "电/毒", "毒电婴精灵", "male", "城市"),

        // 849-858
        "toxtricity" to PokemonData(849, "电/毒", "颤弦蝾螈精灵", "male", "城市"),
        "sizzlipede" to PokemonData(850, "火/虫", "烧火蚣精灵", "male", "恶地, 火山"),
        "centiskorch" to PokemonData(851, "火/虫", "焚焰蚣精灵", "male", "恶地, 火山"),
        "clobbopus" to PokemonData(852, "格斗", "拳击海胆精灵", "male", "海洋"),
        "grapploct" to PokemonData(853, "格斗", "八爪武师精灵", "male", "海洋"),
        "sinistea" to PokemonData(854, "幽灵", "来悲茶精灵", "genderless", "城市"),
        "polteageist" to PokemonData(855, "幽灵", "怖思壶精灵", "genderless", "城市"),
        "hatenna" to PokemonData(856, "超能力", "迷布莉姆精灵", "female", "魔法森林"),
        "hattrem" to PokemonData(857, "超能力", "提布莉姆精灵", "female", "魔法森林"),
        "hatterene" to PokemonData(858, "超能力/妖精", "布莉姆温精灵", "female", "魔法森林"),

        // 859-868
        "impidimp" to PokemonData(859, "恶/妖精", "捣蛋小妖精灵", "male", "魔法森林"),
        "morgrem" to PokemonData(860, "恶/妖精", "诈唬魔精灵", "male", "魔法森林"),
        "grimmsnarl" to PokemonData(861, "恶/妖精", "长毛巨魔精灵", "male", "魔法森林"),
        "obstagoon" to PokemonData(862, "恶/一般", "堵拦熊精灵", "male", "针叶林"),
        "perrserker" to PokemonData(863, "钢", "喵头目精灵", "male", "针叶林, 城市, 村庄"),
        "cursola" to PokemonData(864, "幽灵", "魔灵珊瑚精灵", "male", "海洋"),
        "sirfetchd" to PokemonData(865, "格斗", "葱游兵精灵", "male", "森林"),
        "mrrime" to PokemonData(866, "冰/超能力", "踏冰人偶精灵", "male", "冰冻地区"),
        "runerigus" to PokemonData(867, "地面/幽灵", "死神板精灵", "male", "沙漠"),
        "milcery" to PokemonData(868, "妖精", "小仙奶精灵", "female", "城市"),

        // 869-878
        "alcremie" to PokemonData(869, "妖精", "霜奶仙精灵", "female", "城市"),
        "falinks" to PokemonData(870, "格斗", "列阵兵精灵", "genderless", "恶地"),
        "pincurchin" to PokemonData(871, "电", "啪嚓海胆精灵", "male", "海岸, 海洋"),
        "snom" to PokemonData(872, "冰/虫", "雪吞虫精灵", "male", "冰冻地区"),
        "frosmoth" to PokemonData(873, "冰/虫", "雪绒蛾精灵", "male", "冰冻地区"),
        "stonjourner" to PokemonData(874, "岩石", "巨石丁精灵", "male", "平原"),
        "eiscue" to PokemonData(875, "冰", "冰砌鹅精灵", "male", "冰冻海洋, 苔原"),
        "indeedee" to PokemonData(876, "超能力/一般", "爱管侍精灵", "male", "城市"),
        "morpeko" to PokemonData(877, "电/恶", "莫鲁贝可精灵", "male", "平原"),
        "cufant" to PokemonData(878, "钢", "铜象精灵", "male", "恶地, 丘陵, 热带草原"),

        // 879-888
        "copperajah" to PokemonData(879, "钢", "大王铜象精灵", "male", "恶地, 丘陵, 热带草原"),
        "dracozolt" to PokemonData(880, "电/龙", "雷鸟龙精灵", "genderless", "地下"),
        "arctozolt" to PokemonData(881, "电/冰", "雷鸟海兽精灵", "genderless", "地下"),
        "dracovish" to PokemonData(882, "水/龙", "鳃鱼龙精灵", "genderless", "地下"),
        "arctovish" to PokemonData(883, "水/冰", "鳃鱼海兽精灵", "genderless", "地下"),
        "duraludon" to PokemonData(884, "钢/龙", "铝钢龙精灵", "male", "山地"),
        "dreepy" to PokemonData(885, "龙/幽灵", "多龙梅西亚精灵", "male", "海岸, 海洋"),
        "drakloak" to PokemonData(886, "龙/幽灵", "多龙奇精灵", "male", "海岸, 海洋"),
        "dragapult" to PokemonData(887, "龙/幽灵", "多龙巴鲁托精灵", "male", "海岸, 海洋"),
        "zacian" to PokemonData(888, "妖精", "苍响精灵", "genderless", "传说区域"),

        // 889-898
        "zamazenta" to PokemonData(889, "格斗", "藏玛然特精灵", "genderless", "传说区域"),
        "eternatus" to PokemonData(890, "毒/龙", "无极汰那精灵", "genderless", "传说区域"),
        "kubfu" to PokemonData(891, "格斗", "熊徒弟精灵", "male", "传说区域"),
        "urshifu" to PokemonData(892, "格斗/恶", "武道熊师精灵", "male", "传说区域"),
        "zarude" to PokemonData(893, "恶/草", "萨戮德精灵", "genderless", "传说区域"),
        "regieleki" to PokemonData(894, "电", "雷吉艾勒奇精灵", "genderless", "传说区域"),
        "regidrago" to PokemonData(895, "龙", "雷吉铎拉戈精灵", "genderless", "传说区域"),
        "glastrier" to PokemonData(896, "冰", "雪暴马精灵", "genderless", "传说区域"),
        "spectrier" to PokemonData(897, "幽灵", "灵幽马精灵", "genderless", "传说区域"),
        "calyrex" to PokemonData(898, "超能力/草", "蕾冠王精灵", "genderless", "传说区域"),

        // 899-905 (传说阿尔宙斯DLC)
        "wyrdeer" to PokemonData(899, "一般/超能力", "诡角鹿精灵", "male", "雪山, 针叶林, 苔原"),
        "kleavor" to PokemonData(900, "虫/岩石", "劈斧螳螂精灵", "male", "森林, 丘陵"),
        "ursaluna" to PokemonData(901, "地面/一般", "月月熊精灵", "male", "森林, 山地, 针叶林"),
        "basculegion" to PokemonData(902, "水/幽灵", "幽尾玄鱼精灵", "male", "淡水"),
        "sneasler" to PokemonData(903, "格斗/毒", "大狃拉精灵", "male", "山地"),
        "overqwil" to PokemonData(904, "恶/毒", "万针鱼精灵", "male", "冰冻海洋"),
        "enamorus" to PokemonData(905, "妖精/飞行", "眷恋云精灵", "female", "传说区域")
    )
}
