# 精灵检测机制升级说明

## 概述

本次升级将精灵检测机制从**反射+缓存**模式改为**直接API调用+本地文件存储**模式，大幅提升性能并减少服务器卡顿。

## 主要改进

### 1. 检测方式优化

**之前：**
- 使用反射机制访问混淆的Cobblemon API
- 复杂的错误处理和多种尝试方法
- 频繁的内存缓存操作

**现在：**
- 直接使用 `PlayerExtensionsKt.party()` 和 `PlayerExtensionsKt.pc()` API
- 简洁高效的代码实现
- 类似用户提供的Java代码风格

```kotlin
// 新的检测方法
val party: PlayerPartyStore = PlayerExtensionsKt.party(serverPlayer)
val pcStore: PCStore = PlayerExtensionsKt.pc(serverPlayer)

// 检查队伍精灵
for (i in 0 until 6) {
    val pokemon = party.get(i)
    if (pokemon != null) {
        // 处理精灵数据
    }
}

// 检查PC精灵
for (box in pcStore.boxes) {
    val nonEmptySlots = box.nonEmptySlots
    for ((_, pokemon) in nonEmptySlots) {
        // 处理精灵数据
    }
}
```

### 2. 存储机制改进

**之前：**
- 内存缓存，服务器重启后丢失
- 5分钟缓存有效期
- 频繁的缓存检查和更新

**现在：**
- 本地JSON文件存储，一个玩家一个文件
- 持久化存储，服务器重启后数据保留
- 按需读取，减少内存占用

```kotlin
// 数据存储结构
data class PlayerDataStorage(
    val playerId: String,
    val playerName: String,
    val lastUpdated: Long,
    val partyPokemon: List<StoredPokemonInfo>,
    val pcPokemon: List<StoredPokemonInfo>,
    val totalCaught: Int,
    val shinyCount: Int
)
```

### 3. 事件监听优化

**之前：**
- 监听打开和关闭精灵界面
- 频繁的延迟检测任务

**现在：**
- 只监听关闭精灵界面
- 减少不必要的检测次数
- 玩家加入时智能检查数据文件

### 4. GUI数据获取优化

**之前：**
- 复杂的缓存有效性检查
- 异步数据获取和界面更新

**现在：**
- 直接从本地文件读取
- 快速响应，失败时自动重新检测

## 新增功能

### 1. 精灵检查方法
```kotlin
fun checkPokemon(player: Player, pokemonName: String): Boolean
```
- 直接检查玩家是否拥有指定精灵
- 类似用户提供的Java代码实现

### 2. 新增命令
- `/acedex detect` - 重新检测精灵数据
- `/acedex check <精灵名>` - 检查是否拥有指定精灵
- `/acedex cache refresh` - 刷新数据文件

### 3. 数据文件管理
- 自动创建 `playerdata` 目录
- JSON格式存储，便于调试和备份
- 支持数据文件统计和管理

## 性能提升

1. **减少API调用频率** - 只在必要时检测，避免频繁调用
2. **降低内存使用** - 移除内存缓存，使用文件存储
3. **提升响应速度** - 本地文件读取比实时检测更快
4. **减少服务器卡顿** - 避免持续的精灵数据检测

## 文件结构

```
plugins/AceDex/
├── playerdata/
│   ├── <player-uuid-1>.json
│   ├── <player-uuid-2>.json
│   └── ...
└── config.yml
```

## 兼容性

- 保持原有的API接口不变
- GUI界面无需修改
- 命令系统向后兼容
- 数据结构保持一致

## 使用建议

1. **首次使用** - 玩家需要执行 `/acedex detect` 生成数据文件
2. **数据更新** - 在PC操作后会自动更新数据文件
3. **手动刷新** - 可使用 `/acedx detect` 强制刷新数据
4. **故障排除** - 删除对应的JSON文件可重置玩家数据

## 注意事项

- 确保 `playerdata` 目录有写入权限
- 定期备份玩家数据文件
- 监控磁盘空间使用情况
- 服务器重启后数据自动保留
