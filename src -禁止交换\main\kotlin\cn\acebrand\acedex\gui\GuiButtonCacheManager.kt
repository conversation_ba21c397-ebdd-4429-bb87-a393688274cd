package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import org.bukkit.configuration.file.YamlConfiguration
import org.bukkit.inventory.ItemStack
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * GUI按钮缓存管理器
 * 预加载并缓存所有GUI配置文件中的按钮物品，避免每次打开菜单时重新创建
 * 类似于PokemonModelPreloader的实现方式
 */
class GuiButtonCacheManager(private val plugin: AceDex) {

    // 基础功能按钮缓存
    private val basicButtonCache = ConcurrentHashMap<String, ItemStack>()

    // 世代按钮缓存（按世代ID和状态分组）
    private val generationButtonCache = ConcurrentHashMap<String, ItemStack>()

    // 进度按钮缓存（按进度范围分组）
    private val progressButtonCache = ConcurrentHashMap<String, ItemStack>()

    // 导航按钮缓存（分页、返回等）
    private val navigationButtonCache = ConcurrentHashMap<String, ItemStack>()

    // 装饰物品缓存
    private val decorationCache = ConcurrentHashMap<String, ItemStack>()
    
    // 缓存状态
    private var isCacheLoaded = false
    
    // 本地缓存文件
    private val cacheDir = File(plugin.dataFolder, "gui_cache")
    private val cacheStatusFile = File(cacheDir, "gui_cache_status.yml")
    
    init {
        initializeCacheDirectory()
    }
    
    /**
     * 初始化缓存目录
     */
    private fun initializeCacheDirectory() {
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }
    
    /**
     * 预加载所有GUI按钮
     */
    fun preloadAllButtons() {
        try {
            plugin.logger.info("开始预加载GUI按钮...")
            
            // 清理旧缓存
            clearCache()
            
            // 预加载基础功能按钮
            preloadBasicButtons()

            // 预加载世代按钮
            preloadGenerationButtons()

            // 预加载进度按钮
            preloadProgressButtons()

            // 预加载导航按钮
            preloadNavigationButtons()

            // 预加载装饰物品
            preloadDecorationItems()
            
            // 保存缓存状态
            saveCacheStatus()
            
            isCacheLoaded = true
            val totalButtons = basicButtonCache.size + generationButtonCache.size + progressButtonCache.size + navigationButtonCache.size + decorationCache.size
            plugin.logger.info("GUI按钮预加载完成，共缓存 $totalButtons 个按钮")
            
        } catch (e: Exception) {
            plugin.logger.severe("GUI按钮预加载失败: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 预加载基础功能按钮（从gui.yml的main-menu-buttons配置）
     */
    private fun preloadBasicButtons() {
        try {
            // 统计按钮 - 从gui.yml的main-menu-buttons.stats-button配置
            val statsButton = plugin.config.createStatsButtonItem()
            basicButtonCache["stats_button"] = statsButton.clone()

            // 关闭按钮 - 从gui.yml的main-menu-buttons.close-button配置
            val closeButton = plugin.config.createCloseButtonItem()
            basicButtonCache["close_button"] = closeButton.clone()

            if (plugin.config.enableDebug) {
                plugin.logger.info("预加载基础按钮完成: stats(${plugin.config.statsButtonMaterial}), close(${plugin.config.closeButtonMaterial})")
            }

        } catch (e: Exception) {
            plugin.logger.warning("预加载基础按钮失败: ${e.message}")
        }
    }
    
    /**
     * 预加载世代按钮
     */
    private fun preloadGenerationButtons() {
        try {
            val generations = plugin.generationManager.getAllGenerations()
            
            for (generation in generations) {
                // 为每个世代创建不同状态的按钮
                val generationId = generation.id
                
                // 未收集状态
                val uncaughtButton = plugin.pokeBallItemCreator.createGenerationPokeBallItem(
                    generationId = generationId,
                    hasCaught = false,
                    isCompleted = false
                )
                generationButtonCache["${generationId}_uncaught"] = uncaughtButton.clone()
                
                // 已收集但未完成状态
                val caughtButton = plugin.pokeBallItemCreator.createGenerationPokeBallItem(
                    generationId = generationId,
                    hasCaught = true,
                    isCompleted = false
                )
                generationButtonCache["${generationId}_caught"] = caughtButton.clone()
                
                // 完成状态
                val completedButton = plugin.pokeBallItemCreator.createGenerationPokeBallItem(
                    generationId = generationId,
                    hasCaught = true,
                    isCompleted = true
                )
                generationButtonCache["${generationId}_completed"] = completedButton.clone()
            }
            
            if (plugin.config.enableDebug) {
                plugin.logger.info("预加载世代按钮完成，共 ${generationButtonCache.size} 个")
            }
            
        } catch (e: Exception) {
            plugin.logger.warning("预加载世代按钮失败: ${e.message}")
        }
    }
    
    /**
     * 预加载进度按钮
     */
    private fun preloadProgressButtons() {
        try {
            // 根据配置文件中的进度范围预加载不同的进度按钮
            val progressRanges = mapOf(
                "0-25" to 12.5,    // 0-25% 范围的中间值
                "25-50" to 37.5,   // 25-50% 范围的中间值
                "50-75" to 62.5,   // 50-75% 范围的中间值
                "75-99" to 87.0,   // 75-99% 范围的中间值
                "100" to 100.0     // 100% 完成
            )

            for ((range, percentage) in progressRanges) {
                val progressButton = plugin.config.createProgressButtonItem(percentage)
                progressButtonCache[range] = progressButton.clone()
            }

            if (plugin.config.enableDebug) {
                plugin.logger.info("预加载进度按钮完成，共 ${progressButtonCache.size} 个")
            }

        } catch (e: Exception) {
            plugin.logger.warning("预加载进度按钮失败: ${e.message}")
        }
    }

    /**
     * 预加载导航按钮（从gui.yml的导航配置）
     */
    private fun preloadNavigationButtons() {
        try {
            // 返回主菜单按钮 - 从gui.yml的back-to-main-menu-material配置
            val backButton = plugin.config.createBackToMainMenuItem()
            navigationButtonCache["back_button"] = backButton.clone()

            // 上一页按钮 - 从gui.yml的previous-page-material配置
            val previousPageButton = plugin.config.createPreviousPageItem()
            navigationButtonCache["previous_page"] = previousPageButton.clone()

            // 下一页按钮 - 从gui.yml的next-page-material配置
            val nextPageButton = plugin.config.createNextPageItem()
            navigationButtonCache["next_page"] = nextPageButton.clone()

            // 页码指示器 - 从gui.yml的page-indicator-material配置
            val pageIndicatorButton = plugin.config.createPageIndicatorItem()
            navigationButtonCache["page_indicator"] = pageIndicatorButton.clone()

            // 进度信息按钮 - 从gui.yml的progress-info-material配置
            val progressInfoButton = plugin.config.createProgressInfoItem()
            navigationButtonCache["progress_info"] = progressInfoButton.clone()

            if (plugin.config.enableDebug) {
                plugin.logger.info("预加载导航按钮完成，共 ${navigationButtonCache.size} 个")
            }

        } catch (e: Exception) {
            plugin.logger.warning("预加载导航按钮失败: ${e.message}")
        }
    }

    /**
     * 预加载装饰物品（从gui.yml的装饰配置）
     */
    private fun preloadDecorationItems() {
        try {
            // GUI装饰物品 - 从gui.yml的decoration-material配置
            val decorationItem = plugin.config.createDecorationItem()
            decorationCache["decoration"] = decorationItem.clone()

            if (plugin.config.enableDebug) {
                plugin.logger.info("预加载装饰物品完成: decoration(${plugin.config.guiDecorationMaterial})")
            }

        } catch (e: Exception) {
            plugin.logger.warning("预加载装饰物品失败: ${e.message}")
        }
    }
    
    /**
     * 获取缓存的基础按钮
     */
    fun getCachedButton(buttonType: String): ItemStack? {
        if (!isCacheLoaded) {
            return null
        }
        return basicButtonCache[buttonType]?.clone()
    }

    /**
     * 获取缓存的导航按钮
     */
    fun getCachedNavigationButton(buttonType: String): ItemStack? {
        if (!isCacheLoaded) {
            return null
        }
        return navigationButtonCache[buttonType]?.clone()
    }

    /**
     * 获取缓存的装饰物品
     */
    fun getCachedDecorationItem(itemType: String): ItemStack? {
        if (!isCacheLoaded) {
            return null
        }
        return decorationCache[itemType]?.clone()
    }
    
    /**
     * 获取缓存的世代按钮
     */
    fun getCachedGenerationButton(generationId: String, hasCaught: Boolean, isCompleted: Boolean): ItemStack? {
        if (!isCacheLoaded) {
            return null
        }
        
        val key = when {
            isCompleted -> "${generationId}_completed"
            hasCaught -> "${generationId}_caught"
            else -> "${generationId}_uncaught"
        }
        
        return generationButtonCache[key]?.clone()
    }
    
    /**
     * 获取缓存的进度按钮
     */
    fun getCachedProgressButton(progressRange: String): ItemStack? {
        if (!isCacheLoaded) {
            return null
        }
        return progressButtonCache[progressRange]?.clone()
    }
    
    /**
     * 根据进度百分比获取对应的进度按钮
     */
    fun getCachedProgressButtonByPercentage(percentage: Double): ItemStack? {
        val range = when {
            percentage >= 100.0 -> "100"
            percentage >= 75.0 -> "75-99"
            percentage >= 50.0 -> "50-75"
            percentage >= 25.0 -> "25-50"
            else -> "0-25"
        }
        return getCachedProgressButton(range)
    }
    
    /**
     * 清理所有缓存
     */
    fun clearCache() {
        basicButtonCache.clear()
        generationButtonCache.clear()
        progressButtonCache.clear()
        navigationButtonCache.clear()
        decorationCache.clear()
        isCacheLoaded = false

        // 删除本地缓存文件
        if (cacheStatusFile.exists()) {
            cacheStatusFile.delete()
        }

        plugin.logger.info("已清理所有GUI按钮缓存")
    }
    
    /**
     * 保存缓存状态到本地文件
     */
    private fun saveCacheStatus() {
        try {
            val statusConfig = YamlConfiguration()
            statusConfig.set("last_cache_time", System.currentTimeMillis())
            statusConfig.set("basic_buttons_count", basicButtonCache.size)
            statusConfig.set("generation_buttons_count", generationButtonCache.size)
            statusConfig.set("progress_buttons_count", progressButtonCache.size)
            statusConfig.set("navigation_buttons_count", navigationButtonCache.size)
            statusConfig.set("decoration_items_count", decorationCache.size)
            statusConfig.set("cache_loaded", isCacheLoaded)
            statusConfig.save(cacheStatusFile)

            if (plugin.config.enableDebug) {
                plugin.logger.info("GUI按钮缓存状态已保存到本地文件")
            }
        } catch (e: Exception) {
            plugin.logger.warning("保存GUI按钮缓存状态失败: ${e.message}")
        }
    }
    
    /**
     * 检查缓存是否已加载
     */
    fun isCacheReady(): Boolean = isCacheLoaded
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): String {
        val totalButtons = basicButtonCache.size + generationButtonCache.size + progressButtonCache.size + navigationButtonCache.size + decorationCache.size
        return "GUI按钮缓存统计: 基础按钮=${basicButtonCache.size}, 世代按钮=${generationButtonCache.size}, 进度按钮=${progressButtonCache.size}, 导航按钮=${navigationButtonCache.size}, 装饰物品=${decorationCache.size}, 总计=$totalButtons"
    }
}
