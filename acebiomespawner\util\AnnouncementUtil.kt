package cn.acebrand.acebiomespawner.util

import cn.acebrand.acebiomespawner.AceBiomeSpawner
import cn.acebrand.acebiomespawner.spawner.BiomeSpawnerManager
import org.bukkit.Bukkit
import org.bukkit.ChatColor
import org.bukkit.Location

/**
 * 公告工具类
 */
object AnnouncementUtil {

    private val logger = AceBiomeSpawner.INSTANCE.logger

    /**
     * 发送精灵生成公告
     */
    fun sendSpawnAnnouncement(
        spawnedPokemon: List<BiomeSpawnerManager.SpawnResult>,
        biomeName: String,
        location: Location,
        nearPlayerName: String,
        isManualTrigger: Boolean = false,
        triggerPlayerName: String? = null
    ) {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        if (!config.enableAnnouncement || spawnedPokemon.isEmpty()) {
            return
        }

        try {
            val message = buildAnnouncementMessage(spawnedPokemon, biomeName, location, nearPlayerName, isManualTrigger, triggerPlayerName)
            Bukkit.broadcastMessage(message)
            logger.info("发送生成公告: $message")
        } catch (e: Exception) {
            logger.severe("发送公告时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 构建公告消息
     */
    private fun buildAnnouncementMessage(
        spawnedPokemon: List<BiomeSpawnerManager.SpawnResult>,
        biomeName: String,
        location: Location,
        nearPlayerName: String,
        isManualTrigger: Boolean = false,
        triggerPlayerName: String? = null
    ): String {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        val message = StringBuilder()

        // 添加前缀
        if (config.announcementPrefix.isNotEmpty()) {
            message.append("${config.announcementPrefix} ")
        }

        // 如果是手动触发，添加特殊标识
        if (isManualTrigger && triggerPlayerName != null) {
            message.append("§7[管理员触发] ")
        }

        // 添加主要内容
        message.append(buildMainContent(spawnedPokemon, biomeName, location, nearPlayerName, isManualTrigger, triggerPlayerName))

        return message.toString()
    }

    /**
     * 构建主要内容
     */
    private fun buildMainContent(
        spawnedPokemon: List<BiomeSpawnerManager.SpawnResult>,
        biomeName: String,
        location: Location,
        nearPlayerName: String,
        isManualTrigger: Boolean = false,
        triggerPlayerName: String? = null
    ): String {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        val content = StringBuilder()

        // 世界信息
        val worldName = location.world?.name ?: "unknown"
        content.append("§f在 §e${worldName} §f世界 ")

        // 坐标信息
        if (config.showCoordinates) {
            content.append("§7(${location.blockX}, ${location.blockY}, ${location.blockZ}) ")
        }

        // 生物群系信息
        if (config.showBiomeName) {
            content.append("§f的 §a${BiomeNameUtil.getChineseName(biomeName)} §f生物群系生成了 ")
        } else {
            content.append("§f生成了 ")
        }

        // 精灵信息
        if (spawnedPokemon.size == 1) {
            val pokemon = spawnedPokemon.first()
            content.append(buildSinglePokemonInfo(pokemon))
        } else {
            content.append(buildMultiplePokemonInfo(spawnedPokemon))
        }

        // 附近玩家信息
        if (isManualTrigger && triggerPlayerName != null) {
            content.append(" §7(目标玩家: §e$nearPlayerName§7, 触发者: §6$triggerPlayerName§7)")
        } else {
            content.append(" §7(附近玩家: §e$nearPlayerName§7)")
        }

        return content.toString()
    }

    /**
     * 构建单个精灵信息
     */
    private fun buildSinglePokemonInfo(pokemon: BiomeSpawnerManager.SpawnResult): String {
        val content = StringBuilder()

        if (pokemon.isShiny) {
            content.append("§6✨${formatPokemonName(pokemon.pokemonName)}")
        } else {
            content.append("§b${formatPokemonName(pokemon.pokemonName)}")
        }

        content.append(" §f(等级§a${pokemon.level}§f)")

        if (pokemon.isShiny) {
            content.append(" §6[闪光]")
        }

        content.append("§f 出现了！")

        return content.toString()
    }

    /**
     * 构建多个精灵信息
     */
    private fun buildMultiplePokemonInfo(spawnedPokemon: List<BiomeSpawnerManager.SpawnResult>): String {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        val content = StringBuilder()

        // 检查是否有闪光精灵
        val shinyCount = spawnedPokemon.count { it.isShiny }
        val normalCount = spawnedPokemon.size - shinyCount

        if (shinyCount > 0 && normalCount > 0) {
            // 混合情况：既有普通又有闪光
            content.append("§b${normalCount}只精灵")
            if (shinyCount > 0) {
                content.append("§f和§6✨${shinyCount}只闪光精灵")
            }
        } else if (shinyCount > 0) {
            // 全部是闪光
            content.append("§6✨${shinyCount}只闪光精灵")
        } else {
            // 全部是普通
            content.append("§b${spawnedPokemon.size}只精灵")
        }

        // 显示具体精灵名称（如果数量不多）
        if (spawnedPokemon.size <= 3) {
            content.append(" §7(")
            spawnedPokemon.forEachIndexed { index, pokemon ->
                if (index > 0) content.append("§7, ")
                if (pokemon.isShiny) {
                    content.append("§6✨${formatPokemonName(pokemon.pokemonName)}")
                } else {
                    content.append("§b${formatPokemonName(pokemon.pokemonName)}")
                }
                content.append("§a${pokemon.level}级")
            }
            content.append("§7)")
        }

        return content.toString()
    }



    /**
     * 格式化精灵名称
     * 优先显示中文名称，如果没有中文映射则显示格式化的英文名称
     */
    private fun formatPokemonName(pokemonName: String): String {
        return PokemonNameUtil.getChineseName(pokemonName)
    }



    /**
     * 发送自定义公告
     */
    fun sendCustomAnnouncement(message: String, color: ChatColor = ChatColor.WHITE) {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        if (!config.enableAnnouncement) {
            return
        }

        try {
            val fullMessage = StringBuilder()

            if (config.announcementPrefix.isNotEmpty()) {
                fullMessage.append("${config.announcementPrefix} ")
            }

            fullMessage.append("$color$message${ChatColor.RESET}")

            Bukkit.broadcastMessage(fullMessage.toString())

        } catch (e: Exception) {
            logger.severe("发送自定义公告时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 发送错误公告
     */
    fun sendErrorAnnouncement(message: String) {
        sendCustomAnnouncement("错误: $message", ChatColor.RED)
    }

    /**
     * 发送信息公告
     */
    fun sendInfoAnnouncement(message: String) {
        sendCustomAnnouncement(message, ChatColor.GREEN)
    }
}
