package cn.acebrand.acebiomespawner.placeholder

import cn.acebrand.acebiomespawner.AceBiomeSpawner
import cn.acebrand.acebiomespawner.util.BiomeNameUtil
import me.clip.placeholderapi.expansion.PlaceholderExpansion
import org.bukkit.entity.Player

/**
 * PlaceholderAPI 扩展类
 * 提供生物群系相关的变量
 */
class BiomeSpawnerPlaceholder(private val plugin: AceBiomeSpawner) : PlaceholderExpansion() {

    /**
     * 插件标识符
     */
    override fun getIdentifier(): String {
        return "acebiomespawner"
    }

    /**
     * 插件作者
     */
    override fun getAuthor(): String {
        return plugin.description.authors.toString()
    }

    /**
     * 插件版本
     */
    override fun getVersion(): String {
        return plugin.description.version
    }

    /**
     * 是否持久化
     */
    override fun persist(): Boolean {
        return true
    }

    /**
     * 处理占位符请求
     * @param player 玩家
     * @param params 参数
     * @return 占位符值
     */
    override fun onPlaceholderRequest(player: Player?, params: String): String? {
        if (player == null) {
            return null
        }

        return when (params.lowercase()) {
            // %acebiomespawner_biome% - 当前生物群系英文名
            "biome" -> {
                val biome = player.location.block.biome
                biome.key.toString()
            }

            // %acebiomespawner_biome_chinese% - 当前生物群系中文名
            "biome_chinese" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                BiomeNameUtil.getChineseName(biomeName)
            }

            // %acebiomespawner_biome_simple% - 当前生物群系简化名（去掉命名空间）
            "biome_simple" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                biomeName.substringAfter(":")
            }

            // %acebiomespawner_biome_namespace% - 当前生物群系命名空间
            "biome_namespace" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                biomeName.substringBefore(":")
            }

            // %acebiomespawner_has_config% - 当前生物群系是否有配置
            "has_config" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                val config = plugin.getConfigManager().getConfig()
                val biomeConfig = config.biomeConfigs[biomeName]
                if (biomeConfig != null && biomeConfig.enabled) "是" else "否"
            }

            // %acebiomespawner_spawn_chance% - 当前生物群系生成概率
            "spawn_chance" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                val config = plugin.getConfigManager().getConfig()
                val biomeConfig = config.biomeConfigs[biomeName]
                if (biomeConfig != null && biomeConfig.enabled) {
                    String.format("%.1f%%", biomeConfig.spawnChance * 100)
                } else {
                    "0.0%"
                }
            }

            // %acebiomespawner_pokemon_count% - 当前生物群系精灵种类数量
            "pokemon_count" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                val config = plugin.getConfigManager().getConfig()
                val biomeConfig = config.biomeConfigs[biomeName]
                if (biomeConfig != null && biomeConfig.enabled) {
                    biomeConfig.pokemonList.size.toString()
                } else {
                    "0"
                }
            }

            // %acebiomespawner_pokemon_list% - 当前生物群系精灵列表（逗号分隔）
            "pokemon_list" -> {
                val biome = player.location.block.biome
                val biomeName = biome.key.toString()
                val config = plugin.getConfigManager().getConfig()
                val biomeConfig = config.biomeConfigs[biomeName]
                if (biomeConfig != null && biomeConfig.enabled) {
                    biomeConfig.pokemonList.joinToString(", ") { it.name }
                } else {
                    "无"
                }
            }

            // %acebiomespawner_is_spawning_enabled% - 全局生成是否启用
            "is_spawning_enabled" -> {
                val config = plugin.getConfigManager().getConfig()
                if (config.enabled) "是" else "否"
            }

            // %acebiomespawner_spawn_interval% - 生成间隔（秒）
            "spawn_interval" -> {
                val config = plugin.getConfigManager().getConfig()
                config.spawnInterval.toString()
            }

            // %acebiomespawner_max_distance% - 最大生成距离
            "max_distance" -> {
                val config = plugin.getConfigManager().getConfig()
                config.detectionRange.toString()
            }

            // %acebiomespawner_min_distance% - 最小生成距离
            "min_distance" -> {
                val config = plugin.getConfigManager().getConfig()
                "10" // 固定值，因为配置中没有最小距离设置
            }

            // %acebiomespawner_announcement_enabled% - 生成公告是否启用
            "announcement_enabled" -> {
                val config = plugin.getConfigManager().getConfig()
                if (config.enableAnnouncement) "是" else "否"
            }

            else -> null
        }
    }
}
