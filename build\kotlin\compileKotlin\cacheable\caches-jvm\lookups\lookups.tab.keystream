  AceDex cn.acebrand.acedex  
AceDexCommand cn.acebrand.acedex  AceDexConfig cn.acebrand.acedex  AsyncGuiManager cn.acebrand.acedex  Boolean cn.acebrand.acedex  Class cn.acebrand.acedex  ClassNotFoundException cn.acebrand.acedex  CobblemonEventListener cn.acebrand.acedex  CobblemonListener cn.acebrand.acedex  
DexMainGui cn.acebrand.acedex  	Exception cn.acebrand.acedex  GenerationManager cn.acebrand.acedex  GuiButtonCacheManager cn.acebrand.acedex  
JavaPlugin cn.acebrand.acedex  LicenseManager cn.acebrand.acedex  PLUGIN_NAME cn.acebrand.acedex  PlaceholderAPIExpansion cn.acebrand.acedex  PlayerDataManager cn.acebrand.acedex  PokeBallItemCreator cn.acebrand.acedex  PokemonCaptureListener cn.acebrand.acedex  PokemonDetector cn.acebrand.acedex  PokemonItemCreator cn.acebrand.acedex  PokemonModelPreloader cn.acebrand.acedex  PremiumRewardConfig cn.acebrand.acedex  PremiumRewardGui cn.acebrand.acedex  
RewardManager cn.acebrand.acedex  Runnable cn.acebrand.acedex  VERSION cn.acebrand.acedex  cn cn.acebrand.acedex  debugCobblemonStorage cn.acebrand.acedex  filter cn.acebrand.acedex  forEach cn.acebrand.acedex  instance cn.acebrand.acedex  isBlank cn.acebrand.acedex  
isInitialized cn.acebrand.acedex  
startsWith cn.acebrand.acedex  AceDex cn.acebrand.acedex.AceDex  
AceDexCommand cn.acebrand.acedex.AceDex  AceDexConfig cn.acebrand.acedex.AceDex  AsyncGuiManager cn.acebrand.acedex.AceDex  Boolean cn.acebrand.acedex.AceDex  Class cn.acebrand.acedex.AceDex  ClassNotFoundException cn.acebrand.acedex.AceDex  CobblemonEventListener cn.acebrand.acedex.AceDex  CobblemonListener cn.acebrand.acedex.AceDex  	Companion cn.acebrand.acedex.AceDex  
DexMainGui cn.acebrand.acedex.AceDex  	Exception cn.acebrand.acedex.AceDex  GenerationManager cn.acebrand.acedex.AceDex  GuiButtonCacheManager cn.acebrand.acedex.AceDex  LicenseManager cn.acebrand.acedex.AceDex  PLUGIN_NAME cn.acebrand.acedex.AceDex  PlaceholderAPIExpansion cn.acebrand.acedex.AceDex  PlayerDataManager cn.acebrand.acedex.AceDex  PokeBallItemCreator cn.acebrand.acedex.AceDex  PokemonCaptureListener cn.acebrand.acedex.AceDex  PokemonDetector cn.acebrand.acedex.AceDex  PokemonItemCreator cn.acebrand.acedex.AceDex  PokemonModelPreloader cn.acebrand.acedex.AceDex  PremiumRewardConfig cn.acebrand.acedex.AceDex  PremiumRewardGui cn.acebrand.acedex.AceDex  
RewardManager cn.acebrand.acedex.AceDex  Runnable cn.acebrand.acedex.AceDex  VERSION cn.acebrand.acedex.AceDex  asyncGuiManager cn.acebrand.acedex.AceDex  checkDependencies cn.acebrand.acedex.AceDex  cn cn.acebrand.acedex.AceDex  cobblemonEventListener cn.acebrand.acedex.AceDex  config cn.acebrand.acedex.AceDex  
dataFolder cn.acebrand.acedex.AceDex  dataStorage cn.acebrand.acedex.AceDex  debugCobblemonStorage cn.acebrand.acedex.AceDex  description cn.acebrand.acedex.AceDex  filter cn.acebrand.acedex.AceDex  generationManager cn.acebrand.acedex.AceDex  
getCommand cn.acebrand.acedex.AceDex  guiButtonCacheManager cn.acebrand.acedex.AceDex  initializeConfig cn.acebrand.acedex.AceDex  initializeLicense cn.acebrand.acedex.AceDex  initializeManagers cn.acebrand.acedex.AceDex  initializePlaceholderAPI cn.acebrand.acedex.AceDex  instance cn.acebrand.acedex.AceDex  isBlank cn.acebrand.acedex.AceDex  
isInitialized cn.acebrand.acedex.AceDex  licenseManager cn.acebrand.acedex.AceDex  logger cn.acebrand.acedex.AceDex  mainGui cn.acebrand.acedex.AceDex  placeholderExpansion cn.acebrand.acedex.AceDex  pokeBallItemCreator cn.acebrand.acedex.AceDex  pokemonCaptureListener cn.acebrand.acedex.AceDex  pokemonDetector cn.acebrand.acedex.AceDex  pokemonItemCreator cn.acebrand.acedex.AceDex  pokemonModelPreloader cn.acebrand.acedex.AceDex  premiumRewardConfig cn.acebrand.acedex.AceDex  premiumRewardGui cn.acebrand.acedex.AceDex  registerCommands cn.acebrand.acedex.AceDex  registerListeners cn.acebrand.acedex.AceDex  reload cn.acebrand.acedex.AceDex  
rewardManager cn.acebrand.acedex.AceDex  saveDefaultConfig cn.acebrand.acedex.AceDex  saveResource cn.acebrand.acedex.AceDex  server cn.acebrand.acedex.AceDex  
startsWith cn.acebrand.acedex.AceDex  
AceDexCommand #cn.acebrand.acedex.AceDex.Companion  AceDexConfig #cn.acebrand.acedex.AceDex.Companion  AsyncGuiManager #cn.acebrand.acedex.AceDex.Companion  Class #cn.acebrand.acedex.AceDex.Companion  CobblemonEventListener #cn.acebrand.acedex.AceDex.Companion  CobblemonListener #cn.acebrand.acedex.AceDex.Companion  
DexMainGui #cn.acebrand.acedex.AceDex.Companion  GenerationManager #cn.acebrand.acedex.AceDex.Companion  GuiButtonCacheManager #cn.acebrand.acedex.AceDex.Companion  LicenseManager #cn.acebrand.acedex.AceDex.Companion  PLUGIN_NAME #cn.acebrand.acedex.AceDex.Companion  PlaceholderAPIExpansion #cn.acebrand.acedex.AceDex.Companion  PlayerDataManager #cn.acebrand.acedex.AceDex.Companion  PokeBallItemCreator #cn.acebrand.acedex.AceDex.Companion  PokemonCaptureListener #cn.acebrand.acedex.AceDex.Companion  PokemonDetector #cn.acebrand.acedex.AceDex.Companion  PokemonItemCreator #cn.acebrand.acedex.AceDex.Companion  PokemonModelPreloader #cn.acebrand.acedex.AceDex.Companion  PremiumRewardConfig #cn.acebrand.acedex.AceDex.Companion  PremiumRewardGui #cn.acebrand.acedex.AceDex.Companion  
RewardManager #cn.acebrand.acedex.AceDex.Companion  Runnable #cn.acebrand.acedex.AceDex.Companion  VERSION #cn.acebrand.acedex.AceDex.Companion  cn #cn.acebrand.acedex.AceDex.Companion  debugCobblemonStorage #cn.acebrand.acedex.AceDex.Companion  filter #cn.acebrand.acedex.AceDex.Companion  instance #cn.acebrand.acedex.AceDex.Companion  isBlank #cn.acebrand.acedex.AceDex.Companion  
isInitialized #cn.acebrand.acedex.AceDex.Companion  
startsWith #cn.acebrand.acedex.AceDex.Companion  AceDex cn.acebrand.acedex.command  
AceDexCommand cn.acebrand.acedex.command  Array cn.acebrand.acedex.command  Boolean cn.acebrand.acedex.command  Command cn.acebrand.acedex.command  CommandExecutor cn.acebrand.acedex.command  
CommandSender cn.acebrand.acedex.command  	Exception cn.acebrand.acedex.command  	ItemStack cn.acebrand.acedex.command  List cn.acebrand.acedex.command  Player cn.acebrand.acedex.command  Runnable cn.acebrand.acedex.command  String cn.acebrand.acedex.command  
StringBuilder cn.acebrand.acedex.command  TabCompleter cn.acebrand.acedex.command  $UninitializedPropertyAccessException cn.acebrand.acedex.command  
component1 cn.acebrand.acedex.command  
component2 cn.acebrand.acedex.command  drop cn.acebrand.acedex.command  	emptyList cn.acebrand.acedex.command  filter cn.acebrand.acedex.command  forEach cn.acebrand.acedex.command  	getOrNull cn.acebrand.acedex.command  
isNotBlank cn.acebrand.acedex.command  
isNotEmpty cn.acebrand.acedex.command  iterator cn.acebrand.acedex.command  java cn.acebrand.acedex.command  listOf cn.acebrand.acedex.command  	lowercase cn.acebrand.acedex.command  
mutableListOf cn.acebrand.acedex.command  org cn.acebrand.acedex.command  split cn.acebrand.acedex.command  
startsWith cn.acebrand.acedex.command  AceDex (cn.acebrand.acedex.command.AceDexCommand  Runnable (cn.acebrand.acedex.command.AceDexCommand  
StringBuilder (cn.acebrand.acedex.command.AceDexCommand  
component1 (cn.acebrand.acedex.command.AceDexCommand  
component2 (cn.acebrand.acedex.command.AceDexCommand  drop (cn.acebrand.acedex.command.AceDexCommand  	emptyList (cn.acebrand.acedex.command.AceDexCommand  filter (cn.acebrand.acedex.command.AceDexCommand  getItemDebugInfo (cn.acebrand.acedex.command.AceDexCommand  	getOrNull (cn.acebrand.acedex.command.AceDexCommand  handleAddPokemon (cn.acebrand.acedex.command.AceDexCommand  handleCache (cn.acebrand.acedex.command.AceDexCommand  handleCheck (cn.acebrand.acedex.command.AceDexCommand  handleConfig (cn.acebrand.acedex.command.AceDexCommand  handleForceClose (cn.acebrand.acedex.command.AceDexCommand  handleGivePokemon (cn.acebrand.acedex.command.AceDexCommand  
handleHelp (cn.acebrand.acedex.command.AceDexCommand  
handleInfo (cn.acebrand.acedex.command.AceDexCommand  
handleMain (cn.acebrand.acedex.command.AceDexCommand  
handlePremium (cn.acebrand.acedex.command.AceDexCommand  
handleRefresh (cn.acebrand.acedex.command.AceDexCommand  handleReload (cn.acebrand.acedex.command.AceDexCommand  handleStatus (cn.acebrand.acedex.command.AceDexCommand  handleSyncPlayer (cn.acebrand.acedex.command.AceDexCommand  handleTestEnchant (cn.acebrand.acedex.command.AceDexCommand  
isNotBlank (cn.acebrand.acedex.command.AceDexCommand  
isNotEmpty (cn.acebrand.acedex.command.AceDexCommand  iterator (cn.acebrand.acedex.command.AceDexCommand  java (cn.acebrand.acedex.command.AceDexCommand  listOf (cn.acebrand.acedex.command.AceDexCommand  	lowercase (cn.acebrand.acedex.command.AceDexCommand  
mutableListOf (cn.acebrand.acedex.command.AceDexCommand  org (cn.acebrand.acedex.command.AceDexCommand  plugin (cn.acebrand.acedex.command.AceDexCommand  setKeepDexOnRelease (cn.acebrand.acedex.command.AceDexCommand  split (cn.acebrand.acedex.command.AceDexCommand  
startsWith (cn.acebrand.acedex.command.AceDexCommand  AceDex cn.acebrand.acedex.config  AceDexConfig cn.acebrand.acedex.config  Boolean cn.acebrand.acedex.config  Double cn.acebrand.acedex.config  	Exception cn.acebrand.acedex.config  File cn.acebrand.acedex.config  FileConfiguration cn.acebrand.acedex.config  IllegalArgumentException cn.acebrand.acedex.config  Int cn.acebrand.acedex.config  	ItemStack cn.acebrand.acedex.config  List cn.acebrand.acedex.config  Long cn.acebrand.acedex.config  Map cn.acebrand.acedex.config  Material cn.acebrand.acedex.config  NumberFormatException cn.acebrand.acedex.config  Player cn.acebrand.acedex.config  PremiumRewardConfig cn.acebrand.acedex.config  String cn.acebrand.acedex.config  $UninitializedPropertyAccessException cn.acebrand.acedex.config  VariableReplacer cn.acebrand.acedex.config  YamlConfiguration cn.acebrand.acedex.config  cn cn.acebrand.acedex.config  	emptyList cn.acebrand.acedex.config  forEach cn.acebrand.acedex.config  listOf cn.acebrand.acedex.config  mapOf cn.acebrand.acedex.config  
mutableListOf cn.acebrand.acedex.config  mutableMapOf cn.acebrand.acedex.config  org cn.acebrand.acedex.config  replaceVariables cn.acebrand.acedex.config  set cn.acebrand.acedex.config  sorted cn.acebrand.acedex.config  split cn.acebrand.acedex.config  
startsWith cn.acebrand.acedex.config  toInt cn.acebrand.acedex.config  toIntOrNull cn.acebrand.acedex.config  toList cn.acebrand.acedex.config  	uppercase cn.acebrand.acedex.config  File &cn.acebrand.acedex.config.AceDexConfig  	ItemStack &cn.acebrand.acedex.config.AceDexConfig  Material &cn.acebrand.acedex.config.AceDexConfig  VariableReplacer &cn.acebrand.acedex.config.AceDexConfig  YamlConfiguration &cn.acebrand.acedex.config.AceDexConfig  backToMainMenuMaterial &cn.acebrand.acedex.config.AceDexConfig  closeButtonMaterial &cn.acebrand.acedex.config.AceDexConfig  closeButtonSlot &cn.acebrand.acedex.config.AceDexConfig  config &cn.acebrand.acedex.config.AceDexConfig  
configFile &cn.acebrand.acedex.config.AceDexConfig  createBackToMainMenuItem &cn.acebrand.acedex.config.AceDexConfig  createCloseButtonItem &cn.acebrand.acedex.config.AceDexConfig  createCobblemonButtonItem &cn.acebrand.acedex.config.AceDexConfig  createDecorationItem &cn.acebrand.acedex.config.AceDexConfig  createNextPageItem &cn.acebrand.acedex.config.AceDexConfig  createPageIndicatorItem &cn.acebrand.acedex.config.AceDexConfig  createPaperCustomModelDataItem &cn.acebrand.acedex.config.AceDexConfig  createPaperNavigationItem &cn.acebrand.acedex.config.AceDexConfig  createPaperPaginationItem &cn.acebrand.acedex.config.AceDexConfig  createPreviousPageItem &cn.acebrand.acedex.config.AceDexConfig  createProgressButtonItem &cn.acebrand.acedex.config.AceDexConfig  createProgressInfoItem &cn.acebrand.acedex.config.AceDexConfig  createStatsButtonItem &cn.acebrand.acedex.config.AceDexConfig  customCaughtMaterial &cn.acebrand.acedex.config.AceDexConfig  defaultRewardCooldown &cn.acebrand.acedex.config.AceDexConfig  enableCompletionRewards &cn.acebrand.acedex.config.AceDexConfig  enableDebug &cn.acebrand.acedex.config.AceDexConfig  enableGenerationRewards &cn.acebrand.acedex.config.AceDexConfig  	enableGui &cn.acebrand.acedex.config.AceDexConfig  
enableRewards &cn.acebrand.acedex.config.AceDexConfig  generationMaterials &cn.acebrand.acedex.config.AceDexConfig  generationRewardCooldown &cn.acebrand.acedex.config.AceDexConfig  generationSlots &cn.acebrand.acedex.config.AceDexConfig  generationTitles &cn.acebrand.acedex.config.AceDexConfig  #getAllGenerationsRewardDescriptions &cn.acebrand.acedex.config.AceDexConfig  	getConfig &cn.acebrand.acedex.config.AceDexConfig  getDefaultGenerationMaterial &cn.acebrand.acedex.config.AceDexConfig  getDefaultGenerationSlot &cn.acebrand.acedex.config.AceDexConfig  getDefaultProgressMaterial &cn.acebrand.acedex.config.AceDexConfig  getGenerationMaterial &cn.acebrand.acedex.config.AceDexConfig  getGenerationRewardDescriptions &cn.acebrand.acedex.config.AceDexConfig  getGenerationSlot &cn.acebrand.acedex.config.AceDexConfig  getPremiumMenuTitle &cn.acebrand.acedex.config.AceDexConfig  getProcessedGenerationTitle &cn.acebrand.acedex.config.AceDexConfig  getProcessedGuiTitle &cn.acebrand.acedex.config.AceDexConfig  	guiConfig &cn.acebrand.acedex.config.AceDexConfig  
guiConfigFile &cn.acebrand.acedex.config.AceDexConfig  guiDecorationMaterial &cn.acebrand.acedex.config.AceDexConfig  guiTitle &cn.acebrand.acedex.config.AceDexConfig  keepDexOnRelease &cn.acebrand.acedex.config.AceDexConfig  
licenseKey &cn.acebrand.acedex.config.AceDexConfig  listOf &cn.acebrand.acedex.config.AceDexConfig  load &cn.acebrand.acedex.config.AceDexConfig  loadConfigValues &cn.acebrand.acedex.config.AceDexConfig  mapOf &cn.acebrand.acedex.config.AceDexConfig  mutableMapOf &cn.acebrand.acedex.config.AceDexConfig  nextPageMaterial &cn.acebrand.acedex.config.AceDexConfig  org &cn.acebrand.acedex.config.AceDexConfig  pageIndicatorMaterial &cn.acebrand.acedex.config.AceDexConfig  parseButtonMaterial &cn.acebrand.acedex.config.AceDexConfig  parseDecorationMaterial &cn.acebrand.acedex.config.AceDexConfig  parseNavigationMaterial &cn.acebrand.acedex.config.AceDexConfig  parsePaginationMaterial &cn.acebrand.acedex.config.AceDexConfig  plugin &cn.acebrand.acedex.config.AceDexConfig  previousPageMaterial &cn.acebrand.acedex.config.AceDexConfig  progressButtonCooldown &cn.acebrand.acedex.config.AceDexConfig  progressButtonMaterials &cn.acebrand.acedex.config.AceDexConfig  progressButtonSlot &cn.acebrand.acedex.config.AceDexConfig  progressInfoMaterial &cn.acebrand.acedex.config.AceDexConfig  reload &cn.acebrand.acedex.config.AceDexConfig  replaceVariables &cn.acebrand.acedex.config.AceDexConfig  save &cn.acebrand.acedex.config.AceDexConfig  set &cn.acebrand.acedex.config.AceDexConfig  setDefaults &cn.acebrand.acedex.config.AceDexConfig  split &cn.acebrand.acedex.config.AceDexConfig  
startsWith &cn.acebrand.acedex.config.AceDexConfig  statsButtonCooldown &cn.acebrand.acedex.config.AceDexConfig  statsButtonMaterial &cn.acebrand.acedex.config.AceDexConfig  statsButtonSlot &cn.acebrand.acedex.config.AceDexConfig  toIntOrNull &cn.acebrand.acedex.config.AceDexConfig  	uppercase &cn.acebrand.acedex.config.AceDexConfig  useCustomMaterialForCaught &cn.acebrand.acedex.config.AceDexConfig  File -cn.acebrand.acedex.config.PremiumRewardConfig  YamlConfiguration -cn.acebrand.acedex.config.PremiumRewardConfig  config -cn.acebrand.acedex.config.PremiumRewardConfig  
configFile -cn.acebrand.acedex.config.PremiumRewardConfig  	emptyList -cn.acebrand.acedex.config.PremiumRewardConfig  *getPremiumAllGenerationsCompletionCommands -cn.acebrand.acedex.config.PremiumRewardConfig  .getPremiumAllGenerationsCompletionDescriptions -cn.acebrand.acedex.config.PremiumRewardConfig  getPremiumCloseButtonMaterial -cn.acebrand.acedex.config.PremiumRewardConfig  &getPremiumGenerationCompletionCommands -cn.acebrand.acedex.config.PremiumRewardConfig  *getPremiumGenerationCompletionDescriptions -cn.acebrand.acedex.config.PremiumRewardConfig  $getPremiumGenerationProgressCommands -cn.acebrand.acedex.config.PremiumRewardConfig  (getPremiumGenerationProgressDescriptions -cn.acebrand.acedex.config.PremiumRewardConfig  'getPremiumGenerationProgressPercentages -cn.acebrand.acedex.config.PremiumRewardConfig  !getPremiumOverallProgressCommands -cn.acebrand.acedex.config.PremiumRewardConfig  %getPremiumOverallProgressDescriptions -cn.acebrand.acedex.config.PremiumRewardConfig  getPremiumOverallProgressName -cn.acebrand.acedex.config.PremiumRewardConfig  $getPremiumOverallProgressPercentages -cn.acebrand.acedex.config.PremiumRewardConfig  !getPremiumProgressButtonMaterials -cn.acebrand.acedex.config.PremiumRewardConfig  
initialize -cn.acebrand.acedex.config.PremiumRewardConfig  (isPremiumAllGenerationsCompletionEnabled -cn.acebrand.acedex.config.PremiumRewardConfig  isPremiumOverallProgressEnabled -cn.acebrand.acedex.config.PremiumRewardConfig  listOf -cn.acebrand.acedex.config.PremiumRewardConfig  
mutableListOf -cn.acebrand.acedex.config.PremiumRewardConfig  mutableMapOf -cn.acebrand.acedex.config.PremiumRewardConfig  plugin -cn.acebrand.acedex.config.PremiumRewardConfig  set -cn.acebrand.acedex.config.PremiumRewardConfig  sorted -cn.acebrand.acedex.config.PremiumRewardConfig  toInt -cn.acebrand.acedex.config.PremiumRewardConfig  toList -cn.acebrand.acedex.config.PremiumRewardConfig  acebrand cn.acebrand.acedex.config.cn  acedex %cn.acebrand.acedex.config.cn.acebrand  
generation ,cn.acebrand.acedex.config.cn.acebrand.acedex  
Generation 7cn.acebrand.acedex.config.cn.acebrand.acedex.generation  AceDex cn.acebrand.acedex.data  AllGenerationsProgress cn.acebrand.acedex.data  Boolean cn.acebrand.acedex.data  CompletableFuture cn.acebrand.acedex.data  	Exception cn.acebrand.acedex.data  File cn.acebrand.acedex.data  
FileReader cn.acebrand.acedex.data  
FileWriter cn.acebrand.acedex.data  GenerationProgress cn.acebrand.acedex.data  Gson cn.acebrand.acedex.data  GsonBuilder cn.acebrand.acedex.data  Int cn.acebrand.acedex.data  List cn.acebrand.acedex.data  Long cn.acebrand.acedex.data  Map cn.acebrand.acedex.data  PCStore cn.acebrand.acedex.data  Player cn.acebrand.acedex.data  PlayerDataManager cn.acebrand.acedex.data  PlayerDataStorage cn.acebrand.acedex.data  PlayerPartyStore cn.acebrand.acedex.data  PlayerPokemonData cn.acebrand.acedex.data  Pokemon cn.acebrand.acedex.data  PokemonInfo cn.acebrand.acedex.data  PokemonNameMapping cn.acebrand.acedex.data  Regex cn.acebrand.acedex.data  Runnable cn.acebrand.acedex.data  ServerPlayer cn.acebrand.acedex.data  StoredPokemonInfo cn.acebrand.acedex.data  String cn.acebrand.acedex.data  System cn.acebrand.acedex.data  UUID cn.acebrand.acedex.data  any cn.acebrand.acedex.data  
coerceAtLeast cn.acebrand.acedex.data  coerceIn cn.acebrand.acedex.data  com cn.acebrand.acedex.data  
distinctBy cn.acebrand.acedex.data  drop cn.acebrand.acedex.data  	emptyList cn.acebrand.acedex.data  	extension cn.acebrand.acedex.data  filter cn.acebrand.acedex.data  forEach cn.acebrand.acedex.data  fromPlayerPokemonData cn.acebrand.acedex.data  fromPokemonInfo cn.acebrand.acedex.data  getPokemonChineseName cn.acebrand.acedex.data  hasChineseName cn.acebrand.acedex.data  
isNotEmpty cn.acebrand.acedex.data  java cn.acebrand.acedex.data  	javaClass cn.acebrand.acedex.data  listOf cn.acebrand.acedex.data  	lowercase cn.acebrand.acedex.data  map cn.acebrand.acedex.data  
mapNotNull cn.acebrand.acedex.data  
mutableListOf cn.acebrand.acedex.data  mutableMapOf cn.acebrand.acedex.data  party cn.acebrand.acedex.data  pc cn.acebrand.acedex.data  plus cn.acebrand.acedex.data  
plusAssign cn.acebrand.acedex.data  repeat cn.acebrand.acedex.data  replace cn.acebrand.acedex.data  run cn.acebrand.acedex.data  set cn.acebrand.acedex.data  shuffled cn.acebrand.acedex.data  take cn.acebrand.acedex.data  toList cn.acebrand.acedex.data  toSet cn.acebrand.acedex.data  until cn.acebrand.acedex.data  use cn.acebrand.acedex.data  
caughtPokemon .cn.acebrand.acedex.data.AllGenerationsProgress  
coerceAtLeast .cn.acebrand.acedex.data.AllGenerationsProgress  coerceIn .cn.acebrand.acedex.data.AllGenerationsProgress  completedGenerations .cn.acebrand.acedex.data.AllGenerationsProgress  generationProgresses .cn.acebrand.acedex.data.AllGenerationsProgress  getOverallProgressBar .cn.acebrand.acedex.data.AllGenerationsProgress  listOf .cn.acebrand.acedex.data.AllGenerationsProgress  overallPercentage .cn.acebrand.acedex.data.AllGenerationsProgress  repeat .cn.acebrand.acedex.data.AllGenerationsProgress  totalGenerations .cn.acebrand.acedex.data.AllGenerationsProgress  totalPokemon .cn.acebrand.acedex.data.AllGenerationsProgress  caught *cn.acebrand.acedex.data.GenerationProgress  
coerceAtLeast *cn.acebrand.acedex.data.GenerationProgress  coerceIn *cn.acebrand.acedex.data.GenerationProgress  getProgressBar *cn.acebrand.acedex.data.GenerationProgress  
percentage *cn.acebrand.acedex.data.GenerationProgress  repeat *cn.acebrand.acedex.data.GenerationProgress  total *cn.acebrand.acedex.data.GenerationProgress  File )cn.acebrand.acedex.data.PlayerDataManager  
FileReader )cn.acebrand.acedex.data.PlayerDataManager  
FileWriter )cn.acebrand.acedex.data.PlayerDataManager  GsonBuilder )cn.acebrand.acedex.data.PlayerDataManager  PlayerDataStorage )cn.acebrand.acedex.data.PlayerDataManager  PlayerPokemonData )cn.acebrand.acedex.data.PlayerDataManager  any )cn.acebrand.acedex.data.PlayerDataManager  
dataFolder )cn.acebrand.acedex.data.PlayerDataManager  	emptyList )cn.acebrand.acedex.data.PlayerDataManager  	extension )cn.acebrand.acedex.data.PlayerDataManager  fromPlayerPokemonData )cn.acebrand.acedex.data.PlayerDataManager  gson )cn.acebrand.acedex.data.PlayerDataManager  java )cn.acebrand.acedex.data.PlayerDataManager  loadPlayerData )cn.acebrand.acedex.data.PlayerDataManager  loadPlayerDataFromFile )cn.acebrand.acedex.data.PlayerDataManager  plugin )cn.acebrand.acedex.data.PlayerDataManager  plus )cn.acebrand.acedex.data.PlayerDataManager  run )cn.acebrand.acedex.data.PlayerDataManager  savePlayerData )cn.acebrand.acedex.data.PlayerDataManager  toList )cn.acebrand.acedex.data.PlayerDataManager  use )cn.acebrand.acedex.data.PlayerDataManager  	Companion )cn.acebrand.acedex.data.PlayerDataStorage  	Exception )cn.acebrand.acedex.data.PlayerDataStorage  Int )cn.acebrand.acedex.data.PlayerDataStorage  List )cn.acebrand.acedex.data.PlayerDataStorage  Long )cn.acebrand.acedex.data.PlayerDataStorage  PlayerDataStorage )cn.acebrand.acedex.data.PlayerDataStorage  PlayerPokemonData )cn.acebrand.acedex.data.PlayerDataStorage  StoredPokemonInfo )cn.acebrand.acedex.data.PlayerDataStorage  String )cn.acebrand.acedex.data.PlayerDataStorage  System )cn.acebrand.acedex.data.PlayerDataStorage  UUID )cn.acebrand.acedex.data.PlayerDataStorage  	emptyList )cn.acebrand.acedex.data.PlayerDataStorage  fromPlayerPokemonData )cn.acebrand.acedex.data.PlayerDataStorage  fromPokemonInfo )cn.acebrand.acedex.data.PlayerDataStorage  map )cn.acebrand.acedex.data.PlayerDataStorage  
mapNotNull )cn.acebrand.acedex.data.PlayerDataStorage  partyPokemon )cn.acebrand.acedex.data.PlayerDataStorage  	pcPokemon )cn.acebrand.acedex.data.PlayerDataStorage  toPlayerPokemonData )cn.acebrand.acedex.data.PlayerDataStorage  totalCaught )cn.acebrand.acedex.data.PlayerDataStorage  PlayerDataStorage 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  PlayerPokemonData 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  StoredPokemonInfo 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  System 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  	emptyList 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  fromPlayerPokemonData 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  fromPokemonInfo 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  map 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  
mapNotNull 3cn.acebrand.acedex.data.PlayerDataStorage.Companion  copy )cn.acebrand.acedex.data.PlayerPokemonData  partyPokemon )cn.acebrand.acedex.data.PlayerPokemonData  	pcPokemon )cn.acebrand.acedex.data.PlayerPokemonData  totalCaught )cn.acebrand.acedex.data.PlayerPokemonData  	Companion )cn.acebrand.acedex.data.StoredPokemonInfo  Int )cn.acebrand.acedex.data.StoredPokemonInfo  PokemonInfo )cn.acebrand.acedex.data.StoredPokemonInfo  StoredPokemonInfo )cn.acebrand.acedex.data.StoredPokemonInfo  String )cn.acebrand.acedex.data.StoredPokemonInfo  fromPokemonInfo )cn.acebrand.acedex.data.StoredPokemonInfo  
generation )cn.acebrand.acedex.data.StoredPokemonInfo  name )cn.acebrand.acedex.data.StoredPokemonInfo  nationalDex )cn.acebrand.acedex.data.StoredPokemonInfo  
toPokemonInfo )cn.acebrand.acedex.data.StoredPokemonInfo  PokemonInfo 3cn.acebrand.acedex.data.StoredPokemonInfo.Companion  StoredPokemonInfo 3cn.acebrand.acedex.data.StoredPokemonInfo.Companion  fromPokemonInfo 3cn.acebrand.acedex.data.StoredPokemonInfo.Companion  google cn.acebrand.acedex.data.com  gson "cn.acebrand.acedex.data.com.google  JsonSyntaxException 'cn.acebrand.acedex.data.com.google.gson  io cn.acebrand.acedex.data.java  EOFException cn.acebrand.acedex.data.java.io  BattleVictoryEvent cn.acebrand.acedex.event  Boolean cn.acebrand.acedex.event  CobblemonEventWrapper cn.acebrand.acedex.event  	Companion cn.acebrand.acedex.event  Event cn.acebrand.acedex.event  HandlerList cn.acebrand.acedex.event  Int cn.acebrand.acedex.event  	JvmStatic cn.acebrand.acedex.event  Player cn.acebrand.acedex.event  PokedexUpdateEvent cn.acebrand.acedex.event  PokedexUpdateType cn.acebrand.acedex.event  PokemonCaptureEvent cn.acebrand.acedex.event  PokemonEvolutionEvent cn.acebrand.acedex.event  PokemonHatchEvent cn.acebrand.acedex.event  PokemonReleaseEvent cn.acebrand.acedex.event  PokemonStorageChangeEvent cn.acebrand.acedex.event  PokemonTradeEvent cn.acebrand.acedex.event  StorageChangeType cn.acebrand.acedex.event  String cn.acebrand.acedex.event  Boolean .cn.acebrand.acedex.event.CobblemonEventWrapper  	Companion .cn.acebrand.acedex.event.CobblemonEventWrapper  HandlerList .cn.acebrand.acedex.event.CobblemonEventWrapper  Int .cn.acebrand.acedex.event.CobblemonEventWrapper  	JvmStatic .cn.acebrand.acedex.event.CobblemonEventWrapper  Player .cn.acebrand.acedex.event.CobblemonEventWrapper  PokedexUpdateType .cn.acebrand.acedex.event.CobblemonEventWrapper  StorageChangeType .cn.acebrand.acedex.event.CobblemonEventWrapper  String .cn.acebrand.acedex.event.CobblemonEventWrapper  handlers .cn.acebrand.acedex.event.CobblemonEventWrapper  	Companion 8cn.acebrand.acedex.event.CobblemonEventWrapper.Companion  HandlerList 8cn.acebrand.acedex.event.CobblemonEventWrapper.Companion  handlers 8cn.acebrand.acedex.event.CobblemonEventWrapper.Companion  AceDex cn.acebrand.acedex.generation  Boolean cn.acebrand.acedex.generation  
Collection cn.acebrand.acedex.generation  	Exception cn.acebrand.acedex.generation  Gen1PokemonData cn.acebrand.acedex.generation  Gen2PokemonData cn.acebrand.acedex.generation  Gen3PokemonData cn.acebrand.acedex.generation  Gen4PokemonData cn.acebrand.acedex.generation  Gen5PokemonData cn.acebrand.acedex.generation  Gen6PokemonData cn.acebrand.acedex.generation  Gen7PokemonData cn.acebrand.acedex.generation  Gen8PokemonData cn.acebrand.acedex.generation  Gen9PokemonData cn.acebrand.acedex.generation  
Generation cn.acebrand.acedex.generation  GenerationManager cn.acebrand.acedex.generation  GenerationProgress cn.acebrand.acedex.generation  Int cn.acebrand.acedex.generation  IntRange cn.acebrand.acedex.generation  List cn.acebrand.acedex.generation  Map cn.acebrand.acedex.generation  Player cn.acebrand.acedex.generation  PokemonData cn.acebrand.acedex.generation  Regex cn.acebrand.acedex.generation  String cn.acebrand.acedex.generation  any cn.acebrand.acedex.generation  
component1 cn.acebrand.acedex.generation  
component2 cn.acebrand.acedex.generation  find cn.acebrand.acedex.generation  iterator cn.acebrand.acedex.generation  let cn.acebrand.acedex.generation  listOf cn.acebrand.acedex.generation  	lowercase cn.acebrand.acedex.generation  mutableMapOf cn.acebrand.acedex.generation  replace cn.acebrand.acedex.generation  set cn.acebrand.acedex.generation  toMap cn.acebrand.acedex.generation  Regex (cn.acebrand.acedex.generation.Generation  
addPokemon (cn.acebrand.acedex.generation.Generation  any (cn.acebrand.acedex.generation.Generation  color (cn.acebrand.acedex.generation.Generation  containsPokemonByShowdownId (cn.acebrand.acedex.generation.Generation  description (cn.acebrand.acedex.generation.Generation  displayName (cn.acebrand.acedex.generation.Generation  
getAllPokemon (cn.acebrand.acedex.generation.Generation  getTotalPokemon (cn.acebrand.acedex.generation.Generation  id (cn.acebrand.acedex.generation.Generation  listOf (cn.acebrand.acedex.generation.Generation  	lowercase (cn.acebrand.acedex.generation.Generation  mutableMapOf (cn.acebrand.acedex.generation.Generation  name (cn.acebrand.acedex.generation.Generation  normalizeShowdownId (cn.acebrand.acedex.generation.Generation  pokemonList (cn.acebrand.acedex.generation.Generation  pokemonRange (cn.acebrand.acedex.generation.Generation  region (cn.acebrand.acedex.generation.Generation  replace (cn.acebrand.acedex.generation.Generation  set (cn.acebrand.acedex.generation.Generation  toMap (cn.acebrand.acedex.generation.Generation  Gen1PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen2PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen3PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen4PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen5PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen6PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen7PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen8PokemonData /cn.acebrand.acedex.generation.GenerationManager  Gen9PokemonData /cn.acebrand.acedex.generation.GenerationManager  
Generation /cn.acebrand.acedex.generation.GenerationManager  GenerationProgress /cn.acebrand.acedex.generation.GenerationManager  
component1 /cn.acebrand.acedex.generation.GenerationManager  
component2 /cn.acebrand.acedex.generation.GenerationManager  find /cn.acebrand.acedex.generation.GenerationManager  generations /cn.acebrand.acedex.generation.GenerationManager  getAllGenerations /cn.acebrand.acedex.generation.GenerationManager  
getGeneration /cn.acebrand.acedex.generation.GenerationManager  getGenerationByPokemon /cn.acebrand.acedex.generation.GenerationManager  getPokemonData /cn.acebrand.acedex.generation.GenerationManager  
initialize /cn.acebrand.acedex.generation.GenerationManager  initializeGenerations /cn.acebrand.acedex.generation.GenerationManager  iterator /cn.acebrand.acedex.generation.GenerationManager  let /cn.acebrand.acedex.generation.GenerationManager  loadMockPokemonData /cn.acebrand.acedex.generation.GenerationManager  loadPokemonData /cn.acebrand.acedex.generation.GenerationManager  	lowercase /cn.acebrand.acedex.generation.GenerationManager  mutableMapOf /cn.acebrand.acedex.generation.GenerationManager  plugin /cn.acebrand.acedex.generation.GenerationManager  pokemonToGeneration /cn.acebrand.acedex.generation.GenerationManager  reload /cn.acebrand.acedex.generation.GenerationManager  set /cn.acebrand.acedex.generation.GenerationManager  Int )cn.acebrand.acedex.generation.PokemonData  PokemonData )cn.acebrand.acedex.generation.PokemonData  String )cn.acebrand.acedex.generation.PokemonData  dex )cn.acebrand.acedex.generation.PokemonData  habitat )cn.acebrand.acedex.generation.PokemonData  let )cn.acebrand.acedex.generation.PokemonData  type )cn.acebrand.acedex.generation.PokemonData  PokemonData 3cn.acebrand.acedex.generation.PokemonData.Companion  AceDex "cn.acebrand.acedex.generation.data  
Collection "cn.acebrand.acedex.generation.data  	Exception "cn.acebrand.acedex.generation.data  Gen1PokemonData "cn.acebrand.acedex.generation.data  Gen2PokemonData "cn.acebrand.acedex.generation.data  Gen3PokemonData "cn.acebrand.acedex.generation.data  Gen4PokemonData "cn.acebrand.acedex.generation.data  Gen5PokemonData "cn.acebrand.acedex.generation.data  Gen6PokemonData "cn.acebrand.acedex.generation.data  Gen7PokemonData "cn.acebrand.acedex.generation.data  Gen8PokemonData "cn.acebrand.acedex.generation.data  Gen9PokemonData "cn.acebrand.acedex.generation.data  
Generation "cn.acebrand.acedex.generation.data  GenerationProgress "cn.acebrand.acedex.generation.data  Int "cn.acebrand.acedex.generation.data  Player "cn.acebrand.acedex.generation.data  PokemonData "cn.acebrand.acedex.generation.data  String "cn.acebrand.acedex.generation.data  
component1 "cn.acebrand.acedex.generation.data  
component2 "cn.acebrand.acedex.generation.data  find "cn.acebrand.acedex.generation.data  iterator "cn.acebrand.acedex.generation.data  let "cn.acebrand.acedex.generation.data  	lowercase "cn.acebrand.acedex.generation.data  mapOf "cn.acebrand.acedex.generation.data  mutableMapOf "cn.acebrand.acedex.generation.data  set "cn.acebrand.acedex.generation.data  to "cn.acebrand.acedex.generation.data  PokemonData 2cn.acebrand.acedex.generation.data.Gen1PokemonData  data 2cn.acebrand.acedex.generation.data.Gen1PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen1PokemonData  to 2cn.acebrand.acedex.generation.data.Gen1PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen2PokemonData  data 2cn.acebrand.acedex.generation.data.Gen2PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen2PokemonData  to 2cn.acebrand.acedex.generation.data.Gen2PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen3PokemonData  data 2cn.acebrand.acedex.generation.data.Gen3PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen3PokemonData  to 2cn.acebrand.acedex.generation.data.Gen3PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen4PokemonData  data 2cn.acebrand.acedex.generation.data.Gen4PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen4PokemonData  to 2cn.acebrand.acedex.generation.data.Gen4PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen5PokemonData  data 2cn.acebrand.acedex.generation.data.Gen5PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen5PokemonData  to 2cn.acebrand.acedex.generation.data.Gen5PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen6PokemonData  data 2cn.acebrand.acedex.generation.data.Gen6PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen6PokemonData  to 2cn.acebrand.acedex.generation.data.Gen6PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen7PokemonData  data 2cn.acebrand.acedex.generation.data.Gen7PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen7PokemonData  to 2cn.acebrand.acedex.generation.data.Gen7PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen8PokemonData  data 2cn.acebrand.acedex.generation.data.Gen8PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen8PokemonData  to 2cn.acebrand.acedex.generation.data.Gen8PokemonData  PokemonData 2cn.acebrand.acedex.generation.data.Gen9PokemonData  data 2cn.acebrand.acedex.generation.data.Gen9PokemonData  mapOf 2cn.acebrand.acedex.generation.data.Gen9PokemonData  to 2cn.acebrand.acedex.generation.data.Gen9PokemonData  AceDex cn.acebrand.acedex.gui  AllGenerationsProgress cn.acebrand.acedex.gui  Any cn.acebrand.acedex.gui  AsyncGuiManager cn.acebrand.acedex.gui  Boolean cn.acebrand.acedex.gui  Bukkit cn.acebrand.acedex.gui  Class cn.acebrand.acedex.gui  CobblemonItemHelper cn.acebrand.acedex.gui  CompletableFuture cn.acebrand.acedex.gui  ConcurrentHashMap cn.acebrand.acedex.gui  
Deprecated cn.acebrand.acedex.gui  
DexMainGui cn.acebrand.acedex.gui  Double cn.acebrand.acedex.gui  Enchantment cn.acebrand.acedex.gui  EventHandler cn.acebrand.acedex.gui  
EventPriority cn.acebrand.acedex.gui  	Exception cn.acebrand.acedex.gui  	Executors cn.acebrand.acedex.gui  File cn.acebrand.acedex.gui  
Generation cn.acebrand.acedex.gui  GenerationProgress cn.acebrand.acedex.gui  GuiButtonCacheManager cn.acebrand.acedex.gui  IllegalArgumentException cn.acebrand.acedex.gui  Int cn.acebrand.acedex.gui  InterruptedException cn.acebrand.acedex.gui  	Inventory cn.acebrand.acedex.gui  InventoryClickEvent cn.acebrand.acedex.gui  InventoryCloseEvent cn.acebrand.acedex.gui  ItemFlag cn.acebrand.acedex.gui  	ItemStack cn.acebrand.acedex.gui  List cn.acebrand.acedex.gui  Listener cn.acebrand.acedex.gui  Long cn.acebrand.acedex.gui  Map cn.acebrand.acedex.gui  Material cn.acebrand.acedex.gui  MutableList cn.acebrand.acedex.gui  
MutableMap cn.acebrand.acedex.gui  NumberFormatException cn.acebrand.acedex.gui  Pair cn.acebrand.acedex.gui  Player cn.acebrand.acedex.gui  
PlayerGuiData cn.acebrand.acedex.gui  PlayerJoinEvent cn.acebrand.acedex.gui  PlayerPokemonData cn.acebrand.acedex.gui  PlayerQuitEvent cn.acebrand.acedex.gui  PokeBall cn.acebrand.acedex.gui  PokeBallItemCreator cn.acebrand.acedex.gui  	PokeBalls cn.acebrand.acedex.gui  PokemonDisplayItemBuilder cn.acebrand.acedex.gui  PokemonItemCreator cn.acebrand.acedex.gui  PokemonModelPreloader cn.acebrand.acedex.gui  PremiumRewardGui cn.acebrand.acedex.gui  Regex cn.acebrand.acedex.gui  ReplaceWith cn.acebrand.acedex.gui  Runnable cn.acebrand.acedex.gui  ScheduledExecutorService cn.acebrand.acedex.gui  String cn.acebrand.acedex.gui  System cn.acebrand.acedex.gui  Thread cn.acebrand.acedex.gui  TimeUnit cn.acebrand.acedex.gui  Triple cn.acebrand.acedex.gui  Void cn.acebrand.acedex.gui  Volatile cn.acebrand.acedex.gui  YamlConfiguration cn.acebrand.acedex.gui  any cn.acebrand.acedex.gui  
appendLine cn.acebrand.acedex.gui  buildString cn.acebrand.acedex.gui  buttonIdCacheFile cn.acebrand.acedex.gui  buttonIdToPokemonName cn.acebrand.acedex.gui  cacheStatusFile cn.acebrand.acedex.gui  caughtPokemonItemCache cn.acebrand.acedex.gui  cn cn.acebrand.acedex.gui  
coerceAtLeast cn.acebrand.acedex.gui  coerceIn cn.acebrand.acedex.gui  
component1 cn.acebrand.acedex.gui  
component2 cn.acebrand.acedex.gui  contains cn.acebrand.acedex.gui  convertNMSItemToBukkit cn.acebrand.acedex.gui  count cn.acebrand.acedex.gui  
createDefault cn.acebrand.acedex.gui  createPokeBallItem cn.acebrand.acedex.gui  createPokemonItemByName cn.acebrand.acedex.gui  createSimple cn.acebrand.acedex.gui  
distinctBy cn.acebrand.acedex.gui  	emptyList cn.acebrand.acedex.gui  emptyMap cn.acebrand.acedex.gui  emptySet cn.acebrand.acedex.gui  endsWith cn.acebrand.acedex.gui  filter cn.acebrand.acedex.gui  find cn.acebrand.acedex.gui  forEach cn.acebrand.acedex.gui  forEachIndexed cn.acebrand.acedex.gui  format cn.acebrand.acedex.gui  generationPokemonCache cn.acebrand.acedex.gui  	getOrNull cn.acebrand.acedex.gui  getOrPut cn.acebrand.acedex.gui   getPokemonChineseNameFromEnglish cn.acebrand.acedex.gui  invoke cn.acebrand.acedex.gui  
isNotEmpty cn.acebrand.acedex.gui  isPreloaded cn.acebrand.acedex.gui  iterator cn.acebrand.acedex.gui  java cn.acebrand.acedex.gui  joinToString cn.acebrand.acedex.gui  let cn.acebrand.acedex.gui  listOf cn.acebrand.acedex.gui  	lowercase cn.acebrand.acedex.gui  map cn.acebrand.acedex.gui  mapKeys cn.acebrand.acedex.gui  mapOf cn.acebrand.acedex.gui  minOf cn.acebrand.acedex.gui  
mutableListOf cn.acebrand.acedex.gui  mutableMapOf cn.acebrand.acedex.gui  org cn.acebrand.acedex.gui  playerGuiCache cn.acebrand.acedex.gui  plus cn.acebrand.acedex.gui  
plusAssign cn.acebrand.acedex.gui  pokemonButtonIds cn.acebrand.acedex.gui  preloadStatusFile cn.acebrand.acedex.gui  preloadedPokemonItems cn.acebrand.acedex.gui  refreshQueue cn.acebrand.acedex.gui  removePrefix cn.acebrand.acedex.gui  repeat cn.acebrand.acedex.gui  replace cn.acebrand.acedex.gui  replaceVariables cn.acebrand.acedex.gui  set cn.acebrand.acedex.gui  sortBy cn.acebrand.acedex.gui  sorted cn.acebrand.acedex.gui  split cn.acebrand.acedex.gui  
startsWith cn.acebrand.acedex.gui  step cn.acebrand.acedex.gui  	substring cn.acebrand.acedex.gui  substringAfter cn.acebrand.acedex.gui  testCobblemonIntegration cn.acebrand.acedex.gui  to cn.acebrand.acedex.gui  toInt cn.acebrand.acedex.gui  toIntOrNull cn.acebrand.acedex.gui  toList cn.acebrand.acedex.gui  toRegex cn.acebrand.acedex.gui  toSet cn.acebrand.acedex.gui  uncaughtPokemonItemCache cn.acebrand.acedex.gui  	uppercase cn.acebrand.acedex.gui  AceDex &cn.acebrand.acedex.gui.AsyncGuiManager  Boolean &cn.acebrand.acedex.gui.AsyncGuiManager  CompletableFuture &cn.acebrand.acedex.gui.AsyncGuiManager  ConcurrentHashMap &cn.acebrand.acedex.gui.AsyncGuiManager  EventHandler &cn.acebrand.acedex.gui.AsyncGuiManager  
EventPriority &cn.acebrand.acedex.gui.AsyncGuiManager  	Exception &cn.acebrand.acedex.gui.AsyncGuiManager  	Executors &cn.acebrand.acedex.gui.AsyncGuiManager  InterruptedException &cn.acebrand.acedex.gui.AsyncGuiManager  	Inventory &cn.acebrand.acedex.gui.AsyncGuiManager  Long &cn.acebrand.acedex.gui.AsyncGuiManager  
MutableMap &cn.acebrand.acedex.gui.AsyncGuiManager  Player &cn.acebrand.acedex.gui.AsyncGuiManager  
PlayerGuiData &cn.acebrand.acedex.gui.AsyncGuiManager  PlayerJoinEvent &cn.acebrand.acedex.gui.AsyncGuiManager  PlayerQuitEvent &cn.acebrand.acedex.gui.AsyncGuiManager  Runnable &cn.acebrand.acedex.gui.AsyncGuiManager  ScheduledExecutorService &cn.acebrand.acedex.gui.AsyncGuiManager  String &cn.acebrand.acedex.gui.AsyncGuiManager  System &cn.acebrand.acedex.gui.AsyncGuiManager  Thread &cn.acebrand.acedex.gui.AsyncGuiManager  TimeUnit &cn.acebrand.acedex.gui.AsyncGuiManager  
appendLine &cn.acebrand.acedex.gui.AsyncGuiManager  buildString &cn.acebrand.acedex.gui.AsyncGuiManager  
component1 &cn.acebrand.acedex.gui.AsyncGuiManager  
component2 &cn.acebrand.acedex.gui.AsyncGuiManager  createGenerationInventoryAsync &cn.acebrand.acedex.gui.AsyncGuiManager  createMainInventoryAsync &cn.acebrand.acedex.gui.AsyncGuiManager  executor &cn.acebrand.acedex.gui.AsyncGuiManager  initializePlayerGui &cn.acebrand.acedex.gui.AsyncGuiManager  
mutableListOf &cn.acebrand.acedex.gui.AsyncGuiManager  mutableMapOf &cn.acebrand.acedex.gui.AsyncGuiManager  playerGuiCache &cn.acebrand.acedex.gui.AsyncGuiManager  plugin &cn.acebrand.acedex.gui.AsyncGuiManager  
plusAssign &cn.acebrand.acedex.gui.AsyncGuiManager  refreshPlayerGuiImmediate &cn.acebrand.acedex.gui.AsyncGuiManager  refreshQueue &cn.acebrand.acedex.gui.AsyncGuiManager  set &cn.acebrand.acedex.gui.AsyncGuiManager  shutdown &cn.acebrand.acedex.gui.AsyncGuiManager  startPeriodicRefreshTask &cn.acebrand.acedex.gui.AsyncGuiManager  generationInventories 4cn.acebrand.acedex.gui.AsyncGuiManager.PlayerGuiData  
isInitialized 4cn.acebrand.acedex.gui.AsyncGuiManager.PlayerGuiData  lastUpdateTime 4cn.acebrand.acedex.gui.AsyncGuiManager.PlayerGuiData  
mainInventory 4cn.acebrand.acedex.gui.AsyncGuiManager.PlayerGuiData  AllGenerationsProgress !cn.acebrand.acedex.gui.DexMainGui  Bukkit !cn.acebrand.acedex.gui.DexMainGui  ConcurrentHashMap !cn.acebrand.acedex.gui.DexMainGui  GenerationProgress !cn.acebrand.acedex.gui.DexMainGui  	ItemStack !cn.acebrand.acedex.gui.DexMainGui  Material !cn.acebrand.acedex.gui.DexMainGui  PokeBallItemCreator !cn.acebrand.acedex.gui.DexMainGui  PokemonItemCreator !cn.acebrand.acedex.gui.DexMainGui  Regex !cn.acebrand.acedex.gui.DexMainGui  ReplaceWith !cn.acebrand.acedex.gui.DexMainGui  Runnable !cn.acebrand.acedex.gui.DexMainGui  String !cn.acebrand.acedex.gui.DexMainGui  System !cn.acebrand.acedex.gui.DexMainGui  Triple !cn.acebrand.acedex.gui.DexMainGui  addCloseButton !cn.acebrand.acedex.gui.DexMainGui  addDecorations !cn.acebrand.acedex.gui.DexMainGui  addGenerationButtonsWithData !cn.acebrand.acedex.gui.DexMainGui  addGenerationPokemon !cn.acebrand.acedex.gui.DexMainGui  addGenerationPokemonOptimized !cn.acebrand.acedex.gui.DexMainGui  addGenerationPokemonWithData !cn.acebrand.acedex.gui.DexMainGui  !addOverallProgressDisplayWithData !cn.acebrand.acedex.gui.DexMainGui  addOverallProgressRewardInfo !cn.acebrand.acedex.gui.DexMainGui  addOverallProgressRewardStatus !cn.acebrand.acedex.gui.DexMainGui  addPaginationButtons !cn.acebrand.acedex.gui.DexMainGui  addProgressInfo !cn.acebrand.acedex.gui.DexMainGui  addProgressInfoWithData !cn.acebrand.acedex.gui.DexMainGui  addProgressRewardInfoWithPlayer !cn.acebrand.acedex.gui.DexMainGui  addStatsButtonWithData !cn.acebrand.acedex.gui.DexMainGui  any !cn.acebrand.acedex.gui.DexMainGui  #calculateGenerationProgressRealtime !cn.acebrand.acedex.gui.DexMainGui   clearAllGenerationInventoryCache !cn.acebrand.acedex.gui.DexMainGui  clearAllMainInventoryCache !cn.acebrand.acedex.gui.DexMainGui  clearGenerationInventoryCache !cn.acebrand.acedex.gui.DexMainGui  clearMainInventoryCache !cn.acebrand.acedex.gui.DexMainGui  cn !cn.acebrand.acedex.gui.DexMainGui  
coerceAtLeast !cn.acebrand.acedex.gui.DexMainGui  coerceIn !cn.acebrand.acedex.gui.DexMainGui  
component1 !cn.acebrand.acedex.gui.DexMainGui  
component2 !cn.acebrand.acedex.gui.DexMainGui  contains !cn.acebrand.acedex.gui.DexMainGui  createGenerationButtonOptimized !cn.acebrand.acedex.gui.DexMainGui  "createGenerationInventoryForPlayer !cn.acebrand.acedex.gui.DexMainGui  createGenerationItem !cn.acebrand.acedex.gui.DexMainGui   createGenerationItemWithProgress !cn.acebrand.acedex.gui.DexMainGui  createMainInventoryForPlayer !cn.acebrand.acedex.gui.DexMainGui  
distinctBy !cn.acebrand.acedex.gui.DexMainGui  	emptyList !cn.acebrand.acedex.gui.DexMainGui  emptyMap !cn.acebrand.acedex.gui.DexMainGui  extractGenerationIdFromTitle !cn.acebrand.acedex.gui.DexMainGui  fillBackground !cn.acebrand.acedex.gui.DexMainGui  filter !cn.acebrand.acedex.gui.DexMainGui  find !cn.acebrand.acedex.gui.DexMainGui  forEachIndexed !cn.acebrand.acedex.gui.DexMainGui  forceCleanupPlayerState !cn.acebrand.acedex.gui.DexMainGui  format !cn.acebrand.acedex.gui.DexMainGui  getOrCreateGenerationInventory !cn.acebrand.acedex.gui.DexMainGui  !getOrCreateMainInventoryOptimized !cn.acebrand.acedex.gui.DexMainGui  getOrPut !cn.acebrand.acedex.gui.DexMainGui   getPokemonChineseNameFromEnglish !cn.acebrand.acedex.gui.DexMainGui  getRemainingCooldown !cn.acebrand.acedex.gui.DexMainGui  getTotalProgressBar !cn.acebrand.acedex.gui.DexMainGui  .handleAllGenerationsRewardClaimWithoutCooldown !cn.acebrand.acedex.gui.DexMainGui  handleGenerationGuiClick !cn.acebrand.acedex.gui.DexMainGui  handleGenerationRewardClaim !cn.acebrand.acedex.gui.DexMainGui  handleMainGuiClick !cn.acebrand.acedex.gui.DexMainGui  invoke !cn.acebrand.acedex.gui.DexMainGui  
isNotEmpty !cn.acebrand.acedex.gui.DexMainGui  
isPlayerInGui !cn.acebrand.acedex.gui.DexMainGui  isPlayerOnCooldown !cn.acebrand.acedex.gui.DexMainGui  joinToString !cn.acebrand.acedex.gui.DexMainGui  let !cn.acebrand.acedex.gui.DexMainGui  listOf !cn.acebrand.acedex.gui.DexMainGui  	lowercase !cn.acebrand.acedex.gui.DexMainGui  map !cn.acebrand.acedex.gui.DexMainGui  minOf !cn.acebrand.acedex.gui.DexMainGui  
mutableListOf !cn.acebrand.acedex.gui.DexMainGui  mutableMapOf !cn.acebrand.acedex.gui.DexMainGui  onPlayerDataUpdated !cn.acebrand.acedex.gui.DexMainGui  openGenerationGui !cn.acebrand.acedex.gui.DexMainGui  openGenerationGuiAsync !cn.acebrand.acedex.gui.DexMainGui  openGenerationMenu !cn.acebrand.acedex.gui.DexMainGui  openGuis !cn.acebrand.acedex.gui.DexMainGui  openMainGuiAsync !cn.acebrand.acedex.gui.DexMainGui  openMainMenu !cn.acebrand.acedex.gui.DexMainGui  org !cn.acebrand.acedex.gui.DexMainGui  playerCooldowns !cn.acebrand.acedex.gui.DexMainGui  playerGenerationInventories !cn.acebrand.acedex.gui.DexMainGui  playerMainInventories !cn.acebrand.acedex.gui.DexMainGui  playerPages !cn.acebrand.acedex.gui.DexMainGui  plugin !cn.acebrand.acedex.gui.DexMainGui  plus !cn.acebrand.acedex.gui.DexMainGui  
plusAssign !cn.acebrand.acedex.gui.DexMainGui  pokeBallItemCreator !cn.acebrand.acedex.gui.DexMainGui  pokemonItemCreator !cn.acebrand.acedex.gui.DexMainGui  refreshCurrentGui !cn.acebrand.acedex.gui.DexMainGui  repeat !cn.acebrand.acedex.gui.DexMainGui  replace !cn.acebrand.acedex.gui.DexMainGui  set !cn.acebrand.acedex.gui.DexMainGui  setPlayerCooldown !cn.acebrand.acedex.gui.DexMainGui  showAllGenerationsProgressAsync !cn.acebrand.acedex.gui.DexMainGui  "showAllGenerationsProgressWithData !cn.acebrand.acedex.gui.DexMainGui  (showClaimedOverallProgressRewardsDetails !cn.acebrand.acedex.gui.DexMainGui  !showClaimedProgressRewardsDetails !cn.acebrand.acedex.gui.DexMainGui  showPlayerStats !cn.acebrand.acedex.gui.DexMainGui  showProgressRewardsInOrder !cn.acebrand.acedex.gui.DexMainGui  sortBy !cn.acebrand.acedex.gui.DexMainGui  sorted !cn.acebrand.acedex.gui.DexMainGui  
startsWith !cn.acebrand.acedex.gui.DexMainGui  step !cn.acebrand.acedex.gui.DexMainGui  	substring !cn.acebrand.acedex.gui.DexMainGui  substringAfter !cn.acebrand.acedex.gui.DexMainGui  to !cn.acebrand.acedex.gui.DexMainGui  toInt !cn.acebrand.acedex.gui.DexMainGui  toList !cn.acebrand.acedex.gui.DexMainGui  toRegex !cn.acebrand.acedex.gui.DexMainGui  toSet !cn.acebrand.acedex.gui.DexMainGui  triggerDataUpdateCheck !cn.acebrand.acedex.gui.DexMainGui  updateGenerationGuiDataAsync !cn.acebrand.acedex.gui.DexMainGui  updateMainGuiDataAsync !cn.acebrand.acedex.gui.DexMainGui  ConcurrentHashMap ,cn.acebrand.acedex.gui.GuiButtonCacheManager  File ,cn.acebrand.acedex.gui.GuiButtonCacheManager  System ,cn.acebrand.acedex.gui.GuiButtonCacheManager  YamlConfiguration ,cn.acebrand.acedex.gui.GuiButtonCacheManager  basicButtonCache ,cn.acebrand.acedex.gui.GuiButtonCacheManager  cacheDir ,cn.acebrand.acedex.gui.GuiButtonCacheManager  cacheStatusFile ,cn.acebrand.acedex.gui.GuiButtonCacheManager  
clearCache ,cn.acebrand.acedex.gui.GuiButtonCacheManager  
component1 ,cn.acebrand.acedex.gui.GuiButtonCacheManager  
component2 ,cn.acebrand.acedex.gui.GuiButtonCacheManager  decorationCache ,cn.acebrand.acedex.gui.GuiButtonCacheManager  generationButtonCache ,cn.acebrand.acedex.gui.GuiButtonCacheManager  
getCacheStats ,cn.acebrand.acedex.gui.GuiButtonCacheManager  getCachedButton ,cn.acebrand.acedex.gui.GuiButtonCacheManager  getCachedDecorationItem ,cn.acebrand.acedex.gui.GuiButtonCacheManager  getCachedGenerationButton ,cn.acebrand.acedex.gui.GuiButtonCacheManager  getCachedNavigationButton ,cn.acebrand.acedex.gui.GuiButtonCacheManager  getCachedProgressButton ,cn.acebrand.acedex.gui.GuiButtonCacheManager  #getCachedProgressButtonByPercentage ,cn.acebrand.acedex.gui.GuiButtonCacheManager  initializeCacheDirectory ,cn.acebrand.acedex.gui.GuiButtonCacheManager  
isCacheLoaded ,cn.acebrand.acedex.gui.GuiButtonCacheManager  iterator ,cn.acebrand.acedex.gui.GuiButtonCacheManager  mapOf ,cn.acebrand.acedex.gui.GuiButtonCacheManager  navigationButtonCache ,cn.acebrand.acedex.gui.GuiButtonCacheManager  plugin ,cn.acebrand.acedex.gui.GuiButtonCacheManager  preloadAllButtons ,cn.acebrand.acedex.gui.GuiButtonCacheManager  preloadBasicButtons ,cn.acebrand.acedex.gui.GuiButtonCacheManager  preloadDecorationItems ,cn.acebrand.acedex.gui.GuiButtonCacheManager  preloadGenerationButtons ,cn.acebrand.acedex.gui.GuiButtonCacheManager  preloadNavigationButtons ,cn.acebrand.acedex.gui.GuiButtonCacheManager  preloadProgressButtons ,cn.acebrand.acedex.gui.GuiButtonCacheManager  progressButtonCache ,cn.acebrand.acedex.gui.GuiButtonCacheManager  saveCacheStatus ,cn.acebrand.acedex.gui.GuiButtonCacheManager  set ,cn.acebrand.acedex.gui.GuiButtonCacheManager  to ,cn.acebrand.acedex.gui.GuiButtonCacheManager  Class *cn.acebrand.acedex.gui.PokeBallItemCreator  CobblemonItemHelper *cn.acebrand.acedex.gui.PokeBallItemCreator  Enchantment *cn.acebrand.acedex.gui.PokeBallItemCreator  ItemFlag *cn.acebrand.acedex.gui.PokeBallItemCreator  	ItemStack *cn.acebrand.acedex.gui.PokeBallItemCreator  Material *cn.acebrand.acedex.gui.PokeBallItemCreator  	PokeBalls *cn.acebrand.acedex.gui.PokeBallItemCreator  applyOverallProgressDisplay *cn.acebrand.acedex.gui.PokeBallItemCreator  applyPokeBallDisplay *cn.acebrand.acedex.gui.PokeBallItemCreator  
coerceAtLeast *cn.acebrand.acedex.gui.PokeBallItemCreator  coerceIn *cn.acebrand.acedex.gui.PokeBallItemCreator  contains *cn.acebrand.acedex.gui.PokeBallItemCreator  convertCobblemonItemToBukkit *cn.acebrand.acedex.gui.PokeBallItemCreator  convertNMSItemToBukkit *cn.acebrand.acedex.gui.PokeBallItemCreator  createBaseOverallProgressItem *cn.acebrand.acedex.gui.PokeBallItemCreator  createCobblemonItem *cn.acebrand.acedex.gui.PokeBallItemCreator  createCobblemonItemByAPI *cn.acebrand.acedex.gui.PokeBallItemCreator  createFallbackPokeBallItem *cn.acebrand.acedex.gui.PokeBallItemCreator  createGenerationPokeBallItem *cn.acebrand.acedex.gui.PokeBallItemCreator  createNMSItemStack *cn.acebrand.acedex.gui.PokeBallItemCreator  createPokeBallItem *cn.acebrand.acedex.gui.PokeBallItemCreator  !createRealOverallProgressPokeBall *cn.acebrand.acedex.gui.PokeBallItemCreator  filter *cn.acebrand.acedex.gui.PokeBallItemCreator  getGenerationPokeBall *cn.acebrand.acedex.gui.PokeBallItemCreator  getGenerationProgressBar *cn.acebrand.acedex.gui.PokeBallItemCreator  getPokeBallThemeColor *cn.acebrand.acedex.gui.PokeBallItemCreator  getPokeBallThemeName *cn.acebrand.acedex.gui.PokeBallItemCreator  
isNotEmpty *cn.acebrand.acedex.gui.PokeBallItemCreator  listOf *cn.acebrand.acedex.gui.PokeBallItemCreator  	lowercase *cn.acebrand.acedex.gui.PokeBallItemCreator  map *cn.acebrand.acedex.gui.PokeBallItemCreator  
mutableListOf *cn.acebrand.acedex.gui.PokeBallItemCreator  org *cn.acebrand.acedex.gui.PokeBallItemCreator  parseGenerationMaterial *cn.acebrand.acedex.gui.PokeBallItemCreator  plugin *cn.acebrand.acedex.gui.PokeBallItemCreator  removePrefix *cn.acebrand.acedex.gui.PokeBallItemCreator  repeat *cn.acebrand.acedex.gui.PokeBallItemCreator  split *cn.acebrand.acedex.gui.PokeBallItemCreator  
startsWith *cn.acebrand.acedex.gui.PokeBallItemCreator  toIntOrNull *cn.acebrand.acedex.gui.PokeBallItemCreator  	uppercase *cn.acebrand.acedex.gui.PokeBallItemCreator  CobblemonItemHelper )cn.acebrand.acedex.gui.PokemonItemCreator  ConcurrentHashMap )cn.acebrand.acedex.gui.PokemonItemCreator  Enchantment )cn.acebrand.acedex.gui.PokemonItemCreator  File )cn.acebrand.acedex.gui.PokemonItemCreator  ItemFlag )cn.acebrand.acedex.gui.PokemonItemCreator  	ItemStack )cn.acebrand.acedex.gui.PokemonItemCreator  Material )cn.acebrand.acedex.gui.PokemonItemCreator  PokemonDisplayItemBuilder )cn.acebrand.acedex.gui.PokemonItemCreator  String )cn.acebrand.acedex.gui.PokemonItemCreator  System )cn.acebrand.acedex.gui.PokemonItemCreator  YamlConfiguration )cn.acebrand.acedex.gui.PokemonItemCreator  
appendLine )cn.acebrand.acedex.gui.PokemonItemCreator  applyPokemonDisplayInfo )cn.acebrand.acedex.gui.PokemonItemCreator  buildPokemonPlaceholders )cn.acebrand.acedex.gui.PokemonItemCreator  buildString )cn.acebrand.acedex.gui.PokemonItemCreator  buttonIdCacheFile )cn.acebrand.acedex.gui.PokemonItemCreator  buttonIdToPokemonName )cn.acebrand.acedex.gui.PokemonItemCreator  cacheDir )cn.acebrand.acedex.gui.PokemonItemCreator  cacheStatusFile )cn.acebrand.acedex.gui.PokemonItemCreator  caughtPokemonItemCache )cn.acebrand.acedex.gui.PokemonItemCreator  clearItemCache )cn.acebrand.acedex.gui.PokemonItemCreator  clearPokemonCache )cn.acebrand.acedex.gui.PokemonItemCreator  cn )cn.acebrand.acedex.gui.PokemonItemCreator  
component1 )cn.acebrand.acedex.gui.PokemonItemCreator  
component2 )cn.acebrand.acedex.gui.PokemonItemCreator  contains )cn.acebrand.acedex.gui.PokemonItemCreator  count )cn.acebrand.acedex.gui.PokemonItemCreator  createCustomMaterialItem )cn.acebrand.acedex.gui.PokemonItemCreator  
createDefault )cn.acebrand.acedex.gui.PokemonItemCreator  createFallbackItem )cn.acebrand.acedex.gui.PokemonItemCreator  createNewPokemonItem )cn.acebrand.acedex.gui.PokemonItemCreator  createPaperCustomModelDataItem )cn.acebrand.acedex.gui.PokemonItemCreator  createPokemonItem )cn.acebrand.acedex.gui.PokemonItemCreator  createPokemonItemByName )cn.acebrand.acedex.gui.PokemonItemCreator  createPokemonItemWithCache )cn.acebrand.acedex.gui.PokemonItemCreator  createSimple )cn.acebrand.acedex.gui.PokemonItemCreator  createSimplifiedPokemonItem )cn.acebrand.acedex.gui.PokemonItemCreator  defaultBuilder )cn.acebrand.acedex.gui.PokemonItemCreator  emptySet )cn.acebrand.acedex.gui.PokemonItemCreator  endsWith )cn.acebrand.acedex.gui.PokemonItemCreator  filter )cn.acebrand.acedex.gui.PokemonItemCreator  format )cn.acebrand.acedex.gui.PokemonItemCreator  generateButtonId )cn.acebrand.acedex.gui.PokemonItemCreator  getItemCacheStats )cn.acebrand.acedex.gui.PokemonItemCreator   getPokemonChineseNameFromEnglish )cn.acebrand.acedex.gui.PokemonItemCreator  getPokemonDisplayName )cn.acebrand.acedex.gui.PokemonItemCreator  initializeCacheDirectory )cn.acebrand.acedex.gui.PokemonItemCreator  
isNotEmpty )cn.acebrand.acedex.gui.PokemonItemCreator  iterator )cn.acebrand.acedex.gui.PokemonItemCreator  listOf )cn.acebrand.acedex.gui.PokemonItemCreator  loadCacheFromFiles )cn.acebrand.acedex.gui.PokemonItemCreator  	lowercase )cn.acebrand.acedex.gui.PokemonItemCreator  map )cn.acebrand.acedex.gui.PokemonItemCreator  mapKeys )cn.acebrand.acedex.gui.PokemonItemCreator  
mutableListOf )cn.acebrand.acedex.gui.PokemonItemCreator  mutableMapOf )cn.acebrand.acedex.gui.PokemonItemCreator  org )cn.acebrand.acedex.gui.PokemonItemCreator  parseCustomMaterial )cn.acebrand.acedex.gui.PokemonItemCreator  plugin )cn.acebrand.acedex.gui.PokemonItemCreator  replace )cn.acebrand.acedex.gui.PokemonItemCreator  replacePlaceholders )cn.acebrand.acedex.gui.PokemonItemCreator  	saveCache )cn.acebrand.acedex.gui.PokemonItemCreator  saveCacheToFiles )cn.acebrand.acedex.gui.PokemonItemCreator  set )cn.acebrand.acedex.gui.PokemonItemCreator  
simpleBuilder )cn.acebrand.acedex.gui.PokemonItemCreator  split )cn.acebrand.acedex.gui.PokemonItemCreator  
startsWith )cn.acebrand.acedex.gui.PokemonItemCreator  testCobblemonIntegration )cn.acebrand.acedex.gui.PokemonItemCreator  toIntOrNull )cn.acebrand.acedex.gui.PokemonItemCreator  uncaughtPokemonItemCache )cn.acebrand.acedex.gui.PokemonItemCreator  updateItemForCaughtStatus )cn.acebrand.acedex.gui.PokemonItemCreator  	uppercase )cn.acebrand.acedex.gui.PokemonItemCreator  CompletableFuture ,cn.acebrand.acedex.gui.PokemonModelPreloader  ConcurrentHashMap ,cn.acebrand.acedex.gui.PokemonModelPreloader  	Executors ,cn.acebrand.acedex.gui.PokemonModelPreloader  File ,cn.acebrand.acedex.gui.PokemonModelPreloader  System ,cn.acebrand.acedex.gui.PokemonModelPreloader  YamlConfiguration ,cn.acebrand.acedex.gui.PokemonModelPreloader  
appendLine ,cn.acebrand.acedex.gui.PokemonModelPreloader  buildString ,cn.acebrand.acedex.gui.PokemonModelPreloader  buttonIdCacheFile ,cn.acebrand.acedex.gui.PokemonModelPreloader  cacheDir ,cn.acebrand.acedex.gui.PokemonModelPreloader  
clearCache ,cn.acebrand.acedex.gui.PokemonModelPreloader  
component1 ,cn.acebrand.acedex.gui.PokemonModelPreloader  
component2 ,cn.acebrand.acedex.gui.PokemonModelPreloader  createBasePokemonItem ,cn.acebrand.acedex.gui.PokemonModelPreloader  	emptyList ,cn.acebrand.acedex.gui.PokemonModelPreloader  executor ,cn.acebrand.acedex.gui.PokemonModelPreloader  generateItemKey ,cn.acebrand.acedex.gui.PokemonModelPreloader  generatePokemonModels ,cn.acebrand.acedex.gui.PokemonModelPreloader  generationPokemonCache ,cn.acebrand.acedex.gui.PokemonModelPreloader  
getCacheStats ,cn.acebrand.acedex.gui.PokemonModelPreloader  getGenerationPokemonList ,cn.acebrand.acedex.gui.PokemonModelPreloader  getPokemonNameByButtonId ,cn.acebrand.acedex.gui.PokemonModelPreloader  getPreloadedPokemonItem ,cn.acebrand.acedex.gui.PokemonModelPreloader  isPokemonButton ,cn.acebrand.acedex.gui.PokemonModelPreloader  isPreloadComplete ,cn.acebrand.acedex.gui.PokemonModelPreloader  isPreloaded ,cn.acebrand.acedex.gui.PokemonModelPreloader  iterator ,cn.acebrand.acedex.gui.PokemonModelPreloader  
loadFromCache ,cn.acebrand.acedex.gui.PokemonModelPreloader  	lowercase ,cn.acebrand.acedex.gui.PokemonModelPreloader  
mutableListOf ,cn.acebrand.acedex.gui.PokemonModelPreloader  plugin ,cn.acebrand.acedex.gui.PokemonModelPreloader  pokemonButtonIds ,cn.acebrand.acedex.gui.PokemonModelPreloader  preloadGenerationPokemon ,cn.acebrand.acedex.gui.PokemonModelPreloader  preloadStatusFile ,cn.acebrand.acedex.gui.PokemonModelPreloader  preloadedPokemonItems ,cn.acebrand.acedex.gui.PokemonModelPreloader  regenerateGenerationCache ,cn.acebrand.acedex.gui.PokemonModelPreloader  saveToCache ,cn.acebrand.acedex.gui.PokemonModelPreloader  set ,cn.acebrand.acedex.gui.PokemonModelPreloader  shutdown ,cn.acebrand.acedex.gui.PokemonModelPreloader  startPreloading ,cn.acebrand.acedex.gui.PokemonModelPreloader  
startsWith ,cn.acebrand.acedex.gui.PokemonModelPreloader  toList ,cn.acebrand.acedex.gui.PokemonModelPreloader  Bukkit 'cn.acebrand.acedex.gui.PremiumRewardGui  GenerationProgress 'cn.acebrand.acedex.gui.PremiumRewardGui  	ItemStack 'cn.acebrand.acedex.gui.PremiumRewardGui  Pair 'cn.acebrand.acedex.gui.PremiumRewardGui  PokeBallItemCreator 'cn.acebrand.acedex.gui.PremiumRewardGui  Runnable 'cn.acebrand.acedex.gui.PremiumRewardGui  String 'cn.acebrand.acedex.gui.PremiumRewardGui  System 'cn.acebrand.acedex.gui.PremiumRewardGui  Triple 'cn.acebrand.acedex.gui.PremiumRewardGui  addCloseButton 'cn.acebrand.acedex.gui.PremiumRewardGui  addPremiumGenerationButtons 'cn.acebrand.acedex.gui.PremiumRewardGui  addPremiumOverallProgressButton 'cn.acebrand.acedex.gui.PremiumRewardGui  %addPremiumOverallProgressRewardStatus 'cn.acebrand.acedex.gui.PremiumRewardGui  addPremiumProgressRewardInfo 'cn.acebrand.acedex.gui.PremiumRewardGui  cn 'cn.acebrand.acedex.gui.PremiumRewardGui  
component1 'cn.acebrand.acedex.gui.PremiumRewardGui  
component2 'cn.acebrand.acedex.gui.PremiumRewardGui  contains 'cn.acebrand.acedex.gui.PremiumRewardGui  createPremiumGenerationButton 'cn.acebrand.acedex.gui.PremiumRewardGui  %createPremiumGenerationButtonForCache 'cn.acebrand.acedex.gui.PremiumRewardGui  "createPremiumOverallButtonForCache 'cn.acebrand.acedex.gui.PremiumRewardGui  createPremiumProgressButtonItem 'cn.acebrand.acedex.gui.PremiumRewardGui  fillBackground 'cn.acebrand.acedex.gui.PremiumRewardGui  find 'cn.acebrand.acedex.gui.PremiumRewardGui  forceCleanupPlayerState 'cn.acebrand.acedex.gui.PremiumRewardGui  format 'cn.acebrand.acedex.gui.PremiumRewardGui  !getDefaultPremiumProgressMaterial 'cn.acebrand.acedex.gui.PremiumRewardGui  	getOrNull 'cn.acebrand.acedex.gui.PremiumRewardGui  getOrPut 'cn.acebrand.acedex.gui.PremiumRewardGui  getRemainingCooldown 'cn.acebrand.acedex.gui.PremiumRewardGui  &handlePremiumAllGenerationsRewardClaim 'cn.acebrand.acedex.gui.PremiumRewardGui  "handlePremiumGenerationRewardClaim 'cn.acebrand.acedex.gui.PremiumRewardGui  handlePremiumMainGuiClick 'cn.acebrand.acedex.gui.PremiumRewardGui  
initialize 'cn.acebrand.acedex.gui.PremiumRewardGui  
isNotEmpty 'cn.acebrand.acedex.gui.PremiumRewardGui  isPlayerOnCooldown 'cn.acebrand.acedex.gui.PremiumRewardGui  java 'cn.acebrand.acedex.gui.PremiumRewardGui  joinToString 'cn.acebrand.acedex.gui.PremiumRewardGui  let 'cn.acebrand.acedex.gui.PremiumRewardGui  listOf 'cn.acebrand.acedex.gui.PremiumRewardGui  
mutableListOf 'cn.acebrand.acedex.gui.PremiumRewardGui  mutableMapOf 'cn.acebrand.acedex.gui.PremiumRewardGui  onPremiumPlayerDataUpdated 'cn.acebrand.acedex.gui.PremiumRewardGui  openGuis 'cn.acebrand.acedex.gui.PremiumRewardGui  openPremiumRewardGui 'cn.acebrand.acedex.gui.PremiumRewardGui  org 'cn.acebrand.acedex.gui.PremiumRewardGui  parseButtonMaterial 'cn.acebrand.acedex.gui.PremiumRewardGui  playerCooldowns 'cn.acebrand.acedex.gui.PremiumRewardGui  playerPremiumInventories 'cn.acebrand.acedex.gui.PremiumRewardGui  plugin 'cn.acebrand.acedex.gui.PremiumRewardGui  preloadPremiumButtonCache 'cn.acebrand.acedex.gui.PremiumRewardGui  preloadPremiumGenerationButtons 'cn.acebrand.acedex.gui.PremiumRewardGui  preloadPremiumOverallButtons 'cn.acebrand.acedex.gui.PremiumRewardGui  replaceVariables 'cn.acebrand.acedex.gui.PremiumRewardGui  savePremiumButtonToCache 'cn.acebrand.acedex.gui.PremiumRewardGui  savePremiumOverallButtonToCache 'cn.acebrand.acedex.gui.PremiumRewardGui  set 'cn.acebrand.acedex.gui.PremiumRewardGui  setPlayerCooldown 'cn.acebrand.acedex.gui.PremiumRewardGui  /showClaimedPremiumOverallProgressRewardsDetails 'cn.acebrand.acedex.gui.PremiumRewardGui  (showClaimedPremiumProgressRewardsDetails 'cn.acebrand.acedex.gui.PremiumRewardGui  !showPremiumAllGenerationsProgress 'cn.acebrand.acedex.gui.PremiumRewardGui  showPremiumGenerationDetails 'cn.acebrand.acedex.gui.PremiumRewardGui  !showPremiumProgressRewardsInOrder 'cn.acebrand.acedex.gui.PremiumRewardGui  sortBy 'cn.acebrand.acedex.gui.PremiumRewardGui  sorted 'cn.acebrand.acedex.gui.PremiumRewardGui  split 'cn.acebrand.acedex.gui.PremiumRewardGui  syncPremiumRewardData 'cn.acebrand.acedex.gui.PremiumRewardGui  toIntOrNull 'cn.acebrand.acedex.gui.PremiumRewardGui  toList 'cn.acebrand.acedex.gui.PremiumRewardGui  updatePremiumGuiDataAsync 'cn.acebrand.acedex.gui.PremiumRewardGui  AceDex cn.acebrand.acedex.gui.builder  Boolean cn.acebrand.acedex.gui.builder  Enchantment cn.acebrand.acedex.gui.builder  	Exception cn.acebrand.acedex.gui.builder  ItemFlag cn.acebrand.acedex.gui.builder  	ItemStack cn.acebrand.acedex.gui.builder  LGMenuIntegration cn.acebrand.acedex.gui.builder  List cn.acebrand.acedex.gui.builder  Map cn.acebrand.acedex.gui.builder  Pokemon cn.acebrand.acedex.gui.builder  PokemonDisplayItemBuilder cn.acebrand.acedex.gui.builder  String cn.acebrand.acedex.gui.builder  
component1 cn.acebrand.acedex.gui.builder  
component2 cn.acebrand.acedex.gui.builder  
createPokemon cn.acebrand.acedex.gui.builder  createPokemonItem cn.acebrand.acedex.gui.builder  forEach cn.acebrand.acedex.gui.builder  format cn.acebrand.acedex.gui.builder  getPokemonDisplayName cn.acebrand.acedex.gui.builder  getPokemonTypes cn.acebrand.acedex.gui.builder  getSpeciesByName cn.acebrand.acedex.gui.builder  listOf cn.acebrand.acedex.gui.builder  	lowercase cn.acebrand.acedex.gui.builder  map cn.acebrand.acedex.gui.builder  mutableMapOf cn.acebrand.acedex.gui.builder  replace cn.acebrand.acedex.gui.builder  set cn.acebrand.acedex.gui.builder  AceDex 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  Boolean 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  	Companion 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  Enchantment 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  	Exception 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  ItemFlag 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  	ItemStack 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  LGMenuIntegration 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  List 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  Map 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  Pokemon 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  PokemonDisplayItemBuilder 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  String 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  applyCustomDisplay 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  build 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  buildPlaceholders 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  
component1 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  
component2 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  
createDefault 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  
createPokemon 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  createPokemonItem 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  createSimple 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  format 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  getPokemonDisplayName 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  getPokemonTypes 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  getSpeciesByName 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  listOf 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  loreTemplate 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  	lowercase 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  map 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  mutableMapOf 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  nameTemplate 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  plugin 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  replace 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  replacePlaceholders 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  set 8cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder  Enchantment Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  ItemFlag Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  LGMenuIntegration Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  PokemonDisplayItemBuilder Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  String Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  
component1 Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  
component2 Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  
createDefault Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  
createPokemon Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  createPokemonItem Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  createSimple Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  format Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  getPokemonDisplayName Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  getPokemonTypes Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  getSpeciesByName Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  listOf Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  	lowercase Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  map Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  mutableMapOf Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  replace Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  set Bcn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder.Companion  acebrand cn.acebrand.acedex.gui.cn  acedex "cn.acebrand.acedex.gui.cn.acebrand  
generation )cn.acebrand.acedex.gui.cn.acebrand.acedex  
Generation 4cn.acebrand.acedex.gui.cn.acebrand.acedex.generation  bukkit cn.acebrand.acedex.gui.org  event !cn.acebrand.acedex.gui.org.bukkit  	inventory 'cn.acebrand.acedex.gui.org.bukkit.event  player 'cn.acebrand.acedex.gui.org.bukkit.event  	ClickType 1cn.acebrand.acedex.gui.org.bukkit.event.inventory  InventoryCloseEvent 1cn.acebrand.acedex.gui.org.bukkit.event.inventory  PlayerQuitEvent .cn.acebrand.acedex.gui.org.bukkit.event.player  AceDex cn.acebrand.acedex.integration  Boolean cn.acebrand.acedex.integration  Int cn.acebrand.acedex.integration  NumberFormatException cn.acebrand.acedex.integration  PlaceholderAPIExpansion cn.acebrand.acedex.integration  PlaceholderExpansion cn.acebrand.acedex.integration  Player cn.acebrand.acedex.integration  Regex cn.acebrand.acedex.integration  String cn.acebrand.acedex.integration  
coerceAtLeast cn.acebrand.acedex.integration  coerceIn cn.acebrand.acedex.integration  find cn.acebrand.acedex.integration  	lowercase cn.acebrand.acedex.integration  	minOrNull cn.acebrand.acedex.integration  
mutableListOf cn.acebrand.acedex.integration  repeat cn.acebrand.acedex.integration  toInt cn.acebrand.acedex.integration  toIntOrNull cn.acebrand.acedex.integration  Regex 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  
coerceAtLeast 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  coerceIn 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  createProgressBar 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  createProgressBarWithPercentage 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  find 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  handleGenerationPlaceholder 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  handleProgressRewardPlaceholder 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  	lowercase 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  	minOrNull 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  
mutableListOf 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  plugin 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  register 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  repeat 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  toInt 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  toIntOrNull 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  
unregister 6cn.acebrand.acedex.integration.PlaceholderAPIExpansion  API_BASE_URL cn.acebrand.acedex.license  AceDex cn.acebrand.acedex.license  Base64 cn.acebrand.acedex.license  Boolean cn.acebrand.acedex.license  BufferedReader cn.acebrand.acedex.license  	ByteArray cn.acebrand.acedex.license  	Character cn.acebrand.acedex.license  ERROR_MESSAGES cn.acebrand.acedex.license  	Exception cn.acebrand.acedex.license  	Executors cn.acebrand.acedex.license  GSON cn.acebrand.acedex.license  GsonBuilder cn.acebrand.acedex.license  HEARTBEAT_ENDPOINT cn.acebrand.acedex.license  HttpURLConnection cn.acebrand.acedex.license  IOException cn.acebrand.acedex.license  InetAddress cn.acebrand.acedex.license  InputStream cn.acebrand.acedex.license  InputStreamReader cn.acebrand.acedex.license  Int cn.acebrand.acedex.license  InterruptedException cn.acebrand.acedex.license  
JsonObject cn.acebrand.acedex.license  
KeyFactory cn.acebrand.acedex.license  LicenseInfo cn.acebrand.acedex.license  LicenseManager cn.acebrand.acedex.license  
PRODUCT_ID cn.acebrand.acedex.license  RSA_PUBLIC_KEY cn.acebrand.acedex.license  ScheduledExecutorService cn.acebrand.acedex.license  SecureRandom cn.acebrand.acedex.license  Set cn.acebrand.acedex.license  	Signature cn.acebrand.acedex.license  StandardCharsets cn.acebrand.acedex.license  String cn.acebrand.acedex.license  
StringBuilder cn.acebrand.acedex.license  System cn.acebrand.acedex.license  TEAM_ID cn.acebrand.acedex.license  TIMEOUT_MILLIS cn.acebrand.acedex.license  Thread cn.acebrand.acedex.license  TimeUnit cn.acebrand.acedex.license  URI cn.acebrand.acedex.license  UUID cn.acebrand.acedex.license  VERIFY_ENDPOINT cn.acebrand.acedex.license  VERSION cn.acebrand.acedex.license  X509EncodedKeySpec cn.acebrand.acedex.license  format cn.acebrand.acedex.license  invoke cn.acebrand.acedex.license  isBlank cn.acebrand.acedex.license  java cn.acebrand.acedex.license  let cn.acebrand.acedex.license  mapOf cn.acebrand.acedex.license  replace cn.acebrand.acedex.license  setOf cn.acebrand.acedex.license  step cn.acebrand.acedex.license  to cn.acebrand.acedex.license  toByteArray cn.acebrand.acedex.license  toRegex cn.acebrand.acedex.license  
trimIndent cn.acebrand.acedex.license  until cn.acebrand.acedex.license  use cn.acebrand.acedex.license  API_BASE_URL )cn.acebrand.acedex.license.LicenseManager  AceDex )cn.acebrand.acedex.license.LicenseManager  Base64 )cn.acebrand.acedex.license.LicenseManager  Boolean )cn.acebrand.acedex.license.LicenseManager  BufferedReader )cn.acebrand.acedex.license.LicenseManager  	ByteArray )cn.acebrand.acedex.license.LicenseManager  	Character )cn.acebrand.acedex.license.LicenseManager  ERROR_MESSAGES )cn.acebrand.acedex.license.LicenseManager  	Exception )cn.acebrand.acedex.license.LicenseManager  	Executors )cn.acebrand.acedex.license.LicenseManager  GSON )cn.acebrand.acedex.license.LicenseManager  GsonBuilder )cn.acebrand.acedex.license.LicenseManager  HEARTBEAT_ENDPOINT )cn.acebrand.acedex.license.LicenseManager  HttpURLConnection )cn.acebrand.acedex.license.LicenseManager  IOException )cn.acebrand.acedex.license.LicenseManager  InetAddress )cn.acebrand.acedex.license.LicenseManager  InputStream )cn.acebrand.acedex.license.LicenseManager  InputStreamReader )cn.acebrand.acedex.license.LicenseManager  Int )cn.acebrand.acedex.license.LicenseManager  InterruptedException )cn.acebrand.acedex.license.LicenseManager  
JsonObject )cn.acebrand.acedex.license.LicenseManager  
KeyFactory )cn.acebrand.acedex.license.LicenseManager  LicenseInfo )cn.acebrand.acedex.license.LicenseManager  
PRODUCT_ID )cn.acebrand.acedex.license.LicenseManager  RSA_PUBLIC_KEY )cn.acebrand.acedex.license.LicenseManager  ScheduledExecutorService )cn.acebrand.acedex.license.LicenseManager  SecureRandom )cn.acebrand.acedex.license.LicenseManager  Set )cn.acebrand.acedex.license.LicenseManager  	Signature )cn.acebrand.acedex.license.LicenseManager  StandardCharsets )cn.acebrand.acedex.license.LicenseManager  String )cn.acebrand.acedex.license.LicenseManager  
StringBuilder )cn.acebrand.acedex.license.LicenseManager  System )cn.acebrand.acedex.license.LicenseManager  TEAM_ID )cn.acebrand.acedex.license.LicenseManager  TIMEOUT_MILLIS )cn.acebrand.acedex.license.LicenseManager  Thread )cn.acebrand.acedex.license.LicenseManager  TimeUnit )cn.acebrand.acedex.license.LicenseManager  URI )cn.acebrand.acedex.license.LicenseManager  UUID )cn.acebrand.acedex.license.LicenseManager  VERIFY_ENDPOINT )cn.acebrand.acedex.license.LicenseManager  VERSION )cn.acebrand.acedex.license.LicenseManager  X509EncodedKeySpec )cn.acebrand.acedex.license.LicenseManager  buildUserAgent )cn.acebrand.acedex.license.LicenseManager  
bytesToHex )cn.acebrand.acedex.license.LicenseManager  deviceIdentifier )cn.acebrand.acedex.license.LicenseManager  fetchAndHandleResponse )cn.acebrand.acedex.license.LicenseManager  format )cn.acebrand.acedex.license.LicenseManager  generateHardwareIdentifier )cn.acebrand.acedex.license.LicenseManager  generateRandomChallenge )cn.acebrand.acedex.license.LicenseManager  handleJsonResponse )cn.acebrand.acedex.license.LicenseManager  hexStringToByteArray )cn.acebrand.acedex.license.LicenseManager  invoke )cn.acebrand.acedex.license.LicenseManager  isBlank )cn.acebrand.acedex.license.LicenseManager  isLicenseValid )cn.acebrand.acedex.license.LicenseManager  isValid )cn.acebrand.acedex.license.LicenseManager  java )cn.acebrand.acedex.license.LicenseManager  let )cn.acebrand.acedex.license.LicenseManager  licenseInfo )cn.acebrand.acedex.license.LicenseManager  mapOf )cn.acebrand.acedex.license.LicenseManager  plugin )cn.acebrand.acedex.license.LicenseManager  replace )cn.acebrand.acedex.license.LicenseManager  	scheduler )cn.acebrand.acedex.license.LicenseManager  
sendHeartbeat )cn.acebrand.acedex.license.LicenseManager  setOf )cn.acebrand.acedex.license.LicenseManager  setupHeartbeatScheduler )cn.acebrand.acedex.license.LicenseManager  shutdown )cn.acebrand.acedex.license.LicenseManager  step )cn.acebrand.acedex.license.LicenseManager  to )cn.acebrand.acedex.license.LicenseManager  toByteArray )cn.acebrand.acedex.license.LicenseManager  toRegex )cn.acebrand.acedex.license.LicenseManager  
trimIndent )cn.acebrand.acedex.license.LicenseManager  until )cn.acebrand.acedex.license.LicenseManager  use )cn.acebrand.acedex.license.LicenseManager  validateChallenge )cn.acebrand.acedex.license.LicenseManager  validateLicenseKey )cn.acebrand.acedex.license.LicenseManager  validateResponse )cn.acebrand.acedex.license.LicenseManager  verifySignature )cn.acebrand.acedex.license.LicenseManager  API_BASE_URL 3cn.acebrand.acedex.license.LicenseManager.Companion  Base64 3cn.acebrand.acedex.license.LicenseManager.Companion  BufferedReader 3cn.acebrand.acedex.license.LicenseManager.Companion  	ByteArray 3cn.acebrand.acedex.license.LicenseManager.Companion  	Character 3cn.acebrand.acedex.license.LicenseManager.Companion  ERROR_MESSAGES 3cn.acebrand.acedex.license.LicenseManager.Companion  	Executors 3cn.acebrand.acedex.license.LicenseManager.Companion  GSON 3cn.acebrand.acedex.license.LicenseManager.Companion  GsonBuilder 3cn.acebrand.acedex.license.LicenseManager.Companion  HEARTBEAT_ENDPOINT 3cn.acebrand.acedex.license.LicenseManager.Companion  HttpURLConnection 3cn.acebrand.acedex.license.LicenseManager.Companion  IOException 3cn.acebrand.acedex.license.LicenseManager.Companion  InetAddress 3cn.acebrand.acedex.license.LicenseManager.Companion  InputStreamReader 3cn.acebrand.acedex.license.LicenseManager.Companion  
JsonObject 3cn.acebrand.acedex.license.LicenseManager.Companion  
KeyFactory 3cn.acebrand.acedex.license.LicenseManager.Companion  LicenseInfo 3cn.acebrand.acedex.license.LicenseManager.Companion  
PRODUCT_ID 3cn.acebrand.acedex.license.LicenseManager.Companion  RSA_PUBLIC_KEY 3cn.acebrand.acedex.license.LicenseManager.Companion  SecureRandom 3cn.acebrand.acedex.license.LicenseManager.Companion  	Signature 3cn.acebrand.acedex.license.LicenseManager.Companion  StandardCharsets 3cn.acebrand.acedex.license.LicenseManager.Companion  String 3cn.acebrand.acedex.license.LicenseManager.Companion  
StringBuilder 3cn.acebrand.acedex.license.LicenseManager.Companion  System 3cn.acebrand.acedex.license.LicenseManager.Companion  TEAM_ID 3cn.acebrand.acedex.license.LicenseManager.Companion  TIMEOUT_MILLIS 3cn.acebrand.acedex.license.LicenseManager.Companion  Thread 3cn.acebrand.acedex.license.LicenseManager.Companion  TimeUnit 3cn.acebrand.acedex.license.LicenseManager.Companion  URI 3cn.acebrand.acedex.license.LicenseManager.Companion  UUID 3cn.acebrand.acedex.license.LicenseManager.Companion  VERIFY_ENDPOINT 3cn.acebrand.acedex.license.LicenseManager.Companion  VERSION 3cn.acebrand.acedex.license.LicenseManager.Companion  X509EncodedKeySpec 3cn.acebrand.acedex.license.LicenseManager.Companion  format 3cn.acebrand.acedex.license.LicenseManager.Companion  invoke 3cn.acebrand.acedex.license.LicenseManager.Companion  isBlank 3cn.acebrand.acedex.license.LicenseManager.Companion  java 3cn.acebrand.acedex.license.LicenseManager.Companion  let 3cn.acebrand.acedex.license.LicenseManager.Companion  mapOf 3cn.acebrand.acedex.license.LicenseManager.Companion  replace 3cn.acebrand.acedex.license.LicenseManager.Companion  setOf 3cn.acebrand.acedex.license.LicenseManager.Companion  step 3cn.acebrand.acedex.license.LicenseManager.Companion  to 3cn.acebrand.acedex.license.LicenseManager.Companion  toByteArray 3cn.acebrand.acedex.license.LicenseManager.Companion  toRegex 3cn.acebrand.acedex.license.LicenseManager.Companion  
trimIndent 3cn.acebrand.acedex.license.LicenseManager.Companion  until 3cn.acebrand.acedex.license.LicenseManager.Companion  use 3cn.acebrand.acedex.license.LicenseManager.Companion  features 5cn.acebrand.acedex.license.LicenseManager.LicenseInfo  AceDex cn.acebrand.acedex.listener  Boolean cn.acebrand.acedex.listener  Bukkit cn.acebrand.acedex.listener  CobblemonEventListener cn.acebrand.acedex.listener  CobblemonEvents cn.acebrand.acedex.listener  CobblemonItemHelper cn.acebrand.acedex.listener  CobblemonListener cn.acebrand.acedex.listener  EventHandler cn.acebrand.acedex.listener  	Exception cn.acebrand.acedex.listener  Int cn.acebrand.acedex.listener  InventoryCloseEvent cn.acebrand.acedex.listener  List cn.acebrand.acedex.listener  Listener cn.acebrand.acedex.listener  Player cn.acebrand.acedex.listener  PlayerJoinEvent cn.acebrand.acedex.listener  PlayerPokemonData cn.acebrand.acedex.listener  PlayerQuitEvent cn.acebrand.acedex.listener  Pokemon cn.acebrand.acedex.listener  PokemonCaptureListener cn.acebrand.acedex.listener  PokemonCapturedEvent cn.acebrand.acedex.listener  PokemonInfo cn.acebrand.acedex.listener  ReleasePokemonEvent cn.acebrand.acedex.listener  Runnable cn.acebrand.acedex.listener  ServerPlayer cn.acebrand.acedex.listener  String cn.acebrand.acedex.listener  System cn.acebrand.acedex.listener  any cn.acebrand.acedex.listener  cn cn.acebrand.acedex.listener  contains cn.acebrand.acedex.listener   getPokemonChineseNameFromEnglish cn.acebrand.acedex.listener  getSpeciesByName cn.acebrand.acedex.listener  	lowercase cn.acebrand.acedex.listener  org cn.acebrand.acedex.listener  plus cn.acebrand.acedex.listener  
toMutableList cn.acebrand.acedex.listener  registerEvents 2cn.acebrand.acedex.listener.CobblemonEventListener  Runnable -cn.acebrand.acedex.listener.CobblemonListener  System -cn.acebrand.acedex.listener.CobblemonListener  contains -cn.acebrand.acedex.listener.CobblemonListener  isPokemonRelatedInventory -cn.acebrand.acedex.listener.CobblemonListener  	lowercase -cn.acebrand.acedex.listener.CobblemonListener  plugin -cn.acebrand.acedex.listener.CobblemonListener  Bukkit 2cn.acebrand.acedex.listener.PokemonCaptureListener  CobblemonEvents 2cn.acebrand.acedex.listener.PokemonCaptureListener  CobblemonItemHelper 2cn.acebrand.acedex.listener.PokemonCaptureListener  PlayerPokemonData 2cn.acebrand.acedex.listener.PokemonCaptureListener  PokemonInfo 2cn.acebrand.acedex.listener.PokemonCaptureListener  Runnable 2cn.acebrand.acedex.listener.PokemonCaptureListener  addMultiplePokemonToPlayerData 2cn.acebrand.acedex.listener.PokemonCaptureListener  addPokemonToPlayerData 2cn.acebrand.acedex.listener.PokemonCaptureListener  any 2cn.acebrand.acedex.listener.PokemonCaptureListener  checkProgressRewards 2cn.acebrand.acedex.listener.PokemonCaptureListener  cn 2cn.acebrand.acedex.listener.PokemonCaptureListener  createPokemonInfo 2cn.acebrand.acedex.listener.PokemonCaptureListener  getGenerationFromDexNumber 2cn.acebrand.acedex.listener.PokemonCaptureListener   getPokemonChineseNameFromEnglish 2cn.acebrand.acedex.listener.PokemonCaptureListener  getSpeciesByName 2cn.acebrand.acedex.listener.PokemonCaptureListener  handlePokemonCaptured 2cn.acebrand.acedex.listener.PokemonCaptureListener  handlePokemonReleased 2cn.acebrand.acedex.listener.PokemonCaptureListener  plugin 2cn.acebrand.acedex.listener.PokemonCaptureListener  plus 2cn.acebrand.acedex.listener.PokemonCaptureListener  registerCobblemonEvents 2cn.acebrand.acedex.listener.PokemonCaptureListener  saveCapturedPokemonToLocalData 2cn.acebrand.acedex.listener.PokemonCaptureListener  
toMutableList 2cn.acebrand.acedex.listener.PokemonCaptureListener  Post /cn.acebrand.acedex.listener.ReleasePokemonEvent  bukkit cn.acebrand.acedex.listener.org  entity &cn.acebrand.acedex.listener.org.bukkit  Player -cn.acebrand.acedex.listener.org.bukkit.entity  AceDex cn.acebrand.acedex.pokemon  AllGenerationsProgress cn.acebrand.acedex.pokemon  Boolean cn.acebrand.acedex.pokemon  CompletableFuture cn.acebrand.acedex.pokemon  	Exception cn.acebrand.acedex.pokemon  File cn.acebrand.acedex.pokemon  
FileReader cn.acebrand.acedex.pokemon  
FileWriter cn.acebrand.acedex.pokemon  GenerationProgress cn.acebrand.acedex.pokemon  Gson cn.acebrand.acedex.pokemon  GsonBuilder cn.acebrand.acedex.pokemon  Int cn.acebrand.acedex.pokemon  List cn.acebrand.acedex.pokemon  Long cn.acebrand.acedex.pokemon  PCStore cn.acebrand.acedex.pokemon  Player cn.acebrand.acedex.pokemon  PlayerDataStorage cn.acebrand.acedex.pokemon  PlayerPartyStore cn.acebrand.acedex.pokemon  PlayerPokemonData cn.acebrand.acedex.pokemon  Pokemon cn.acebrand.acedex.pokemon  PokemonDetector cn.acebrand.acedex.pokemon  PokemonGen1Mapping cn.acebrand.acedex.pokemon  PokemonGen2Mapping cn.acebrand.acedex.pokemon  PokemonGen3Mapping cn.acebrand.acedex.pokemon  PokemonGen4Mapping cn.acebrand.acedex.pokemon  PokemonGen5Mapping cn.acebrand.acedex.pokemon  PokemonGen6Mapping cn.acebrand.acedex.pokemon  PokemonGen7Mapping cn.acebrand.acedex.pokemon  PokemonGen8Mapping cn.acebrand.acedex.pokemon  PokemonGen9Mapping cn.acebrand.acedex.pokemon  PokemonInfo cn.acebrand.acedex.pokemon  PokemonNameMapping cn.acebrand.acedex.pokemon  PokemonSpecialMapping cn.acebrand.acedex.pokemon  Regex cn.acebrand.acedex.pokemon  Runnable cn.acebrand.acedex.pokemon  ServerPlayer cn.acebrand.acedex.pokemon  Set cn.acebrand.acedex.pokemon  String cn.acebrand.acedex.pokemon  any cn.acebrand.acedex.pokemon  buildMap cn.acebrand.acedex.pokemon  com cn.acebrand.acedex.pokemon  
distinctBy cn.acebrand.acedex.pokemon  drop cn.acebrand.acedex.pokemon  	emptyList cn.acebrand.acedex.pokemon  	extension cn.acebrand.acedex.pokemon  filter cn.acebrand.acedex.pokemon  forEach cn.acebrand.acedex.pokemon  fromPlayerPokemonData cn.acebrand.acedex.pokemon  
getMapping cn.acebrand.acedex.pokemon  getPokemonChineseName cn.acebrand.acedex.pokemon  hasChineseName cn.acebrand.acedex.pokemon  
isNotEmpty cn.acebrand.acedex.pokemon  java cn.acebrand.acedex.pokemon  	javaClass cn.acebrand.acedex.pokemon  listOf cn.acebrand.acedex.pokemon  	lowercase cn.acebrand.acedex.pokemon  map cn.acebrand.acedex.pokemon  
mutableListOf cn.acebrand.acedex.pokemon  mutableMapOf cn.acebrand.acedex.pokemon  party cn.acebrand.acedex.pokemon  pc cn.acebrand.acedex.pokemon  plus cn.acebrand.acedex.pokemon  
plusAssign cn.acebrand.acedex.pokemon  replace cn.acebrand.acedex.pokemon  run cn.acebrand.acedex.pokemon  set cn.acebrand.acedex.pokemon  shuffled cn.acebrand.acedex.pokemon  take cn.acebrand.acedex.pokemon  toList cn.acebrand.acedex.pokemon  toSet cn.acebrand.acedex.pokemon  until cn.acebrand.acedex.pokemon  use cn.acebrand.acedex.pokemon  AceDex *cn.acebrand.acedex.pokemon.PokemonDetector  AllGenerationsProgress *cn.acebrand.acedex.pokemon.PokemonDetector  Boolean *cn.acebrand.acedex.pokemon.PokemonDetector  CompletableFuture *cn.acebrand.acedex.pokemon.PokemonDetector  	Exception *cn.acebrand.acedex.pokemon.PokemonDetector  File *cn.acebrand.acedex.pokemon.PokemonDetector  
FileReader *cn.acebrand.acedex.pokemon.PokemonDetector  
FileWriter *cn.acebrand.acedex.pokemon.PokemonDetector  GenerationProgress *cn.acebrand.acedex.pokemon.PokemonDetector  Gson *cn.acebrand.acedex.pokemon.PokemonDetector  GsonBuilder *cn.acebrand.acedex.pokemon.PokemonDetector  Int *cn.acebrand.acedex.pokemon.PokemonDetector  List *cn.acebrand.acedex.pokemon.PokemonDetector  Long *cn.acebrand.acedex.pokemon.PokemonDetector  PCStore *cn.acebrand.acedex.pokemon.PokemonDetector  Player *cn.acebrand.acedex.pokemon.PokemonDetector  PlayerDataStorage *cn.acebrand.acedex.pokemon.PokemonDetector  PlayerPartyStore *cn.acebrand.acedex.pokemon.PokemonDetector  PlayerPokemonData *cn.acebrand.acedex.pokemon.PokemonDetector  Pokemon *cn.acebrand.acedex.pokemon.PokemonDetector  PokemonInfo *cn.acebrand.acedex.pokemon.PokemonDetector  PokemonNameMapping *cn.acebrand.acedex.pokemon.PokemonDetector  Regex *cn.acebrand.acedex.pokemon.PokemonDetector  Runnable *cn.acebrand.acedex.pokemon.PokemonDetector  ServerPlayer *cn.acebrand.acedex.pokemon.PokemonDetector  String *cn.acebrand.acedex.pokemon.PokemonDetector  any *cn.acebrand.acedex.pokemon.PokemonDetector  calculateAllGenerationsProgress *cn.acebrand.acedex.pokemon.PokemonDetector  checkAndUpdatePlayerData *cn.acebrand.acedex.pokemon.PokemonDetector  checkPokemon *cn.acebrand.acedex.pokemon.PokemonDetector  com *cn.acebrand.acedex.pokemon.PokemonDetector  createPokemonInfo *cn.acebrand.acedex.pokemon.PokemonDetector  
dataFolder *cn.acebrand.acedex.pokemon.PokemonDetector  detectAndSavePlayerPokemon *cn.acebrand.acedex.pokemon.PokemonDetector  )detectAndSavePlayerPokemonWithCheckMethod *cn.acebrand.acedex.pokemon.PokemonDetector  determineGeneration *cn.acebrand.acedex.pokemon.PokemonDetector  
distinctBy *cn.acebrand.acedex.pokemon.PokemonDetector  drop *cn.acebrand.acedex.pokemon.PokemonDetector  	emptyList *cn.acebrand.acedex.pokemon.PokemonDetector  	extension *cn.acebrand.acedex.pokemon.PokemonDetector  filter *cn.acebrand.acedex.pokemon.PokemonDetector  fromPlayerPokemonData *cn.acebrand.acedex.pokemon.PokemonDetector  getAllGenerationsProgress *cn.acebrand.acedex.pokemon.PokemonDetector  getAllPlayerDataFiles *cn.acebrand.acedex.pokemon.PokemonDetector  getGenerationProgress *cn.acebrand.acedex.pokemon.PokemonDetector  getPlayerDataFileLastModified *cn.acebrand.acedex.pokemon.PokemonDetector  getPlayerPokemon *cn.acebrand.acedex.pokemon.PokemonDetector  getPlayerPokemonFromCobblemon *cn.acebrand.acedex.pokemon.PokemonDetector  ,getPlayerPokemonFromCobblemonWithCheckMethod *cn.acebrand.acedex.pokemon.PokemonDetector  getPokemonChineseName *cn.acebrand.acedex.pokemon.PokemonDetector  getServerPlayer *cn.acebrand.acedex.pokemon.PokemonDetector  getTestPokemonData *cn.acebrand.acedex.pokemon.PokemonDetector  gson *cn.acebrand.acedex.pokemon.PokemonDetector  hasChineseName *cn.acebrand.acedex.pokemon.PokemonDetector  hasDataChanged *cn.acebrand.acedex.pokemon.PokemonDetector  hasPlayerCaughtPokemon *cn.acebrand.acedex.pokemon.PokemonDetector  
isNotEmpty *cn.acebrand.acedex.pokemon.PokemonDetector  java *cn.acebrand.acedex.pokemon.PokemonDetector  	javaClass *cn.acebrand.acedex.pokemon.PokemonDetector  listOf *cn.acebrand.acedex.pokemon.PokemonDetector  loadPlayerDataFromFile *cn.acebrand.acedex.pokemon.PokemonDetector  	lowercase *cn.acebrand.acedex.pokemon.PokemonDetector  map *cn.acebrand.acedex.pokemon.PokemonDetector  
mutableListOf *cn.acebrand.acedex.pokemon.PokemonDetector  mutableMapOf *cn.acebrand.acedex.pokemon.PokemonDetector  normalizeShowdownId *cn.acebrand.acedex.pokemon.PokemonDetector  party *cn.acebrand.acedex.pokemon.PokemonDetector  pc *cn.acebrand.acedex.pokemon.PokemonDetector  plugin *cn.acebrand.acedex.pokemon.PokemonDetector  plus *cn.acebrand.acedex.pokemon.PokemonDetector  
plusAssign *cn.acebrand.acedex.pokemon.PokemonDetector  replace *cn.acebrand.acedex.pokemon.PokemonDetector  run *cn.acebrand.acedex.pokemon.PokemonDetector  savePlayerDataToFile *cn.acebrand.acedex.pokemon.PokemonDetector  set *cn.acebrand.acedex.pokemon.PokemonDetector  shuffled *cn.acebrand.acedex.pokemon.PokemonDetector  take *cn.acebrand.acedex.pokemon.PokemonDetector  toList *cn.acebrand.acedex.pokemon.PokemonDetector  toSet *cn.acebrand.acedex.pokemon.PokemonDetector  until *cn.acebrand.acedex.pokemon.PokemonDetector  use *cn.acebrand.acedex.pokemon.PokemonDetector  AllGenerationsProgress 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  CompletableFuture 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  File 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  
FileReader 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  
FileWriter 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  GenerationProgress 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  GsonBuilder 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  PlayerDataStorage 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  PlayerPokemonData 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  PokemonInfo 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  PokemonNameMapping 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  Regex 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  Runnable 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  any 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  
distinctBy 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  drop 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  	emptyList 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  	extension 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  filter 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  fromPlayerPokemonData 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  getPokemonChineseName 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  hasChineseName 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  
isNotEmpty 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  java 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  	javaClass 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  listOf 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  	lowercase 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  map 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  
mutableListOf 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  mutableMapOf 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  party 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  pc 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  plus 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  
plusAssign 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  replace 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  run 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  set 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  shuffled 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  take 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  toList 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  toSet 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  until 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  use 4cn.acebrand.acedex.pokemon.PokemonDetector.Companion  google .cn.acebrand.acedex.pokemon.PokemonDetector.com  gson 5cn.acebrand.acedex.pokemon.PokemonDetector.com.google  JsonSyntaxException :cn.acebrand.acedex.pokemon.PokemonDetector.com.google.gson  io /cn.acebrand.acedex.pokemon.PokemonDetector.java  EOFException 2cn.acebrand.acedex.pokemon.PokemonDetector.java.io  PokemonGen1Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen2Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen3Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen4Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen5Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen6Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen7Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen8Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonGen9Mapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  PokemonSpecialMapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  Regex -cn.acebrand.acedex.pokemon.PokemonNameMapping  buildMap -cn.acebrand.acedex.pokemon.PokemonNameMapping  
getMapping -cn.acebrand.acedex.pokemon.PokemonNameMapping  getPokemonChineseName -cn.acebrand.acedex.pokemon.PokemonNameMapping   getPokemonChineseNameFromEnglish -cn.acebrand.acedex.pokemon.PokemonNameMapping  hasChineseName -cn.acebrand.acedex.pokemon.PokemonNameMapping  	lowercase -cn.acebrand.acedex.pokemon.PokemonNameMapping  pokemonChineseNames -cn.acebrand.acedex.pokemon.PokemonNameMapping  replace -cn.acebrand.acedex.pokemon.PokemonNameMapping  google cn.acebrand.acedex.pokemon.com  gson %cn.acebrand.acedex.pokemon.com.google  JsonSyntaxException *cn.acebrand.acedex.pokemon.com.google.gson  Boolean %cn.acebrand.acedex.pokemon.generation  Int %cn.acebrand.acedex.pokemon.generation  Map %cn.acebrand.acedex.pokemon.generation  PokemonGen1Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen2Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen3Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen4Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen5Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen6Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen7Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen8Mapping %cn.acebrand.acedex.pokemon.generation  PokemonGen9Mapping %cn.acebrand.acedex.pokemon.generation  PokemonSpecialMapping %cn.acebrand.acedex.pokemon.generation  Regex %cn.acebrand.acedex.pokemon.generation  Set %cn.acebrand.acedex.pokemon.generation  String %cn.acebrand.acedex.pokemon.generation  buildMap %cn.acebrand.acedex.pokemon.generation  
getMapping %cn.acebrand.acedex.pokemon.generation  	lowercase %cn.acebrand.acedex.pokemon.generation  mapOf %cn.acebrand.acedex.pokemon.generation  replace %cn.acebrand.acedex.pokemon.generation  to %cn.acebrand.acedex.pokemon.generation  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen1Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen1Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen1Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen2Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen2Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen2Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen3Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen3Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen3Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen4Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen4Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen4Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen5Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen5Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen5Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen6Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen6Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen6Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen7Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen7Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen7Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen8Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen8Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen8Mapping  
getMapping 8cn.acebrand.acedex.pokemon.generation.PokemonGen9Mapping  mapOf 8cn.acebrand.acedex.pokemon.generation.PokemonGen9Mapping  to 8cn.acebrand.acedex.pokemon.generation.PokemonGen9Mapping  
getMapping ;cn.acebrand.acedex.pokemon.generation.PokemonSpecialMapping  mapOf ;cn.acebrand.acedex.pokemon.generation.PokemonSpecialMapping  to ;cn.acebrand.acedex.pokemon.generation.PokemonSpecialMapping  io cn.acebrand.acedex.pokemon.java  EOFException "cn.acebrand.acedex.pokemon.java.io  AceDex cn.acebrand.acedex.reward  Boolean cn.acebrand.acedex.reward  Bukkit cn.acebrand.acedex.reward  CompletableFuture cn.acebrand.acedex.reward  	Exception cn.acebrand.acedex.reward  GenerationReward cn.acebrand.acedex.reward  Int cn.acebrand.acedex.reward  List cn.acebrand.acedex.reward  Long cn.acebrand.acedex.reward  
MutableMap cn.acebrand.acedex.reward  
MutableSet cn.acebrand.acedex.reward  NumberFormatException cn.acebrand.acedex.reward  OverallProgressReward cn.acebrand.acedex.reward  Player cn.acebrand.acedex.reward  ProgressReward cn.acebrand.acedex.reward  
RewardManager cn.acebrand.acedex.reward  RewardStatus cn.acebrand.acedex.reward  Runnable cn.acebrand.acedex.reward  Set cn.acebrand.acedex.reward  String cn.acebrand.acedex.reward  System cn.acebrand.acedex.reward  UUID cn.acebrand.acedex.reward  Void cn.acebrand.acedex.reward  
component1 cn.acebrand.acedex.reward  
component2 cn.acebrand.acedex.reward  	emptyList cn.acebrand.acedex.reward  emptySet cn.acebrand.acedex.reward  find cn.acebrand.acedex.reward  forEach cn.acebrand.acedex.reward  getOrPut cn.acebrand.acedex.reward  ifEmpty cn.acebrand.acedex.reward  indices cn.acebrand.acedex.reward  
isNotEmpty cn.acebrand.acedex.reward  java cn.acebrand.acedex.reward  listOf cn.acebrand.acedex.reward  
mutableListOf cn.acebrand.acedex.reward  mutableMapOf cn.acebrand.acedex.reward  mutableSetOf cn.acebrand.acedex.reward  org cn.acebrand.acedex.reward  replace cn.acebrand.acedex.reward  set cn.acebrand.acedex.reward  sortBy cn.acebrand.acedex.reward  sorted cn.acebrand.acedex.reward  sumOf cn.acebrand.acedex.reward  toInt cn.acebrand.acedex.reward  toList cn.acebrand.acedex.reward  toMutableSet cn.acebrand.acedex.reward  toSet cn.acebrand.acedex.reward  command *cn.acebrand.acedex.reward.GenerationReward  description *cn.acebrand.acedex.reward.GenerationReward  requiredPercentage *cn.acebrand.acedex.reward.GenerationReward  commands /cn.acebrand.acedex.reward.OverallProgressReward  descriptions /cn.acebrand.acedex.reward.OverallProgressReward  name /cn.acebrand.acedex.reward.OverallProgressReward  requiredPercentage /cn.acebrand.acedex.reward.OverallProgressReward  command (cn.acebrand.acedex.reward.ProgressReward  description (cn.acebrand.acedex.reward.ProgressReward  requiredPercentage (cn.acebrand.acedex.reward.ProgressReward  Bukkit 'cn.acebrand.acedex.reward.RewardManager  CompletableFuture 'cn.acebrand.acedex.reward.RewardManager  GenerationReward 'cn.acebrand.acedex.reward.RewardManager  OverallProgressReward 'cn.acebrand.acedex.reward.RewardManager  ProgressReward 'cn.acebrand.acedex.reward.RewardManager  RewardStatus 'cn.acebrand.acedex.reward.RewardManager  Runnable 'cn.acebrand.acedex.reward.RewardManager  System 'cn.acebrand.acedex.reward.RewardManager  canClaimAllGenerationsReward 'cn.acebrand.acedex.reward.RewardManager  #canClaimPremiumAllGenerationsReward 'cn.acebrand.acedex.reward.RewardManager  canClaimReward 'cn.acebrand.acedex.reward.RewardManager  checkAllGenerationsCompleted 'cn.acebrand.acedex.reward.RewardManager  checkGenerationProgressRewards 'cn.acebrand.acedex.reward.RewardManager  checkGenerationRewards 'cn.acebrand.acedex.reward.RewardManager  checkOverallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  claimAllGenerationsReward 'cn.acebrand.acedex.reward.RewardManager  $claimAvailableOverallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  +claimAvailablePremiumOverallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  $claimAvailablePremiumProgressRewards 'cn.acebrand.acedex.reward.RewardManager  claimAvailableProgressRewards 'cn.acebrand.acedex.reward.RewardManager  claimOverallProgressReward 'cn.acebrand.acedex.reward.RewardManager   claimPremiumAllGenerationsReward 'cn.acebrand.acedex.reward.RewardManager  !claimPremiumOverallProgressReward 'cn.acebrand.acedex.reward.RewardManager  claimPremiumProgressReward 'cn.acebrand.acedex.reward.RewardManager  claimProgressReward 'cn.acebrand.acedex.reward.RewardManager  claimedAllGenerationsRewards 'cn.acebrand.acedex.reward.RewardManager  claimedGenerationRewards 'cn.acebrand.acedex.reward.RewardManager  claimedOverallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  #claimedPremiumAllGenerationsRewards 'cn.acebrand.acedex.reward.RewardManager  claimedPremiumGenerationRewards 'cn.acebrand.acedex.reward.RewardManager  $claimedPremiumOverallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  claimedPremiumProgressRewards 'cn.acebrand.acedex.reward.RewardManager  claimedProgressRewards 'cn.acebrand.acedex.reward.RewardManager  
component1 'cn.acebrand.acedex.reward.RewardManager  
component2 'cn.acebrand.acedex.reward.RewardManager  	emptyList 'cn.acebrand.acedex.reward.RewardManager  emptySet 'cn.acebrand.acedex.reward.RewardManager  find 'cn.acebrand.acedex.reward.RewardManager  generationProgressRewards 'cn.acebrand.acedex.reward.RewardManager  generationRewards 'cn.acebrand.acedex.reward.RewardManager  getAllGenerationsRewardCommands 'cn.acebrand.acedex.reward.RewardManager  getGenerationCompletionCommands 'cn.acebrand.acedex.reward.RewardManager  getNextAvailableReward 'cn.acebrand.acedex.reward.RewardManager  getOrPut 'cn.acebrand.acedex.reward.RewardManager  &getPremiumAllGenerationsRewardCommands 'cn.acebrand.acedex.reward.RewardManager  &getPremiumGenerationCompletionCommands 'cn.acebrand.acedex.reward.RewardManager  giveGenerationReward 'cn.acebrand.acedex.reward.RewardManager  givePremiumGenerationReward 'cn.acebrand.acedex.reward.RewardManager  
giveReward 'cn.acebrand.acedex.reward.RewardManager  hasClaimedAllGenerationsReward 'cn.acebrand.acedex.reward.RewardManager  hasClaimedGenerationReward 'cn.acebrand.acedex.reward.RewardManager  hasClaimedOverallProgressReward 'cn.acebrand.acedex.reward.RewardManager  %hasClaimedPremiumAllGenerationsReward 'cn.acebrand.acedex.reward.RewardManager  !hasClaimedPremiumGenerationReward 'cn.acebrand.acedex.reward.RewardManager  &hasClaimedPremiumOverallProgressReward 'cn.acebrand.acedex.reward.RewardManager  hasClaimedPremiumProgressReward 'cn.acebrand.acedex.reward.RewardManager  hasClaimedProgressReward 'cn.acebrand.acedex.reward.RewardManager   hasClaimedProgressRewardInternal 'cn.acebrand.acedex.reward.RewardManager  ifEmpty 'cn.acebrand.acedex.reward.RewardManager  indices 'cn.acebrand.acedex.reward.RewardManager  
initialize 'cn.acebrand.acedex.reward.RewardManager  
isNotEmpty 'cn.acebrand.acedex.reward.RewardManager  java 'cn.acebrand.acedex.reward.RewardManager  listOf 'cn.acebrand.acedex.reward.RewardManager  loadRewardConfigs 'cn.acebrand.acedex.reward.RewardManager  loadRewardData 'cn.acebrand.acedex.reward.RewardManager   markOverallProgressRewardClaimed 'cn.acebrand.acedex.reward.RewardManager  markProgressRewardClaimed 'cn.acebrand.acedex.reward.RewardManager  
mutableListOf 'cn.acebrand.acedex.reward.RewardManager  mutableMapOf 'cn.acebrand.acedex.reward.RewardManager  mutableSetOf 'cn.acebrand.acedex.reward.RewardManager  org 'cn.acebrand.acedex.reward.RewardManager  overallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  plugin 'cn.acebrand.acedex.reward.RewardManager   premiumGenerationProgressRewards 'cn.acebrand.acedex.reward.RewardManager  premiumGenerationRewards 'cn.acebrand.acedex.reward.RewardManager  premiumOverallProgressRewards 'cn.acebrand.acedex.reward.RewardManager  reload 'cn.acebrand.acedex.reward.RewardManager  replace 'cn.acebrand.acedex.reward.RewardManager  rewardCooldowns 'cn.acebrand.acedex.reward.RewardManager  saveRewardData 'cn.acebrand.acedex.reward.RewardManager  set 'cn.acebrand.acedex.reward.RewardManager  setRewardCooldown 'cn.acebrand.acedex.reward.RewardManager  sortBy 'cn.acebrand.acedex.reward.RewardManager  sorted 'cn.acebrand.acedex.reward.RewardManager  sumOf 'cn.acebrand.acedex.reward.RewardManager  toInt 'cn.acebrand.acedex.reward.RewardManager  toList 'cn.acebrand.acedex.reward.RewardManager  toMutableSet 'cn.acebrand.acedex.reward.RewardManager  toSet 'cn.acebrand.acedex.reward.RewardManager  bukkit cn.acebrand.acedex.reward.org  entity $cn.acebrand.acedex.reward.org.bukkit  Player +cn.acebrand.acedex.reward.org.bukkit.entity  AceDex cn.acebrand.acedex.util  Any cn.acebrand.acedex.util  Boolean cn.acebrand.acedex.util  Bukkit cn.acebrand.acedex.util  Class cn.acebrand.acedex.util  ClassNotFoundException cn.acebrand.acedex.util  	Cobblemon cn.acebrand.acedex.util  CobblemonApiHelper cn.acebrand.acedex.util  CobblemonItemHelper cn.acebrand.acedex.util  	Exception cn.acebrand.acedex.util  Int cn.acebrand.acedex.util  	ItemStack cn.acebrand.acedex.util  LGMenuIntegration cn.acebrand.acedex.util  Map cn.acebrand.acedex.util  NoSuchFieldException cn.acebrand.acedex.util  NoSuchMethodException cn.acebrand.acedex.util  PCStore cn.acebrand.acedex.util  Pair cn.acebrand.acedex.util  PlaceholderAPI cn.acebrand.acedex.util  Player cn.acebrand.acedex.util  PlayerPartyStore cn.acebrand.acedex.util  Pokemon cn.acebrand.acedex.util  PokemonInfo cn.acebrand.acedex.util  PokemonItem cn.acebrand.acedex.util  PokemonSpecies cn.acebrand.acedex.util  Regex cn.acebrand.acedex.util  RegistryAccess cn.acebrand.acedex.util  Species cn.acebrand.acedex.util  String cn.acebrand.acedex.util  Suppress cn.acebrand.acedex.util  UUID cn.acebrand.acedex.util  VariableReplacer cn.acebrand.acedex.util  com cn.acebrand.acedex.util  contains cn.acebrand.acedex.util  count cn.acebrand.acedex.util  find cn.acebrand.acedex.util  forEach cn.acebrand.acedex.util  from cn.acebrand.acedex.util  getByIdentifier cn.acebrand.acedex.util  getByPokedexNumber cn.acebrand.acedex.util  
isNotEmpty cn.acebrand.acedex.util  java cn.acebrand.acedex.util  	javaClass cn.acebrand.acedex.util  listOf cn.acebrand.acedex.util  	lowercase cn.acebrand.acedex.util  mapOf cn.acebrand.acedex.util  mutableMapOf cn.acebrand.acedex.util  net cn.acebrand.acedex.util  println cn.acebrand.acedex.util  replace cn.acebrand.acedex.util  set cn.acebrand.acedex.util  to cn.acebrand.acedex.util  toInt cn.acebrand.acedex.util  Bukkit *cn.acebrand.acedex.util.CobblemonApiHelper  Class *cn.acebrand.acedex.util.CobblemonApiHelper  	Cobblemon *cn.acebrand.acedex.util.CobblemonApiHelper  Pair *cn.acebrand.acedex.util.CobblemonApiHelper  Pokemon *cn.acebrand.acedex.util.CobblemonApiHelper  PokemonInfo *cn.acebrand.acedex.util.CobblemonApiHelper  PokemonItem *cn.acebrand.acedex.util.CobblemonApiHelper  PokemonSpecies *cn.acebrand.acedex.util.CobblemonApiHelper  com *cn.acebrand.acedex.util.CobblemonApiHelper  contains *cn.acebrand.acedex.util.CobblemonApiHelper  convertToItemStack *cn.acebrand.acedex.util.CobblemonApiHelper  
createPokemon *cn.acebrand.acedex.util.CobblemonApiHelper  createPokemonItem *cn.acebrand.acedex.util.CobblemonApiHelper  createPokemonItemByLGMenu *cn.acebrand.acedex.util.CobblemonApiHelper  debugCobblemonStorage *cn.acebrand.acedex.util.CobblemonApiHelper  determineGeneration *cn.acebrand.acedex.util.CobblemonApiHelper  find *cn.acebrand.acedex.util.CobblemonApiHelper  from *cn.acebrand.acedex.util.CobblemonApiHelper  getByIdentifier *cn.acebrand.acedex.util.CobblemonApiHelper  getByPokedexNumber *cn.acebrand.acedex.util.CobblemonApiHelper  getIRegistryCustom *cn.acebrand.acedex.util.CobblemonApiHelper  getServerPlayer *cn.acebrand.acedex.util.CobblemonApiHelper  getSpeciesByDexNumber *cn.acebrand.acedex.util.CobblemonApiHelper  getSpeciesByName *cn.acebrand.acedex.util.CobblemonApiHelper  java *cn.acebrand.acedex.util.CobblemonApiHelper  	javaClass *cn.acebrand.acedex.util.CobblemonApiHelper  listOf *cn.acebrand.acedex.util.CobblemonApiHelper  	lowercase *cn.acebrand.acedex.util.CobblemonApiHelper  Class +cn.acebrand.acedex.util.CobblemonItemHelper  Pair +cn.acebrand.acedex.util.CobblemonItemHelper  Pokemon +cn.acebrand.acedex.util.CobblemonItemHelper  PokemonItem +cn.acebrand.acedex.util.CobblemonItemHelper  PokemonSpecies +cn.acebrand.acedex.util.CobblemonItemHelper  Regex +cn.acebrand.acedex.util.CobblemonItemHelper  com +cn.acebrand.acedex.util.CobblemonItemHelper  convertNMSItemToBukkit +cn.acebrand.acedex.util.CobblemonItemHelper  convertThroughBukkit +cn.acebrand.acedex.util.CobblemonItemHelper  convertWithVersion +cn.acebrand.acedex.util.CobblemonItemHelper  count +cn.acebrand.acedex.util.CobblemonItemHelper  createPokeBallItem +cn.acebrand.acedex.util.CobblemonItemHelper  
createPokemon +cn.acebrand.acedex.util.CobblemonItemHelper  createPokemonItemByName +cn.acebrand.acedex.util.CobblemonItemHelper  from +cn.acebrand.acedex.util.CobblemonItemHelper  getByIdentifier +cn.acebrand.acedex.util.CobblemonItemHelper  getByPokedexNumber +cn.acebrand.acedex.util.CobblemonItemHelper  getPokemonItem +cn.acebrand.acedex.util.CobblemonItemHelper  getServerVersion +cn.acebrand.acedex.util.CobblemonItemHelper  getSpeciesByDexNumber +cn.acebrand.acedex.util.CobblemonItemHelper  getSpeciesByName +cn.acebrand.acedex.util.CobblemonItemHelper  	javaClass +cn.acebrand.acedex.util.CobblemonItemHelper  listOf +cn.acebrand.acedex.util.CobblemonItemHelper  	lowercase +cn.acebrand.acedex.util.CobblemonItemHelper  mutableMapOf +cn.acebrand.acedex.util.CobblemonItemHelper  set +cn.acebrand.acedex.util.CobblemonItemHelper  testCobblemonIntegration +cn.acebrand.acedex.util.CobblemonItemHelper  Class )cn.acebrand.acedex.util.LGMenuIntegration  Map )cn.acebrand.acedex.util.LGMenuIntegration  Pair )cn.acebrand.acedex.util.LGMenuIntegration  Pokemon )cn.acebrand.acedex.util.LGMenuIntegration  PokemonSpecies )cn.acebrand.acedex.util.LGMenuIntegration  String )cn.acebrand.acedex.util.LGMenuIntegration  buildPokemonPlaceholders )cn.acebrand.acedex.util.LGMenuIntegration  com )cn.acebrand.acedex.util.LGMenuIntegration  
createPokemon )cn.acebrand.acedex.util.LGMenuIntegration  createPokemonItem )cn.acebrand.acedex.util.LGMenuIntegration  createPokemonItemByName )cn.acebrand.acedex.util.LGMenuIntegration  getByIdentifier )cn.acebrand.acedex.util.LGMenuIntegration  getByPokedexNumber )cn.acebrand.acedex.util.LGMenuIntegration  getPokemonDisplayName )cn.acebrand.acedex.util.LGMenuIntegration  getPokemonTypes )cn.acebrand.acedex.util.LGMenuIntegration  getSpeciesByDexNumber )cn.acebrand.acedex.util.LGMenuIntegration  getSpeciesByName )cn.acebrand.acedex.util.LGMenuIntegration  isLGMenuAvailable )cn.acebrand.acedex.util.LGMenuIntegration  
isNotEmpty )cn.acebrand.acedex.util.LGMenuIntegration  java )cn.acebrand.acedex.util.LGMenuIntegration  	lowercase )cn.acebrand.acedex.util.LGMenuIntegration  mutableMapOf )cn.acebrand.acedex.util.LGMenuIntegration  println )cn.acebrand.acedex.util.LGMenuIntegration  set )cn.acebrand.acedex.util.LGMenuIntegration  
generation #cn.acebrand.acedex.util.PokemonInfo  name #cn.acebrand.acedex.util.PokemonInfo  nationalDex #cn.acebrand.acedex.util.PokemonInfo  Bukkit (cn.acebrand.acedex.util.VariableReplacer  PlaceholderAPI (cn.acebrand.acedex.util.VariableReplacer  Regex (cn.acebrand.acedex.util.VariableReplacer  contains (cn.acebrand.acedex.util.VariableReplacer  containsCustomVariables (cn.acebrand.acedex.util.VariableReplacer  
isNotEmpty (cn.acebrand.acedex.util.VariableReplacer  mapOf (cn.acebrand.acedex.util.VariableReplacer  replace (cn.acebrand.acedex.util.VariableReplacer  replaceAceDexVariables (cn.acebrand.acedex.util.VariableReplacer  replaceCustomVariables (cn.acebrand.acedex.util.VariableReplacer  replaceVariables (cn.acebrand.acedex.util.VariableReplacer  to (cn.acebrand.acedex.util.VariableReplacer  toInt (cn.acebrand.acedex.util.VariableReplacer  	cobblemon cn.acebrand.acedex.util.com  mod %cn.acebrand.acedex.util.com.cobblemon  common )cn.acebrand.acedex.util.com.cobblemon.mod  pokeball 0cn.acebrand.acedex.util.com.cobblemon.mod.common  PokeBall 9cn.acebrand.acedex.util.com.cobblemon.mod.common.pokeball  	minecraft cn.acebrand.acedex.util.net  world %cn.acebrand.acedex.util.net.minecraft  item +cn.acebrand.acedex.util.net.minecraft.world  	ItemStack 0cn.acebrand.acedex.util.net.minecraft.world.item  	Cobblemon com.cobblemon.mod.common  storage "com.cobblemon.mod.common.Cobblemon  CobblemonEvents #com.cobblemon.mod.common.api.events  POKEMON_CAPTURED 3com.cobblemon.mod.common.api.events.CobblemonEvents  POKEMON_RELEASED_EVENT_POST 3com.cobblemon.mod.common.api.events.CobblemonEvents  PokemonCapturedEvent +com.cobblemon.mod.common.api.events.pokemon  player @com.cobblemon.mod.common.api.events.pokemon.PokemonCapturedEvent  pokemon @com.cobblemon.mod.common.api.events.pokemon.PokemonCapturedEvent  ReleasePokemonEvent +com.cobblemon.mod.common.api.events.storage  Post ?com.cobblemon.mod.common.api.events.storage.ReleasePokemonEvent  player Dcom.cobblemon.mod.common.api.events.storage.ReleasePokemonEvent.Post  pokemon Dcom.cobblemon.mod.common.api.events.storage.ReleasePokemonEvent.Post  	PokeBalls %com.cobblemon.mod.common.api.pokeball  	DUSK_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  
GREAT_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  LUXURY_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  MASTER_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  	POKE_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  PREMIER_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  
QUICK_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  
TIMER_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  
ULTRA_BALL /com.cobblemon.mod.common.api.pokeball.PokeBalls  PokemonSpecies $com.cobblemon.mod.common.api.pokemon  getByIdentifier 3com.cobblemon.mod.common.api.pokemon.PokemonSpecies  getByPokedexNumber 3com.cobblemon.mod.common.api.pokemon.PokemonSpecies  EventObservable %com.cobblemon.mod.common.api.reactive  ObservableSubscription %com.cobblemon.mod.common.api.reactive  	subscribe 5com.cobblemon.mod.common.api.reactive.EventObservable  PokemonStoreManager $com.cobblemon.mod.common.api.storage  	javaClass 8com.cobblemon.mod.common.api.storage.PokemonStoreManager  PlayerPartyStore *com.cobblemon.mod.common.api.storage.party  get 5com.cobblemon.mod.common.api.storage.party.PartyStore  get ;com.cobblemon.mod.common.api.storage.party.PlayerPartyStore  PCBox 'com.cobblemon.mod.common.api.storage.pc  PCStore 'com.cobblemon.mod.common.api.storage.pc  getNonEmptySlots -com.cobblemon.mod.common.api.storage.pc.PCBox  boxes /com.cobblemon.mod.common.api.storage.pc.PCStore  
ElementalType "com.cobblemon.mod.common.api.types  displayName 0com.cobblemon.mod.common.api.types.ElementalType  PokemonItem com.cobblemon.mod.common.item  	Companion )com.cobblemon.mod.common.item.PokemonItem  from )com.cobblemon.mod.common.item.PokemonItem  from 3com.cobblemon.mod.common.item.PokemonItem.Companion  PokeBall !com.cobblemon.mod.common.pokeball  stack *com.cobblemon.mod.common.pokeball.PokeBall  Gender  com.cobblemon.mod.common.pokemon  Pokemon  com.cobblemon.mod.common.pokemon  Species  com.cobblemon.mod.common.pokemon  name 'com.cobblemon.mod.common.pokemon.Gender  	Companion (com.cobblemon.mod.common.pokemon.Pokemon  gender (com.cobblemon.mod.common.pokemon.Pokemon  
initialize (com.cobblemon.mod.common.pokemon.Pokemon  level (com.cobblemon.mod.common.pokemon.Pokemon  shiny (com.cobblemon.mod.common.pokemon.Pokemon  species (com.cobblemon.mod.common.pokemon.Pokemon  name (com.cobblemon.mod.common.pokemon.Species  nationalPokedexNumber (com.cobblemon.mod.common.pokemon.Species  primaryType (com.cobblemon.mod.common.pokemon.Species  
secondaryType (com.cobblemon.mod.common.pokemon.Species  translatedName (com.cobblemon.mod.common.pokemon.Species  cobblemonResource com.cobblemon.mod.common.util  party com.cobblemon.mod.common.util  pc com.cobblemon.mod.common.util  Gson com.google.gson  GsonBuilder com.google.gson  
JsonObject com.google.gson  JsonSyntaxException com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  create com.google.gson.GsonBuilder  disableHtmlEscaping com.google.gson.GsonBuilder  setPrettyPrinting com.google.gson.GsonBuilder  	asBoolean com.google.gson.JsonElement  asString com.google.gson.JsonElement  getAsJsonObject com.google.gson.JsonElement  get com.google.gson.JsonObject  getAsJsonObject com.google.gson.JsonObject  has com.google.gson.JsonObject  message #com.google.gson.JsonSyntaxException  id com.mojang.authlib.GameProfile  BufferedReader java.io  EOFException java.io  File java.io  
FileFilter java.io  
FileReader java.io  
FileWriter java.io  FilenameFilter java.io  IOException java.io  InputStream java.io  InputStreamReader java.io  use java.io.BufferedReader  message java.io.EOFException  delete java.io.File  exists java.io.File  lastModified java.io.File  	listFiles java.io.File  mkdirs java.io.File  <SAM-CONSTRUCTOR> java.io.FileFilter  use java.io.FileReader  use java.io.FileWriter  <SAM-CONSTRUCTOR> java.io.FilenameFilter  use java.io.InputStream  write java.io.OutputStream  
Appendable 	java.lang  Class 	java.lang  ClassNotFoundException 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  InterruptedException 	java.lang  NoSuchFieldException 	java.lang  NoSuchMethodException 	java.lang  NumberFormatException 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  Void 	java.lang  append java.lang.AbstractStringBuilder  digit java.lang.Character  constructors java.lang.Class  declaredFields java.lang.Class  forName java.lang.Class  getConstructor java.lang.Class  getDeclaredField java.lang.Class  	getMethod java.lang.Class  methods java.lang.Class  
simpleName java.lang.Class  message  java.lang.ClassNotFoundException  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  
appendLine java.lang.StringBuilder  buttonIdCacheFile java.lang.StringBuilder  buttonIdToPokemonName java.lang.StringBuilder  cacheStatusFile java.lang.StringBuilder  caughtPokemonItemCache java.lang.StringBuilder  generationPokemonCache java.lang.StringBuilder  isPreloaded java.lang.StringBuilder  playerGuiCache java.lang.StringBuilder  
plusAssign java.lang.StringBuilder  pokemonButtonIds java.lang.StringBuilder  preloadStatusFile java.lang.StringBuilder  preloadedPokemonItems java.lang.StringBuilder  refreshQueue java.lang.StringBuilder  toString java.lang.StringBuilder  uncaughtPokemonItemCache java.lang.StringBuilder  currentTimeMillis java.lang.System  getProperty java.lang.System  
currentThread java.lang.Thread  	interrupt java.lang.Thread  sleep java.lang.Thread  Field java.lang.reflect  Method java.lang.reflect  isAccessible "java.lang.reflect.AccessibleObject  newInstance java.lang.reflect.Constructor  get java.lang.reflect.Field  isAccessible java.lang.reflect.Field  type java.lang.reflect.Field  invoke java.lang.reflect.Method  name java.lang.reflect.Method  parameterCount java.lang.reflect.Method  parameterTypes java.lang.reflect.Method  
BigDecimal 	java.math  
BigInteger 	java.math  HttpURLConnection java.net  InetAddress java.net  URI java.net  HTTP_OK java.net.HttpURLConnection  connectTimeout java.net.HttpURLConnection  
disconnect java.net.HttpURLConnection  doOutput java.net.HttpURLConnection  errorStream java.net.HttpURLConnection  inputStream java.net.HttpURLConnection  outputStream java.net.HttpURLConnection  readTimeout java.net.HttpURLConnection  
requestMethod java.net.HttpURLConnection  responseCode java.net.HttpURLConnection  setRequestProperty java.net.HttpURLConnection  getLocalHost java.net.InetAddress  hostName java.net.InetAddress  create java.net.URI  toURL java.net.URI  openConnection java.net.URL  connectTimeout java.net.URLConnection  doOutput java.net.URLConnection  inputStream java.net.URLConnection  outputStream java.net.URLConnection  readTimeout java.net.URLConnection  setRequestProperty java.net.URLConnection  StandardCharsets java.nio.charset  UTF_8 !java.nio.charset.StandardCharsets  
KeyFactory 
java.security  	PublicKey 
java.security  SecureRandom 
java.security  	Signature 
java.security  generatePublic java.security.KeyFactory  getInstance java.security.KeyFactory  	nextBytes java.security.SecureRandom  getInstance java.security.Signature  
initVerify java.security.Signature  update java.security.Signature  verify java.security.Signature  X509EncodedKeySpec java.security.spec  API_BASE_URL 	java.util  AceDex 	java.util  AllGenerationsProgress 	java.util  Any 	java.util  Base64 	java.util  Boolean 	java.util  BufferedReader 	java.util  Bukkit 	java.util  	ByteArray 	java.util  	Character 	java.util  Class 	java.util  	Cobblemon 	java.util  CobblemonEventWrapper 	java.util  
Collection 	java.util  	Companion 	java.util  CompletableFuture 	java.util  ConcurrentHashMap 	java.util  
Deprecated 	java.util  Double 	java.util  ERROR_MESSAGES 	java.util  Event 	java.util  EventHandler 	java.util  	Exception 	java.util  	Executors 	java.util  File 	java.util  
FileReader 	java.util  
FileWriter 	java.util  GSON 	java.util  Gen1PokemonData 	java.util  Gen2PokemonData 	java.util  Gen3PokemonData 	java.util  Gen4PokemonData 	java.util  Gen5PokemonData 	java.util  Gen6PokemonData 	java.util  Gen7PokemonData 	java.util  Gen8PokemonData 	java.util  Gen9PokemonData 	java.util  
Generation 	java.util  GenerationProgress 	java.util  GenerationReward 	java.util  Gson 	java.util  GsonBuilder 	java.util  HEARTBEAT_ENDPOINT 	java.util  HandlerList 	java.util  HashMap 	java.util  HttpURLConnection 	java.util  IOException 	java.util  InetAddress 	java.util  InputStream 	java.util  InputStreamReader 	java.util  Int 	java.util  InterruptedException 	java.util  	Inventory 	java.util  InventoryClickEvent 	java.util  	ItemStack 	java.util  
JsonObject 	java.util  	JvmStatic 	java.util  
KeyFactory 	java.util  LicenseInfo 	java.util  List 	java.util  Listener 	java.util  Long 	java.util  Map 	java.util  Material 	java.util  MutableList 	java.util  
MutableMap 	java.util  
MutableSet 	java.util  NoSuchFieldException 	java.util  NoSuchMethodException 	java.util  NumberFormatException 	java.util  OverallProgressReward 	java.util  PCStore 	java.util  
PRODUCT_ID 	java.util  Pair 	java.util  Player 	java.util  PlayerDataStorage 	java.util  PlayerPartyStore 	java.util  PlayerPokemonData 	java.util  PokeBallItemCreator 	java.util  PokedexUpdateType 	java.util  Pokemon 	java.util  PokemonData 	java.util  PokemonInfo 	java.util  PokemonItem 	java.util  PokemonItemCreator 	java.util  PokemonNameMapping 	java.util  PokemonSpecies 	java.util  ProgressReward 	java.util  RSA_PUBLIC_KEY 	java.util  Regex 	java.util  RegistryAccess 	java.util  ReplaceWith 	java.util  RewardStatus 	java.util  Runnable 	java.util  ScheduledExecutorService 	java.util  SecureRandom 	java.util  ServerPlayer 	java.util  Set 	java.util  	Signature 	java.util  Species 	java.util  StandardCharsets 	java.util  StorageChangeType 	java.util  StoredPokemonInfo 	java.util  String 	java.util  
StringBuilder 	java.util  System 	java.util  TEAM_ID 	java.util  TIMEOUT_MILLIS 	java.util  Thread 	java.util  TimeUnit 	java.util  Triple 	java.util  URI 	java.util  UUID 	java.util  VERIFY_ENDPOINT 	java.util  VERSION 	java.util  Void 	java.util  X509EncodedKeySpec 	java.util  any 	java.util  cn 	java.util  
coerceAtLeast 	java.util  coerceIn 	java.util  com 	java.util  
component1 	java.util  
component2 	java.util  contains 	java.util  
distinctBy 	java.util  drop 	java.util  	emptyList 	java.util  emptyMap 	java.util  emptySet 	java.util  	extension 	java.util  filter 	java.util  find 	java.util  forEach 	java.util  forEachIndexed 	java.util  format 	java.util  from 	java.util  fromPlayerPokemonData 	java.util  fromPokemonInfo 	java.util  getByIdentifier 	java.util  getByPokedexNumber 	java.util  getOrPut 	java.util  getPokemonChineseName 	java.util   getPokemonChineseNameFromEnglish 	java.util  hasChineseName 	java.util  ifEmpty 	java.util  indices 	java.util  invoke 	java.util  isBlank 	java.util  
isNotEmpty 	java.util  iterator 	java.util  java 	java.util  	javaClass 	java.util  joinToString 	java.util  let 	java.util  listOf 	java.util  	lowercase 	java.util  map 	java.util  
mapNotNull 	java.util  mapOf 	java.util  minOf 	java.util  
mutableListOf 	java.util  mutableMapOf 	java.util  mutableSetOf 	java.util  org 	java.util  party 	java.util  pc 	java.util  plus 	java.util  
plusAssign 	java.util  repeat 	java.util  replace 	java.util  run 	java.util  set 	java.util  setOf 	java.util  shuffled 	java.util  sortBy 	java.util  sorted 	java.util  
startsWith 	java.util  step 	java.util  	substring 	java.util  substringAfter 	java.util  sumOf 	java.util  take 	java.util  to 	java.util  toByteArray 	java.util  toInt 	java.util  toList 	java.util  toMutableSet 	java.util  toRegex 	java.util  toSet 	java.util  
trimIndent 	java.util  until 	java.util  use 	java.util  
getDecoder java.util.Base64  decode java.util.Base64.Decoder  
fromString java.util.UUID  nameUUIDFromBytes java.util.UUID  
randomUUID java.util.UUID  toString java.util.UUID  acebrand java.util.cn  acedex java.util.cn.acebrand  
generation java.util.cn.acebrand.acedex  
Generation 'java.util.cn.acebrand.acedex.generation  google 
java.util.com  gson java.util.com.google  JsonSyntaxException java.util.com.google.gson  Callable java.util.concurrent  CompletableFuture java.util.concurrent  ConcurrentHashMap java.util.concurrent  	Executors java.util.concurrent  ScheduledExecutorService java.util.concurrent  ScheduledFuture java.util.concurrent  TimeUnit java.util.concurrent  <SAM-CONSTRUCTOR> java.util.concurrent.Callable  
exceptionally &java.util.concurrent.CompletableFuture  runAsync &java.util.concurrent.CompletableFuture  supplyAsync &java.util.concurrent.CompletableFuture  thenRun &java.util.concurrent.CompletableFuture  
KeySetView &java.util.concurrent.ConcurrentHashMap  clear &java.util.concurrent.ConcurrentHashMap  containsKey &java.util.concurrent.ConcurrentHashMap  entries &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  getOrPut &java.util.concurrent.ConcurrentHashMap  iterator &java.util.concurrent.ConcurrentHashMap  keys &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  size &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  filter 1java.util.concurrent.ConcurrentHashMap.KeySetView  awaitTermination $java.util.concurrent.ExecutorService  
isShutdown $java.util.concurrent.ExecutorService  shutdown $java.util.concurrent.ExecutorService  shutdownNow $java.util.concurrent.ExecutorService  newFixedThreadPool java.util.concurrent.Executors  newScheduledThreadPool java.util.concurrent.Executors   newSingleThreadScheduledExecutor java.util.concurrent.Executors  awaitTermination -java.util.concurrent.ScheduledExecutorService  
isShutdown -java.util.concurrent.ScheduledExecutorService  let -java.util.concurrent.ScheduledExecutorService  schedule -java.util.concurrent.ScheduledExecutorService  scheduleAtFixedRate -java.util.concurrent.ScheduledExecutorService  shutdown -java.util.concurrent.ScheduledExecutorService  shutdownNow -java.util.concurrent.ScheduledExecutorService  MINUTES java.util.concurrent.TimeUnit  SECONDS java.util.concurrent.TimeUnit  Function java.util.function  	Predicate java.util.function  Supplier java.util.function  <SAM-CONSTRUCTOR> java.util.function.Function  <SAM-CONSTRUCTOR> java.util.function.Predicate  <SAM-CONSTRUCTOR> java.util.function.Supplier  io java.util.java  EOFException java.util.java.io  Logger java.util.logging  info java.util.logging.Logger  severe java.util.logging.Logger  warning java.util.logging.Logger  bukkit 
java.util.org  entity java.util.org.bukkit  event java.util.org.bukkit  Player java.util.org.bukkit.entity  	inventory java.util.org.bukkit.event  player java.util.org.bukkit.event  	ClickType $java.util.org.bukkit.event.inventory  InventoryCloseEvent $java.util.org.bukkit.event.inventory  PlayerQuitEvent !java.util.org.bukkit.event.player  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  
Deprecated kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  Pair kotlin  ReplaceWith kotlin  Result kotlin  
ShortArray kotlin  String kotlin  Suppress kotlin  Triple kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  $UninitializedPropertyAccessException kotlin  
isInitialized kotlin  let kotlin  map kotlin  plus kotlin  repeat kotlin  run kotlin  to kotlin  toList kotlin  use kotlin  hashCode 
kotlin.Any  	javaClass 
kotlin.Any  toString 
kotlin.Any  drop kotlin.Array  filter kotlin.Array  get kotlin.Array  	getOrNull kotlin.Array  size kotlin.Array  not kotlin.Boolean  iterator kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  	compareTo 
kotlin.Double  div 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  
coerceAtLeast 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  invoke 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  shl 
kotlin.Int  times 
kotlin.Int  to 
kotlin.Int  toByte 
kotlin.Int  toDouble 
kotlin.Int  toString 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  second kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  endsWith 
kotlin.String  equals 
kotlin.String  format 
kotlin.String  get 
kotlin.String  hashCode 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  plus 
kotlin.String  removePrefix 
kotlin.String  repeat 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  substringAfter 
kotlin.String  to 
kotlin.String  toByteArray 
kotlin.String  toInt 
kotlin.String  toIntOrNull 
kotlin.String  toRegex 
kotlin.String  toString 
kotlin.String  
trimIndent 
kotlin.String  	uppercase 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  first 
kotlin.Triple  ByteIterator kotlin.collections  CharIterator kotlin.collections  
Collection kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  buildMap kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  
distinctBy kotlin.collections  drop kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  emptySet kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  	getOrNull kotlin.collections  getOrPut kotlin.collections  ifEmpty kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  iterator kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapKeys kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  minOf kotlin.collections  	minOrNull kotlin.collections  mutableIterator kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  setOf kotlin.collections  shuffled kotlin.collections  sortBy kotlin.collections  sorted kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toMap kotlin.collections  
toMutableList kotlin.collections  toMutableSet kotlin.collections  toSet kotlin.collections  hasNext kotlin.collections.ByteIterator  next kotlin.collections.ByteIterator  find kotlin.collections.Collection  forEachIndexed kotlin.collections.Collection  iterator kotlin.collections.Collection  size kotlin.collections.Collection  toList kotlin.collections.Collection  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  contains kotlin.collections.List  
distinctBy kotlin.collections.List  drop kotlin.collections.List  filter kotlin.collections.List  find kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  	getOrNull kotlin.collections.List  ifEmpty kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  
mapNotNull kotlin.collections.List  plus kotlin.collections.List  shuffled kotlin.collections.List  size kotlin.collections.List  sorted kotlin.collections.List  subList kotlin.collections.List  take kotlin.collections.List  
toMutableList kotlin.collections.List  toSet kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  entries kotlin.collections.Map  get kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  iterator kotlin.collections.Map  keys kotlin.collections.Map  mapKeys kotlin.collections.Map  size kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  count $kotlin.collections.MutableCollection  find $kotlin.collections.MutableCollection  sumOf $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  filter kotlin.collections.MutableList  find kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  map kotlin.collections.MutableList  	minOrNull kotlin.collections.MutableList  size kotlin.collections.MutableList  sortBy kotlin.collections.MutableList  sorted kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  PokemonGen1Mapping kotlin.collections.MutableMap  PokemonGen2Mapping kotlin.collections.MutableMap  PokemonGen3Mapping kotlin.collections.MutableMap  PokemonGen4Mapping kotlin.collections.MutableMap  PokemonGen5Mapping kotlin.collections.MutableMap  PokemonGen6Mapping kotlin.collections.MutableMap  PokemonGen7Mapping kotlin.collections.MutableMap  PokemonGen8Mapping kotlin.collections.MutableMap  PokemonGen9Mapping kotlin.collections.MutableMap  PokemonSpecialMapping kotlin.collections.MutableMap  clear kotlin.collections.MutableMap  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  
getMapping kotlin.collections.MutableMap  getOrPut kotlin.collections.MutableMap  isEmpty kotlin.collections.MutableMap  keys kotlin.collections.MutableMap  putAll kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  toMap kotlin.collections.MutableMap  values kotlin.collections.MutableMap  
component1 *kotlin.collections.MutableMap.MutableEntry  
component2 *kotlin.collections.MutableMap.MutableEntry  add kotlin.collections.MutableSet  any kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  iterator kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  removeIf kotlin.collections.MutableSet  toList kotlin.collections.MutableSet  toSet kotlin.collections.MutableSet  contains kotlin.collections.Set  iterator kotlin.collections.Set  toList kotlin.collections.Set  minOf kotlin.comparisons  endsWith 	kotlin.io  	extension 	kotlin.io  iterator 	kotlin.io  println 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  	JvmStatic 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  min kotlin.math  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  step 
kotlin.ranges  until 
kotlin.ranges  first kotlin.ranges.IntProgression  iterator kotlin.ranges.IntProgression  last kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  first kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  last kotlin.ranges.IntRange  step kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  
distinctBy kotlin.sequences  drop kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  ifEmpty kotlin.sequences  iterator kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  minOf kotlin.sequences  	minOrNull kotlin.sequences  plus kotlin.sequences  shuffled kotlin.sequences  sorted kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  
toMutableList kotlin.sequences  toMutableSet kotlin.sequences  toSet kotlin.sequences  MatchResult kotlin.text  Regex kotlin.text  String kotlin.text  any kotlin.text  
appendLine kotlin.text  buildString kotlin.text  contains kotlin.text  count kotlin.text  drop kotlin.text  endsWith kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  	getOrNull kotlin.text  ifEmpty kotlin.text  indices kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  iterator kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapNotNull kotlin.text  minOf kotlin.text  	minOrNull kotlin.text  plus kotlin.text  removePrefix kotlin.text  repeat kotlin.text  replace kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  substringAfter kotlin.text  sumOf kotlin.text  take kotlin.text  toByteArray kotlin.text  toInt kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  
toMutableList kotlin.text  toRegex kotlin.text  toSet kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  groupValues kotlin.text.MatchResult  value kotlin.text.MatchResult  containsMatchIn kotlin.text.Regex  find kotlin.text.Regex  matchEntire kotlin.text.Regex  replace kotlin.text.Regex  PlaceholderAPI me.clip.placeholderapi  setPlaceholders %me.clip.placeholderapi.PlaceholderAPI  Int &me.clip.placeholderapi.PlaceholderHook  NumberFormatException &me.clip.placeholderapi.PlaceholderHook  Regex &me.clip.placeholderapi.PlaceholderHook  
coerceAtLeast &me.clip.placeholderapi.PlaceholderHook  coerceIn &me.clip.placeholderapi.PlaceholderHook  find &me.clip.placeholderapi.PlaceholderHook  	lowercase &me.clip.placeholderapi.PlaceholderHook  	minOrNull &me.clip.placeholderapi.PlaceholderHook  
mutableListOf &me.clip.placeholderapi.PlaceholderHook  repeat &me.clip.placeholderapi.PlaceholderHook  toInt &me.clip.placeholderapi.PlaceholderHook  toIntOrNull &me.clip.placeholderapi.PlaceholderHook  PlaceholderExpansion  me.clip.placeholderapi.expansion  Int 5me.clip.placeholderapi.expansion.PlaceholderExpansion  NumberFormatException 5me.clip.placeholderapi.expansion.PlaceholderExpansion  Regex 5me.clip.placeholderapi.expansion.PlaceholderExpansion  
coerceAtLeast 5me.clip.placeholderapi.expansion.PlaceholderExpansion  coerceIn 5me.clip.placeholderapi.expansion.PlaceholderExpansion  find 5me.clip.placeholderapi.expansion.PlaceholderExpansion  	lowercase 5me.clip.placeholderapi.expansion.PlaceholderExpansion  	minOrNull 5me.clip.placeholderapi.expansion.PlaceholderExpansion  
mutableListOf 5me.clip.placeholderapi.expansion.PlaceholderExpansion  plugin 5me.clip.placeholderapi.expansion.PlaceholderExpansion  register 5me.clip.placeholderapi.expansion.PlaceholderExpansion  repeat 5me.clip.placeholderapi.expansion.PlaceholderExpansion  toInt 5me.clip.placeholderapi.expansion.PlaceholderExpansion  toIntOrNull 5me.clip.placeholderapi.expansion.PlaceholderExpansion  
unregister 5me.clip.placeholderapi.expansion.PlaceholderExpansion  RegistryAccess net.minecraft.core  MutableComponent net.minecraft.network.chat  string $net.minecraft.network.chat.Component  string +net.minecraft.network.chat.MutableComponent  ServerPlayer net.minecraft.server.level  gameProfile 'net.minecraft.server.level.ServerPlayer  party 'net.minecraft.server.level.ServerPlayer  pc 'net.minecraft.server.level.ServerPlayer  gameProfile (net.minecraft.world.entity.player.Player  	ItemStack net.minecraft.world.item  Bukkit 
org.bukkit  Location 
org.bukkit  Material 
org.bukkit  
NamespacedKey 
org.bukkit  Server 
org.bukkit  Sound 
org.bukkit  createInventory org.bukkit.Bukkit  dispatchCommand org.bukkit.Bukkit  getConsoleSender org.bukkit.Bukkit  	getPlayer org.bukkit.Bukkit  getPluginManager org.bukkit.Bukkit  getScheduler org.bukkit.Bukkit  	getServer org.bukkit.Bukkit  key org.bukkit.Keyed  ARROW org.bukkit.Material  BARRIER org.bukkit.Material  BLACK_CONCRETE_POWDER org.bukkit.Material  BLACK_GLAZED_TERRACOTTA org.bukkit.Material  BLAZE_POWDER org.bukkit.Material  BLUE_CONCRETE_POWDER org.bukkit.Material  BLUE_GLAZED_TERRACOTTA org.bukkit.Material  CYAN_CONCRETE_POWDER org.bukkit.Material  CYAN_GLAZED_TERRACOTTA org.bukkit.Material  DIAMOND org.bukkit.Material  
DIAMOND_BLOCK org.bukkit.Material  
DIAMOND_SWORD org.bukkit.Material  EMERALD org.bukkit.Material  
EMERALD_BLOCK org.bukkit.Material  ENDER_PEARL org.bukkit.Material  EXPERIENCE_BOTTLE org.bukkit.Material  GLOWSTONE_DUST org.bukkit.Material  
GOLD_BLOCK org.bukkit.Material  GRAY_CONCRETE_POWDER org.bukkit.Material  GRAY_GLAZED_TERRACOTTA org.bukkit.Material  GRAY_STAINED_GLASS_PANE org.bukkit.Material  GREEN_CONCRETE_POWDER org.bukkit.Material  GREEN_GLAZED_TERRACOTTA org.bukkit.Material  
IRON_BLOCK org.bukkit.Material  LAPIS_BLOCK org.bukkit.Material  MAGENTA_CONCRETE_POWDER org.bukkit.Material  MAGENTA_GLAZED_TERRACOTTA org.bukkit.Material  NETHER_STAR org.bukkit.Material  OBSIDIAN org.bukkit.Material  PAPER org.bukkit.Material  
PRISMARINE org.bukkit.Material  PRISMARINE_SHARD org.bukkit.Material  PURPLE_CONCRETE_POWDER org.bukkit.Material  PURPLE_GLAZED_TERRACOTTA org.bukkit.Material  PURPUR_BLOCK org.bukkit.Material  QUARTZ_BLOCK org.bukkit.Material  REDSTONE_BLOCK org.bukkit.Material  RED_CONCRETE_POWDER org.bukkit.Material  RED_GLAZED_TERRACOTTA org.bukkit.Material  WHITE_CONCRETE_POWDER org.bukkit.Material  WHITE_GLAZED_TERRACOTTA org.bukkit.Material  YELLOW_CONCRETE_POWDER org.bukkit.Material  YELLOW_GLAZED_TERRACOTTA org.bukkit.Material  name org.bukkit.Material  valueOf org.bukkit.Material  values org.bukkit.Material  hasPlayedBefore org.bukkit.OfflinePlayer  isOnline org.bukkit.OfflinePlayer  	getPlayer org.bukkit.Server  	javaClass org.bukkit.Server  
onlinePlayers org.bukkit.Server  
pluginManager org.bukkit.Server  	scheduler org.bukkit.Server  BLOCK_NOTE_BLOCK_PLING org.bukkit.Sound  ENTITY_VILLAGER_NO org.bukkit.Sound  UI_BUTTON_CLICK org.bukkit.Sound  UI_TOAST_CHALLENGE_COMPLETE org.bukkit.Sound  Command org.bukkit.command  CommandExecutor org.bukkit.command  
CommandSender org.bukkit.command  ConsoleCommandSender org.bukkit.command  
PluginCommand org.bukkit.command  TabCompleter org.bukkit.command  
hasPermission  org.bukkit.command.CommandSender  sendMessage  org.bukkit.command.CommandSender  setExecutor  org.bukkit.command.PluginCommand  ConfigurationSection org.bukkit.configuration  copyDefaults -org.bukkit.configuration.ConfigurationOptions  getKeys -org.bukkit.configuration.ConfigurationSection  	getString -org.bukkit.configuration.ConfigurationSection  	getValues -org.bukkit.configuration.ConfigurationSection  set -org.bukkit.configuration.ConfigurationSection  
addDefault ,org.bukkit.configuration.MemoryConfiguration  contains ,org.bukkit.configuration.MemoryConfiguration  
createSection ,org.bukkit.configuration.MemoryConfiguration  
getBoolean ,org.bukkit.configuration.MemoryConfiguration  getConfigurationSection ,org.bukkit.configuration.MemoryConfiguration  getInt ,org.bukkit.configuration.MemoryConfiguration  getLong ,org.bukkit.configuration.MemoryConfiguration  	getString ,org.bukkit.configuration.MemoryConfiguration  
getStringList ,org.bukkit.configuration.MemoryConfiguration  set ,org.bukkit.configuration.MemoryConfiguration  FileConfiguration org.bukkit.configuration.file  FileConfigurationOptions org.bukkit.configuration.file  YamlConfiguration org.bukkit.configuration.file  
addDefault /org.bukkit.configuration.file.FileConfiguration  contains /org.bukkit.configuration.file.FileConfiguration  
getBoolean /org.bukkit.configuration.file.FileConfiguration  getConfigurationSection /org.bukkit.configuration.file.FileConfiguration  getInt /org.bukkit.configuration.file.FileConfiguration  getLong /org.bukkit.configuration.file.FileConfiguration  	getString /org.bukkit.configuration.file.FileConfiguration  
getStringList /org.bukkit.configuration.file.FileConfiguration  options /org.bukkit.configuration.file.FileConfiguration  save /org.bukkit.configuration.file.FileConfiguration  copyDefaults 6org.bukkit.configuration.file.FileConfigurationOptions  
createSection /org.bukkit.configuration.file.YamlConfiguration  
getBoolean /org.bukkit.configuration.file.YamlConfiguration  getConfigurationSection /org.bukkit.configuration.file.YamlConfiguration  getInt /org.bukkit.configuration.file.YamlConfiguration  getLong /org.bukkit.configuration.file.YamlConfiguration  
getStringList /org.bukkit.configuration.file.YamlConfiguration  loadConfiguration /org.bukkit.configuration.file.YamlConfiguration  save /org.bukkit.configuration.file.YamlConfiguration  set /org.bukkit.configuration.file.YamlConfiguration  Enchantment org.bukkit.enchantments  LURE #org.bukkit.enchantments.Enchantment  
UNBREAKING #org.bukkit.enchantments.Enchantment  HumanEntity org.bukkit.entity  Player org.bukkit.entity  location org.bukkit.entity.Entity  uniqueId org.bukkit.entity.Entity  closeInventory org.bukkit.entity.HumanEntity  	inventory org.bukkit.entity.HumanEntity  
openInventory org.bukkit.entity.HumanEntity  closeInventory org.bukkit.entity.Player  
hasPermission org.bukkit.entity.Player  hasPlayedBefore org.bukkit.entity.Player  	inventory org.bukkit.entity.Player  isOnline org.bukkit.entity.Player  	javaClass org.bukkit.entity.Player  location org.bukkit.entity.Player  name org.bukkit.entity.Player  
openInventory org.bukkit.entity.Player  	playSound org.bukkit.entity.Player  sendMessage org.bukkit.entity.Player  uniqueId org.bukkit.entity.Player  Event org.bukkit.event  EventHandler org.bukkit.event  
EventPriority org.bukkit.event  HandlerList org.bukkit.event  Listener org.bukkit.event  	Companion org.bukkit.event.Event  HandlerList org.bukkit.event.Event  MONITOR org.bukkit.event.EventPriority  	ClickType org.bukkit.event.inventory  InventoryClickEvent org.bukkit.event.inventory  InventoryCloseEvent org.bukkit.event.inventory  InventoryOpenEvent org.bukkit.event.inventory  LEFT $org.bukkit.event.inventory.ClickType  RIGHT $org.bukkit.event.inventory.ClickType  click .org.bukkit.event.inventory.InventoryClickEvent  clickedInventory .org.bukkit.event.inventory.InventoryClickEvent  currentItem .org.bukkit.event.inventory.InventoryClickEvent  isCancelled .org.bukkit.event.inventory.InventoryClickEvent  slot .org.bukkit.event.inventory.InventoryClickEvent  view .org.bukkit.event.inventory.InventoryClickEvent  
whoClicked .org.bukkit.event.inventory.InventoryClickEvent  player .org.bukkit.event.inventory.InventoryCloseEvent  view .org.bukkit.event.inventory.InventoryCloseEvent  view )org.bukkit.event.inventory.InventoryEvent  isCancelled 1org.bukkit.event.inventory.InventoryInteractEvent  
whoClicked 1org.bukkit.event.inventory.InventoryInteractEvent  PlayerJoinEvent org.bukkit.event.player  PlayerQuitEvent org.bukkit.event.player  player #org.bukkit.event.player.PlayerEvent  player 'org.bukkit.event.player.PlayerJoinEvent  player 'org.bukkit.event.player.PlayerQuitEvent  	Inventory org.bukkit.inventory  
InventoryView org.bukkit.inventory  ItemFlag org.bukkit.inventory  	ItemStack org.bukkit.inventory  PlayerInventory org.bukkit.inventory  addItem org.bukkit.inventory.Inventory  getItem org.bukkit.inventory.Inventory  setItem org.bukkit.inventory.Inventory  size org.bukkit.inventory.Inventory  title "org.bukkit.inventory.InventoryView  topInventory "org.bukkit.inventory.InventoryView  HIDE_ADDITIONAL_TOOLTIP org.bukkit.inventory.ItemFlag  HIDE_ATTRIBUTES org.bukkit.inventory.ItemFlag  
HIDE_DESTROYS org.bukkit.inventory.ItemFlag  HIDE_DYE org.bukkit.inventory.ItemFlag  
HIDE_ENCHANTS org.bukkit.inventory.ItemFlag  HIDE_PLACED_ON org.bukkit.inventory.ItemFlag  HIDE_UNBREAKABLE org.bukkit.inventory.ItemFlag  addUnsafeEnchantment org.bukkit.inventory.ItemStack  amount org.bukkit.inventory.ItemStack  clone org.bukkit.inventory.ItemStack  deserialize org.bukkit.inventory.ItemStack  enchantments org.bukkit.inventory.ItemStack  itemMeta org.bukkit.inventory.ItemStack  	serialize org.bukkit.inventory.ItemStack  type org.bukkit.inventory.ItemStack  addItem $org.bukkit.inventory.PlayerInventory  ItemMeta org.bukkit.inventory.meta  
addEnchant "org.bukkit.inventory.meta.ItemMeta  addItemFlags "org.bukkit.inventory.meta.ItemMeta  displayName "org.bukkit.inventory.meta.ItemMeta  
isUnbreakable "org.bukkit.inventory.meta.ItemMeta  	itemFlags "org.bukkit.inventory.meta.ItemMeta  let "org.bukkit.inventory.meta.ItemMeta  lore "org.bukkit.inventory.meta.ItemMeta  setCustomModelData "org.bukkit.inventory.meta.ItemMeta  setDisplayName "org.bukkit.inventory.meta.ItemMeta  
hasPermission "org.bukkit.permissions.Permissible  Plugin org.bukkit.plugin  PluginDescriptionFile org.bukkit.plugin  
PluginManager org.bukkit.plugin  
AceDexCommand org.bukkit.plugin.PluginBase  AceDexConfig org.bukkit.plugin.PluginBase  AsyncGuiManager org.bukkit.plugin.PluginBase  Class org.bukkit.plugin.PluginBase  ClassNotFoundException org.bukkit.plugin.PluginBase  CobblemonEventListener org.bukkit.plugin.PluginBase  CobblemonListener org.bukkit.plugin.PluginBase  
DexMainGui org.bukkit.plugin.PluginBase  	Exception org.bukkit.plugin.PluginBase  GenerationManager org.bukkit.plugin.PluginBase  GuiButtonCacheManager org.bukkit.plugin.PluginBase  LicenseManager org.bukkit.plugin.PluginBase  PLUGIN_NAME org.bukkit.plugin.PluginBase  PlaceholderAPIExpansion org.bukkit.plugin.PluginBase  PlayerDataManager org.bukkit.plugin.PluginBase  PokeBallItemCreator org.bukkit.plugin.PluginBase  PokemonCaptureListener org.bukkit.plugin.PluginBase  PokemonDetector org.bukkit.plugin.PluginBase  PokemonItemCreator org.bukkit.plugin.PluginBase  PokemonModelPreloader org.bukkit.plugin.PluginBase  PremiumRewardConfig org.bukkit.plugin.PluginBase  PremiumRewardGui org.bukkit.plugin.PluginBase  
RewardManager org.bukkit.plugin.PluginBase  Runnable org.bukkit.plugin.PluginBase  VERSION org.bukkit.plugin.PluginBase  cn org.bukkit.plugin.PluginBase  debugCobblemonStorage org.bukkit.plugin.PluginBase  filter org.bukkit.plugin.PluginBase  instance org.bukkit.plugin.PluginBase  isBlank org.bukkit.plugin.PluginBase  
isInitialized org.bukkit.plugin.PluginBase  
startsWith org.bukkit.plugin.PluginBase  version 'org.bukkit.plugin.PluginDescriptionFile  
disablePlugin org.bukkit.plugin.PluginManager  	getPlugin org.bukkit.plugin.PluginManager  registerEvents org.bukkit.plugin.PluginManager  
JavaPlugin org.bukkit.plugin.java  
AceDexCommand !org.bukkit.plugin.java.JavaPlugin  AceDexConfig !org.bukkit.plugin.java.JavaPlugin  AsyncGuiManager !org.bukkit.plugin.java.JavaPlugin  Class !org.bukkit.plugin.java.JavaPlugin  ClassNotFoundException !org.bukkit.plugin.java.JavaPlugin  CobblemonEventListener !org.bukkit.plugin.java.JavaPlugin  CobblemonListener !org.bukkit.plugin.java.JavaPlugin  
DexMainGui !org.bukkit.plugin.java.JavaPlugin  	Exception !org.bukkit.plugin.java.JavaPlugin  GenerationManager !org.bukkit.plugin.java.JavaPlugin  GuiButtonCacheManager !org.bukkit.plugin.java.JavaPlugin  LicenseManager !org.bukkit.plugin.java.JavaPlugin  PLUGIN_NAME !org.bukkit.plugin.java.JavaPlugin  PlaceholderAPIExpansion !org.bukkit.plugin.java.JavaPlugin  PlayerDataManager !org.bukkit.plugin.java.JavaPlugin  PokeBallItemCreator !org.bukkit.plugin.java.JavaPlugin  PokemonCaptureListener !org.bukkit.plugin.java.JavaPlugin  PokemonDetector !org.bukkit.plugin.java.JavaPlugin  PokemonItemCreator !org.bukkit.plugin.java.JavaPlugin  PokemonModelPreloader !org.bukkit.plugin.java.JavaPlugin  PremiumRewardConfig !org.bukkit.plugin.java.JavaPlugin  PremiumRewardGui !org.bukkit.plugin.java.JavaPlugin  
RewardManager !org.bukkit.plugin.java.JavaPlugin  Runnable !org.bukkit.plugin.java.JavaPlugin  VERSION !org.bukkit.plugin.java.JavaPlugin  cn !org.bukkit.plugin.java.JavaPlugin  config !org.bukkit.plugin.java.JavaPlugin  
dataFolder !org.bukkit.plugin.java.JavaPlugin  debugCobblemonStorage !org.bukkit.plugin.java.JavaPlugin  description !org.bukkit.plugin.java.JavaPlugin  filter !org.bukkit.plugin.java.JavaPlugin  
getCommand !org.bukkit.plugin.java.JavaPlugin  instance !org.bukkit.plugin.java.JavaPlugin  isBlank !org.bukkit.plugin.java.JavaPlugin  
isInitialized !org.bukkit.plugin.java.JavaPlugin  logger !org.bukkit.plugin.java.JavaPlugin  saveDefaultConfig !org.bukkit.plugin.java.JavaPlugin  saveResource !org.bukkit.plugin.java.JavaPlugin  server !org.bukkit.plugin.java.JavaPlugin  
startsWith !org.bukkit.plugin.java.JavaPlugin  BukkitScheduler org.bukkit.scheduler  
BukkitTask org.bukkit.scheduler  runTask $org.bukkit.scheduler.BukkitScheduler  runTaskAsynchronously $org.bukkit.scheduler.BukkitScheduler  runTaskLater $org.bukkit.scheduler.BukkitScheduler  runTaskTimerAsynchronously $org.bukkit.scheduler.BukkitScheduler                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          