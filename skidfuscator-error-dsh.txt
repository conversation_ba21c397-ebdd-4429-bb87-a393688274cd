handler=Block #VY, types=[Ljava/lang/RuntimeException;], range=[Block #CQ, Block #CP]
handler=Block #WC, types=[Ljava/io/IOException;], range=[Block #CT, Block #CS]
handler=Block #WG, types=[Ljava/lang/RuntimeException;], range=[Block #CW, Block #CV]
handler=Block #WK, types=[Ljava/lang/IllegalAccessException;], range=[Block #CZ, Block #CY]
handler=Block #WO, types=[Ljava/lang/RuntimeException;], range=[Block #DC, Block #DB]
handler=Block #WS, types=[Ljava/io/IOException;], range=[Block #DF, Block #DE]
handler=Block #WW, types=[Ljava/io/IOException;], range=[Block #DI, Block #DH]
handler=Block #XA, types=[Ljava/lang/RuntimeException;], range=[Block #DL, Block #DK]
handler=Block #XE, types=[Ljava/lang/IllegalAccessException;], range=[Block #DO, Block #DN]
handler=Block #XI, types=[Ljava/lang/IllegalAccessException;], range=[Block #DR, Block #DQ]
handler=Block #XM, types=[Ljava/lang/IllegalAccessException;], range=[Block #DU, Block #DT]
handler=Block #XQ, types=[Ljava/lang/RuntimeException;], range=[Block #DX, Block #DW]
handler=Block #XU, types=[Ljava/lang/RuntimeException;], range=[Block #EA, Block #DZ]
handler=Block #XY, types=[Ljava/io/IOException;], range=[Block #ED, Block #EC]
handler=Block #YC, types=[Ljava/lang/RuntimeException;], range=[Block #EG, Block #EF]
handler=Block #YG, types=[Ljava/lang/RuntimeException;], range=[Block #EJ, Block #EI]
handler=Block #YK, types=[Ljava/lang/RuntimeException;], range=[Block #EM, Block #EL]
handler=Block #YO, types=[Ljava/io/IOException;], range=[Block #EP, Block #EO]
handler=Block #YS, types=[Ljava/lang/IllegalAccessException;], range=[Block #ES, Block #ER]
handler=Block #YW, types=[Ljava/io/IOException;], range=[Block #EV, Block #EU]
handler=Block #ZA, types=[Ljava/lang/RuntimeException;], range=[Block #EY, Block #EX]
handler=Block #ZE, types=[Ljava/lang/RuntimeException;], range=[Block #FB, Block #FA]
handler=Block #ZI, types=[Ljava/lang/RuntimeException;], range=[Block #FE, Block #FD]
handler=Block #ZM, types=[Ljava/lang/IllegalAccessException;], range=[Block #FH, Block #FG]
handler=Block #ZQ, types=[Ljava/io/IOException;], range=[Block #FK, Block #FJ]
handler=Block #ZU, types=[Ljava/lang/IllegalAccessException;], range=[Block #FN, Block #FM]
handler=Block #ZY, types=[Ljava/lang/RuntimeException;], range=[Block #FQ, Block #FP]
handler=Block #AAC, types=[Ljava/lang/RuntimeException;], range=[Block #FT, Block #FS]
handler=Block #AAG, types=[Ljava/lang/IllegalAccessException;], range=[Block #FW, Block #FV]
handler=Block #AAK, types=[Ljava/lang/IllegalAccessException;], range=[Block #FZ, Block #FY]
handler=Block #AAO, types=[Ljava/lang/RuntimeException;], range=[Block #GC, Block #GB]
handler=Block #AAS, types=[Ljava/lang/RuntimeException;], range=[Block #GF, Block #GE]
handler=Block #AAW, types=[Ljava/lang/RuntimeException;], range=[Block #GI, Block #GH]
handler=Block #ABA, types=[Ljava/lang/IllegalAccessException;], range=[Block #GL, Block #GK]
handler=Block #ABE, types=[Ljava/lang/IllegalAccessException;], range=[Block #GO, Block #GN]
handler=Block #ABI, types=[Ljava/lang/RuntimeException;], range=[Block #GR, Block #GQ]
handler=Block #ABM, types=[Ljava/io/IOException;], range=[Block #GU, Block #GT]
handler=Block #ABQ, types=[Ljava/lang/RuntimeException;], range=[Block #GX, Block #GW]
handler=Block #ABU, types=[Ljava/lang/RuntimeException;], range=[Block #HA, Block #GZ]
handler=Block #ABY, types=[Ljava/lang/RuntimeException;], range=[Block #HD, Block #HC]
handler=Block #ACC, types=[Ljava/lang/IllegalAccessException;], range=[Block #HG, Block #HF]
handler=Block #ACG, types=[Ljava/io/IOException;], range=[Block #HJ, Block #HI]
handler=Block #ACK, types=[Ljava/io/IOException;], range=[Block #HM, Block #HL]
handler=Block #ACO, types=[Ljava/io/IOException;], range=[Block #HP, Block #HO]
handler=Block #ACS, types=[Ljava/io/IOException;], range=[Block #HS, Block #HR]
handler=Block #ACW, types=[Ljava/lang/RuntimeException;], range=[Block #HV, Block #HU]
handler=Block #ADA, types=[Ljava/lang/RuntimeException;], range=[Block #HY, Block #HX]
handler=Block #ADE, types=[Ljava/lang/RuntimeException;], range=[Block #IB, Block #IA]
handler=Block #ADI, types=[Ljava/io/IOException;], range=[Block #IE, Block #ID]
handler=Block #ADM, types=[Ljava/lang/IllegalAccessException;], range=[Block #IH, Block #IG]
handler=Block #ADQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #IK, Block #IJ]
handler=Block #ADU, types=[Ljava/lang/IllegalAccessException;], range=[Block #IN, Block #IM]
handler=Block #ADY, types=[Ljava/lang/IllegalAccessException;], range=[Block #IQ, Block #IP]
handler=Block #AEC, types=[Ljava/lang/IllegalAccessException;], range=[Block #IT, Block #IS]
handler=Block #AEG, types=[Ljava/lang/RuntimeException;], range=[Block #IW, Block #IV]
handler=Block #AEK, types=[Ljava/io/IOException;], range=[Block #IZ, Block #IY]
===#Block A(size=6, flags=1)===
   0. lvar105 = {400697033 ^ {1740402787 ^ 1079147594}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. synth(lvar2 = lvar2);
   4. synth(lvar3 = lvar3);
   5. lvar105 = {526277965 ^ lvar105};
      -> Immediate #A -> #B
===#Block B(size=1, flags=0)===
   0. lvar105 = {756523224 ^ lvar105};
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=3, flags=0)===
   0. lvar4 = lvar3;
   1. if (lvar4 == {38131573 ^ lvar105})
      goto TT
   2. lvar105 = {500201371 ^ lvar105};
      -> ConditionalJump[IF_ICMPEQ] #C -> #TT
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=6, flags=0)===
   0. lvar9 = lvar1;
   1. lvar8 = lvar9;
   2. lvar10 = lvar8;
   3. lvar11 = lvar10.hashCode();
   4. svar107 = {lvar11 ^ lvar105};
   5. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(svar107)) {
      case 223448008:
      	 goto	#UO
      case 223448010:
      	 goto	#UP
      case 223448014:
      	 goto	#UQ
      case *********:
      	 goto	#US
      case 223448018:
      	 goto	#UT
      case 223448020:
      	 goto	#UU
      case 223448022:
      	 goto	#UV
      case 223448024:
      	 goto	#UX
      case 223448028:
      	 goto	#UY
      default:
      	 goto	#UZ
   }
      -> DefaultSwitch #D -> #UZ
      -> Switch[223448014] #D -> #UQ
      -> Switch[223448022] #D -> #UV
      -> Switch[*********] #D -> #US
      -> Switch[223448010] #D -> #UP
      -> Switch[223448008] #D -> #UO
      -> Switch[223448024] #D -> #UX
      -> Switch[223448018] #D -> #UT
      -> Switch[223448020] #D -> #UU
      -> Switch[223448028] #D -> #UY
      <- Immediate #C -> #D
===#Block UY(size=2, flags=10100)===
   0. lvar105 = {1840588209 ^ lvar105};
   1. goto K
      -> UnconditionalJump[GOTO] #UY -> #K
      <- Switch[223448028] #D -> #UY
===#Block K(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar8;
   2. lvar76 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.veuknfauotwmwkm(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar18 = lvar17.equals(lvar76);
   4. if (lvar18 != {1914733919 ^ lvar105})
      goto SW
   5. lvar105 = {633149154 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #K -> #SW
      -> Immediate #K -> #M
      <- UnconditionalJump[GOTO] #UY -> #K
===#Block M(size=1, flags=0)===
   0. goto MU
      -> UnconditionalJump[GOTO] #M -> #MU
      <- Immediate #K -> #M
===#Block MU(size=2, flags=10100)===
   0. lvar105 = {1790358579 ^ lvar105};
   1. goto GC
      -> UnconditionalJump[GOTO] #MU -> #GC
      <- UnconditionalJump[GOTO] #M -> #MU
===#Block GC(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 109289864)
      goto GB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GC -> #GB
      -> TryCatch range: [GC...GB] -> AAO ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #MU -> #GC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GC...GB] -> AAO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GC -> #GB
===#Block AAO(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -380027791:
      	 goto	#AAP
      case 923153362:
      	 goto	#AAQ
      default:
      	 goto	#AAR
   }
      -> Switch[923153362] #AAO -> #AAQ
      -> Switch[-380027791] #AAO -> #AAP
      -> DefaultSwitch #AAO -> #AAR
      <- TryCatch range: [GC...GB] -> AAO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GC...GB] -> AAO ([Ljava/lang/RuntimeException;])
===#Block AAR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAO -> #AAR
===#Block AAP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1055887129);
   1. goto GD
      -> UnconditionalJump[GOTO] #AAP -> #GD
      <- Switch[-380027791] #AAO -> #AAP
===#Block AAQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1160015597);
   1. goto GD
      -> UnconditionalJump[GOTO] #AAQ -> #GD
      <- Switch[923153362] #AAO -> #AAQ
===#Block GD(size=2, flags=0)===
   0. _consume(catch());
   1. goto PN
      -> UnconditionalJump[GOTO] #GD -> #PN
      <- UnconditionalJump[GOTO] #AAQ -> #GD
      <- UnconditionalJump[GOTO] #AAP -> #GD
===#Block PN(size=2, flags=10100)===
   0. lvar105 = {84537397 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #PN -> #AF
      <- UnconditionalJump[GOTO] #GD -> #PN
===#Block SW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1324322164);
   1. goto JB
      -> UnconditionalJump[GOTO] #SW -> #JB
      <- ConditionalJump[IF_ICMPNE] #K -> #SW
===#Block JB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -428326567)
      goto SY
   1. goto LC
      -> UnconditionalJump[GOTO] #JB -> #LC
      -> ConditionalJump[IF_ICMPEQ] #JB -> #SY
      <- UnconditionalJump[GOTO] #SW -> #JB
===#Block SY(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 97592092:
      	 goto	#SZ
      case 800927982:
      	 goto	#JC
      case 976005689:
      	 goto	#SY
      case 1245959417:
      	 goto	#L
      default:
      	 goto	#JC
   }
      -> Immediate #SY -> #SZ
      -> Switch[800927982] #SY -> #JC
      -> DefaultSwitch #SY -> #JC
      -> Switch[976005689] #SY -> #SY
      -> Switch[1245959417] #SY -> #L
      -> Switch[97592092] #SY -> #SZ
      <- Switch[976005689] #SY -> #SY
      <- ConditionalJump[IF_ICMPEQ] #JB -> #SY
===#Block SZ(size=2, flags=100)===
   0. lvar105 = {236842965 ^ lvar105};
   1. goto L
      -> UnconditionalJump[GOTO] #SZ -> #L
      <- Immediate #SY -> #SZ
      <- Switch[97592092] #SY -> #SZ
===#Block L(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GOLD_BLOCK;
   2. goto NW
      -> UnconditionalJump[GOTO] #L -> #NW
      <- UnconditionalJump[GOTO] #SZ -> #L
      <- Switch[1245959417] #SY -> #L
===#Block NW(size=2, flags=10100)===
   0. lvar105 = {2038367039 ^ lvar105};
   1. goto CQ
      -> UnconditionalJump[GOTO] #NW -> #CQ
      <- UnconditionalJump[GOTO] #L -> #NW
===#Block CQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 44081304)
      goto CP
   1. throw nullconst;
      -> TryCatch range: [CQ...CP] -> VY ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #CQ -> #CP
      <- UnconditionalJump[GOTO] #NW -> #CQ
===#Block CP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CQ...CP] -> VY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CQ -> #CP
===#Block VY(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 243898066:
      	 goto	#WA
      case 1567565322:
      	 goto	#VZ
      default:
      	 goto	#WB
   }
      -> Switch[243898066] #VY -> #WA
      -> Switch[1567565322] #VY -> #VZ
      -> DefaultSwitch #VY -> #WB
      <- TryCatch range: [CQ...CP] -> VY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CQ...CP] -> VY ([Ljava/lang/RuntimeException;])
===#Block WB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #VY -> #WB
===#Block VZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1568375650);
   1. goto CR
      -> UnconditionalJump[GOTO] #VZ -> #CR
      <- Switch[1567565322] #VY -> #VZ
===#Block WA(size=2, flags=10100)===
   0. lvar105 = {1460098425 ^ lvar105};
   1. goto CR
      -> UnconditionalJump[GOTO] #WA -> #CR
      <- Switch[243898066] #VY -> #WA
===#Block CR(size=2, flags=0)===
   0. _consume(catch());
   1. goto KH
      -> UnconditionalJump[GOTO] #CR -> #KH
      <- UnconditionalJump[GOTO] #WA -> #CR
      <- UnconditionalJump[GOTO] #VZ -> #CR
===#Block KH(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 114253960:
      	 goto	#JC
      case 205487645:
      	 goto	#KI
      case 1379263213:
      	 goto	#CN
      case 1863430438:
      	 goto	#KH
      default:
      	 goto	#JC
   }
      -> Immediate #KH -> #KI
      -> Switch[114253960] #KH -> #JC
      -> Switch[1379263213] #KH -> #CN
      -> DefaultSwitch #KH -> #JC
      -> Switch[205487645] #KH -> #KI
      -> Switch[1863430438] #KH -> #KH
      <- Switch[1863430438] #KH -> #KH
      <- UnconditionalJump[GOTO] #CR -> #KH
===#Block KI(size=2, flags=100)===
   0. lvar105 = {1602276319 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #KI -> #CN
      <- Immediate #KH -> #KI
      <- Switch[205487645] #KH -> #KI
===#Block LC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1738517633);
   1. goto JC
      -> UnconditionalJump[GOTO] #LC -> #JC
      <- UnconditionalJump[GOTO] #JB -> #LC
===#Block UU(size=2, flags=10100)===
   0. lvar105 = {994358302 ^ lvar105};
   1. goto E
      -> UnconditionalJump[GOTO] #UU -> #E
      <- Switch[223448020] #D -> #UU
===#Block E(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar8;
   2. lvar5 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.vypunnokmfnwmtl(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar13 = lvar12.equals(lvar5);
   4. if (lvar13 != {617682160 ^ lvar105})
      goto UF
   5. lvar105 = {698849876 ^ lvar105};
      -> Immediate #E -> #F
      -> ConditionalJump[IF_ICMPNE] #E -> #UF
      <- UnconditionalJump[GOTO] #UU -> #E
===#Block UF(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 123026496:
      	 goto	#UG
      case 334759031:
      	 goto	#UF
      case 610314873:
      	 goto	#JC
      case 1789931725:
      	 goto	#JW
      default:
      	 goto	#JC
   }
      -> Switch[123026496] #UF -> #UG
      -> Immediate #UF -> #UG
      -> Switch[1789931725] #UF -> #JW
      -> DefaultSwitch #UF -> #JC
      -> Switch[610314873] #UF -> #JC
      -> Switch[334759031] #UF -> #UF
      <- Switch[334759031] #UF -> #UF
      <- ConditionalJump[IF_ICMPNE] #E -> #UF
===#Block UG(size=2, flags=100)===
   0. lvar105 = {1799985858 ^ lvar105};
   1. goto JW
      -> UnconditionalJump[GOTO] #UG -> #JW
      <- Switch[123026496] #UF -> #UG
      <- Immediate #UF -> #UG
===#Block JW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 2093248914)
      goto SU
   1. goto RG
      -> UnconditionalJump[GOTO] #JW -> #RG
      -> ConditionalJump[IF_ICMPEQ] #JW -> #SU
      <- Switch[1789931725] #UF -> #JW
      <- UnconditionalJump[GOTO] #UG -> #JW
===#Block SU(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 108389884:
      	 goto	#G
      case 171432984:
      	 goto	#SV
      case 1530570872:
      	 goto	#SU
      case 1820454188:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #SU -> #JC
      -> Immediate #SU -> #SV
      -> Switch[108389884] #SU -> #G
      -> Switch[1530570872] #SU -> #SU
      -> Switch[171432984] #SU -> #SV
      -> Switch[1820454188] #SU -> #JC
      <- ConditionalJump[IF_ICMPEQ] #JW -> #SU
      <- Switch[1530570872] #SU -> #SU
===#Block SV(size=2, flags=100)===
   0. lvar105 = {1241498426 ^ lvar105};
   1. goto G
      -> UnconditionalJump[GOTO] #SV -> #G
      <- Immediate #SU -> #SV
      <- Switch[171432984] #SU -> #SV
===#Block G(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.QUARTZ_BLOCK;
   2. goto KP
      -> UnconditionalJump[GOTO] #G -> #KP
      <- Switch[108389884] #SU -> #G
      <- UnconditionalJump[GOTO] #SV -> #G
===#Block KP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1127104980);
   1. goto DX
      -> UnconditionalJump[GOTO] #KP -> #DX
      <- UnconditionalJump[GOTO] #G -> #KP
===#Block DX(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 104713635)
      goto DW
   1. throw nullconst;
      -> TryCatch range: [DX...DW] -> XQ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #DX -> #DW
      <- UnconditionalJump[GOTO] #KP -> #DX
===#Block DW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DX...DW] -> XQ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DX -> #DW
===#Block XQ(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1855611589:
      	 goto	#XS
      case 709388002:
      	 goto	#XR
      default:
      	 goto	#XT
   }
      -> Switch[709388002] #XQ -> #XR
      -> Switch[-1855611589] #XQ -> #XS
      -> DefaultSwitch #XQ -> #XT
      <- TryCatch range: [DX...DW] -> XQ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DX...DW] -> XQ ([Ljava/lang/RuntimeException;])
===#Block XT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XQ -> #XT
===#Block XS(size=2, flags=10100)===
   0. lvar105 = {484735381 ^ lvar105};
   1. goto DY
      -> UnconditionalJump[GOTO] #XS -> #DY
      <- Switch[-1855611589] #XQ -> #XS
===#Block XR(size=2, flags=10100)===
   0. lvar105 = {729925358 ^ lvar105};
   1. goto DY
      -> UnconditionalJump[GOTO] #XR -> #DY
      <- Switch[709388002] #XQ -> #XR
===#Block DY(size=2, flags=0)===
   0. _consume(catch());
   1. goto QV
      -> UnconditionalJump[GOTO] #DY -> #QV
      <- UnconditionalJump[GOTO] #XS -> #DY
      <- UnconditionalJump[GOTO] #XR -> #DY
===#Block QV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 664692302);
   1. goto CN
      -> UnconditionalJump[GOTO] #QV -> #CN
      <- UnconditionalJump[GOTO] #DY -> #QV
===#Block RG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 348662424);
   1. goto JC
      -> UnconditionalJump[GOTO] #RG -> #JC
      <- UnconditionalJump[GOTO] #JW -> #RG
===#Block F(size=1, flags=0)===
   0. goto LV
      -> UnconditionalJump[GOTO] #F -> #LV
      <- Immediate #E -> #F
===#Block LV(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 139392687:
      	 goto	#LV
      case 169195819:
      	 goto	#LW
      case 959515151:
      	 goto	#JC
      case 1158358828:
      	 goto	#IK
      default:
      	 goto	#JC
   }
      -> Switch[1158358828] #LV -> #IK
      -> Immediate #LV -> #LW
      -> Switch[959515151] #LV -> #JC
      -> DefaultSwitch #LV -> #JC
      -> Switch[139392687] #LV -> #LV
      -> Switch[169195819] #LV -> #LW
      <- UnconditionalJump[GOTO] #F -> #LV
      <- Switch[139392687] #LV -> #LV
===#Block LW(size=2, flags=100)===
   0. lvar105 = {224518275 ^ lvar105};
   1. goto IK
      -> UnconditionalJump[GOTO] #LW -> #IK
      <- Immediate #LV -> #LW
      <- Switch[169195819] #LV -> #LW
===#Block IK(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 1439571)
      goto IJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IK -> #IJ
      -> TryCatch range: [IK...IJ] -> ADQ ([Ljava/lang/IllegalAccessException;])
      <- Switch[1158358828] #LV -> #IK
      <- UnconditionalJump[GOTO] #LW -> #IK
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IK...IJ] -> ADQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IK -> #IJ
===#Block ADQ(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 12284216:
      	 goto	#ADR
      case 680481563:
      	 goto	#ADS
      default:
      	 goto	#ADT
   }
      -> DefaultSwitch #ADQ -> #ADT
      -> Switch[680481563] #ADQ -> #ADS
      -> Switch[12284216] #ADQ -> #ADR
      <- TryCatch range: [IK...IJ] -> ADQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IK...IJ] -> ADQ ([Ljava/lang/IllegalAccessException;])
===#Block ADR(size=2, flags=10100)===
   0. lvar105 = {941246307 ^ lvar105};
   1. goto IL
      -> UnconditionalJump[GOTO] #ADR -> #IL
      <- Switch[12284216] #ADQ -> #ADR
===#Block ADS(size=2, flags=10100)===
   0. lvar105 = {1562166055 ^ lvar105};
   1. goto IL
      -> UnconditionalJump[GOTO] #ADS -> #IL
      <- Switch[680481563] #ADQ -> #ADS
===#Block IL(size=2, flags=0)===
   0. _consume(catch());
   1. goto KJ
      -> UnconditionalJump[GOTO] #IL -> #KJ
      <- UnconditionalJump[GOTO] #ADS -> #IL
      <- UnconditionalJump[GOTO] #ADR -> #IL
===#Block KJ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 211372030:
      	 goto	#KK
      case 327236265:
      	 goto	#AF
      case 527548329:
      	 goto	#KJ
      case 849621575:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[849621575] #KJ -> #JC
      -> Immediate #KJ -> #KK
      -> DefaultSwitch #KJ -> #JC
      -> Switch[327236265] #KJ -> #AF
      -> Switch[211372030] #KJ -> #KK
      -> Switch[527548329] #KJ -> #KJ
      <- UnconditionalJump[GOTO] #IL -> #KJ
      <- Switch[527548329] #KJ -> #KJ
===#Block KK(size=2, flags=100)===
   0. lvar105 = {1052799462 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #KK -> #AF
      <- Immediate #KJ -> #KK
      <- Switch[211372030] #KJ -> #KK
===#Block ADT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADQ -> #ADT
===#Block UT(size=2, flags=10100)===
   0. lvar105 = {572433881 ^ lvar105};
   1. goto AC
      -> UnconditionalJump[GOTO] #UT -> #AC
      <- Switch[223448018] #D -> #UT
===#Block AC(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar8;
   2. lvar82 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.xcbigesvzirrjzu(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar30 = lvar29.equals(lvar82);
   4. if (lvar30 != {1032521015 ^ lvar105})
      goto UL
   5. lvar105 = {401184926 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AC -> #UL
      -> Immediate #AC -> #AE
      <- UnconditionalJump[GOTO] #UT -> #AC
===#Block AE(size=1, flags=0)===
   0. goto PY
      -> UnconditionalJump[GOTO] #AE -> #PY
      <- Immediate #AC -> #AE
===#Block PY(size=2, flags=10100)===
   0. lvar105 = {1201084901 ^ lvar105};
   1. goto DO
      -> UnconditionalJump[GOTO] #PY -> #DO
      <- UnconditionalJump[GOTO] #AE -> #PY
===#Block DO(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 84663718)
      goto DN
   1. throw nullconst;
      -> TryCatch range: [DO...DN] -> XE ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DO -> #DN
      <- UnconditionalJump[GOTO] #PY -> #DO
===#Block DN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DO...DN] -> XE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DO -> #DN
===#Block XE(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 1292162123:
      	 goto	#XG
      case 1873551971:
      	 goto	#XF
      default:
      	 goto	#XH
   }
      -> Switch[1292162123] #XE -> #XG
      -> Switch[1873551971] #XE -> #XF
      -> DefaultSwitch #XE -> #XH
      <- TryCatch range: [DO...DN] -> XE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DO...DN] -> XE ([Ljava/lang/IllegalAccessException;])
===#Block XH(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #XE -> #XH
===#Block XF(size=2, flags=10100)===
   0. lvar105 = {619580932 ^ lvar105};
   1. goto DP
      -> UnconditionalJump[GOTO] #XF -> #DP
      <- Switch[1873551971] #XE -> #XF
===#Block XG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 549130433);
   1. goto DP
      -> UnconditionalJump[GOTO] #XG -> #DP
      <- Switch[1292162123] #XE -> #XG
===#Block DP(size=2, flags=0)===
   0. _consume(catch());
   1. goto PO
      -> UnconditionalJump[GOTO] #DP -> #PO
      <- UnconditionalJump[GOTO] #XF -> #DP
      <- UnconditionalJump[GOTO] #XG -> #DP
===#Block PO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1339478762);
   1. goto AF
      -> UnconditionalJump[GOTO] #PO -> #AF
      <- UnconditionalJump[GOTO] #DP -> #PO
===#Block UL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 973290554);
   1. goto KD
      -> UnconditionalJump[GOTO] #UL -> #KD
      <- ConditionalJump[IF_ICMPNE] #AC -> #UL
===#Block KD(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 1010944104)
      goto TE
   1. goto LL
      -> ConditionalJump[IF_ICMPEQ] #KD -> #TE
      -> UnconditionalJump[GOTO] #KD -> #LL
      <- UnconditionalJump[GOTO] #UL -> #KD
===#Block LL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1557700007);
   1. goto JC
      -> UnconditionalJump[GOTO] #LL -> #JC
      <- UnconditionalJump[GOTO] #KD -> #LL
===#Block TE(size=2, flags=10100)===
   0. lvar105 = {2098244622 ^ lvar105};
   1. goto AD
      -> UnconditionalJump[GOTO] #TE -> #AD
      <- ConditionalJump[IF_ICMPEQ] #KD -> #TE
===#Block AD(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.LAPIS_BLOCK;
   2. goto PR
      -> UnconditionalJump[GOTO] #AD -> #PR
      <- UnconditionalJump[GOTO] #TE -> #AD
===#Block PR(size=2, flags=10100)===
   0. lvar105 = {54631621 ^ lvar105};
   1. goto CZ
      -> UnconditionalJump[GOTO] #PR -> #CZ
      <- UnconditionalJump[GOTO] #AD -> #PR
===#Block CZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 202701622)
      goto CY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CZ -> #CY
      -> TryCatch range: [CZ...CY] -> WK ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #PR -> #CZ
===#Block CY(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CZ...CY] -> WK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CZ -> #CY
===#Block WK(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1240264719:
      	 goto	#WM
      case -825733581:
      	 goto	#WL
      default:
      	 goto	#WN
   }
      -> DefaultSwitch #WK -> #WN
      -> Switch[-1240264719] #WK -> #WM
      -> Switch[-825733581] #WK -> #WL
      <- TryCatch range: [CZ...CY] -> WK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CZ...CY] -> WK ([Ljava/lang/IllegalAccessException;])
===#Block WL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1472794613);
   1. goto DA
      -> UnconditionalJump[GOTO] #WL -> #DA
      <- Switch[-825733581] #WK -> #WL
===#Block WM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 416442317);
   1. goto DA
      -> UnconditionalJump[GOTO] #WM -> #DA
      <- Switch[-1240264719] #WK -> #WM
===#Block DA(size=2, flags=0)===
   0. _consume(catch());
   1. goto QN
      -> UnconditionalJump[GOTO] #DA -> #QN
      <- UnconditionalJump[GOTO] #WL -> #DA
      <- UnconditionalJump[GOTO] #WM -> #DA
===#Block QN(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 155132434:
      	 goto	#QO
      case 981869356:
      	 goto	#QN
      case 1478464266:
      	 goto	#JC
      case 1642426341:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[155132434] #QN -> #QO
      -> Immediate #QN -> #QO
      -> Switch[981869356] #QN -> #QN
      -> DefaultSwitch #QN -> #JC
      -> Switch[1642426341] #QN -> #CN
      -> Switch[1478464266] #QN -> #JC
      <- UnconditionalJump[GOTO] #DA -> #QN
      <- Switch[981869356] #QN -> #QN
===#Block QO(size=2, flags=100)===
   0. lvar105 = {1732684367 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QO -> #CN
      <- Switch[155132434] #QN -> #QO
      <- Immediate #QN -> #QO
===#Block WN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #WK -> #WN
===#Block UX(size=2, flags=10100)===
   0. lvar105 = {244299800 ^ lvar105};
   1. goto T
      -> UnconditionalJump[GOTO] #UX -> #T
      <- Switch[223448024] #D -> #UX
===#Block T(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar23 = lvar8;
   2. lvar79 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.wwyjlldqasrhvsc(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar24 = lvar23.equals(lvar79);
   4. if (lvar24 != {286922998 ^ lvar105})
      goto SS
   5. lvar105 = {539240483 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #T -> #SS
      -> Immediate #T -> #U
      <- UnconditionalJump[GOTO] #UX -> #T
===#Block U(size=1, flags=0)===
   0. goto MR
      -> UnconditionalJump[GOTO] #U -> #MR
      <- Immediate #T -> #U
===#Block MR(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 258494370:
      	 goto	#MS
      case 270968402:
      	 goto	#FQ
      case 792107753:
      	 goto	#MR
      case 1318063051:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[792107753] #MR -> #MR
      -> Switch[258494370] #MR -> #MS
      -> Immediate #MR -> #MS
      -> Switch[1318063051] #MR -> #JC
      -> DefaultSwitch #MR -> #JC
      -> Switch[270968402] #MR -> #FQ
      <- Switch[792107753] #MR -> #MR
      <- UnconditionalJump[GOTO] #U -> #MR
===#Block MS(size=2, flags=100)===
   0. lvar105 = {1523102642 ^ lvar105};
   1. goto FQ
      -> UnconditionalJump[GOTO] #MS -> #FQ
      <- Switch[258494370] #MR -> #MS
      <- Immediate #MR -> #MS
===#Block FQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 19789153)
      goto FP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FQ -> #FP
      -> TryCatch range: [FQ...FP] -> ZY ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #MS -> #FQ
      <- Switch[270968402] #MR -> #FQ
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FQ...FP] -> ZY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FQ -> #FP
===#Block ZY(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 1605638971:
      	 goto	#ZZ
      case 2070106218:
      	 goto	#AAA
      default:
      	 goto	#AAB
   }
      -> Switch[1605638971] #ZY -> #ZZ
      -> DefaultSwitch #ZY -> #AAB
      -> Switch[2070106218] #ZY -> #AAA
      <- TryCatch range: [FQ...FP] -> ZY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FQ...FP] -> ZY ([Ljava/lang/RuntimeException;])
===#Block AAA(size=2, flags=10100)===
   0. lvar105 = {1803900419 ^ lvar105};
   1. goto FR
      -> UnconditionalJump[GOTO] #AAA -> #FR
      <- Switch[2070106218] #ZY -> #AAA
===#Block AAB(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ZY -> #AAB
===#Block ZZ(size=2, flags=10100)===
   0. lvar105 = {1327480681 ^ lvar105};
   1. goto FR
      -> UnconditionalJump[GOTO] #ZZ -> #FR
      <- Switch[1605638971] #ZY -> #ZZ
===#Block FR(size=2, flags=0)===
   0. _consume(catch());
   1. goto NB
      -> UnconditionalJump[GOTO] #FR -> #NB
      <- UnconditionalJump[GOTO] #ZZ -> #FR
      <- UnconditionalJump[GOTO] #AAA -> #FR
===#Block NB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 572810412);
   1. goto AF
      -> UnconditionalJump[GOTO] #NB -> #AF
      <- UnconditionalJump[GOTO] #FR -> #NB
===#Block SS(size=2, flags=10100)===
   0. lvar105 = {240251178 ^ lvar105};
   1. goto KB
      -> UnconditionalJump[GOTO] #SS -> #KB
      <- ConditionalJump[IF_ICMPNE] #T -> #SS
===#Block KB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -94417184)
      goto SM
   1. goto PV
      -> UnconditionalJump[GOTO] #KB -> #PV
      -> ConditionalJump[IF_ICMPEQ] #KB -> #SM
      <- UnconditionalJump[GOTO] #SS -> #KB
===#Block SM(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 212011601:
      	 goto	#SN
      case 728412332:
      	 goto	#JC
      case 1694507721:
      	 goto	#SM
      case 1930171535:
      	 goto	#V
      default:
      	 goto	#JC
   }
      -> Switch[1930171535] #SM -> #V
      -> Switch[728412332] #SM -> #JC
      -> DefaultSwitch #SM -> #JC
      -> Immediate #SM -> #SN
      -> Switch[1694507721] #SM -> #SM
      -> Switch[212011601] #SM -> #SN
      <- Switch[1694507721] #SM -> #SM
      <- ConditionalJump[IF_ICMPEQ] #KB -> #SM
===#Block SN(size=2, flags=100)===
   0. lvar105 = {1245287716 ^ lvar105};
   1. goto V
      -> UnconditionalJump[GOTO] #SN -> #V
      <- Immediate #SM -> #SN
      <- Switch[212011601] #SM -> #SN
===#Block V(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.REDSTONE_BLOCK;
   2. goto KQ
      -> UnconditionalJump[GOTO] #V -> #KQ
      <- UnconditionalJump[GOTO] #SN -> #V
      <- Switch[1930171535] #SM -> #V
===#Block KQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1578307091);
   1. goto EA
      -> UnconditionalJump[GOTO] #KQ -> #EA
      <- UnconditionalJump[GOTO] #V -> #KQ
===#Block EA(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 101500358)
      goto DZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EA -> #DZ
      -> TryCatch range: [EA...DZ] -> XU ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #KQ -> #EA
===#Block DZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EA...DZ] -> XU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EA -> #DZ
===#Block XU(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 1358411361:
      	 goto	#XW
      case 1527486296:
      	 goto	#XV
      default:
      	 goto	#XX
   }
      -> Switch[1358411361] #XU -> #XW
      -> Switch[1527486296] #XU -> #XV
      -> DefaultSwitch #XU -> #XX
      <- TryCatch range: [EA...DZ] -> XU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EA...DZ] -> XU ([Ljava/lang/RuntimeException;])
===#Block XX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #XU -> #XX
===#Block XV(size=2, flags=10100)===
   0. lvar105 = {617474780 ^ lvar105};
   1. goto EB
      -> UnconditionalJump[GOTO] #XV -> #EB
      <- Switch[1527486296] #XU -> #XV
===#Block XW(size=2, flags=10100)===
   0. lvar105 = {95579899 ^ lvar105};
   1. goto EB
      -> UnconditionalJump[GOTO] #XW -> #EB
      <- Switch[1358411361] #XU -> #XW
===#Block EB(size=2, flags=0)===
   0. _consume(catch());
   1. goto NA
      -> UnconditionalJump[GOTO] #EB -> #NA
      <- UnconditionalJump[GOTO] #XV -> #EB
      <- UnconditionalJump[GOTO] #XW -> #EB
===#Block NA(size=2, flags=10100)===
   0. lvar105 = {1727673419 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NA -> #CN
      <- UnconditionalJump[GOTO] #EB -> #NA
===#Block PV(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 212011601:
      	 goto	#PW
      case 397577648:
      	 goto	#PV
      case 484573390:
      	 goto	#JC
      case 2077877356:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[2077877356] #PV -> #JC
      -> DefaultSwitch #PV -> #JC
      -> Switch[212011601] #PV -> #PW
      -> Immediate #PV -> #PW
      -> Switch[397577648] #PV -> #PV
      <- UnconditionalJump[GOTO] #KB -> #PV
      <- Switch[397577648] #PV -> #PV
===#Block PW(size=2, flags=100)===
   0. lvar105 = {1142639990 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PW -> #JC
      <- Switch[212011601] #PV -> #PW
      <- Immediate #PV -> #PW
===#Block UO(size=2, flags=10100)===
   0. lvar105 = {616571772 ^ lvar105};
   1. goto W
      -> UnconditionalJump[GOTO] #UO -> #W
      <- Switch[223448008] #D -> #UO
===#Block W(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar8;
   2. lvar80 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.kjeeaxnqlghsenk(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar26 = lvar25.equals(lvar80);
   4. if (lvar26 != {995459986 ^ lvar105})
      goto SL
   5. lvar105 = {117201852 ^ lvar105};
      -> Immediate #W -> #X
      -> ConditionalJump[IF_ICMPNE] #W -> #SL
      <- UnconditionalJump[GOTO] #UO -> #W
===#Block SL(size=2, flags=10100)===
   0. lvar105 = {1099506535 ^ lvar105};
   1. goto JX
      -> UnconditionalJump[GOTO] #SL -> #JX
      <- ConditionalJump[IF_ICMPNE] #W -> #SL
===#Block JX(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -689633365)
      goto TZ
   1. goto PB
      -> UnconditionalJump[GOTO] #JX -> #PB
      -> ConditionalJump[IF_ICMPEQ] #JX -> #TZ
      <- UnconditionalJump[GOTO] #SL -> #JX
===#Block TZ(size=2, flags=10100)===
   0. lvar105 = {917381047 ^ lvar105};
   1. goto Y
      -> UnconditionalJump[GOTO] #TZ -> #Y
      <- ConditionalJump[IF_ICMPEQ] #JX -> #TZ
===#Block Y(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.PRISMARINE;
   2. goto KS
      -> UnconditionalJump[GOTO] #Y -> #KS
      <- UnconditionalJump[GOTO] #TZ -> #Y
===#Block KS(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 69082877:
      	 goto	#KT
      case 936943733:
      	 goto	#KS
      case 955792217:
      	 goto	#EJ
      case 2110769486:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[955792217] #KS -> #EJ
      -> Immediate #KS -> #KT
      -> Switch[936943733] #KS -> #KS
      -> Switch[2110769486] #KS -> #JC
      -> DefaultSwitch #KS -> #JC
      -> Switch[69082877] #KS -> #KT
      <- Switch[936943733] #KS -> #KS
      <- UnconditionalJump[GOTO] #Y -> #KS
===#Block KT(size=2, flags=100)===
   0. lvar105 = {1807006695 ^ lvar105};
   1. goto EJ
      -> UnconditionalJump[GOTO] #KT -> #EJ
      <- Immediate #KS -> #KT
      <- Switch[69082877] #KS -> #KT
===#Block EJ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 219123513)
      goto EI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EJ -> #EI
      -> TryCatch range: [EJ...EI] -> YG ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #KT -> #EJ
      <- Switch[955792217] #KS -> #EJ
===#Block EI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EJ...EI] -> YG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EJ -> #EI
===#Block YG(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 160245731:
      	 goto	#YI
      case 1043342633:
      	 goto	#YH
      default:
      	 goto	#YJ
   }
      -> Switch[1043342633] #YG -> #YH
      -> Switch[160245731] #YG -> #YI
      -> DefaultSwitch #YG -> #YJ
      <- TryCatch range: [EJ...EI] -> YG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EJ...EI] -> YG ([Ljava/lang/RuntimeException;])
===#Block YJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YG -> #YJ
===#Block YI(size=2, flags=10100)===
   0. lvar105 = {1802856811 ^ lvar105};
   1. goto EK
      -> UnconditionalJump[GOTO] #YI -> #EK
      <- Switch[160245731] #YG -> #YI
===#Block YH(size=2, flags=10100)===
   0. lvar105 = {763545906 ^ lvar105};
   1. goto EK
      -> UnconditionalJump[GOTO] #YH -> #EK
      <- Switch[1043342633] #YG -> #YH
===#Block EK(size=2, flags=0)===
   0. _consume(catch());
   1. goto OH
      -> UnconditionalJump[GOTO] #EK -> #OH
      <- UnconditionalJump[GOTO] #YI -> #EK
      <- UnconditionalJump[GOTO] #YH -> #EK
===#Block OH(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 65356816:
      	 goto	#OI
      case 105731754:
      	 goto	#OH
      case 480753084:
      	 goto	#CN
      case 933539225:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #OH -> #OI
      -> Switch[105731754] #OH -> #OH
      -> DefaultSwitch #OH -> #JC
      -> Switch[480753084] #OH -> #CN
      -> Switch[933539225] #OH -> #JC
      -> Switch[65356816] #OH -> #OI
      <- Switch[105731754] #OH -> #OH
      <- UnconditionalJump[GOTO] #EK -> #OH
===#Block OI(size=2, flags=100)===
   0. lvar105 = {1125278187 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OI -> #CN
      <- Immediate #OH -> #OI
      <- Switch[65356816] #OH -> #OI
===#Block PB(size=2, flags=10100)===
   0. lvar105 = {562824287 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PB -> #JC
      <- UnconditionalJump[GOTO] #JX -> #PB
===#Block X(size=1, flags=0)===
   0. goto QL
      -> UnconditionalJump[GOTO] #X -> #QL
      <- Immediate #W -> #X
===#Block QL(size=2, flags=10100)===
   0. lvar105 = {321047539 ^ lvar105};
   1. goto GF
      -> UnconditionalJump[GOTO] #QL -> #GF
      <- UnconditionalJump[GOTO] #X -> #QL
===#Block GF(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 170744055)
      goto GE
   1. throw nullconst;
      -> TryCatch range: [GF...GE] -> AAS ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GF -> #GE
      <- UnconditionalJump[GOTO] #QL -> #GF
===#Block GE(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GF...GE] -> AAS ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GF -> #GE
===#Block AAS(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 848811746:
      	 goto	#AAU
      case 1951964905:
      	 goto	#AAT
      default:
      	 goto	#AAV
   }
      -> Switch[848811746] #AAS -> #AAU
      -> Switch[1951964905] #AAS -> #AAT
      -> DefaultSwitch #AAS -> #AAV
      <- TryCatch range: [GF...GE] -> AAS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GF...GE] -> AAS ([Ljava/lang/RuntimeException;])
===#Block AAV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAS -> #AAV
===#Block AAT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 758416802);
   1. goto GG
      -> UnconditionalJump[GOTO] #AAT -> #GG
      <- Switch[1951964905] #AAS -> #AAT
===#Block AAU(size=2, flags=10100)===
   0. lvar105 = {1173187747 ^ lvar105};
   1. goto GG
      -> UnconditionalJump[GOTO] #AAU -> #GG
      <- Switch[848811746] #AAS -> #AAU
===#Block GG(size=2, flags=0)===
   0. _consume(catch());
   1. goto MI
      -> UnconditionalJump[GOTO] #GG -> #MI
      <- UnconditionalJump[GOTO] #AAU -> #GG
      <- UnconditionalJump[GOTO] #AAT -> #GG
===#Block MI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 91410141);
   1. goto AF
      -> UnconditionalJump[GOTO] #MI -> #AF
      <- UnconditionalJump[GOTO] #GG -> #MI
===#Block UP(size=2, flags=10100)===
   0. lvar105 = {244220656 ^ lvar105};
   1. goto H
      -> UnconditionalJump[GOTO] #UP -> #H
      <- Switch[223448010] #D -> #UP
===#Block H(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar15 = lvar8;
   2. lvar75 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.vgfgkcpbrnoumdz(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar16 = lvar15.equals(lvar75);
   4. if (lvar16 != {286990878 ^ lvar105})
      goto RZ
   5. lvar105 = {191578848 ^ lvar105};
      -> Immediate #H -> #I
      -> ConditionalJump[IF_ICMPNE] #H -> #RZ
      <- UnconditionalJump[GOTO] #UP -> #H
===#Block RZ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 19170049:
      	 goto	#SA
      case 1140086181:
      	 goto	#RZ
      case 1306429211:
      	 goto	#JO
      case 2001676707:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1306429211] #RZ -> #JO
      -> Switch[2001676707] #RZ -> #JC
      -> DefaultSwitch #RZ -> #JC
      -> Immediate #RZ -> #SA
      -> Switch[1140086181] #RZ -> #RZ
      -> Switch[19170049] #RZ -> #SA
      <- Switch[1140086181] #RZ -> #RZ
      <- ConditionalJump[IF_ICMPNE] #H -> #RZ
===#Block SA(size=2, flags=100)===
   0. lvar105 = {1090097755 ^ lvar105};
   1. goto JO
      -> UnconditionalJump[GOTO] #SA -> #JO
      <- Immediate #RZ -> #SA
      <- Switch[19170049] #RZ -> #SA
===#Block JO(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1894415830)
      goto RV
   1. goto NM
      -> UnconditionalJump[GOTO] #JO -> #NM
      -> ConditionalJump[IF_ICMPEQ] #JO -> #RV
      <- Switch[1306429211] #RZ -> #JO
      <- UnconditionalJump[GOTO] #SA -> #JO
===#Block RV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 197271432);
   1. goto J
      -> UnconditionalJump[GOTO] #RV -> #J
      <- ConditionalJump[IF_ICMPEQ] #JO -> #RV
===#Block J(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.EMERALD_BLOCK;
   2. goto KZ
      -> UnconditionalJump[GOTO] #J -> #KZ
      <- UnconditionalJump[GOTO] #RV -> #J
===#Block KZ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 245305021:
      	 goto	#LA
      case 303725975:
      	 goto	#EY
      case 785646770:
      	 goto	#JC
      case 1543695284:
      	 goto	#KZ
      default:
      	 goto	#JC
   }
      -> Immediate #KZ -> #LA
      -> Switch[245305021] #KZ -> #LA
      -> Switch[303725975] #KZ -> #EY
      -> DefaultSwitch #KZ -> #JC
      -> Switch[785646770] #KZ -> #JC
      -> Switch[1543695284] #KZ -> #KZ
      <- UnconditionalJump[GOTO] #J -> #KZ
      <- Switch[1543695284] #KZ -> #KZ
===#Block LA(size=2, flags=100)===
   0. lvar105 = {1894639680 ^ lvar105};
   1. goto EY
      -> UnconditionalJump[GOTO] #LA -> #EY
      <- Immediate #KZ -> #LA
      <- Switch[245305021] #KZ -> #LA
===#Block EY(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 49123436)
      goto EX
   1. throw nullconst;
      -> TryCatch range: [EY...EX] -> ZA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EY -> #EX
      <- UnconditionalJump[GOTO] #LA -> #EY
      <- Switch[303725975] #KZ -> #EY
===#Block EX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EY...EX] -> ZA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EY -> #EX
===#Block ZA(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1957510814:
      	 goto	#ZC
      case 1449802857:
      	 goto	#ZB
      default:
      	 goto	#ZD
   }
      -> DefaultSwitch #ZA -> #ZD
      -> Switch[1449802857] #ZA -> #ZB
      -> Switch[-1957510814] #ZA -> #ZC
      <- TryCatch range: [EY...EX] -> ZA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EY...EX] -> ZA ([Ljava/lang/RuntimeException;])
===#Block ZC(size=2, flags=10100)===
   0. lvar105 = {1018832402 ^ lvar105};
   1. goto EZ
      -> UnconditionalJump[GOTO] #ZC -> #EZ
      <- Switch[-1957510814] #ZA -> #ZC
===#Block ZB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1193097651);
   1. goto EZ
      -> UnconditionalJump[GOTO] #ZB -> #EZ
      <- Switch[1449802857] #ZA -> #ZB
===#Block EZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto NS
      -> UnconditionalJump[GOTO] #EZ -> #NS
      <- UnconditionalJump[GOTO] #ZB -> #EZ
      <- UnconditionalJump[GOTO] #ZC -> #EZ
===#Block NS(size=2, flags=10100)===
   0. lvar105 = {612815426 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NS -> #CN
      <- UnconditionalJump[GOTO] #EZ -> #NS
===#Block ZD(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ZA -> #ZD
===#Block NM(size=2, flags=10100)===
   0. lvar105 = {179442927 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NM -> #JC
      <- UnconditionalJump[GOTO] #JO -> #NM
===#Block I(size=1, flags=0)===
   0. goto PF
      -> UnconditionalJump[GOTO] #I -> #PF
      <- Immediate #H -> #I
===#Block PF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 297951470);
   1. goto HM
      -> UnconditionalJump[GOTO] #PF -> #HM
      <- UnconditionalJump[GOTO] #I -> #PF
===#Block HM(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 111733677)
      goto HL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HM -> #HL
      -> TryCatch range: [HM...HL] -> ACK ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #PF -> #HM
===#Block HL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HM...HL] -> ACK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HM -> #HL
===#Block ACK(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -*********:
      	 goto	#ACM
      case 1569718400:
      	 goto	#ACL
      default:
      	 goto	#ACN
   }
      -> DefaultSwitch #ACK -> #ACN
      -> Switch[-*********] #ACK -> #ACM
      -> Switch[1569718400] #ACK -> #ACL
      <- TryCatch range: [HM...HL] -> ACK ([Ljava/io/IOException;])
      <- TryCatch range: [HM...HL] -> ACK ([Ljava/io/IOException;])
===#Block ACL(size=2, flags=10100)===
   0. lvar105 = {1078745935 ^ lvar105};
   1. goto HN
      -> UnconditionalJump[GOTO] #ACL -> #HN
      <- Switch[1569718400] #ACK -> #ACL
===#Block ACM(size=2, flags=10100)===
   0. lvar105 = {********* ^ lvar105};
   1. goto HN
      -> UnconditionalJump[GOTO] #ACM -> #HN
      <- Switch[-*********] #ACK -> #ACM
===#Block HN(size=2, flags=0)===
   0. _consume(catch());
   1. goto OV
      -> UnconditionalJump[GOTO] #HN -> #OV
      <- UnconditionalJump[GOTO] #ACL -> #HN
      <- UnconditionalJump[GOTO] #ACM -> #HN
===#Block OV(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 54303478:
      	 goto	#OW
      case *********:
      	 goto	#JC
      case *********:
      	 goto	#AF
      case *********:
      	 goto	#OV
      default:
      	 goto	#JC
   }
      -> Immediate #OV -> #OW
      -> DefaultSwitch #OV -> #JC
      -> Switch[54303478] #OV -> #OW
      -> Switch[*********] #OV -> #JC
      -> Switch[*********] #OV -> #OV
      -> Switch[*********] #OV -> #AF
      <- UnconditionalJump[GOTO] #HN -> #OV
      <- Switch[*********] #OV -> #OV
===#Block OW(size=2, flags=100)===
   0. lvar105 = {1295188989 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #OW -> #AF
      <- Immediate #OV -> #OW
      <- Switch[54303478] #OV -> #OW
===#Block ACN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACK -> #ACN
===#Block US(size=2, flags=10100)===
   0. lvar105 = {********* ^ lvar105};
   1. goto Z
      -> UnconditionalJump[GOTO] #US -> #Z
      <- Switch[*********] #D -> #US
===#Block Z(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = lvar8;
   2. lvar81 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.aeuaybikyrwpwer(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar28 = lvar27.equals(lvar81);
   4. if (lvar28 != {********* ^ lvar105})
      goto UJ
   5. lvar105 = {********* ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #Z -> #UJ
      -> Immediate #Z -> #AA
      <- UnconditionalJump[GOTO] #US -> #Z
===#Block AA(size=1, flags=0)===
   0. goto PZ
      -> UnconditionalJump[GOTO] #AA -> #PZ
      <- Immediate #Z -> #AA
===#Block PZ(size=2, flags=10100)===
   0. lvar105 = {********* ^ lvar105};
   1. goto DU
      -> UnconditionalJump[GOTO] #PZ -> #DU
      <- UnconditionalJump[GOTO] #AA -> #PZ
===#Block DU(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 205863636)
      goto DT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DU -> #DT
      -> TryCatch range: [DU...DT] -> XM ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #PZ -> #DU
===#Block DT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DU...DT] -> XM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DU -> #DT
===#Block XM(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1446454528:
      	 goto	#XO
      case -1228208008:
      	 goto	#XN
      default:
      	 goto	#XP
   }
      -> Switch[-1446454528] #XM -> #XO
      -> Switch[-1228208008] #XM -> #XN
      -> DefaultSwitch #XM -> #XP
      <- TryCatch range: [DU...DT] -> XM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DU...DT] -> XM ([Ljava/lang/IllegalAccessException;])
===#Block XP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XM -> #XP
===#Block XN(size=2, flags=10100)===
   0. lvar105 = {703102309 ^ lvar105};
   1. goto DV
      -> UnconditionalJump[GOTO] #XN -> #DV
      <- Switch[-1228208008] #XM -> #XN
===#Block XO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 705169162);
   1. goto DV
      -> UnconditionalJump[GOTO] #XO -> #DV
      <- Switch[-1446454528] #XM -> #XO
===#Block DV(size=2, flags=0)===
   0. _consume(catch());
   1. goto QA
      -> UnconditionalJump[GOTO] #DV -> #QA
      <- UnconditionalJump[GOTO] #XO -> #DV
      <- UnconditionalJump[GOTO] #XN -> #DV
===#Block QA(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 175052860:
      	 goto	#QB
      case 280652411:
      	 goto	#JC
      case 1254045997:
      	 goto	#AF
      case 2100989627:
      	 goto	#QA
      default:
      	 goto	#JC
   }
      -> Immediate #QA -> #QB
      -> Switch[175052860] #QA -> #QB
      -> Switch[280652411] #QA -> #JC
      -> DefaultSwitch #QA -> #JC
      -> Switch[2100989627] #QA -> #QA
      -> Switch[1254045997] #QA -> #AF
      <- Switch[2100989627] #QA -> #QA
      <- UnconditionalJump[GOTO] #DV -> #QA
===#Block QB(size=2, flags=100)===
   0. lvar105 = {972833992 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #QB -> #AF
      <- Immediate #QA -> #QB
      <- Switch[175052860] #QA -> #QB
===#Block UJ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 163775142:
      	 goto	#UK
      case 424083366:
      	 goto	#JY
      case 1046660488:
      	 goto	#JC
      case 1196688879:
      	 goto	#UJ
      default:
      	 goto	#JC
   }
      -> Switch[1196688879] #UJ -> #UJ
      -> Switch[424083366] #UJ -> #JY
      -> Switch[163775142] #UJ -> #UK
      -> Immediate #UJ -> #UK
      -> DefaultSwitch #UJ -> #JC
      -> Switch[1046660488] #UJ -> #JC
      <- ConditionalJump[IF_ICMPNE] #Z -> #UJ
      <- Switch[1196688879] #UJ -> #UJ
===#Block UK(size=2, flags=100)===
   0. lvar105 = {1543654032 ^ lvar105};
   1. goto JY
      -> UnconditionalJump[GOTO] #UK -> #JY
      <- Switch[163775142] #UJ -> #UK
      <- Immediate #UJ -> #UK
===#Block JY(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1833724581)
      goto SF
   1. goto OY
      -> ConditionalJump[IF_ICMPEQ] #JY -> #SF
      -> UnconditionalJump[GOTO] #JY -> #OY
      <- UnconditionalJump[GOTO] #UK -> #JY
      <- Switch[424083366] #UJ -> #JY
===#Block OY(size=2, flags=10100)===
   0. lvar105 = {688313857 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OY -> #JC
      <- UnconditionalJump[GOTO] #JY -> #OY
===#Block SF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 399287850);
   1. goto AB
      -> UnconditionalJump[GOTO] #SF -> #AB
      <- ConditionalJump[IF_ICMPEQ] #JY -> #SF
===#Block AB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.OBSIDIAN;
   2. goto OL
      -> UnconditionalJump[GOTO] #AB -> #OL
      <- UnconditionalJump[GOTO] #SF -> #AB
===#Block OL(size=2, flags=10100)===
   0. lvar105 = {222557073 ^ lvar105};
   1. goto FE
      -> UnconditionalJump[GOTO] #OL -> #FE
      <- UnconditionalJump[GOTO] #AB -> #OL
===#Block FE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 186962806)
      goto FD
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FE -> #FD
      -> TryCatch range: [FE...FD] -> ZI ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #OL -> #FE
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FE...FD] -> ZI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FE -> #FD
===#Block ZI(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -966365885:
      	 goto	#ZK
      case 1187592323:
      	 goto	#ZJ
      default:
      	 goto	#ZL
   }
      -> DefaultSwitch #ZI -> #ZL
      -> Switch[1187592323] #ZI -> #ZJ
      -> Switch[-966365885] #ZI -> #ZK
      <- TryCatch range: [FE...FD] -> ZI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FE...FD] -> ZI ([Ljava/lang/RuntimeException;])
===#Block ZK(size=2, flags=10100)===
   0. lvar105 = {78238979 ^ lvar105};
   1. goto FF
      -> UnconditionalJump[GOTO] #ZK -> #FF
      <- Switch[-966365885] #ZI -> #ZK
===#Block ZJ(size=2, flags=10100)===
   0. lvar105 = {347881403 ^ lvar105};
   1. goto FF
      -> UnconditionalJump[GOTO] #ZJ -> #FF
      <- Switch[1187592323] #ZI -> #ZJ
===#Block FF(size=2, flags=0)===
   0. _consume(catch());
   1. goto NV
      -> UnconditionalJump[GOTO] #FF -> #NV
      <- UnconditionalJump[GOTO] #ZJ -> #FF
      <- UnconditionalJump[GOTO] #ZK -> #FF
===#Block NV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 892581079);
   1. goto CN
      -> UnconditionalJump[GOTO] #NV -> #CN
      <- UnconditionalJump[GOTO] #FF -> #NV
===#Block ZL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZI -> #ZL
===#Block UV(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 221378648:
      	 goto	#UW
      case 830115341:
      	 goto	#Q
      case 1133295260:
      	 goto	#UV
      case 1197965418:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #UV -> #JC
      -> Switch[1197965418] #UV -> #JC
      -> Switch[1133295260] #UV -> #UV
      -> Switch[830115341] #UV -> #Q
      -> Immediate #UV -> #UW
      -> Switch[221378648] #UV -> #UW
      <- Switch[1133295260] #UV -> #UV
      <- Switch[223448022] #D -> #UV
===#Block UW(size=2, flags=100)===
   0. lvar105 = {993553380 ^ lvar105};
   1. goto Q
      -> UnconditionalJump[GOTO] #UW -> #Q
      <- Immediate #UV -> #UW
      <- Switch[221378648] #UV -> #UW
===#Block Q(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar8;
   2. lvar78 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.dfkhxadsbxfbjyn(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar22 = lvar21.equals(lvar78);
   4. if (lvar22 != {615369482 ^ lvar105})
      goto SJ
   5. lvar105 = {1367646406 ^ lvar105};
      -> Immediate #Q -> #R
      -> ConditionalJump[IF_ICMPNE] #Q -> #SJ
      <- Switch[830115341] #UV -> #Q
      <- UnconditionalJump[GOTO] #UW -> #Q
===#Block SJ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 118529486:
      	 goto	#SK
      case 485375558:
      	 goto	#JC
      case 542201297:
      	 goto	#SJ
      case 1068096797:
      	 goto	#JS
      default:
      	 goto	#JC
   }
      -> Switch[1068096797] #SJ -> #JS
      -> Immediate #SJ -> #SK
      -> Switch[542201297] #SJ -> #SJ
      -> Switch[118529486] #SJ -> #SK
      -> DefaultSwitch #SJ -> #JC
      -> Switch[485375558] #SJ -> #JC
      <- ConditionalJump[IF_ICMPNE] #Q -> #SJ
      <- Switch[542201297] #SJ -> #SJ
===#Block SK(size=2, flags=100)===
   0. lvar105 = {1010040139 ^ lvar105};
   1. goto JS
      -> UnconditionalJump[GOTO] #SK -> #JS
      <- Immediate #SJ -> #SK
      <- Switch[118529486] #SJ -> #SK
===#Block JS(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -990793208)
      goto TX
   1. goto OK
      -> UnconditionalJump[GOTO] #JS -> #OK
      -> ConditionalJump[IF_ICMPEQ] #JS -> #TX
      <- Switch[1068096797] #SJ -> #JS
      <- UnconditionalJump[GOTO] #SK -> #JS
===#Block TX(size=2, flags=10100)===
   0. lvar105 = {973351523 ^ lvar105};
   1. goto S
      -> UnconditionalJump[GOTO] #TX -> #S
      <- ConditionalJump[IF_ICMPEQ] #JS -> #TX
===#Block S(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.DIAMOND_BLOCK;
   2. goto KL
      -> UnconditionalJump[GOTO] #S -> #KL
      <- UnconditionalJump[GOTO] #TX -> #S
===#Block KL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1393899667);
   1. goto DC
      -> UnconditionalJump[GOTO] #KL -> #DC
      <- UnconditionalJump[GOTO] #S -> #KL
===#Block DC(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 201664153)
      goto DB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DC -> #DB
      -> TryCatch range: [DC...DB] -> WO ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #KL -> #DC
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DC...DB] -> WO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DC -> #DB
===#Block WO(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1938217589:
      	 goto	#WP
      case -825122294:
      	 goto	#WQ
      default:
      	 goto	#WR
   }
      -> Switch[-1938217589] #WO -> #WP
      -> DefaultSwitch #WO -> #WR
      -> Switch[-825122294] #WO -> #WQ
      <- TryCatch range: [DC...DB] -> WO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DC...DB] -> WO ([Ljava/lang/RuntimeException;])
===#Block WQ(size=2, flags=10100)===
   0. lvar105 = {1925764211 ^ lvar105};
   1. goto DD
      -> UnconditionalJump[GOTO] #WQ -> #DD
      <- Switch[-825122294] #WO -> #WQ
===#Block WR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #WO -> #WR
===#Block WP(size=2, flags=10100)===
   0. lvar105 = {1520288899 ^ lvar105};
   1. goto DD
      -> UnconditionalJump[GOTO] #WP -> #DD
      <- Switch[-1938217589] #WO -> #WP
===#Block DD(size=2, flags=0)===
   0. _consume(catch());
   1. goto MM
      -> UnconditionalJump[GOTO] #DD -> #MM
      <- UnconditionalJump[GOTO] #WQ -> #DD
      <- UnconditionalJump[GOTO] #WP -> #DD
===#Block MM(size=2, flags=10100)===
   0. lvar105 = {1648643150 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #MM -> #CN
      <- UnconditionalJump[GOTO] #DD -> #MM
===#Block OK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1137611499);
   1. goto JC
      -> UnconditionalJump[GOTO] #OK -> #JC
      <- UnconditionalJump[GOTO] #JS -> #OK
===#Block R(size=1, flags=0)===
   0. goto LY
      -> UnconditionalJump[GOTO] #R -> #LY
      <- Immediate #Q -> #R
===#Block LY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1254263492);
   1. goto IN
      -> UnconditionalJump[GOTO] #LY -> #IN
      <- UnconditionalJump[GOTO] #R -> #LY
===#Block IN(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 198786068)
      goto IM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IN -> #IM
      -> TryCatch range: [IN...IM] -> ADU ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #LY -> #IN
===#Block IM(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IN...IM] -> ADU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IN -> #IM
===#Block ADU(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -161817255:
      	 goto	#ADW
      case -10549183:
      	 goto	#ADV
      default:
      	 goto	#ADX
   }
      -> DefaultSwitch #ADU -> #ADX
      -> Switch[-10549183] #ADU -> #ADV
      -> Switch[-161817255] #ADU -> #ADW
      <- TryCatch range: [IN...IM] -> ADU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IN...IM] -> ADU ([Ljava/lang/IllegalAccessException;])
===#Block ADW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 793202265);
   1. goto IO
      -> UnconditionalJump[GOTO] #ADW -> #IO
      <- Switch[-161817255] #ADU -> #ADW
===#Block ADV(size=2, flags=10100)===
   0. lvar105 = {778564858 ^ lvar105};
   1. goto IO
      -> UnconditionalJump[GOTO] #ADV -> #IO
      <- Switch[-10549183] #ADU -> #ADV
===#Block IO(size=2, flags=0)===
   0. _consume(catch());
   1. goto NE
      -> UnconditionalJump[GOTO] #IO -> #NE
      <- UnconditionalJump[GOTO] #ADW -> #IO
      <- UnconditionalJump[GOTO] #ADV -> #IO
===#Block NE(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 33520392:
      	 goto	#NF
      case 675938371:
      	 goto	#AF
      case 990819448:
      	 goto	#NE
      case 2039780132:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #NE -> #JC
      -> Switch[2039780132] #NE -> #JC
      -> Switch[33520392] #NE -> #NF
      -> Immediate #NE -> #NF
      -> Switch[990819448] #NE -> #NE
      -> Switch[675938371] #NE -> #AF
      <- UnconditionalJump[GOTO] #IO -> #NE
      <- Switch[990819448] #NE -> #NE
===#Block NF(size=2, flags=100)===
   0. lvar105 = {390155600 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #NF -> #AF
      <- Switch[33520392] #NE -> #NF
      <- Immediate #NE -> #NF
===#Block ADX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADU -> #ADX
===#Block UQ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 130202323:
      	 goto	#UQ
      case 221378648:
      	 goto	#UR
      case 1130477980:
      	 goto	#JC
      case 1614390143:
      	 goto	#N
      default:
      	 goto	#JC
   }
      -> Immediate #UQ -> #UR
      -> Switch[1614390143] #UQ -> #N
      -> Switch[221378648] #UQ -> #UR
      -> Switch[1130477980] #UQ -> #JC
      -> DefaultSwitch #UQ -> #JC
      -> Switch[130202323] #UQ -> #UQ
      <- Switch[223448014] #D -> #UQ
      <- Switch[130202323] #UQ -> #UQ
===#Block UR(size=2, flags=100)===
   0. lvar105 = {1699332122 ^ lvar105};
   1. goto N
      -> UnconditionalJump[GOTO] #UR -> #N
      <- Immediate #UQ -> #UR
      <- Switch[221378648] #UQ -> #UR
===#Block N(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar8;
   2. lvar77 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.mkirvyoppkeahey(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar20 = lvar19.equals(lvar77);
   4. if (lvar20 != {2061263092 ^ lvar105})
      goto ST
   5. lvar105 = {392861085 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #N -> #ST
      -> Immediate #N -> #O
      <- UnconditionalJump[GOTO] #UR -> #N
      <- Switch[1614390143] #UQ -> #N
===#Block O(size=1, flags=0)===
   0. goto MQ
      -> UnconditionalJump[GOTO] #O -> #MQ
      <- Immediate #N -> #O
===#Block MQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1529608961);
   1. goto FN
      -> UnconditionalJump[GOTO] #MQ -> #FN
      <- UnconditionalJump[GOTO] #O -> #MQ
===#Block FN(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 164658548)
      goto FM
   1. throw nullconst;
      -> TryCatch range: [FN...FM] -> ZU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #FN -> #FM
      <- UnconditionalJump[GOTO] #MQ -> #FN
===#Block FM(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FN...FM] -> ZU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FN -> #FM
===#Block ZU(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1451216952:
      	 goto	#ZW
      case -1259834559:
      	 goto	#ZV
      default:
      	 goto	#ZX
   }
      -> Switch[-1259834559] #ZU -> #ZV
      -> DefaultSwitch #ZU -> #ZX
      -> Switch[-1451216952] #ZU -> #ZW
      <- TryCatch range: [FN...FM] -> ZU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FN...FM] -> ZU ([Ljava/lang/IllegalAccessException;])
===#Block ZW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 376438513);
   1. goto FO
      -> UnconditionalJump[GOTO] #ZW -> #FO
      <- Switch[-1451216952] #ZU -> #ZW
===#Block ZX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZU -> #ZX
===#Block ZV(size=2, flags=10100)===
   0. lvar105 = {901969248 ^ lvar105};
   1. goto FO
      -> UnconditionalJump[GOTO] #ZV -> #FO
      <- Switch[-1259834559] #ZU -> #ZV
===#Block FO(size=2, flags=0)===
   0. _consume(catch());
   1. goto LD
      -> UnconditionalJump[GOTO] #FO -> #LD
      <- UnconditionalJump[GOTO] #ZW -> #FO
      <- UnconditionalJump[GOTO] #ZV -> #FO
===#Block LD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 93496234);
   1. goto AF
      -> UnconditionalJump[GOTO] #LD -> #AF
      <- UnconditionalJump[GOTO] #FO -> #LD
===#Block ST(size=2, flags=10100)===
   0. lvar105 = {1919344604 ^ lvar105};
   1. goto KC
      -> UnconditionalJump[GOTO] #ST -> #KC
      <- ConditionalJump[IF_ICMPNE] #N -> #ST
===#Block KC(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 1171650880)
      goto TA
   1. goto LE
      -> UnconditionalJump[GOTO] #KC -> #LE
      -> ConditionalJump[IF_ICMPEQ] #KC -> #TA
      <- UnconditionalJump[GOTO] #ST -> #KC
===#Block TA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 377175412);
   1. goto P
      -> UnconditionalJump[GOTO] #TA -> #P
      <- ConditionalJump[IF_ICMPEQ] #KC -> #TA
===#Block P(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.PURPUR_BLOCK;
   2. goto PD
      -> UnconditionalJump[GOTO] #P -> #PD
      <- UnconditionalJump[GOTO] #TA -> #P
===#Block PD(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 153349665:
      	 goto	#JC
      case 194441811:
      	 goto	#PE
      case 347625972:
      	 goto	#PD
      case 1965264583:
      	 goto	#HJ
      default:
      	 goto	#JC
   }
      -> Switch[347625972] #PD -> #PD
      -> DefaultSwitch #PD -> #JC
      -> Switch[153349665] #PD -> #JC
      -> Immediate #PD -> #PE
      -> Switch[1965264583] #PD -> #HJ
      -> Switch[194441811] #PD -> #PE
      <- Switch[347625972] #PD -> #PD
      <- UnconditionalJump[GOTO] #P -> #PD
===#Block PE(size=2, flags=100)===
   0. lvar105 = {242530801 ^ lvar105};
   1. goto HJ
      -> UnconditionalJump[GOTO] #PE -> #HJ
      <- Immediate #PD -> #PE
      <- Switch[194441811] #PD -> #PE
===#Block HJ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 6239018)
      goto HI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HJ -> #HI
      -> TryCatch range: [HJ...HI] -> ACG ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #PE -> #HJ
      <- Switch[1965264583] #PD -> #HJ
===#Block HI(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HJ...HI] -> ACG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HJ -> #HI
===#Block ACG(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -2052465304:
      	 goto	#ACH
      case -224994853:
      	 goto	#ACI
      default:
      	 goto	#ACJ
   }
      -> Switch[-2052465304] #ACG -> #ACH
      -> Switch[-224994853] #ACG -> #ACI
      -> DefaultSwitch #ACG -> #ACJ
      <- TryCatch range: [HJ...HI] -> ACG ([Ljava/io/IOException;])
      <- TryCatch range: [HJ...HI] -> ACG ([Ljava/io/IOException;])
===#Block ACJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ACG -> #ACJ
===#Block ACI(size=2, flags=10100)===
   0. lvar105 = {982473198 ^ lvar105};
   1. goto HK
      -> UnconditionalJump[GOTO] #ACI -> #HK
      <- Switch[-224994853] #ACG -> #ACI
===#Block ACH(size=2, flags=10100)===
   0. lvar105 = {1416149368 ^ lvar105};
   1. goto HK
      -> UnconditionalJump[GOTO] #ACH -> #HK
      <- Switch[-2052465304] #ACG -> #ACH
===#Block HK(size=2, flags=0)===
   0. _consume(catch());
   1. goto NX
      -> UnconditionalJump[GOTO] #HK -> #NX
      <- UnconditionalJump[GOTO] #ACI -> #HK
      <- UnconditionalJump[GOTO] #ACH -> #HK
===#Block NX(size=2, flags=10100)===
   0. lvar105 = {227225257 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NX -> #CN
      <- UnconditionalJump[GOTO] #HK -> #NX
===#Block LE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1407851394);
   1. goto JC
      -> UnconditionalJump[GOTO] #LE -> #JC
      <- UnconditionalJump[GOTO] #KC -> #LE
===#Block UZ(size=2, flags=10100)===
   0. lvar105 = {425261132 ^ lvar105};
   1. goto AF
      -> UnconditionalJump[GOTO] #UZ -> #AF
      <- DefaultSwitch #D -> #UZ
===#Block AF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.IRON_BLOCK;
   2. goto PC
      -> UnconditionalJump[GOTO] #AF -> #PC
      <- UnconditionalJump[GOTO] #PO -> #AF
      <- UnconditionalJump[GOTO] #OW -> #AF
      <- Switch[327236265] #KJ -> #AF
      <- UnconditionalJump[GOTO] #KK -> #AF
      <- UnconditionalJump[GOTO] #LD -> #AF
      <- Switch[675938371] #NE -> #AF
      <- UnconditionalJump[GOTO] #QB -> #AF
      <- UnconditionalJump[GOTO] #NB -> #AF
      <- UnconditionalJump[GOTO] #NF -> #AF
      <- UnconditionalJump[GOTO] #UZ -> #AF
      <- UnconditionalJump[GOTO] #PN -> #AF
      <- Switch[*********] #OV -> #AF
      <- UnconditionalJump[GOTO] #MI -> #AF
      <- Switch[1254045997] #QA -> #AF
===#Block PC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1232415717);
   1. goto HD
      -> UnconditionalJump[GOTO] #PC -> #HD
      <- UnconditionalJump[GOTO] #AF -> #PC
===#Block HD(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 175444193)
      goto HC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HD -> #HC
      -> TryCatch range: [HD...HC] -> ABY ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #PC -> #HD
===#Block HC(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HD...HC] -> ABY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HD -> #HC
===#Block ABY(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 1727143289:
      	 goto	#ACA
      case 2109971002:
      	 goto	#ABZ
      default:
      	 goto	#ACB
   }
      -> Switch[1727143289] #ABY -> #ACA
      -> Switch[2109971002] #ABY -> #ABZ
      -> DefaultSwitch #ABY -> #ACB
      <- TryCatch range: [HD...HC] -> ABY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HD...HC] -> ABY ([Ljava/lang/RuntimeException;])
===#Block ACB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABY -> #ACB
===#Block ABZ(size=2, flags=10100)===
   0. lvar105 = {1926768063 ^ lvar105};
   1. goto HE
      -> UnconditionalJump[GOTO] #ABZ -> #HE
      <- Switch[2109971002] #ABY -> #ABZ
===#Block ACA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 297669975);
   1. goto HE
      -> UnconditionalJump[GOTO] #ACA -> #HE
      <- Switch[1727143289] #ABY -> #ACA
===#Block HE(size=2, flags=0)===
   0. _consume(catch());
   1. goto PS
      -> UnconditionalJump[GOTO] #HE -> #PS
      <- UnconditionalJump[GOTO] #ABZ -> #HE
      <- UnconditionalJump[GOTO] #ACA -> #HE
===#Block PS(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1949760132);
   1. goto CN
      -> UnconditionalJump[GOTO] #PS -> #CN
      <- UnconditionalJump[GOTO] #HE -> #PS
===#Block TT(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 35748792:
      	 goto	#TU
      case 926518611:
      	 goto	#TT
      case 1017094404:
      	 goto	#JL
      case 2040990895:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1017094404] #TT -> #JL
      -> Immediate #TT -> #TU
      -> Switch[2040990895] #TT -> #JC
      -> DefaultSwitch #TT -> #JC
      -> Switch[35748792] #TT -> #TU
      -> Switch[926518611] #TT -> #TT
      <- Switch[926518611] #TT -> #TT
      <- ConditionalJump[IF_ICMPEQ] #C -> #TT
===#Block TU(size=2, flags=100)===
   0. lvar105 = {1569199893 ^ lvar105};
   1. goto JL
      -> UnconditionalJump[GOTO] #TU -> #JL
      <- Immediate #TT -> #TU
      <- Switch[35748792] #TT -> #TU
===#Block JL(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -26344702)
      goto TQ
   1. goto NO
      -> ConditionalJump[IF_ICMPEQ] #JL -> #TQ
      -> UnconditionalJump[GOTO] #JL -> #NO
      <- Switch[1017094404] #TT -> #JL
      <- UnconditionalJump[GOTO] #TU -> #JL
===#Block NO(size=2, flags=10100)===
   0. lvar105 = {77423818 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NO -> #JC
      <- UnconditionalJump[GOTO] #JL -> #NO
===#Block TQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1999496309);
   1. goto AG
      -> UnconditionalJump[GOTO] #TQ -> #AG
      <- ConditionalJump[IF_ICMPEQ] #JL -> #TQ
===#Block AG(size=4, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar31 = lvar2;
   2. if (lvar31 == {685777941 ^ lvar105})
      goto UM
   3. lvar105 = {744930768 ^ lvar105};
      -> Immediate #AG -> #AH
      -> ConditionalJump[IF_ICMPEQ] #AG -> #UM
      <- UnconditionalJump[GOTO] #TQ -> #AG
===#Block UM(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 172427175:
      	 goto	#UM
      case 254962952:
      	 goto	#UN
      case 899424892:
      	 goto	#KE
      case 1900878551:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #UM -> #UN
      -> Switch[1900878551] #UM -> #JC
      -> DefaultSwitch #UM -> #JC
      -> Switch[172427175] #UM -> #UM
      -> Switch[254962952] #UM -> #UN
      -> Switch[899424892] #UM -> #KE
      <- Switch[172427175] #UM -> #UM
      <- ConditionalJump[IF_ICMPEQ] #AG -> #UM
===#Block UN(size=2, flags=100)===
   0. lvar105 = {516148433 ^ lvar105};
   1. goto KE
      -> UnconditionalJump[GOTO] #UN -> #KE
      <- Immediate #UM -> #UN
      <- Switch[254962952] #UM -> #UN
===#Block KE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1323350495)
      goto RP
   1. goto MD
      -> ConditionalJump[IF_ICMPEQ] #KE -> #RP
      -> UnconditionalJump[GOTO] #KE -> #MD
      <- Switch[899424892] #UM -> #KE
      <- UnconditionalJump[GOTO] #UN -> #KE
===#Block MD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1836272750);
   1. goto JC
      -> UnconditionalJump[GOTO] #MD -> #JC
      <- UnconditionalJump[GOTO] #KE -> #MD
===#Block RP(size=2, flags=10100)===
   0. lvar105 = {602560602 ^ lvar105};
   1. goto BK
      -> UnconditionalJump[GOTO] #RP -> #BK
      <- ConditionalJump[IF_ICMPEQ] #KE -> #RP
===#Block BK(size=7, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar54 = lvar1;
   2. lvar102 = lvar54;
   3. lvar55 = lvar102;
   4. lvar56 = lvar55.hashCode();
   5. svar107 = {lvar56 ^ lvar105};
   6. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(svar107)) {
      case 177480617:
      	 goto	#VM
      case 177480619:
      	 goto	#VN
      case 177480623:
      	 goto	#VO
      case 177480625:
      	 goto	#VP
      case 177480627:
      	 goto	#VQ
      case 177480629:
      	 goto	#VS
      case 177480631:
      	 goto	#VU
      case 177480637:
      	 goto	#VV
      case 177480639:
      	 goto	#VW
      default:
      	 goto	#VX
   }
      -> Switch[177480629] #BK -> #VS
      -> Switch[177480623] #BK -> #VO
      -> Switch[177480625] #BK -> #VP
      -> Switch[177480631] #BK -> #VU
      -> DefaultSwitch #BK -> #VX
      -> Switch[177480627] #BK -> #VQ
      -> Switch[177480637] #BK -> #VV
      -> Switch[177480619] #BK -> #VN
      -> Switch[177480639] #BK -> #VW
      -> Switch[177480617] #BK -> #VM
      <- UnconditionalJump[GOTO] #RP -> #BK
===#Block VM(size=2, flags=10100)===
   0. lvar105 = {322977336 ^ lvar105};
   1. goto BX
      -> UnconditionalJump[GOTO] #VM -> #BX
      <- Switch[177480617] #BK -> #VM
===#Block BX(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar65 = lvar102;
   2. lvar96 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.awldnhtdopepktk(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar66 = lvar65.equals(lvar96);
   4. if (lvar66 != {109678246 ^ lvar105})
      goto SH
   5. lvar105 = {1063387588 ^ lvar105};
      -> Immediate #BX -> #BZ
      -> ConditionalJump[IF_ICMPNE] #BX -> #SH
      <- UnconditionalJump[GOTO] #VM -> #BX
===#Block SH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1698859245);
   1. goto JQ
      -> UnconditionalJump[GOTO] #SH -> #JQ
      <- ConditionalJump[IF_ICMPNE] #BX -> #SH
===#Block JQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 509096539)
      goto RR
   1. goto MT
      -> UnconditionalJump[GOTO] #JQ -> #MT
      -> ConditionalJump[IF_ICMPEQ] #JQ -> #RR
      <- UnconditionalJump[GOTO] #SH -> #JQ
===#Block RR(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 22626298:
      	 goto	#RS
      case 1186289433:
      	 goto	#BY
      case 1584125179:
      	 goto	#RR
      case 1854773026:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1186289433] #RR -> #BY
      -> Switch[1584125179] #RR -> #RR
      -> Switch[1854773026] #RR -> #JC
      -> DefaultSwitch #RR -> #JC
      -> Immediate #RR -> #RS
      -> Switch[22626298] #RR -> #RS
      <- ConditionalJump[IF_ICMPEQ] #JQ -> #RR
      <- Switch[1584125179] #RR -> #RR
===#Block RS(size=2, flags=100)===
   0. lvar105 = {616499883 ^ lvar105};
   1. goto BY
      -> UnconditionalJump[GOTO] #RS -> #BY
      <- Immediate #RR -> #RS
      <- Switch[22626298] #RR -> #RS
===#Block BY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLUE_CONCRETE_POWDER;
   2. goto OD
      -> UnconditionalJump[GOTO] #BY -> #OD
      <- Switch[1186289433] #RR -> #BY
      <- UnconditionalJump[GOTO] #RS -> #BY
===#Block OD(size=2, flags=10100)===
   0. lvar105 = {568291282 ^ lvar105};
   1. goto ED
      -> UnconditionalJump[GOTO] #OD -> #ED
      <- UnconditionalJump[GOTO] #BY -> #OD
===#Block ED(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 116160155)
      goto EC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #ED -> #EC
      -> TryCatch range: [ED...EC] -> XY ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #OD -> #ED
===#Block EC(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [ED...EC] -> XY ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #ED -> #EC
===#Block XY(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -32536141:
      	 goto	#YA
      case 895162771:
      	 goto	#XZ
      default:
      	 goto	#YB
   }
      -> DefaultSwitch #XY -> #YB
      -> Switch[895162771] #XY -> #XZ
      -> Switch[-32536141] #XY -> #YA
      <- TryCatch range: [ED...EC] -> XY ([Ljava/io/IOException;])
      <- TryCatch range: [ED...EC] -> XY ([Ljava/io/IOException;])
===#Block YA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 686905486);
   1. goto EE
      -> UnconditionalJump[GOTO] #YA -> #EE
      <- Switch[-32536141] #XY -> #YA
===#Block XZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 832293514);
   1. goto EE
      -> UnconditionalJump[GOTO] #XZ -> #EE
      <- Switch[895162771] #XY -> #XZ
===#Block EE(size=2, flags=0)===
   0. _consume(catch());
   1. goto RE
      -> UnconditionalJump[GOTO] #EE -> #RE
      <- UnconditionalJump[GOTO] #XZ -> #EE
      <- UnconditionalJump[GOTO] #YA -> #EE
===#Block RE(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 149823460:
      	 goto	#RF
      case 464687733:
      	 goto	#CN
      case 666009838:
      	 goto	#JC
      case 1856669261:
      	 goto	#RE
      default:
      	 goto	#JC
   }
      -> Switch[1856669261] #RE -> #RE
      -> Switch[149823460] #RE -> #RF
      -> Switch[666009838] #RE -> #JC
      -> Switch[464687733] #RE -> #CN
      -> DefaultSwitch #RE -> #JC
      -> Immediate #RE -> #RF
      <- Switch[1856669261] #RE -> #RE
      <- UnconditionalJump[GOTO] #EE -> #RE
===#Block RF(size=2, flags=100)===
   0. lvar105 = {510002628 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #RF -> #CN
      <- Switch[149823460] #RE -> #RF
      <- Immediate #RE -> #RF
===#Block YB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XY -> #YB
===#Block MT(size=2, flags=10100)===
   0. lvar105 = {949723873 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MT -> #JC
      <- UnconditionalJump[GOTO] #JQ -> #MT
===#Block BZ(size=1, flags=0)===
   0. goto KX
      -> UnconditionalJump[GOTO] #BZ -> #KX
      <- Immediate #BX -> #BZ
===#Block KX(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 3693606:
      	 goto	#KY
      case 66677869:
      	 goto	#KX
      case 2099056217:
      	 goto	#EV
      case 2101303664:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[3693606] #KX -> #KY
      -> Switch[2099056217] #KX -> #EV
      -> Immediate #KX -> #KY
      -> Switch[2101303664] #KX -> #JC
      -> Switch[66677869] #KX -> #KX
      -> DefaultSwitch #KX -> #JC
      <- UnconditionalJump[GOTO] #BZ -> #KX
      <- Switch[66677869] #KX -> #KX
===#Block KY(size=2, flags=100)===
   0. lvar105 = {1377647521 ^ lvar105};
   1. goto EV
      -> UnconditionalJump[GOTO] #KY -> #EV
      <- Switch[3693606] #KX -> #KY
      <- Immediate #KX -> #KY
===#Block EV(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 19764687)
      goto EU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EV -> #EU
      -> TryCatch range: [EV...EU] -> YW ([Ljava/io/IOException;])
      <- Switch[2099056217] #KX -> #EV
      <- UnconditionalJump[GOTO] #KY -> #EV
===#Block EU(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EV...EU] -> YW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EV -> #EU
===#Block YW(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1916706830:
      	 goto	#YY
      case 1605740059:
      	 goto	#YX
      default:
      	 goto	#YZ
   }
      -> Switch[-1916706830] #YW -> #YY
      -> DefaultSwitch #YW -> #YZ
      -> Switch[1605740059] #YW -> #YX
      <- TryCatch range: [EV...EU] -> YW ([Ljava/io/IOException;])
      <- TryCatch range: [EV...EU] -> YW ([Ljava/io/IOException;])
===#Block YX(size=2, flags=10100)===
   0. lvar105 = {1245788865 ^ lvar105};
   1. goto EW
      -> UnconditionalJump[GOTO] #YX -> #EW
      <- Switch[1605740059] #YW -> #YX
===#Block YZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YW -> #YZ
===#Block YY(size=2, flags=10100)===
   0. lvar105 = {1880073724 ^ lvar105};
   1. goto EW
      -> UnconditionalJump[GOTO] #YY -> #EW
      <- Switch[-1916706830] #YW -> #YY
===#Block EW(size=2, flags=0)===
   0. _consume(catch());
   1. goto OQ
      -> UnconditionalJump[GOTO] #EW -> #OQ
      <- UnconditionalJump[GOTO] #YY -> #EW
      <- UnconditionalJump[GOTO] #YX -> #EW
===#Block OQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1590536943);
   1. goto CL
      -> UnconditionalJump[GOTO] #OQ -> #CL
      <- UnconditionalJump[GOTO] #EW -> #OQ
===#Block VW(size=2, flags=10100)===
   0. lvar105 = {850528825 ^ lvar105};
   1. goto CJ
      -> UnconditionalJump[GOTO] #VW -> #CJ
      <- Switch[177480639] #BK -> #VW
===#Block CJ(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar73 = lvar102;
   2. lvar100 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.nltfbnawgicmzkh(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar74 = lvar73.equals(lvar100);
   4. if (lvar74 != {662420135 ^ lvar105})
      goto UD
   5. lvar105 = {558390450 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CJ -> #UD
      -> Immediate #CJ -> #CK
      <- UnconditionalJump[GOTO] #VW -> #CJ
===#Block CK(size=1, flags=0)===
   0. goto PK
      -> UnconditionalJump[GOTO] #CK -> #PK
      <- Immediate #CJ -> #CK
===#Block PK(size=2, flags=10100)===
   0. lvar105 = {512551290 ^ lvar105};
   1. goto IZ
      -> UnconditionalJump[GOTO] #PK -> #IZ
      <- UnconditionalJump[GOTO] #CK -> #PK
===#Block IZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 267531608)
      goto IY
   1. throw nullconst;
      -> TryCatch range: [IZ...IY] -> AEK ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #IZ -> #IY
      <- UnconditionalJump[GOTO] #PK -> #IZ
===#Block IY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IZ...IY] -> AEK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IZ -> #IY
===#Block AEK(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1189252224:
      	 goto	#AEM
      case -973571208:
      	 goto	#AEL
      default:
      	 goto	#AEN
   }
      -> Switch[-973571208] #AEK -> #AEL
      -> DefaultSwitch #AEK -> #AEN
      -> Switch[-1189252224] #AEK -> #AEM
      <- TryCatch range: [IZ...IY] -> AEK ([Ljava/io/IOException;])
      <- TryCatch range: [IZ...IY] -> AEK ([Ljava/io/IOException;])
===#Block AEM(size=2, flags=10100)===
   0. lvar105 = {737621439 ^ lvar105};
   1. goto JA
      -> UnconditionalJump[GOTO] #AEM -> #JA
      <- Switch[-1189252224] #AEK -> #AEM
===#Block AEN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEK -> #AEN
===#Block AEL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 611030176);
   1. goto JA
      -> UnconditionalJump[GOTO] #AEL -> #JA
      <- Switch[-973571208] #AEK -> #AEL
===#Block JA(size=2, flags=0)===
   0. _consume(catch());
   1. goto LM
      -> UnconditionalJump[GOTO] #JA -> #LM
      <- UnconditionalJump[GOTO] #AEL -> #JA
      <- UnconditionalJump[GOTO] #AEM -> #JA
===#Block LM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1135525666);
   1. goto CL
      -> UnconditionalJump[GOTO] #LM -> #CL
      <- UnconditionalJump[GOTO] #JA -> #LM
===#Block UD(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 38474522:
      	 goto	#JV
      case 209690328:
      	 goto	#UE
      case 1533062192:
      	 goto	#UD
      case 2127003669:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #UD -> #UE
      -> Switch[209690328] #UD -> #UE
      -> Switch[1533062192] #UD -> #UD
      -> Switch[38474522] #UD -> #JV
      -> Switch[2127003669] #UD -> #JC
      -> DefaultSwitch #UD -> #JC
      <- ConditionalJump[IF_ICMPNE] #CJ -> #UD
      <- Switch[1533062192] #UD -> #UD
===#Block UE(size=2, flags=100)===
   0. lvar105 = {2057509548 ^ lvar105};
   1. goto JV
      -> UnconditionalJump[GOTO] #UE -> #JV
      <- Immediate #UD -> #UE
      <- Switch[209690328] #UD -> #UE
===#Block JV(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -289079206)
      goto RJ
   1. goto KM
      -> UnconditionalJump[GOTO] #JV -> #KM
      -> ConditionalJump[IF_ICMPEQ] #JV -> #RJ
      <- Switch[38474522] #UD -> #JV
      <- UnconditionalJump[GOTO] #UE -> #JV
===#Block RJ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 97774541:
      	 goto	#RK
      case 862248754:
      	 goto	#CM
      case 1269765388:
      	 goto	#RJ
      case 1840685497:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[862248754] #RJ -> #CM
      -> Switch[1269765388] #RJ -> #RJ
      -> Switch[97774541] #RJ -> #RK
      -> Immediate #RJ -> #RK
      -> Switch[1840685497] #RJ -> #JC
      -> DefaultSwitch #RJ -> #JC
      <- Switch[1269765388] #RJ -> #RJ
      <- ConditionalJump[IF_ICMPEQ] #JV -> #RJ
===#Block RK(size=2, flags=100)===
   0. lvar105 = {1999851187 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #RK -> #CM
      <- Switch[97774541] #RJ -> #RK
      <- Immediate #RJ -> #RK
===#Block CM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.CYAN_CONCRETE_POWDER;
   2. goto OT
      -> UnconditionalJump[GOTO] #CM -> #OT
      <- Switch[862248754] #RJ -> #CM
      <- UnconditionalJump[GOTO] #RK -> #CM
===#Block OT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1189107088);
   1. goto FZ
      -> UnconditionalJump[GOTO] #OT -> #FZ
      <- UnconditionalJump[GOTO] #CM -> #OT
===#Block FZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 22468246)
      goto FY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FZ -> #FY
      -> TryCatch range: [FZ...FY] -> AAK ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #OT -> #FZ
===#Block FY(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FZ...FY] -> AAK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FZ -> #FY
===#Block AAK(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 166013242:
      	 goto	#AAM
      case 1616763203:
      	 goto	#AAL
      default:
      	 goto	#AAN
   }
      -> DefaultSwitch #AAK -> #AAN
      -> Switch[166013242] #AAK -> #AAM
      -> Switch[1616763203] #AAK -> #AAL
      <- TryCatch range: [FZ...FY] -> AAK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FZ...FY] -> AAK ([Ljava/lang/IllegalAccessException;])
===#Block AAL(size=2, flags=10100)===
   0. lvar105 = {1514715508 ^ lvar105};
   1. goto GA
      -> UnconditionalJump[GOTO] #AAL -> #GA
      <- Switch[1616763203] #AAK -> #AAL
===#Block AAM(size=2, flags=10100)===
   0. lvar105 = {2004854651 ^ lvar105};
   1. goto GA
      -> UnconditionalJump[GOTO] #AAM -> #GA
      <- Switch[166013242] #AAK -> #AAM
===#Block GA(size=2, flags=0)===
   0. _consume(catch());
   1. goto OM
      -> UnconditionalJump[GOTO] #GA -> #OM
      <- UnconditionalJump[GOTO] #AAM -> #GA
      <- UnconditionalJump[GOTO] #AAL -> #GA
===#Block OM(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 153215697:
      	 goto	#ON
      case 479780169:
      	 goto	#JC
      case 828515760:
      	 goto	#OM
      case 1905056542:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[153215697] #OM -> #ON
      -> Switch[1905056542] #OM -> #CN
      -> Switch[828515760] #OM -> #OM
      -> Switch[479780169] #OM -> #JC
      -> DefaultSwitch #OM -> #JC
      -> Immediate #OM -> #ON
      <- Switch[828515760] #OM -> #OM
      <- UnconditionalJump[GOTO] #GA -> #OM
===#Block ON(size=2, flags=100)===
   0. lvar105 = {2132127264 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #ON -> #CN
      <- Switch[153215697] #OM -> #ON
      <- Immediate #OM -> #ON
===#Block AAN(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAK -> #AAN
===#Block KM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 109577377);
   1. goto JC
      -> UnconditionalJump[GOTO] #KM -> #JC
      <- UnconditionalJump[GOTO] #JV -> #KM
===#Block VN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 100877866);
   1. goto BU
      -> UnconditionalJump[GOTO] #VN -> #BU
      <- Switch[177480619] #BK -> #VN
===#Block BU(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar63 = lvar102;
   2. lvar95 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.cedhrnrirbscepn(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar64 = lvar63.equals(lvar95);
   4. if (lvar64 != {332068532 ^ lvar105})
      goto TB
   5. lvar105 = {782993230 ^ lvar105};
      -> Immediate #BU -> #BW
      -> ConditionalJump[IF_ICMPNE] #BU -> #TB
      <- UnconditionalJump[GOTO] #VN -> #BU
===#Block TB(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 106507830:
      	 goto	#TC
      case 1277423756:
      	 goto	#TB
      case 1473855728:
      	 goto	#JF
      case 1675832967:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1473855728] #TB -> #JF
      -> Immediate #TB -> #TC
      -> Switch[1277423756] #TB -> #TB
      -> DefaultSwitch #TB -> #JC
      -> Switch[106507830] #TB -> #TC
      -> Switch[1675832967] #TB -> #JC
      <- Switch[1277423756] #TB -> #TB
      <- ConditionalJump[IF_ICMPNE] #BU -> #TB
===#Block TC(size=2, flags=100)===
   0. lvar105 = {1278340042 ^ lvar105};
   1. goto JF
      -> UnconditionalJump[GOTO] #TC -> #JF
      <- Immediate #TB -> #TC
      <- Switch[106507830] #TB -> #TC
===#Block JF(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -2561038)
      goto RY
   1. goto OJ
      -> ConditionalJump[IF_ICMPEQ] #JF -> #RY
      -> UnconditionalJump[GOTO] #JF -> #OJ
      <- UnconditionalJump[GOTO] #TC -> #JF
      <- Switch[1473855728] #TB -> #JF
===#Block OJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 78363092);
   1. goto JC
      -> UnconditionalJump[GOTO] #OJ -> #JC
      <- UnconditionalJump[GOTO] #JF -> #OJ
===#Block RY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 733282024);
   1. goto BV
      -> UnconditionalJump[GOTO] #RY -> #BV
      <- ConditionalJump[IF_ICMPEQ] #JF -> #RY
===#Block BV(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.YELLOW_CONCRETE_POWDER;
   2. goto LZ
      -> UnconditionalJump[GOTO] #BV -> #LZ
      <- UnconditionalJump[GOTO] #RY -> #BV
===#Block LZ(size=2, flags=10100)===
   0. lvar105 = {844188418 ^ lvar105};
   1. goto IT
      -> UnconditionalJump[GOTO] #LZ -> #IT
      <- UnconditionalJump[GOTO] #BV -> #LZ
===#Block IT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 131801216)
      goto IS
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IT -> #IS
      -> TryCatch range: [IT...IS] -> AEC ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #LZ -> #IT
===#Block IS(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IT...IS] -> AEC ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IT -> #IS
===#Block AEC(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 738985544:
      	 goto	#AEE
      case 821380258:
      	 goto	#AED
      default:
      	 goto	#AEF
   }
      -> Switch[738985544] #AEC -> #AEE
      -> Switch[821380258] #AEC -> #AED
      -> DefaultSwitch #AEC -> #AEF
      <- TryCatch range: [IT...IS] -> AEC ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IT...IS] -> AEC ([Ljava/lang/IllegalAccessException;])
===#Block AEF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEC -> #AEF
===#Block AED(size=2, flags=10100)===
   0. lvar105 = {1694158906 ^ lvar105};
   1. goto IU
      -> UnconditionalJump[GOTO] #AED -> #IU
      <- Switch[821380258] #AEC -> #AED
===#Block AEE(size=2, flags=10100)===
   0. lvar105 = {660994151 ^ lvar105};
   1. goto IU
      -> UnconditionalJump[GOTO] #AEE -> #IU
      <- Switch[738985544] #AEC -> #AEE
===#Block IU(size=2, flags=0)===
   0. _consume(catch());
   1. goto PH
      -> UnconditionalJump[GOTO] #IU -> #PH
      <- UnconditionalJump[GOTO] #AED -> #IU
      <- UnconditionalJump[GOTO] #AEE -> #IU
===#Block PH(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 60459829:
      	 goto	#PI
      case 1517690522:
      	 goto	#CN
      case 1601230024:
      	 goto	#PH
      case 1914529424:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #PH -> #JC
      -> Switch[1601230024] #PH -> #PH
      -> Switch[1914529424] #PH -> #JC
      -> Switch[60459829] #PH -> #PI
      -> Switch[1517690522] #PH -> #CN
      -> Immediate #PH -> #PI
      <- Switch[1601230024] #PH -> #PH
      <- UnconditionalJump[GOTO] #IU -> #PH
===#Block PI(size=2, flags=100)===
   0. lvar105 = {1806882002 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PI -> #CN
      <- Switch[60459829] #PH -> #PI
      <- Immediate #PH -> #PI
===#Block BW(size=1, flags=0)===
   0. goto QT
      -> UnconditionalJump[GOTO] #BW -> #QT
      <- Immediate #BU -> #BW
===#Block QT(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 42342370:
      	 goto	#HA
      case 116191669:
      	 goto	#QU
      case 435055237:
      	 goto	#JC
      case 1840323095:
      	 goto	#QT
      default:
      	 goto	#JC
   }
      -> Switch[42342370] #QT -> #HA
      -> Switch[1840323095] #QT -> #QT
      -> Immediate #QT -> #QU
      -> Switch[116191669] #QT -> #QU
      -> Switch[435055237] #QT -> #JC
      -> DefaultSwitch #QT -> #JC
      <- UnconditionalJump[GOTO] #BW -> #QT
      <- Switch[1840323095] #QT -> #QT
===#Block QU(size=2, flags=100)===
   0. lvar105 = {901169374 ^ lvar105};
   1. goto HA
      -> UnconditionalJump[GOTO] #QU -> #HA
      <- Immediate #QT -> #QU
      <- Switch[116191669] #QT -> #QU
===#Block HA(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 19001314)
      goto GZ
   1. throw nullconst;
      -> TryCatch range: [HA...GZ] -> ABU ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #HA -> #GZ
      <- Switch[42342370] #QT -> #HA
      <- UnconditionalJump[GOTO] #QU -> #HA
===#Block GZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HA...GZ] -> ABU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HA -> #GZ
===#Block ABU(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 1186826528:
      	 goto	#ABV
      case 1936566211:
      	 goto	#ABW
      default:
      	 goto	#ABX
   }
      -> Switch[1186826528] #ABU -> #ABV
      -> Switch[1936566211] #ABU -> #ABW
      -> DefaultSwitch #ABU -> #ABX
      <- TryCatch range: [HA...GZ] -> ABU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HA...GZ] -> ABU ([Ljava/lang/RuntimeException;])
===#Block ABX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABU -> #ABX
===#Block ABW(size=2, flags=10100)===
   0. lvar105 = {652386363 ^ lvar105};
   1. goto HB
      -> UnconditionalJump[GOTO] #ABW -> #HB
      <- Switch[1936566211] #ABU -> #ABW
===#Block ABV(size=2, flags=10100)===
   0. lvar105 = {1079551463 ^ lvar105};
   1. goto HB
      -> UnconditionalJump[GOTO] #ABV -> #HB
      <- Switch[1186826528] #ABU -> #ABV
===#Block HB(size=2, flags=0)===
   0. _consume(catch());
   1. goto OX
      -> UnconditionalJump[GOTO] #HB -> #OX
      <- UnconditionalJump[GOTO] #ABV -> #HB
      <- UnconditionalJump[GOTO] #ABW -> #HB
===#Block OX(size=2, flags=10100)===
   0. lvar105 = {938815534 ^ lvar105};
   1. goto CL
      -> UnconditionalJump[GOTO] #OX -> #CL
      <- UnconditionalJump[GOTO] #HB -> #OX
===#Block VV(size=2, flags=10100)===
   0. lvar105 = {242478869 ^ lvar105};
   1. goto BR
      -> UnconditionalJump[GOTO] #VV -> #BR
      <- Switch[177480637] #BK -> #VV
===#Block BR(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar61 = lvar102;
   2. lvar94 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.bnjbpnhustapfib(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar62 = lvar61.equals(lvar94);
   4. if (lvar62 != {465198987 ^ lvar105})
      goto RX
   5. lvar105 = {1806750026 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BR -> #RX
      -> Immediate #BR -> #BS
      <- UnconditionalJump[GOTO] #VV -> #BR
===#Block BS(size=1, flags=0)===
   0. goto OB
      -> UnconditionalJump[GOTO] #BS -> #OB
      <- Immediate #BR -> #BS
===#Block OB(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 152306847:
      	 goto	#OC
      case 702183915:
      	 goto	#OB
      case 1238257122:
      	 goto	#DR
      case 1725488817:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1725488817] #OB -> #JC
      -> Switch[702183915] #OB -> #OB
      -> Switch[1238257122] #OB -> #DR
      -> Immediate #OB -> #OC
      -> DefaultSwitch #OB -> #JC
      -> Switch[152306847] #OB -> #OC
      <- Switch[702183915] #OB -> #OB
      <- UnconditionalJump[GOTO] #BS -> #OB
===#Block OC(size=2, flags=100)===
   0. lvar105 = {2010785898 ^ lvar105};
   1. goto DR
      -> UnconditionalJump[GOTO] #OC -> #DR
      <- Immediate #OB -> #OC
      <- Switch[152306847] #OB -> #OC
===#Block DR(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 122919824)
      goto DQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DR -> #DQ
      -> TryCatch range: [DR...DQ] -> XI ([Ljava/lang/IllegalAccessException;])
      <- Switch[1238257122] #OB -> #DR
      <- UnconditionalJump[GOTO] #OC -> #DR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DR...DQ] -> XI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DR -> #DQ
===#Block XI(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -81496165:
      	 goto	#XK
      case 1048917336:
      	 goto	#XJ
      default:
      	 goto	#XL
   }
      -> Switch[-81496165] #XI -> #XK
      -> DefaultSwitch #XI -> #XL
      -> Switch[1048917336] #XI -> #XJ
      <- TryCatch range: [DR...DQ] -> XI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DR...DQ] -> XI ([Ljava/lang/IllegalAccessException;])
===#Block XJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 388477790);
   1. goto DS
      -> UnconditionalJump[GOTO] #XJ -> #DS
      <- Switch[1048917336] #XI -> #XJ
===#Block XL(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #XI -> #XL
===#Block XK(size=2, flags=10100)===
   0. lvar105 = {1871944454 ^ lvar105};
   1. goto DS
      -> UnconditionalJump[GOTO] #XK -> #DS
      <- Switch[-81496165] #XI -> #XK
===#Block DS(size=2, flags=0)===
   0. _consume(catch());
   1. goto QS
      -> UnconditionalJump[GOTO] #DS -> #QS
      <- UnconditionalJump[GOTO] #XK -> #DS
      <- UnconditionalJump[GOTO] #XJ -> #DS
===#Block QS(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1871524120);
   1. goto CL
      -> UnconditionalJump[GOTO] #QS -> #CL
      <- UnconditionalJump[GOTO] #DS -> #QS
===#Block RX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1723196939);
   1. goto JM
      -> UnconditionalJump[GOTO] #RX -> #JM
      <- ConditionalJump[IF_ICMPNE] #BR -> #RX
===#Block JM(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -394408957)
      goto RM
   1. goto KW
      -> UnconditionalJump[GOTO] #JM -> #KW
      -> ConditionalJump[IF_ICMPEQ] #JM -> #RM
      <- UnconditionalJump[GOTO] #RX -> #JM
===#Block RM(size=2, flags=10100)===
   0. lvar105 = {1270219852 ^ lvar105};
   1. goto BT
      -> UnconditionalJump[GOTO] #RM -> #BT
      <- ConditionalJump[IF_ICMPEQ] #JM -> #RM
===#Block BT(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.MAGENTA_CONCRETE_POWDER;
   2. goto MG
      -> UnconditionalJump[GOTO] #BT -> #MG
      <- UnconditionalJump[GOTO] #RM -> #BT
===#Block MG(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 168290562:
      	 goto	#MH
      case 708376905:
      	 goto	#MG
      case 748182093:
      	 goto	#DI
      case 1150434850:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #MG -> #MH
      -> DefaultSwitch #MG -> #JC
      -> Switch[168290562] #MG -> #MH
      -> Switch[708376905] #MG -> #MG
      -> Switch[748182093] #MG -> #DI
      -> Switch[1150434850] #MG -> #JC
      <- UnconditionalJump[GOTO] #BT -> #MG
      <- Switch[708376905] #MG -> #MG
===#Block MH(size=2, flags=100)===
   0. lvar105 = {111949046 ^ lvar105};
   1. goto DI
      -> UnconditionalJump[GOTO] #MH -> #DI
      <- Immediate #MG -> #MH
      <- Switch[168290562] #MG -> #MH
===#Block DI(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 220826101)
      goto DH
   1. throw nullconst;
      -> TryCatch range: [DI...DH] -> WW ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DI -> #DH
      <- Switch[748182093] #MG -> #DI
      <- UnconditionalJump[GOTO] #MH -> #DI
===#Block DH(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DI...DH] -> WW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DI -> #DH
===#Block WW(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -2136192559:
      	 goto	#WX
      case -838561629:
      	 goto	#WY
      default:
      	 goto	#WZ
   }
      -> Switch[-838561629] #WW -> #WY
      -> Switch[-2136192559] #WW -> #WX
      -> DefaultSwitch #WW -> #WZ
      <- TryCatch range: [DI...DH] -> WW ([Ljava/io/IOException;])
      <- TryCatch range: [DI...DH] -> WW ([Ljava/io/IOException;])
===#Block WZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #WW -> #WZ
===#Block WX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 531647509);
   1. goto DJ
      -> UnconditionalJump[GOTO] #WX -> #DJ
      <- Switch[-2136192559] #WW -> #WX
===#Block WY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1449482043);
   1. goto DJ
      -> UnconditionalJump[GOTO] #WY -> #DJ
      <- Switch[-838561629] #WW -> #WY
===#Block DJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto OZ
      -> UnconditionalJump[GOTO] #DJ -> #OZ
      <- UnconditionalJump[GOTO] #WX -> #DJ
      <- UnconditionalJump[GOTO] #WY -> #DJ
===#Block OZ(size=2, flags=10100)===
   0. lvar105 = {1727228243 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OZ -> #CN
      <- UnconditionalJump[GOTO] #DJ -> #OZ
===#Block KW(size=2, flags=10100)===
   0. lvar105 = {643767594 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #KW -> #JC
      <- UnconditionalJump[GOTO] #JM -> #KW
===#Block VQ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 171365371:
      	 goto	#VR
      case 791335906:
      	 goto	#BL
      case 1382768470:
      	 goto	#JC
      case 1552686626:
      	 goto	#VQ
      default:
      	 goto	#JC
   }
      -> Switch[171365371] #VQ -> #VR
      -> Immediate #VQ -> #VR
      -> Switch[1552686626] #VQ -> #VQ
      -> Switch[1382768470] #VQ -> #JC
      -> DefaultSwitch #VQ -> #JC
      -> Switch[791335906] #VQ -> #BL
      <- Switch[1552686626] #VQ -> #VQ
      <- Switch[177480627] #BK -> #VQ
===#Block VR(size=2, flags=100)===
   0. lvar105 = {965917220 ^ lvar105};
   1. goto BL
      -> UnconditionalJump[GOTO] #VR -> #BL
      <- Switch[171365371] #VQ -> #VR
      <- Immediate #VQ -> #VR
===#Block BL(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar57 = lvar102;
   2. lvar92 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.wgdehfwmnqxzlee(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar58 = lvar57.equals(lvar92);
   4. if (lvar58 != {744164026 ^ lvar105})
      goto TK
   5. lvar105 = {1065561889 ^ lvar105};
      -> Immediate #BL -> #BN
      -> ConditionalJump[IF_ICMPNE] #BL -> #TK
      <- Switch[791335906] #VQ -> #BL
      <- UnconditionalJump[GOTO] #VR -> #BL
===#Block TK(size=2, flags=10100)===
   0. lvar105 = {1858594962 ^ lvar105};
   1. goto JH
      -> UnconditionalJump[GOTO] #TK -> #JH
      <- ConditionalJump[IF_ICMPNE] #BL -> #TK
===#Block JH(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 350687554)
      goto TY
   1. goto OR
      -> ConditionalJump[IF_ICMPEQ] #JH -> #TY
      -> UnconditionalJump[GOTO] #JH -> #OR
      <- UnconditionalJump[GOTO] #TK -> #JH
===#Block OR(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 17806513:
      	 goto	#OS
      case 91007740:
      	 goto	#OR
      case 1444656472:
      	 goto	#JC
      case 1792388023:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #OR -> #OS
      -> Switch[17806513] #OR -> #OS
      -> DefaultSwitch #OR -> #JC
      -> Switch[1444656472] #OR -> #JC
      -> Switch[91007740] #OR -> #OR
      <- UnconditionalJump[GOTO] #JH -> #OR
      <- Switch[91007740] #OR -> #OR
===#Block OS(size=2, flags=100)===
   0. lvar105 = {432817794 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OS -> #JC
      <- Immediate #OR -> #OS
      <- Switch[17806513] #OR -> #OS
===#Block TY(size=2, flags=10100)===
   0. lvar105 = {1749328466 ^ lvar105};
   1. goto BM
      -> UnconditionalJump[GOTO] #TY -> #BM
      <- ConditionalJump[IF_ICMPEQ] #JH -> #TY
===#Block BM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.WHITE_CONCRETE_POWDER;
   2. goto PP
      -> UnconditionalJump[GOTO] #BM -> #PP
      <- UnconditionalJump[GOTO] #TY -> #BM
===#Block PP(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 50517556:
      	 goto	#PQ
      case 581507232:
      	 goto	#CW
      case 887506608:
      	 goto	#PP
      case 1856209290:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[50517556] #PP -> #PQ
      -> Switch[887506608] #PP -> #PP
      -> Immediate #PP -> #PQ
      -> Switch[581507232] #PP -> #CW
      -> Switch[1856209290] #PP -> #JC
      -> DefaultSwitch #PP -> #JC
      <- Switch[887506608] #PP -> #PP
      <- UnconditionalJump[GOTO] #BM -> #PP
===#Block PQ(size=2, flags=100)===
   0. lvar105 = {1733192809 ^ lvar105};
   1. goto CW
      -> UnconditionalJump[GOTO] #PQ -> #CW
      <- Switch[50517556] #PP -> #PQ
      <- Immediate #PP -> #PQ
===#Block CW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 106084594)
      goto CV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CW -> #CV
      -> TryCatch range: [CW...CV] -> WG ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #PQ -> #CW
      <- Switch[581507232] #PP -> #CW
===#Block CV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CW...CV] -> WG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CW -> #CV
===#Block WG(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 919207097:
      	 goto	#WI
      case 1823563930:
      	 goto	#WH
      default:
      	 goto	#WJ
   }
      -> DefaultSwitch #WG -> #WJ
      -> Switch[919207097] #WG -> #WI
      -> Switch[1823563930] #WG -> #WH
      <- TryCatch range: [CW...CV] -> WG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CW...CV] -> WG ([Ljava/lang/RuntimeException;])
===#Block WH(size=2, flags=10100)===
   0. lvar105 = {2130876924 ^ lvar105};
   1. goto CX
      -> UnconditionalJump[GOTO] #WH -> #CX
      <- Switch[1823563930] #WG -> #WH
===#Block WI(size=2, flags=10100)===
   0. lvar105 = {340626040 ^ lvar105};
   1. goto CX
      -> UnconditionalJump[GOTO] #WI -> #CX
      <- Switch[919207097] #WG -> #WI
===#Block CX(size=2, flags=0)===
   0. _consume(catch());
   1. goto LX
      -> UnconditionalJump[GOTO] #CX -> #LX
      <- UnconditionalJump[GOTO] #WH -> #CX
      <- UnconditionalJump[GOTO] #WI -> #CX
===#Block LX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 2076318099);
   1. goto CN
      -> UnconditionalJump[GOTO] #LX -> #CN
      <- UnconditionalJump[GOTO] #CX -> #LX
===#Block WJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #WG -> #WJ
===#Block BN(size=1, flags=0)===
   0. goto QR
      -> UnconditionalJump[GOTO] #BN -> #QR
      <- Immediate #BL -> #BN
===#Block QR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 121012823);
   1. goto GU
      -> UnconditionalJump[GOTO] #QR -> #GU
      <- UnconditionalJump[GOTO] #BN -> #QR
===#Block GU(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 143553013)
      goto GT
   1. throw nullconst;
      -> TryCatch range: [GU...GT] -> ABM ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #GU -> #GT
      <- UnconditionalJump[GOTO] #QR -> #GU
===#Block GT(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [GU...GT] -> ABM ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #GU -> #GT
===#Block ABM(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1485488544:
      	 goto	#ABN
      case 2039245691:
      	 goto	#ABO
      default:
      	 goto	#ABP
   }
      -> DefaultSwitch #ABM -> #ABP
      -> Switch[2039245691] #ABM -> #ABO
      -> Switch[-1485488544] #ABM -> #ABN
      <- TryCatch range: [GU...GT] -> ABM ([Ljava/io/IOException;])
      <- TryCatch range: [GU...GT] -> ABM ([Ljava/io/IOException;])
===#Block ABN(size=2, flags=10100)===
   0. lvar105 = {1669856080 ^ lvar105};
   1. goto GV
      -> UnconditionalJump[GOTO] #ABN -> #GV
      <- Switch[-1485488544] #ABM -> #ABN
===#Block ABO(size=2, flags=10100)===
   0. lvar105 = {408474099 ^ lvar105};
   1. goto GV
      -> UnconditionalJump[GOTO] #ABO -> #GV
      <- Switch[2039245691] #ABM -> #ABO
===#Block GV(size=2, flags=0)===
   0. _consume(catch());
   1. goto RC
      -> UnconditionalJump[GOTO] #GV -> #RC
      <- UnconditionalJump[GOTO] #ABO -> #GV
      <- UnconditionalJump[GOTO] #ABN -> #GV
===#Block RC(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 123491335:
      	 goto	#RD
      case 997802030:
      	 goto	#RC
      case 2106601945:
      	 goto	#JC
      case 2126003434:
      	 goto	#CL
      default:
      	 goto	#JC
   }
      -> Switch[997802030] #RC -> #RC
      -> Switch[2126003434] #RC -> #CL
      -> Switch[123491335] #RC -> #RD
      -> Switch[2106601945] #RC -> #JC
      -> Immediate #RC -> #RD
      -> DefaultSwitch #RC -> #JC
      <- Switch[997802030] #RC -> #RC
      <- UnconditionalJump[GOTO] #GV -> #RC
===#Block RD(size=2, flags=100)===
   0. lvar105 = {135493745 ^ lvar105};
   1. goto CL
      -> UnconditionalJump[GOTO] #RD -> #CL
      <- Switch[123491335] #RC -> #RD
      <- Immediate #RC -> #RD
===#Block ABP(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABM -> #ABP
===#Block VX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1790153843);
   1. goto CL
      -> UnconditionalJump[GOTO] #VX -> #CL
      <- DefaultSwitch #BK -> #VX
===#Block VU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 794442122);
   1. goto CD
      -> UnconditionalJump[GOTO] #VU -> #CD
      <- Switch[177480631] #BK -> #VU
===#Block CD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar69 = lvar102;
   2. lvar98 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.xrmdsqhjxfmajau(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar70 = lvar69.equals(lvar98);
   4. if (lvar70 != {982747412 ^ lvar105})
      goto RL
   5. lvar105 = {2126292431 ^ lvar105};
      -> Immediate #CD -> #CF
      -> ConditionalJump[IF_ICMPNE] #CD -> #RL
      <- UnconditionalJump[GOTO] #VU -> #CD
===#Block RL(size=2, flags=10100)===
   0. lvar105 = {95974681 ^ lvar105};
   1. goto JD
      -> UnconditionalJump[GOTO] #RL -> #JD
      <- ConditionalJump[IF_ICMPNE] #CD -> #RL
===#Block JD(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -111157143)
      goto SG
   1. goto PA
      -> UnconditionalJump[GOTO] #JD -> #PA
      -> ConditionalJump[IF_ICMPEQ] #JD -> #SG
      <- UnconditionalJump[GOTO] #RL -> #JD
===#Block SG(size=2, flags=10100)===
   0. lvar105 = {106486314 ^ lvar105};
   1. goto CE
      -> UnconditionalJump[GOTO] #SG -> #CE
      <- ConditionalJump[IF_ICMPEQ] #JD -> #SG
===#Block CE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLACK_CONCRETE_POWDER;
   2. goto RB
      -> UnconditionalJump[GOTO] #CE -> #RB
      <- UnconditionalJump[GOTO] #SG -> #CE
===#Block RB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 426132433);
   1. goto IH
      -> UnconditionalJump[GOTO] #RB -> #IH
      <- UnconditionalJump[GOTO] #CE -> #RB
===#Block IH(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 237589913)
      goto IG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IH -> #IG
      -> TryCatch range: [IH...IG] -> ADM ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #RB -> #IH
===#Block IG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IH...IG] -> ADM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IH -> #IG
===#Block ADM(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 11218865:
      	 goto	#ADN
      case 1420046138:
      	 goto	#ADO
      default:
      	 goto	#ADP
   }
      -> Switch[1420046138] #ADM -> #ADO
      -> Switch[11218865] #ADM -> #ADN
      -> DefaultSwitch #ADM -> #ADP
      <- TryCatch range: [IH...IG] -> ADM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IH...IG] -> ADM ([Ljava/lang/IllegalAccessException;])
===#Block ADP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADM -> #ADP
===#Block ADN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1912122514);
   1. goto II
      -> UnconditionalJump[GOTO] #ADN -> #II
      <- Switch[11218865] #ADM -> #ADN
===#Block ADO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 460934275);
   1. goto II
      -> UnconditionalJump[GOTO] #ADO -> #II
      <- Switch[1420046138] #ADM -> #ADO
===#Block II(size=2, flags=0)===
   0. _consume(catch());
   1. goto NG
      -> UnconditionalJump[GOTO] #II -> #NG
      <- UnconditionalJump[GOTO] #ADN -> #II
      <- UnconditionalJump[GOTO] #ADO -> #II
===#Block NG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 414934296);
   1. goto CN
      -> UnconditionalJump[GOTO] #NG -> #CN
      <- UnconditionalJump[GOTO] #II -> #NG
===#Block PA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1685805223);
   1. goto JC
      -> UnconditionalJump[GOTO] #PA -> #JC
      <- UnconditionalJump[GOTO] #JD -> #PA
===#Block CF(size=1, flags=0)===
   0. goto MJ
      -> UnconditionalJump[GOTO] #CF -> #MJ
      <- Immediate #CD -> #CF
===#Block MJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 610445954);
   1. goto EG
      -> UnconditionalJump[GOTO] #MJ -> #EG
      <- UnconditionalJump[GOTO] #CF -> #MJ
===#Block EG(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 177624065)
      goto EF
   1. throw nullconst;
      -> TryCatch range: [EG...EF] -> YC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EG -> #EF
      <- UnconditionalJump[GOTO] #MJ -> #EG
===#Block EF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EG...EF] -> YC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EG -> #EF
===#Block YC(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 40645323:
      	 goto	#YD
      case 1166966553:
      	 goto	#YE
      default:
      	 goto	#YF
   }
      -> Switch[1166966553] #YC -> #YE
      -> DefaultSwitch #YC -> #YF
      -> Switch[40645323] #YC -> #YD
      <- TryCatch range: [EG...EF] -> YC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EG...EF] -> YC ([Ljava/lang/RuntimeException;])
===#Block YD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 58709315);
   1. goto EH
      -> UnconditionalJump[GOTO] #YD -> #EH
      <- Switch[40645323] #YC -> #YD
===#Block YF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #YC -> #YF
===#Block YE(size=2, flags=10100)===
   0. lvar105 = {1266916601 ^ lvar105};
   1. goto EH
      -> UnconditionalJump[GOTO] #YE -> #EH
      <- Switch[1166966553] #YC -> #YE
===#Block EH(size=2, flags=0)===
   0. _consume(catch());
   1. goto KF
      -> UnconditionalJump[GOTO] #EH -> #KF
      <- UnconditionalJump[GOTO] #YE -> #EH
      <- UnconditionalJump[GOTO] #YD -> #EH
===#Block KF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 474511351);
   1. goto CL
      -> UnconditionalJump[GOTO] #KF -> #CL
      <- UnconditionalJump[GOTO] #EH -> #KF
===#Block VP(size=2, flags=10100)===
   0. lvar105 = {1411077010 ^ lvar105};
   1. goto CA
      -> UnconditionalJump[GOTO] #VP -> #CA
      <- Switch[177480625] #BK -> #VP
===#Block CA(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar67 = lvar102;
   2. lvar97 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.avlofnbbvgchwvl(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar68 = lvar67.equals(lvar97);
   4. if (lvar68 != {1104339724 ^ lvar105})
      goto TL
   5. lvar105 = {756117390 ^ lvar105};
      -> Immediate #CA -> #CC
      -> ConditionalJump[IF_ICMPNE] #CA -> #TL
      <- UnconditionalJump[GOTO] #VP -> #CA
===#Block TL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1510985022);
   1. goto JJ
      -> UnconditionalJump[GOTO] #TL -> #JJ
      <- ConditionalJump[IF_ICMPNE] #CA -> #TL
===#Block JJ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -555126384)
      goto TI
   1. goto MB
      -> ConditionalJump[IF_ICMPEQ] #JJ -> #TI
      -> UnconditionalJump[GOTO] #JJ -> #MB
      <- UnconditionalJump[GOTO] #TL -> #JJ
===#Block MB(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 100438621:
      	 goto	#MC
      case 315096594:
      	 goto	#MB
      case 1245325294:
      	 goto	#JC
      case 1959065777:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #MB -> #MC
      -> Switch[315096594] #MB -> #MB
      -> Switch[1245325294] #MB -> #JC
      -> DefaultSwitch #MB -> #JC
      -> Switch[100438621] #MB -> #MC
      <- Switch[315096594] #MB -> #MB
      <- UnconditionalJump[GOTO] #JJ -> #MB
===#Block MC(size=2, flags=100)===
   0. lvar105 = {1083017880 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MC -> #JC
      <- Immediate #MB -> #MC
      <- Switch[100438621] #MB -> #MC
===#Block TI(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 100438621:
      	 goto	#TJ
      case 658966550:
      	 goto	#CB
      case 1315524604:
      	 goto	#TI
      case 1849020325:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #TI -> #JC
      -> Switch[100438621] #TI -> #TJ
      -> Switch[1849020325] #TI -> #JC
      -> Immediate #TI -> #TJ
      -> Switch[658966550] #TI -> #CB
      -> Switch[1315524604] #TI -> #TI
      <- ConditionalJump[IF_ICMPEQ] #JJ -> #TI
      <- Switch[1315524604] #TI -> #TI
===#Block TJ(size=2, flags=100)===
   0. lvar105 = {1743097790 ^ lvar105};
   1. goto CB
      -> UnconditionalJump[GOTO] #TJ -> #CB
      <- Switch[100438621] #TI -> #TJ
      <- Immediate #TI -> #TJ
===#Block CB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GREEN_CONCRETE_POWDER;
   2. goto PJ
      -> UnconditionalJump[GOTO] #CB -> #PJ
      <- UnconditionalJump[GOTO] #TJ -> #CB
      <- Switch[658966550] #TI -> #CB
===#Block PJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 252898748);
   1. goto IQ
      -> UnconditionalJump[GOTO] #PJ -> #IQ
      <- UnconditionalJump[GOTO] #CB -> #PJ
===#Block IQ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 253853879)
      goto IP
   1. throw nullconst;
      -> TryCatch range: [IQ...IP] -> ADY ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IQ -> #IP
      <- UnconditionalJump[GOTO] #PJ -> #IQ
===#Block IP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IQ...IP] -> ADY ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IQ -> #IP
===#Block ADY(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1722654333:
      	 goto	#ADZ
      case 1914056376:
      	 goto	#AEA
      default:
      	 goto	#AEB
   }
      -> Switch[1914056376] #ADY -> #AEA
      -> DefaultSwitch #ADY -> #AEB
      -> Switch[-1722654333] #ADY -> #ADZ
      <- TryCatch range: [IQ...IP] -> ADY ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IQ...IP] -> ADY ([Ljava/lang/IllegalAccessException;])
===#Block ADZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1676616052);
   1. goto IR
      -> UnconditionalJump[GOTO] #ADZ -> #IR
      <- Switch[-1722654333] #ADY -> #ADZ
===#Block AEB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADY -> #AEB
===#Block AEA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 512206995);
   1. goto IR
      -> UnconditionalJump[GOTO] #AEA -> #IR
      <- Switch[1914056376] #ADY -> #AEA
===#Block IR(size=2, flags=0)===
   0. _consume(catch());
   1. goto PT
      -> UnconditionalJump[GOTO] #IR -> #PT
      <- UnconditionalJump[GOTO] #AEA -> #IR
      <- UnconditionalJump[GOTO] #ADZ -> #IR
===#Block PT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1502855480);
   1. goto CN
      -> UnconditionalJump[GOTO] #PT -> #CN
      <- UnconditionalJump[GOTO] #IR -> #PT
===#Block CC(size=1, flags=0)===
   0. goto MZ
      -> UnconditionalJump[GOTO] #CC -> #MZ
      <- Immediate #CA -> #CC
===#Block MZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 683133149);
   1. goto GX
      -> UnconditionalJump[GOTO] #MZ -> #GX
      <- UnconditionalJump[GOTO] #CC -> #MZ
===#Block GX(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 77660172)
      goto GW
   1. throw nullconst;
      -> TryCatch range: [GX...GW] -> ABQ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GX -> #GW
      <- UnconditionalJump[GOTO] #MZ -> #GX
===#Block GW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GX...GW] -> ABQ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GX -> #GW
===#Block ABQ(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 597828346:
      	 goto	#ABR
      case 1233284739:
      	 goto	#ABS
      default:
      	 goto	#ABT
   }
      -> Switch[1233284739] #ABQ -> #ABS
      -> Switch[597828346] #ABQ -> #ABR
      -> DefaultSwitch #ABQ -> #ABT
      <- TryCatch range: [GX...GW] -> ABQ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GX...GW] -> ABQ ([Ljava/lang/RuntimeException;])
===#Block ABT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABQ -> #ABT
===#Block ABR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 216427407);
   1. goto GY
      -> UnconditionalJump[GOTO] #ABR -> #GY
      <- Switch[597828346] #ABQ -> #ABR
===#Block ABS(size=2, flags=10100)===
   0. lvar105 = {564290048 ^ lvar105};
   1. goto GY
      -> UnconditionalJump[GOTO] #ABS -> #GY
      <- Switch[1233284739] #ABQ -> #ABS
===#Block GY(size=2, flags=0)===
   0. _consume(catch());
   1. goto LI
      -> UnconditionalJump[GOTO] #GY -> #LI
      <- UnconditionalJump[GOTO] #ABR -> #GY
      <- UnconditionalJump[GOTO] #ABS -> #GY
===#Block LI(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 50725330:
      	 goto	#LI
      case 211514417:
      	 goto	#LJ
      case 457770059:
      	 goto	#CL
      case 537876272:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[457770059] #LI -> #CL
      -> Switch[211514417] #LI -> #LJ
      -> DefaultSwitch #LI -> #JC
      -> Switch[537876272] #LI -> #JC
      -> Switch[50725330] #LI -> #LI
      -> Immediate #LI -> #LJ
      <- UnconditionalJump[GOTO] #GY -> #LI
      <- Switch[50725330] #LI -> #LI
===#Block LJ(size=2, flags=100)===
   0. lvar105 = {937954109 ^ lvar105};
   1. goto CL
      -> UnconditionalJump[GOTO] #LJ -> #CL
      <- Switch[211514417] #LI -> #LJ
      <- Immediate #LI -> #LJ
===#Block VO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1237030952);
   1. goto CG
      -> UnconditionalJump[GOTO] #VO -> #CG
      <- Switch[177480623] #BK -> #VO
===#Block CG(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar71 = lvar102;
   2. lvar99 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.gdiivelbjailnxu(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar72 = lvar71.equals(lvar99);
   4. if (lvar72 != {1550985398 ^ lvar105})
      goto SO
   5. lvar105 = {1605936790 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CG -> #SO
      -> Immediate #CG -> #CI
      <- UnconditionalJump[GOTO] #VO -> #CG
===#Block CI(size=1, flags=0)===
   0. goto QX
      -> UnconditionalJump[GOTO] #CI -> #QX
      <- Immediate #CG -> #CI
===#Block QX(size=2, flags=10100)===
   0. lvar105 = {752447503 ^ lvar105};
   1. goto HV
      -> UnconditionalJump[GOTO] #QX -> #HV
      <- UnconditionalJump[GOTO] #CI -> #QX
===#Block HV(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 188126056)
      goto HU
   1. throw nullconst;
      -> TryCatch range: [HV...HU] -> ACW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #HV -> #HU
      <- UnconditionalJump[GOTO] #QX -> #HV
===#Block HU(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HV...HU] -> ACW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HV -> #HU
===#Block ACW(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1784455397:
      	 goto	#ACY
      case 2023747961:
      	 goto	#ACX
      default:
      	 goto	#ACZ
   }
      -> Switch[-1784455397] #ACW -> #ACY
      -> DefaultSwitch #ACW -> #ACZ
      -> Switch[2023747961] #ACW -> #ACX
      <- TryCatch range: [HV...HU] -> ACW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HV...HU] -> ACW ([Ljava/lang/RuntimeException;])
===#Block ACX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 2100564503);
   1. goto HW
      -> UnconditionalJump[GOTO] #ACX -> #HW
      <- Switch[2023747961] #ACW -> #ACX
===#Block ACZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ACW -> #ACZ
===#Block ACY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 546539483);
   1. goto HW
      -> UnconditionalJump[GOTO] #ACY -> #HW
      <- Switch[-1784455397] #ACW -> #ACY
===#Block HW(size=2, flags=0)===
   0. _consume(catch());
   1. goto OE
      -> UnconditionalJump[GOTO] #HW -> #OE
      <- UnconditionalJump[GOTO] #ACY -> #HW
      <- UnconditionalJump[GOTO] #ACX -> #HW
===#Block OE(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 254610315:
      	 goto	#OF
      case 362858104:
      	 goto	#CL
      case 405014194:
      	 goto	#OE
      case 1430060284:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #OE -> #JC
      -> Switch[1430060284] #OE -> #JC
      -> Switch[362858104] #OE -> #CL
      -> Switch[405014194] #OE -> #OE
      -> Immediate #OE -> #OF
      -> Switch[254610315] #OE -> #OF
      <- Switch[405014194] #OE -> #OE
      <- UnconditionalJump[GOTO] #HW -> #OE
===#Block OF(size=2, flags=100)===
   0. lvar105 = {761120981 ^ lvar105};
   1. goto CL
      -> UnconditionalJump[GOTO] #OF -> #CL
      <- Immediate #OE -> #OF
      <- Switch[254610315] #OE -> #OF
===#Block SO(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 52261522:
      	 goto	#SP
      case 305741524:
      	 goto	#JZ
      case 637902977:
      	 goto	#JC
      case 1058428000:
      	 goto	#SO
      default:
      	 goto	#JC
   }
      -> Switch[305741524] #SO -> #JZ
      -> Switch[1058428000] #SO -> #SO
      -> Switch[637902977] #SO -> #JC
      -> DefaultSwitch #SO -> #JC
      -> Immediate #SO -> #SP
      -> Switch[52261522] #SO -> #SP
      <- Switch[1058428000] #SO -> #SO
      <- ConditionalJump[IF_ICMPNE] #CG -> #SO
===#Block SP(size=2, flags=100)===
   0. lvar105 = {1683996103 ^ lvar105};
   1. goto JZ
      -> UnconditionalJump[GOTO] #SP -> #JZ
      <- Immediate #SO -> #SP
      <- Switch[52261522] #SO -> #SP
===#Block JZ(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1049842807)
      goto TG
   1. goto LT
      -> ConditionalJump[IF_ICMPEQ] #JZ -> #TG
      -> UnconditionalJump[GOTO] #JZ -> #LT
      <- Switch[305741524] #SO -> #JZ
      <- UnconditionalJump[GOTO] #SP -> #JZ
===#Block LT(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 215509415:
      	 goto	#LU
      case 220043151:
      	 goto	#JC
      case 255155166:
      	 goto	#LT
      case 1842362603:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #LT -> #LU
      -> DefaultSwitch #LT -> #JC
      -> Switch[255155166] #LT -> #LT
      -> Switch[220043151] #LT -> #JC
      -> Switch[215509415] #LT -> #LU
      <- Switch[255155166] #LT -> #LT
      <- UnconditionalJump[GOTO] #JZ -> #LT
===#Block LU(size=2, flags=100)===
   0. lvar105 = {1669149147 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LU -> #JC
      <- Immediate #LT -> #LU
      <- Switch[215509415] #LT -> #LU
===#Block TG(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 215509415:
      	 goto	#TH
      case 547933472:
      	 goto	#CH
      case 1087779687:
      	 goto	#TG
      case 1814607939:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[547933472] #TG -> #CH
      -> DefaultSwitch #TG -> #JC
      -> Switch[1814607939] #TG -> #JC
      -> Switch[215509415] #TG -> #TH
      -> Switch[1087779687] #TG -> #TG
      -> Immediate #TG -> #TH
      <- ConditionalJump[IF_ICMPEQ] #JZ -> #TG
      <- Switch[1087779687] #TG -> #TG
===#Block TH(size=2, flags=100)===
   0. lvar105 = {1349338467 ^ lvar105};
   1. goto CH
      -> UnconditionalJump[GOTO] #TH -> #CH
      <- Switch[215509415] #TG -> #TH
      <- Immediate #TG -> #TH
===#Block CH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.RED_CONCRETE_POWDER;
   2. goto KV
      -> UnconditionalJump[GOTO] #CH -> #KV
      <- Switch[547933472] #TG -> #CH
      <- UnconditionalJump[GOTO] #TH -> #CH
===#Block KV(size=2, flags=10100)===
   0. lvar105 = {460561605 ^ lvar105};
   1. goto EM
      -> UnconditionalJump[GOTO] #KV -> #EM
      <- UnconditionalJump[GOTO] #CH -> #KV
===#Block EM(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 255006675)
      goto EL
   1. throw nullconst;
      -> TryCatch range: [EM...EL] -> YK ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #EM -> #EL
      <- UnconditionalJump[GOTO] #KV -> #EM
===#Block EL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [EM...EL] -> YK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #EM -> #EL
===#Block YK(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1717934405:
      	 goto	#YL
      case 406586450:
      	 goto	#YM
      default:
      	 goto	#YN
   }
      -> DefaultSwitch #YK -> #YN
      -> Switch[406586450] #YK -> #YM
      -> Switch[-1717934405] #YK -> #YL
      <- TryCatch range: [EM...EL] -> YK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [EM...EL] -> YK ([Ljava/lang/RuntimeException;])
===#Block YL(size=2, flags=10100)===
   0. lvar105 = {1376021153 ^ lvar105};
   1. goto EN
      -> UnconditionalJump[GOTO] #YL -> #EN
      <- Switch[-1717934405] #YK -> #YL
===#Block YM(size=2, flags=10100)===
   0. lvar105 = {1647355644 ^ lvar105};
   1. goto EN
      -> UnconditionalJump[GOTO] #YM -> #EN
      <- Switch[406586450] #YK -> #YM
===#Block EN(size=2, flags=0)===
   0. _consume(catch());
   1. goto LO
      -> UnconditionalJump[GOTO] #EN -> #LO
      <- UnconditionalJump[GOTO] #YM -> #EN
      <- UnconditionalJump[GOTO] #YL -> #EN
===#Block LO(size=2, flags=10100)===
   0. lvar105 = {1751222794 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #LO -> #CN
      <- UnconditionalJump[GOTO] #EN -> #LO
===#Block YN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #YK -> #YN
===#Block VS(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 171365371:
      	 goto	#VT
      case 198670967:
      	 goto	#VS
      case 1094501979:
      	 goto	#BO
      case 2031664399:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[171365371] #VS -> #VT
      -> Switch[1094501979] #VS -> #BO
      -> DefaultSwitch #VS -> #JC
      -> Switch[2031664399] #VS -> #JC
      -> Switch[198670967] #VS -> #VS
      -> Immediate #VS -> #VT
      <- Switch[177480629] #BK -> #VS
      <- Switch[198670967] #VS -> #VS
===#Block VT(size=2, flags=100)===
   0. lvar105 = {256156367 ^ lvar105};
   1. goto BO
      -> UnconditionalJump[GOTO] #VT -> #BO
      <- Switch[171365371] #VS -> #VT
      <- Immediate #VS -> #VT
===#Block BO(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar59 = lvar102;
   2. lvar93 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.bfidogxfiyuansa(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar60 = lvar59.equals(lvar93);
   4. if (lvar60 != {445452881 ^ lvar105})
      goto SB
   5. lvar105 = {661516988 ^ lvar105};
      -> Immediate #BO -> #BP
      -> ConditionalJump[IF_ICMPNE] #BO -> #SB
      <- Switch[1094501979] #VS -> #BO
      <- UnconditionalJump[GOTO] #VT -> #BO
===#Block SB(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 57756145:
      	 goto	#SC
      case 407876415:
      	 goto	#SB
      case 1385620124:
      	 goto	#JC
      case 1439403417:
      	 goto	#JP
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #SB -> #JC
      -> Switch[57756145] #SB -> #SC
      -> Switch[1385620124] #SB -> #JC
      -> Switch[1439403417] #SB -> #JP
      -> Immediate #SB -> #SC
      -> Switch[407876415] #SB -> #SB
      <- ConditionalJump[IF_ICMPNE] #BO -> #SB
      <- Switch[407876415] #SB -> #SB
===#Block SC(size=2, flags=100)===
   0. lvar105 = {1256261648 ^ lvar105};
   1. goto JP
      -> UnconditionalJump[GOTO] #SC -> #JP
      <- Switch[57756145] #SB -> #SC
      <- Immediate #SB -> #SC
===#Block JP(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -2090806774)
      goto TR
   1. goto NP
      -> UnconditionalJump[GOTO] #JP -> #NP
      -> ConditionalJump[IF_ICMPEQ] #JP -> #TR
      <- Switch[1439403417] #SB -> #JP
      <- UnconditionalJump[GOTO] #SC -> #JP
===#Block TR(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 198255281:
      	 goto	#TS
      case 864023977:
      	 goto	#BQ
      case 928822199:
      	 goto	#JC
      case 1397470473:
      	 goto	#TR
      default:
      	 goto	#JC
   }
      -> Immediate #TR -> #TS
      -> Switch[198255281] #TR -> #TS
      -> Switch[1397470473] #TR -> #TR
      -> Switch[928822199] #TR -> #JC
      -> DefaultSwitch #TR -> #JC
      -> Switch[864023977] #TR -> #BQ
      <- Switch[1397470473] #TR -> #TR
      <- ConditionalJump[IF_ICMPEQ] #JP -> #TR
===#Block TS(size=2, flags=100)===
   0. lvar105 = {849855431 ^ lvar105};
   1. goto BQ
      -> UnconditionalJump[GOTO] #TS -> #BQ
      <- Immediate #TR -> #TS
      <- Switch[198255281] #TR -> #TS
===#Block BQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.PURPLE_CONCRETE_POWDER;
   2. goto QG
      -> UnconditionalJump[GOTO] #BQ -> #QG
      <- UnconditionalJump[GOTO] #TS -> #BQ
      <- Switch[864023977] #TR -> #BQ
===#Block QG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 67078955);
   1. goto FB
      -> UnconditionalJump[GOTO] #QG -> #FB
      <- UnconditionalJump[GOTO] #BQ -> #QG
===#Block FB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 206897435)
      goto FA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FB -> #FA
      -> TryCatch range: [FB...FA] -> ZE ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #QG -> #FB
===#Block FA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FB...FA] -> ZE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FB -> #FA
===#Block ZE(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 161650027:
      	 goto	#ZF
      case 1623421403:
      	 goto	#ZG
      default:
      	 goto	#ZH
   }
      -> DefaultSwitch #ZE -> #ZH
      -> Switch[161650027] #ZE -> #ZF
      -> Switch[1623421403] #ZE -> #ZG
      <- TryCatch range: [FB...FA] -> ZE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FB...FA] -> ZE ([Ljava/lang/RuntimeException;])
===#Block ZG(size=2, flags=10100)===
   0. lvar105 = {712988819 ^ lvar105};
   1. goto FC
      -> UnconditionalJump[GOTO] #ZG -> #FC
      <- Switch[1623421403] #ZE -> #ZG
===#Block ZF(size=2, flags=10100)===
   0. lvar105 = {659776517 ^ lvar105};
   1. goto FC
      -> UnconditionalJump[GOTO] #ZF -> #FC
      <- Switch[161650027] #ZE -> #ZF
===#Block FC(size=2, flags=0)===
   0. _consume(catch());
   1. goto MF
      -> UnconditionalJump[GOTO] #FC -> #MF
      <- UnconditionalJump[GOTO] #ZG -> #FC
      <- UnconditionalJump[GOTO] #ZF -> #FC
===#Block MF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 254909140);
   1. goto CN
      -> UnconditionalJump[GOTO] #MF -> #CN
      <- UnconditionalJump[GOTO] #FC -> #MF
===#Block ZH(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ZE -> #ZH
===#Block NP(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 198255281:
      	 goto	#NQ
      case 1383872095:
      	 goto	#JC
      case 1448091559:
      	 goto	#JC
      case 1495136587:
      	 goto	#NP
      default:
      	 goto	#JC
   }
      -> Immediate #NP -> #NQ
      -> Switch[1495136587] #NP -> #NP
      -> Switch[198255281] #NP -> #NQ
      -> DefaultSwitch #NP -> #JC
      -> Switch[1448091559] #NP -> #JC
      <- UnconditionalJump[GOTO] #JP -> #NP
      <- Switch[1495136587] #NP -> #NP
===#Block NQ(size=2, flags=100)===
   0. lvar105 = {188530411 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NQ -> #JC
      <- Immediate #NP -> #NQ
      <- Switch[198255281] #NP -> #NQ
===#Block BP(size=1, flags=0)===
   0. goto NH
      -> UnconditionalJump[GOTO] #BP -> #NH
      <- Immediate #BO -> #BP
===#Block NH(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 132353579:
      	 goto	#NI
      case 388809052:
      	 goto	#JC
      case 642721978:
      	 goto	#NH
      case 1333886237:
      	 goto	#HS
      default:
      	 goto	#JC
   }
      -> Switch[642721978] #NH -> #NH
      -> Switch[132353579] #NH -> #NI
      -> Switch[388809052] #NH -> #JC
      -> DefaultSwitch #NH -> #JC
      -> Immediate #NH -> #NI
      -> Switch[1333886237] #NH -> #HS
      <- Switch[642721978] #NH -> #NH
      <- UnconditionalJump[GOTO] #BP -> #NH
===#Block NI(size=2, flags=100)===
   0. lvar105 = {1799754228 ^ lvar105};
   1. goto HS
      -> UnconditionalJump[GOTO] #NI -> #HS
      <- Switch[132353579] #NH -> #NI
      <- Immediate #NH -> #NI
===#Block HS(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 132326790)
      goto HR
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HS -> #HR
      -> TryCatch range: [HS...HR] -> ACS ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #NI -> #HS
      <- Switch[1333886237] #NH -> #HS
===#Block HR(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HS...HR] -> ACS ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HS -> #HR
===#Block ACS(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1254651702:
      	 goto	#ACT
      case 2001653283:
      	 goto	#ACU
      default:
      	 goto	#ACV
   }
      -> DefaultSwitch #ACS -> #ACV
      -> Switch[-1254651702] #ACS -> #ACT
      -> Switch[2001653283] #ACS -> #ACU
      <- TryCatch range: [HS...HR] -> ACS ([Ljava/io/IOException;])
      <- TryCatch range: [HS...HR] -> ACS ([Ljava/io/IOException;])
===#Block ACU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 303586230);
   1. goto HT
      -> UnconditionalJump[GOTO] #ACU -> #HT
      <- Switch[2001653283] #ACS -> #ACU
===#Block ACT(size=2, flags=10100)===
   0. lvar105 = {710374251 ^ lvar105};
   1. goto HT
      -> UnconditionalJump[GOTO] #ACT -> #HT
      <- Switch[-1254651702] #ACS -> #ACT
===#Block HT(size=2, flags=0)===
   0. _consume(catch());
   1. goto RH
      -> UnconditionalJump[GOTO] #HT -> #RH
      <- UnconditionalJump[GOTO] #ACU -> #HT
      <- UnconditionalJump[GOTO] #ACT -> #HT
===#Block RH(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 34836557:
      	 goto	#RI
      case 694458236:
      	 goto	#RH
      case 923672462:
      	 goto	#JC
      case 1419817334:
      	 goto	#CL
      default:
      	 goto	#JC
   }
      -> Switch[694458236] #RH -> #RH
      -> Switch[1419817334] #RH -> #CL
      -> Immediate #RH -> #RI
      -> Switch[34836557] #RH -> #RI
      -> DefaultSwitch #RH -> #JC
      -> Switch[923672462] #RH -> #JC
      <- Switch[694458236] #RH -> #RH
      <- UnconditionalJump[GOTO] #HT -> #RH
===#Block RI(size=2, flags=100)===
   0. lvar105 = {59483807 ^ lvar105};
   1. goto CL
      -> UnconditionalJump[GOTO] #RI -> #CL
      <- Immediate #RH -> #RI
      <- Switch[34836557] #RH -> #RI
===#Block CL(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GRAY_CONCRETE_POWDER;
   2. lvar105 = {908888209 ^ lvar105};
      -> Immediate #CL -> #CN
      <- UnconditionalJump[GOTO] #RD -> #CL
      <- Switch[457770059] #LI -> #CL
      <- UnconditionalJump[GOTO] #OQ -> #CL
      <- UnconditionalJump[GOTO] #KF -> #CL
      <- Switch[1419817334] #RH -> #CL
      <- Switch[362858104] #OE -> #CL
      <- UnconditionalJump[GOTO] #QS -> #CL
      <- Switch[2126003434] #RC -> #CL
      <- UnconditionalJump[GOTO] #OX -> #CL
      <- UnconditionalJump[GOTO] #RI -> #CL
      <- UnconditionalJump[GOTO] #VX -> #CL
      <- UnconditionalJump[GOTO] #LJ -> #CL
      <- UnconditionalJump[GOTO] #OF -> #CL
      <- UnconditionalJump[GOTO] #LM -> #CL
===#Block ACV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ACS -> #ACV
===#Block AH(size=6, flags=0)===
   0. lvar32 = lvar1;
   1. lvar101 = lvar32;
   2. lvar33 = lvar101;
   3. lvar34 = lvar33.hashCode();
   4. svar107 = {lvar34 ^ lvar105};
   5. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(svar107)) {
      case 74142274:
      	 goto	#VA
      case 74142277:
      	 goto	#VB
      case 74142278:
      	 goto	#VD
      case 74142279:
      	 goto	#VF
      case 74142284:
      	 goto	#VG
      case 74142287:
      	 goto	#VH
      case 74142296:
      	 goto	#VI
      case 74142297:
      	 goto	#VJ
      default:
      	 goto	#VK
   }
      -> Switch[74142274] #AH -> #VA
      -> DefaultSwitch #AH -> #VK
      -> Switch[74142296] #AH -> #VI
      -> Switch[74142287] #AH -> #VH
      -> Switch[74142277] #AH -> #VB
      -> Switch[74142284] #AH -> #VG
      -> Switch[74142297] #AH -> #VJ
      -> Switch[74142278] #AH -> #VD
      -> Switch[74142279] #AH -> #VF
      <- Immediate #AG -> #AH
===#Block VF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 958272677);
   1. goto BA
      -> UnconditionalJump[GOTO] #VF -> #BA
      <- Switch[74142279] #AH -> #VF
===#Block BA(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar48 = lvar101;
   2. lvar89 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.wkrkueqcoimgyqu(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar49 = lvar48.equals(lvar89);
   4. if (lvar49 != {1033407840 ^ lvar105})
      goto TW
   5. lvar105 = {1564830739 ^ lvar105};
      -> Immediate #BA -> #BB
      -> ConditionalJump[IF_ICMPNE] #BA -> #TW
      <- UnconditionalJump[GOTO] #VF -> #BA
===#Block TW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 10259163);
   1. goto JN
      -> UnconditionalJump[GOTO] #TW -> #JN
      <- ConditionalJump[IF_ICMPNE] #BA -> #TW
===#Block JN(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -400540199)
      goto TV
   1. goto OG
      -> ConditionalJump[IF_ICMPEQ] #JN -> #TV
      -> UnconditionalJump[GOTO] #JN -> #OG
      <- UnconditionalJump[GOTO] #TW -> #JN
===#Block OG(size=2, flags=10100)===
   0. lvar105 = {1716822801 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OG -> #JC
      <- UnconditionalJump[GOTO] #JN -> #OG
===#Block TV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 59225592);
   1. goto BC
      -> UnconditionalJump[GOTO] #TV -> #BC
      <- ConditionalJump[IF_ICMPEQ] #JN -> #TV
===#Block BC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.RED_GLAZED_TERRACOTTA;
   2. goto LF
      -> UnconditionalJump[GOTO] #BC -> #LF
      <- UnconditionalJump[GOTO] #TV -> #BC
===#Block LF(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 153032674:
      	 goto	#LG
      case 1688016763:
      	 goto	#LF
      case 1842746558:
      	 goto	#FK
      case 1857685002:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1842746558] #LF -> #FK
      -> Immediate #LF -> #LG
      -> Switch[153032674] #LF -> #LG
      -> DefaultSwitch #LF -> #JC
      -> Switch[1688016763] #LF -> #LF
      -> Switch[1857685002] #LF -> #JC
      <- UnconditionalJump[GOTO] #BC -> #LF
      <- Switch[1688016763] #LF -> #LF
===#Block LG(size=2, flags=100)===
   0. lvar105 = {359471033 ^ lvar105};
   1. goto FK
      -> UnconditionalJump[GOTO] #LG -> #FK
      <- Immediate #LF -> #LG
      <- Switch[153032674] #LF -> #LG
===#Block FK(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 85871226)
      goto FJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FK -> #FJ
      -> TryCatch range: [FK...FJ] -> ZQ ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #LG -> #FK
      <- Switch[1842746558] #LF -> #FK
===#Block FJ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FK...FJ] -> ZQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FK -> #FJ
===#Block ZQ(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -742591032:
      	 goto	#ZS
      case 1601515473:
      	 goto	#ZR
      default:
      	 goto	#ZT
   }
      -> DefaultSwitch #ZQ -> #ZT
      -> Switch[1601515473] #ZQ -> #ZR
      -> Switch[-742591032] #ZQ -> #ZS
      <- TryCatch range: [FK...FJ] -> ZQ ([Ljava/io/IOException;])
      <- TryCatch range: [FK...FJ] -> ZQ ([Ljava/io/IOException;])
===#Block ZS(size=2, flags=10100)===
   0. lvar105 = {2087924867 ^ lvar105};
   1. goto FL
      -> UnconditionalJump[GOTO] #ZS -> #FL
      <- Switch[-742591032] #ZQ -> #ZS
===#Block ZR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1307185088);
   1. goto FL
      -> UnconditionalJump[GOTO] #ZR -> #FL
      <- Switch[1601515473] #ZQ -> #ZR
===#Block FL(size=2, flags=0)===
   0. _consume(catch());
   1. goto PL
      -> UnconditionalJump[GOTO] #FL -> #PL
      <- UnconditionalJump[GOTO] #ZR -> #FL
      <- UnconditionalJump[GOTO] #ZS -> #FL
===#Block PL(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 94990292:
      	 goto	#PM
      case 121776647:
      	 goto	#PL
      case 1805284077:
      	 goto	#JC
      case 2100908013:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[2100908013] #PL -> #CN
      -> Switch[121776647] #PL -> #PL
      -> Immediate #PL -> #PM
      -> Switch[94990292] #PL -> #PM
      -> Switch[1805284077] #PL -> #JC
      -> DefaultSwitch #PL -> #JC
      <- Switch[121776647] #PL -> #PL
      <- UnconditionalJump[GOTO] #FL -> #PL
===#Block PM(size=2, flags=100)===
   0. lvar105 = {793904710 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PM -> #CN
      <- Immediate #PL -> #PM
      <- Switch[94990292] #PL -> #PM
===#Block ZT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ZQ -> #ZT
===#Block BB(size=1, flags=0)===
   0. goto MX
      -> UnconditionalJump[GOTO] #BB -> #MX
      <- Immediate #BA -> #BB
===#Block MX(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 195920915:
      	 goto	#MY
      case 1065512853:
      	 goto	#JC
      case 1188116488:
      	 goto	#MX
      case 2023332924:
      	 goto	#GR
      default:
      	 goto	#JC
   }
      -> Switch[2023332924] #MX -> #GR
      -> DefaultSwitch #MX -> #JC
      -> Switch[1188116488] #MX -> #MX
      -> Switch[1065512853] #MX -> #JC
      -> Immediate #MX -> #MY
      -> Switch[195920915] #MX -> #MY
      <- Switch[1188116488] #MX -> #MX
      <- UnconditionalJump[GOTO] #BB -> #MX
===#Block MY(size=2, flags=100)===
   0. lvar105 = {130592011 ^ lvar105};
   1. goto GR
      -> UnconditionalJump[GOTO] #MY -> #GR
      <- Immediate #MX -> #MY
      <- Switch[195920915] #MX -> #MY
===#Block GR(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 129573693)
      goto GQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GR -> #GQ
      -> TryCatch range: [GR...GQ] -> ABI ([Ljava/lang/RuntimeException;])
      <- Switch[2023332924] #MX -> #GR
      <- UnconditionalJump[GOTO] #MY -> #GR
===#Block GQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GR...GQ] -> ABI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GR -> #GQ
===#Block ABI(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1650539390:
      	 goto	#ABK
      case 950690755:
      	 goto	#ABJ
      default:
      	 goto	#ABL
   }
      -> DefaultSwitch #ABI -> #ABL
      -> Switch[950690755] #ABI -> #ABJ
      -> Switch[-1650539390] #ABI -> #ABK
      <- TryCatch range: [GR...GQ] -> ABI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GR...GQ] -> ABI ([Ljava/lang/RuntimeException;])
===#Block ABK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1873411805);
   1. goto GS
      -> UnconditionalJump[GOTO] #ABK -> #GS
      <- Switch[-1650539390] #ABI -> #ABK
===#Block ABJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1527736885);
   1. goto GS
      -> UnconditionalJump[GOTO] #ABJ -> #GS
      <- Switch[950690755] #ABI -> #ABJ
===#Block GS(size=2, flags=0)===
   0. _consume(catch());
   1. goto QH
      -> UnconditionalJump[GOTO] #GS -> #QH
      <- UnconditionalJump[GOTO] #ABK -> #GS
      <- UnconditionalJump[GOTO] #ABJ -> #GS
===#Block QH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1340739744);
   1. goto BJ
      -> UnconditionalJump[GOTO] #QH -> #BJ
      <- UnconditionalJump[GOTO] #GS -> #QH
===#Block ABL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ABI -> #ABL
===#Block VD(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 71185582:
      	 goto	#VE
      case 378544618:
      	 goto	#JC
      case 1104346468:
      	 goto	#VD
      case 1665126495:
      	 goto	#AU
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #VD -> #JC
      -> Switch[1665126495] #VD -> #AU
      -> Switch[378544618] #VD -> #JC
      -> Immediate #VD -> #VE
      -> Switch[71185582] #VD -> #VE
      -> Switch[1104346468] #VD -> #VD
      <- Switch[74142278] #AH -> #VD
      <- Switch[1104346468] #VD -> #VD
===#Block VE(size=2, flags=100)===
   0. lvar105 = {585702820 ^ lvar105};
   1. goto AU
      -> UnconditionalJump[GOTO] #VE -> #AU
      <- Immediate #VD -> #VE
      <- Switch[71185582] #VD -> #VE
===#Block AU(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar43 = lvar101;
   2. lvar87 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.kaqomqnavbsavha(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar44 = lvar43.equals(lvar87);
   4. if (lvar44 != {644842593 ^ lvar105})
      goto SX
   5. lvar105 = {1430855748 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AU -> #SX
      -> Immediate #AU -> #AV
      <- Switch[1665126495] #VD -> #AU
      <- UnconditionalJump[GOTO] #VE -> #AU
===#Block AV(size=1, flags=0)===
   0. goto QP
      -> UnconditionalJump[GOTO] #AV -> #QP
      <- Immediate #AU -> #AV
===#Block QP(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 253406497:
      	 goto	#QQ
      case 391362465:
      	 goto	#GO
      case 884730906:
      	 goto	#QP
      case 1822833912:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[391362465] #QP -> #GO
      -> Switch[1822833912] #QP -> #JC
      -> DefaultSwitch #QP -> #JC
      -> Switch[884730906] #QP -> #QP
      -> Immediate #QP -> #QQ
      -> Switch[253406497] #QP -> #QQ
      <- Switch[884730906] #QP -> #QP
      <- UnconditionalJump[GOTO] #AV -> #QP
===#Block QQ(size=2, flags=100)===
   0. lvar105 = {1241673472 ^ lvar105};
   1. goto GO
      -> UnconditionalJump[GOTO] #QQ -> #GO
      <- Immediate #QP -> #QQ
      <- Switch[253406497] #QP -> #QQ
===#Block GO(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 246902963)
      goto GN
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GO -> #GN
      -> TryCatch range: [GO...GN] -> ABE ([Ljava/lang/IllegalAccessException;])
      <- Switch[391362465] #QP -> #GO
      <- UnconditionalJump[GOTO] #QQ -> #GO
===#Block GN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GO...GN] -> ABE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GO -> #GN
===#Block ABE(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -920233687:
      	 goto	#ABF
      case -628407696:
      	 goto	#ABG
      default:
      	 goto	#ABH
   }
      -> DefaultSwitch #ABE -> #ABH
      -> Switch[-628407696] #ABE -> #ABG
      -> Switch[-920233687] #ABE -> #ABF
      <- TryCatch range: [GO...GN] -> ABE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GO...GN] -> ABE ([Ljava/lang/IllegalAccessException;])
===#Block ABF(size=2, flags=10100)===
   0. lvar105 = {886204114 ^ lvar105};
   1. goto GP
      -> UnconditionalJump[GOTO] #ABF -> #GP
      <- Switch[-920233687] #ABE -> #ABF
===#Block ABG(size=2, flags=10100)===
   0. lvar105 = {380093881 ^ lvar105};
   1. goto GP
      -> UnconditionalJump[GOTO] #ABG -> #GP
      <- Switch[-628407696] #ABE -> #ABG
===#Block GP(size=2, flags=0)===
   0. _consume(catch());
   1. goto QD
      -> UnconditionalJump[GOTO] #GP -> #QD
      <- UnconditionalJump[GOTO] #ABF -> #GP
      <- UnconditionalJump[GOTO] #ABG -> #GP
===#Block QD(size=2, flags=10100)===
   0. lvar105 = {2114362138 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #QD -> #BJ
      <- UnconditionalJump[GOTO] #GP -> #QD
===#Block ABH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ABE -> #ABH
===#Block SX(size=2, flags=10100)===
   0. lvar105 = {57849569 ^ lvar105};
   1. goto JE
      -> UnconditionalJump[GOTO] #SX -> #JE
      <- ConditionalJump[IF_ICMPNE] #AU -> #SX
===#Block JE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 686396417)
      goto UH
   1. goto PU
      -> ConditionalJump[IF_ICMPEQ] #JE -> #UH
      -> UnconditionalJump[GOTO] #JE -> #PU
      <- UnconditionalJump[GOTO] #SX -> #JE
===#Block PU(size=2, flags=10100)===
   0. lvar105 = {2119013930 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PU -> #JC
      <- UnconditionalJump[GOTO] #JE -> #PU
===#Block UH(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 132691141:
      	 goto	#UI
      case 383042192:
      	 goto	#AW
      case 673482594:
      	 goto	#JC
      case 1404520623:
      	 goto	#UH
      default:
      	 goto	#JC
   }
      -> Switch[1404520623] #UH -> #UH
      -> Switch[673482594] #UH -> #JC
      -> Switch[132691141] #UH -> #UI
      -> DefaultSwitch #UH -> #JC
      -> Switch[383042192] #UH -> #AW
      -> Immediate #UH -> #UI
      <- ConditionalJump[IF_ICMPEQ] #JE -> #UH
      <- Switch[1404520623] #UH -> #UH
===#Block UI(size=2, flags=100)===
   0. lvar105 = {2024253728 ^ lvar105};
   1. goto AW
      -> UnconditionalJump[GOTO] #UI -> #AW
      <- Switch[132691141] #UH -> #UI
      <- Immediate #UH -> #UI
===#Block AW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GREEN_GLAZED_TERRACOTTA;
   2. goto QZ
      -> UnconditionalJump[GOTO] #AW -> #QZ
      <- UnconditionalJump[GOTO] #UI -> #AW
      <- Switch[383042192] #UH -> #AW
===#Block QZ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 93966396:
      	 goto	#RA
      case 426080463:
      	 goto	#QZ
      case 642835201:
      	 goto	#JC
      case 1667698508:
      	 goto	#IB
      default:
      	 goto	#JC
   }
      -> Immediate #QZ -> #RA
      -> DefaultSwitch #QZ -> #JC
      -> Switch[1667698508] #QZ -> #IB
      -> Switch[93966396] #QZ -> #RA
      -> Switch[642835201] #QZ -> #JC
      -> Switch[426080463] #QZ -> #QZ
      <- UnconditionalJump[GOTO] #AW -> #QZ
      <- Switch[426080463] #QZ -> #QZ
===#Block RA(size=2, flags=100)===
   0. lvar105 = {1599552279 ^ lvar105};
   1. goto IB
      -> UnconditionalJump[GOTO] #RA -> #IB
      <- Immediate #QZ -> #RA
      <- Switch[93966396] #QZ -> #RA
===#Block IB(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 46056518)
      goto IA
   1. throw nullconst;
      -> TryCatch range: [IB...IA] -> ADE ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #IB -> #IA
      <- Switch[1667698508] #QZ -> #IB
      <- UnconditionalJump[GOTO] #RA -> #IB
===#Block IA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IB...IA] -> ADE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IB -> #IA
===#Block ADE(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1504333087:
      	 goto	#ADG
      case 393020856:
      	 goto	#ADF
      default:
      	 goto	#ADH
   }
      -> DefaultSwitch #ADE -> #ADH
      -> Switch[393020856] #ADE -> #ADF
      -> Switch[-1504333087] #ADE -> #ADG
      <- TryCatch range: [IB...IA] -> ADE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IB...IA] -> ADE ([Ljava/lang/RuntimeException;])
===#Block ADG(size=2, flags=10100)===
   0. lvar105 = {397320960 ^ lvar105};
   1. goto IC
      -> UnconditionalJump[GOTO] #ADG -> #IC
      <- Switch[-1504333087] #ADE -> #ADG
===#Block ADF(size=2, flags=10100)===
   0. lvar105 = {562673131 ^ lvar105};
   1. goto IC
      -> UnconditionalJump[GOTO] #ADF -> #IC
      <- Switch[393020856] #ADE -> #ADF
===#Block IC(size=2, flags=0)===
   0. _consume(catch());
   1. goto QK
      -> UnconditionalJump[GOTO] #IC -> #QK
      <- UnconditionalJump[GOTO] #ADG -> #IC
      <- UnconditionalJump[GOTO] #ADF -> #IC
===#Block QK(size=2, flags=10100)===
   0. lvar105 = {1781709088 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QK -> #CN
      <- UnconditionalJump[GOTO] #IC -> #QK
===#Block ADH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ADE -> #ADH
===#Block VJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 399390932);
   1. goto AO
      -> UnconditionalJump[GOTO] #VJ -> #AO
      <- Switch[74142297] #AH -> #VJ
===#Block AO(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar39 = lvar101;
   2. lvar85 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.pwsgkjhqdnfmvse(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar40 = lvar39.equals(lvar85);
   4. if (lvar40 != {323526929 ^ lvar105})
      goto UA
   5. lvar105 = {1970053910 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AO -> #UA
      -> Immediate #AO -> #AP
      <- UnconditionalJump[GOTO] #VJ -> #AO
===#Block AP(size=1, flags=0)===
   0. goto KG
      -> UnconditionalJump[GOTO] #AP -> #KG
      <- Immediate #AO -> #AP
===#Block KG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1462534339);
   1. goto CT
      -> UnconditionalJump[GOTO] #KG -> #CT
      <- UnconditionalJump[GOTO] #AP -> #KG
===#Block CT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 251677779)
      goto CS
   1. throw nullconst;
      -> TryCatch range: [CT...CS] -> WC ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CT -> #CS
      <- UnconditionalJump[GOTO] #KG -> #CT
===#Block CS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CT...CS] -> WC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CT -> #CS
===#Block WC(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -2008803807:
      	 goto	#WD
      case 1051484234:
      	 goto	#WE
      default:
      	 goto	#WF
   }
      -> Switch[-2008803807] #WC -> #WD
      -> Switch[1051484234] #WC -> #WE
      -> DefaultSwitch #WC -> #WF
      <- TryCatch range: [CT...CS] -> WC ([Ljava/io/IOException;])
      <- TryCatch range: [CT...CS] -> WC ([Ljava/io/IOException;])
===#Block WF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WC -> #WF
===#Block WE(size=2, flags=10100)===
   0. lvar105 = {712130196 ^ lvar105};
   1. goto CU
      -> UnconditionalJump[GOTO] #WE -> #CU
      <- Switch[1051484234] #WC -> #WE
===#Block WD(size=2, flags=10100)===
   0. lvar105 = {1554985049 ^ lvar105};
   1. goto CU
      -> UnconditionalJump[GOTO] #WD -> #CU
      <- Switch[-2008803807] #WC -> #WD
===#Block CU(size=2, flags=0)===
   0. _consume(catch());
   1. goto QE
      -> UnconditionalJump[GOTO] #CU -> #QE
      <- UnconditionalJump[GOTO] #WE -> #CU
      <- UnconditionalJump[GOTO] #WD -> #CU
===#Block QE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 509052016);
   1. goto BJ
      -> UnconditionalJump[GOTO] #QE -> #BJ
      <- UnconditionalJump[GOTO] #CU -> #QE
===#Block UA(size=2, flags=10100)===
   0. lvar105 = {13512932 ^ lvar105};
   1. goto JT
      -> UnconditionalJump[GOTO] #UA -> #JT
      <- ConditionalJump[IF_ICMPNE] #AO -> #UA
===#Block JT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1674276952)
      goto SD
   1. goto OU
      -> UnconditionalJump[GOTO] #JT -> #OU
      -> ConditionalJump[IF_ICMPEQ] #JT -> #SD
      <- UnconditionalJump[GOTO] #UA -> #JT
===#Block SD(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 97823052:
      	 goto	#SE
      case 217122230:
      	 goto	#JC
      case 1625615074:
      	 goto	#AQ
      case 1827861910:
      	 goto	#SD
      default:
      	 goto	#JC
   }
      -> Switch[1625615074] #SD -> #AQ
      -> Switch[97823052] #SD -> #SE
      -> DefaultSwitch #SD -> #JC
      -> Switch[217122230] #SD -> #JC
      -> Immediate #SD -> #SE
      -> Switch[1827861910] #SD -> #SD
      <- ConditionalJump[IF_ICMPEQ] #JT -> #SD
      <- Switch[1827861910] #SD -> #SD
===#Block SE(size=2, flags=100)===
   0. lvar105 = {167424385 ^ lvar105};
   1. goto AQ
      -> UnconditionalJump[GOTO] #SE -> #AQ
      <- Switch[97823052] #SD -> #SE
      <- Immediate #SD -> #SE
===#Block AQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.WHITE_GLAZED_TERRACOTTA;
   2. goto MA
      -> UnconditionalJump[GOTO] #AQ -> #MA
      <- UnconditionalJump[GOTO] #SE -> #AQ
      <- Switch[1625615074] #SD -> #AQ
===#Block MA(size=2, flags=10100)===
   0. lvar105 = {764900879 ^ lvar105};
   1. goto IW
      -> UnconditionalJump[GOTO] #MA -> #IW
      <- UnconditionalJump[GOTO] #AQ -> #MA
===#Block IW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 207077477)
      goto IV
   1. throw nullconst;
      -> TryCatch range: [IW...IV] -> AEG ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #IW -> #IV
      <- UnconditionalJump[GOTO] #MA -> #IW
===#Block IV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IW...IV] -> AEG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IW -> #IV
===#Block AEG(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1967449462:
      	 goto	#AEI
      case -1084574759:
      	 goto	#AEH
      default:
      	 goto	#AEJ
   }
      -> Switch[-1967449462] #AEG -> #AEI
      -> DefaultSwitch #AEG -> #AEJ
      -> Switch[-1084574759] #AEG -> #AEH
      <- TryCatch range: [IW...IV] -> AEG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IW...IV] -> AEG ([Ljava/lang/RuntimeException;])
===#Block AEH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1200870853);
   1. goto IX
      -> UnconditionalJump[GOTO] #AEH -> #IX
      <- Switch[-1084574759] #AEG -> #AEH
===#Block AEJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AEG -> #AEJ
===#Block AEI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 556785903);
   1. goto IX
      -> UnconditionalJump[GOTO] #AEI -> #IX
      <- Switch[-1967449462] #AEG -> #AEI
===#Block IX(size=2, flags=0)===
   0. _consume(catch());
   1. goto QY
      -> UnconditionalJump[GOTO] #IX -> #QY
      <- UnconditionalJump[GOTO] #AEI -> #IX
      <- UnconditionalJump[GOTO] #AEH -> #IX
===#Block QY(size=2, flags=10100)===
   0. lvar105 = {959328194 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QY -> #CN
      <- UnconditionalJump[GOTO] #IX -> #QY
===#Block OU(size=2, flags=10100)===
   0. lvar105 = {1221997919 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OU -> #JC
      <- UnconditionalJump[GOTO] #JT -> #OU
===#Block VG(size=2, flags=10100)===
   0. lvar105 = {350618102 ^ lvar105};
   1. goto BG
      -> UnconditionalJump[GOTO] #VG -> #BG
      <- Switch[74142284] #AH -> #VG
===#Block BG(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar52 = lvar101;
   2. lvar91 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.hbfwnxlzucdudiq(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar53 = lvar52.equals(lvar91);
   4. if (lvar53 != {274765875 ^ lvar105})
      goto TN
   5. lvar105 = {1284710356 ^ lvar105};
      -> Immediate #BG -> #BI
      -> ConditionalJump[IF_ICMPNE] #BG -> #TN
      <- UnconditionalJump[GOTO] #VG -> #BG
===#Block TN(size=2, flags=10100)===
   0. lvar105 = {1284960907 ^ lvar105};
   1. goto JK
      -> UnconditionalJump[GOTO] #TN -> #JK
      <- ConditionalJump[IF_ICMPNE] #BG -> #TN
===#Block JK(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -407677502)
      goto TF
   1. goto LR
      -> UnconditionalJump[GOTO] #JK -> #LR
      -> ConditionalJump[IF_ICMPEQ] #JK -> #TF
      <- UnconditionalJump[GOTO] #TN -> #JK
===#Block TF(size=2, flags=10100)===
   0. lvar105 = {1521133419 ^ lvar105};
   1. goto BH
      -> UnconditionalJump[GOTO] #TF -> #BH
      <- ConditionalJump[IF_ICMPEQ] #JK -> #TF
===#Block BH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.MAGENTA_GLAZED_TERRACOTTA;
   2. goto NN
      -> UnconditionalJump[GOTO] #BH -> #NN
      <- UnconditionalJump[GOTO] #TF -> #BH
===#Block NN(size=2, flags=10100)===
   0. lvar105 = {1796550544 ^ lvar105};
   1. goto HY
      -> UnconditionalJump[GOTO] #NN -> #HY
      <- UnconditionalJump[GOTO] #BH -> #NN
===#Block HY(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 62868264)
      goto HX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HY -> #HX
      -> TryCatch range: [HY...HX] -> ADA ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #NN -> #HY
===#Block HX(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HY...HX] -> ADA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HY -> #HX
===#Block ADA(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 606764499:
      	 goto	#ADC
      case 1783566875:
      	 goto	#ADB
      default:
      	 goto	#ADD
   }
      -> Switch[1783566875] #ADA -> #ADB
      -> Switch[606764499] #ADA -> #ADC
      -> DefaultSwitch #ADA -> #ADD
      <- TryCatch range: [HY...HX] -> ADA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HY...HX] -> ADA ([Ljava/lang/RuntimeException;])
===#Block ADD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADA -> #ADD
===#Block ADC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1400391068);
   1. goto HZ
      -> UnconditionalJump[GOTO] #ADC -> #HZ
      <- Switch[606764499] #ADA -> #ADC
===#Block ADB(size=2, flags=10100)===
   0. lvar105 = {1521805413 ^ lvar105};
   1. goto HZ
      -> UnconditionalJump[GOTO] #ADB -> #HZ
      <- Switch[1783566875] #ADA -> #ADB
===#Block HZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto NJ
      -> UnconditionalJump[GOTO] #HZ -> #NJ
      <- UnconditionalJump[GOTO] #ADB -> #HZ
      <- UnconditionalJump[GOTO] #ADC -> #HZ
===#Block NJ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 209351764:
      	 goto	#NK
      case 1546718935:
      	 goto	#CN
      case 1761061509:
      	 goto	#JC
      case 1841349771:
      	 goto	#NJ
      default:
      	 goto	#JC
   }
      -> Switch[209351764] #NJ -> #NK
      -> Switch[1841349771] #NJ -> #NJ
      -> Switch[1546718935] #NJ -> #CN
      -> Switch[1761061509] #NJ -> #JC
      -> DefaultSwitch #NJ -> #JC
      -> Immediate #NJ -> #NK
      <- Switch[1841349771] #NJ -> #NJ
      <- UnconditionalJump[GOTO] #HZ -> #NJ
===#Block NK(size=2, flags=100)===
   0. lvar105 = {2125178458 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NK -> #CN
      <- Switch[209351764] #NJ -> #NK
      <- Immediate #NJ -> #NK
===#Block LR(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 69022258:
      	 goto	#LS
      case 482862063:
      	 goto	#JC
      case 656704022:
      	 goto	#LR
      case 1705498519:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[656704022] #LR -> #LR
      -> Switch[69022258] #LR -> #LS
      -> DefaultSwitch #LR -> #JC
      -> Immediate #LR -> #LS
      -> Switch[482862063] #LR -> #JC
      <- Switch[656704022] #LR -> #LR
      <- UnconditionalJump[GOTO] #JK -> #LR
===#Block LS(size=2, flags=100)===
   0. lvar105 = {128372242 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LS -> #JC
      <- Switch[69022258] #LR -> #LS
      <- Immediate #LR -> #LS
===#Block BI(size=1, flags=0)===
   0. goto LK
      -> UnconditionalJump[GOTO] #BI -> #LK
      <- Immediate #BG -> #BI
===#Block LK(size=2, flags=10100)===
   0. lvar105 = {668644135 ^ lvar105};
   1. goto FW
      -> UnconditionalJump[GOTO] #LK -> #FW
      <- UnconditionalJump[GOTO] #BI -> #LK
===#Block FW(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 245345565)
      goto FV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FW -> #FV
      -> TryCatch range: [FW...FV] -> AAG ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #LK -> #FW
===#Block FV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FW...FV] -> AAG ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FW -> #FV
===#Block AAG(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1699361269:
      	 goto	#AAI
      case -649484797:
      	 goto	#AAH
      default:
      	 goto	#AAJ
   }
      -> Switch[-1699361269] #AAG -> #AAI
      -> Switch[-649484797] #AAG -> #AAH
      -> DefaultSwitch #AAG -> #AAJ
      <- TryCatch range: [FW...FV] -> AAG ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FW...FV] -> AAG ([Ljava/lang/IllegalAccessException;])
===#Block AAJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AAG -> #AAJ
===#Block AAH(size=2, flags=10100)===
   0. lvar105 = {1243878994 ^ lvar105};
   1. goto FX
      -> UnconditionalJump[GOTO] #AAH -> #FX
      <- Switch[-649484797] #AAG -> #AAH
===#Block AAI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1113299795);
   1. goto FX
      -> UnconditionalJump[GOTO] #AAI -> #FX
      <- Switch[-1699361269] #AAG -> #AAI
===#Block FX(size=2, flags=0)===
   0. _consume(catch());
   1. goto KU
      -> UnconditionalJump[GOTO] #FX -> #KU
      <- UnconditionalJump[GOTO] #AAH -> #FX
      <- UnconditionalJump[GOTO] #AAI -> #FX
===#Block KU(size=2, flags=10100)===
   0. lvar105 = {1123882111 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #KU -> #BJ
      <- UnconditionalJump[GOTO] #FX -> #KU
===#Block VB(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 71185582:
      	 goto	#VC
      case 904722178:
      	 goto	#JC
      case 1661204458:
      	 goto	#VB
      case 2006197366:
      	 goto	#AI
      default:
      	 goto	#JC
   }
      -> Immediate #VB -> #VC
      -> Switch[1661204458] #VB -> #VB
      -> DefaultSwitch #VB -> #JC
      -> Switch[71185582] #VB -> #VC
      -> Switch[904722178] #VB -> #JC
      -> Switch[2006197366] #VB -> #AI
      <- Switch[1661204458] #VB -> #VB
      <- Switch[74142277] #AH -> #VB
===#Block VC(size=2, flags=100)===
   0. lvar105 = {63579881 ^ lvar105};
   1. goto AI
      -> UnconditionalJump[GOTO] #VC -> #AI
      <- Immediate #VB -> #VC
      <- Switch[71185582] #VB -> #VC
===#Block AI(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar35 = lvar101;
   2. lvar83 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.amyshjbprxqwwbg(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar36 = lvar35.equals(lvar83);
   4. if (lvar36 != {122470188 ^ lvar105})
      goto UB
   5. lvar105 = {1603772444 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AI -> #UB
      -> Immediate #AI -> #AK
      <- UnconditionalJump[GOTO] #VC -> #AI
      <- Switch[2006197366] #VB -> #AI
===#Block AK(size=1, flags=0)===
   0. goto LH
      -> UnconditionalJump[GOTO] #AK -> #LH
      <- Immediate #AI -> #AK
===#Block LH(size=2, flags=10100)===
   0. lvar105 = {1023270753 ^ lvar105};
   1. goto FT
      -> UnconditionalJump[GOTO] #LH -> #FT
      <- UnconditionalJump[GOTO] #AK -> #LH
===#Block FT(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 34304186)
      goto FS
   1. throw nullconst;
      -> TryCatch range: [FT...FS] -> AAC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FT -> #FS
      <- UnconditionalJump[GOTO] #LH -> #FT
===#Block FS(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FT...FS] -> AAC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FT -> #FS
===#Block AAC(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 557187723:
      	 goto	#AAD
      case 717935555:
      	 goto	#AAE
      default:
      	 goto	#AAF
   }
      -> DefaultSwitch #AAC -> #AAF
      -> Switch[557187723] #AAC -> #AAD
      -> Switch[717935555] #AAC -> #AAE
      <- TryCatch range: [FT...FS] -> AAC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FT...FS] -> AAC ([Ljava/lang/RuntimeException;])
===#Block AAE(size=2, flags=10100)===
   0. lvar105 = {598961601 ^ lvar105};
   1. goto FU
      -> UnconditionalJump[GOTO] #AAE -> #FU
      <- Switch[717935555] #AAC -> #AAE
===#Block AAD(size=2, flags=10100)===
   0. lvar105 = {583858152 ^ lvar105};
   1. goto FU
      -> UnconditionalJump[GOTO] #AAD -> #FU
      <- Switch[557187723] #AAC -> #AAD
===#Block FU(size=2, flags=0)===
   0. _consume(catch());
   1. goto OO
      -> UnconditionalJump[GOTO] #FU -> #OO
      <- UnconditionalJump[GOTO] #AAD -> #FU
      <- UnconditionalJump[GOTO] #AAE -> #FU
===#Block OO(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 157650204:
      	 goto	#OP
      case 626582831:
      	 goto	#BJ
      case 730359961:
      	 goto	#OO
      case 1038535129:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[626582831] #OO -> #BJ
      -> Switch[730359961] #OO -> #OO
      -> Switch[157650204] #OO -> #OP
      -> DefaultSwitch #OO -> #JC
      -> Switch[1038535129] #OO -> #JC
      -> Immediate #OO -> #OP
      <- Switch[730359961] #OO -> #OO
      <- UnconditionalJump[GOTO] #FU -> #OO
===#Block OP(size=2, flags=100)===
   0. lvar105 = {890896724 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #OP -> #BJ
      <- Switch[157650204] #OO -> #OP
      <- Immediate #OO -> #OP
===#Block AAF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAC -> #AAF
===#Block UB(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 7398202:
      	 goto	#UB
      case 114816117:
      	 goto	#UC
      case 1681741298:
      	 goto	#JU
      case 2069429416:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1681741298] #UB -> #JU
      -> Switch[7398202] #UB -> #UB
      -> Immediate #UB -> #UC
      -> Switch[2069429416] #UB -> #JC
      -> DefaultSwitch #UB -> #JC
      -> Switch[114816117] #UB -> #UC
      <- Switch[7398202] #UB -> #UB
      <- ConditionalJump[IF_ICMPNE] #AI -> #UB
===#Block UC(size=2, flags=100)===
   0. lvar105 = {971902459 ^ lvar105};
   1. goto JU
      -> UnconditionalJump[GOTO] #UC -> #JU
      <- Immediate #UB -> #UC
      <- Switch[114816117] #UB -> #UC
===#Block JU(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -183134535)
      goto TM
   1. goto MP
      -> UnconditionalJump[GOTO] #JU -> #MP
      -> ConditionalJump[IF_ICMPEQ] #JU -> #TM
      <- Switch[1681741298] #UB -> #JU
      <- UnconditionalJump[GOTO] #UC -> #JU
===#Block TM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1605287323);
   1. goto AJ
      -> UnconditionalJump[GOTO] #TM -> #AJ
      <- ConditionalJump[IF_ICMPEQ] #JU -> #TM
===#Block AJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.YELLOW_GLAZED_TERRACOTTA;
   2. goto PG
      -> UnconditionalJump[GOTO] #AJ -> #PG
      <- UnconditionalJump[GOTO] #TM -> #AJ
===#Block PG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 621122307);
   1. goto HP
      -> UnconditionalJump[GOTO] #PG -> #HP
      <- UnconditionalJump[GOTO] #AJ -> #PG
===#Block HP(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 64150224)
      goto HO
   1. throw nullconst;
      -> TryCatch range: [HP...HO] -> ACO ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HP -> #HO
      <- UnconditionalJump[GOTO] #PG -> #HP
===#Block HO(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HP...HO] -> ACO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HP -> #HO
===#Block ACO(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1891639808:
      	 goto	#ACQ
      case 541909626:
      	 goto	#ACP
      default:
      	 goto	#ACR
   }
      -> Switch[541909626] #ACO -> #ACP
      -> DefaultSwitch #ACO -> #ACR
      -> Switch[-1891639808] #ACO -> #ACQ
      <- TryCatch range: [HP...HO] -> ACO ([Ljava/io/IOException;])
      <- TryCatch range: [HP...HO] -> ACO ([Ljava/io/IOException;])
===#Block ACQ(size=2, flags=10100)===
   0. lvar105 = {995143364 ^ lvar105};
   1. goto HQ
      -> UnconditionalJump[GOTO] #ACQ -> #HQ
      <- Switch[-1891639808] #ACO -> #ACQ
===#Block ACR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACO -> #ACR
===#Block ACP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1857997131);
   1. goto HQ
      -> UnconditionalJump[GOTO] #ACP -> #HQ
      <- Switch[541909626] #ACO -> #ACP
===#Block HQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto QI
      -> UnconditionalJump[GOTO] #HQ -> #QI
      <- UnconditionalJump[GOTO] #ACQ -> #HQ
      <- UnconditionalJump[GOTO] #ACP -> #HQ
===#Block QI(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 46341632:
      	 goto	#QJ
      case 165041697:
      	 goto	#CN
      case 1167194937:
      	 goto	#JC
      case 1385633717:
      	 goto	#QI
      default:
      	 goto	#JC
   }
      -> Switch[1385633717] #QI -> #QI
      -> Switch[46341632] #QI -> #QJ
      -> Immediate #QI -> #QJ
      -> DefaultSwitch #QI -> #JC
      -> Switch[165041697] #QI -> #CN
      -> Switch[1167194937] #QI -> #JC
      <- Switch[1385633717] #QI -> #QI
      <- UnconditionalJump[GOTO] #HQ -> #QI
===#Block QJ(size=2, flags=100)===
   0. lvar105 = {1675753848 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QJ -> #CN
      <- Switch[46341632] #QI -> #QJ
      <- Immediate #QI -> #QJ
===#Block MP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1710364285);
   1. goto JC
      -> UnconditionalJump[GOTO] #MP -> #JC
      <- UnconditionalJump[GOTO] #JU -> #MP
===#Block VH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 748801845);
   1. goto AR
      -> UnconditionalJump[GOTO] #VH -> #AR
      <- Switch[74142287] #AH -> #VH
===#Block AR(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar41 = lvar101;
   2. lvar86 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.fnzabfdarxpejkp(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar42 = lvar41.equals(lvar86);
   4. if (lvar42 != {673666800 ^ lvar105})
      goto SQ
   5. lvar105 = {115066947 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AR -> #SQ
      -> Immediate #AR -> #AS
      <- UnconditionalJump[GOTO] #VH -> #AR
===#Block AS(size=1, flags=0)===
   0. goto QM
      -> UnconditionalJump[GOTO] #AS -> #QM
      <- Immediate #AR -> #AS
===#Block QM(size=2, flags=10100)===
   0. lvar105 = {344887516 ^ lvar105};
   1. goto GI
      -> UnconditionalJump[GOTO] #QM -> #GI
      <- UnconditionalJump[GOTO] #AS -> #QM
===#Block GI(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 20758917)
      goto GH
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GI -> #GH
      -> TryCatch range: [GI...GH] -> AAW ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #QM -> #GI
===#Block GH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GI...GH] -> AAW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GI -> #GH
===#Block AAW(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -745507975:
      	 goto	#AAX
      case 1897654403:
      	 goto	#AAY
      default:
      	 goto	#AAZ
   }
      -> DefaultSwitch #AAW -> #AAZ
      -> Switch[1897654403] #AAW -> #AAY
      -> Switch[-745507975] #AAW -> #AAX
      <- TryCatch range: [GI...GH] -> AAW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GI...GH] -> AAW ([Ljava/lang/RuntimeException;])
===#Block AAX(size=2, flags=10100)===
   0. lvar105 = {1931042764 ^ lvar105};
   1. goto GJ
      -> UnconditionalJump[GOTO] #AAX -> #GJ
      <- Switch[-745507975] #AAW -> #AAX
===#Block AAY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 659041459);
   1. goto GJ
      -> UnconditionalJump[GOTO] #AAY -> #GJ
      <- Switch[1897654403] #AAW -> #AAY
===#Block GJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto QF
      -> UnconditionalJump[GOTO] #GJ -> #QF
      <- UnconditionalJump[GOTO] #AAX -> #GJ
      <- UnconditionalJump[GOTO] #AAY -> #GJ
===#Block QF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 983263054);
   1. goto BJ
      -> UnconditionalJump[GOTO] #QF -> #BJ
      <- UnconditionalJump[GOTO] #GJ -> #QF
===#Block AAZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAW -> #AAZ
===#Block SQ(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 95526929:
      	 goto	#JC
      case 231477334:
      	 goto	#SR
      case 1689246697:
      	 goto	#SQ
      case 2065284451:
      	 goto	#KA
      default:
      	 goto	#JC
   }
      -> Immediate #SQ -> #SR
      -> Switch[231477334] #SQ -> #SR
      -> Switch[2065284451] #SQ -> #KA
      -> Switch[1689246697] #SQ -> #SQ
      -> Switch[95526929] #SQ -> #JC
      -> DefaultSwitch #SQ -> #JC
      <- ConditionalJump[IF_ICMPNE] #AR -> #SQ
      <- Switch[1689246697] #SQ -> #SQ
===#Block SR(size=2, flags=100)===
   0. lvar105 = {1828589538 ^ lvar105};
   1. goto KA
      -> UnconditionalJump[GOTO] #SR -> #KA
      <- Immediate #SQ -> #SR
      <- Switch[231477334] #SQ -> #SR
===#Block KA(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 650823826)
      goto TO
   1. goto NL
      -> ConditionalJump[IF_ICMPEQ] #KA -> #TO
      -> UnconditionalJump[GOTO] #KA -> #NL
      <- Switch[2065284451] #SQ -> #KA
      <- UnconditionalJump[GOTO] #SR -> #KA
===#Block NL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 529137080);
   1. goto JC
      -> UnconditionalJump[GOTO] #NL -> #JC
      <- UnconditionalJump[GOTO] #KA -> #NL
===#Block TO(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 90527819:
      	 goto	#TP
      case 516624030:
      	 goto	#TO
      case 673124842:
      	 goto	#JC
      case 1696624108:
      	 goto	#AT
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #TO -> #JC
      -> Immediate #TO -> #TP
      -> Switch[673124842] #TO -> #JC
      -> Switch[90527819] #TO -> #TP
      -> Switch[516624030] #TO -> #TO
      -> Switch[1696624108] #TO -> #AT
      <- ConditionalJump[IF_ICMPEQ] #KA -> #TO
      <- Switch[516624030] #TO -> #TO
===#Block TP(size=2, flags=100)===
   0. lvar105 = {2003122599 ^ lvar105};
   1. goto AT
      -> UnconditionalJump[GOTO] #TP -> #AT
      <- Immediate #TO -> #TP
      <- Switch[90527819] #TO -> #TP
===#Block AT(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.CYAN_GLAZED_TERRACOTTA;
   2. goto MN
      -> UnconditionalJump[GOTO] #AT -> #MN
      <- UnconditionalJump[GOTO] #TP -> #AT
      <- Switch[1696624108] #TO -> #AT
===#Block MN(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 71092450:
      	 goto	#MO
      case 547700612:
      	 goto	#FH
      case 630894159:
      	 goto	#MN
      case 987125482:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #MN -> #MO
      -> Switch[71092450] #MN -> #MO
      -> Switch[547700612] #MN -> #FH
      -> Switch[630894159] #MN -> #MN
      -> DefaultSwitch #MN -> #JC
      -> Switch[987125482] #MN -> #JC
      <- UnconditionalJump[GOTO] #AT -> #MN
      <- Switch[630894159] #MN -> #MN
===#Block MO(size=2, flags=100)===
   0. lvar105 = {78234086 ^ lvar105};
   1. goto FH
      -> UnconditionalJump[GOTO] #MO -> #FH
      <- Immediate #MN -> #MO
      <- Switch[71092450] #MN -> #MO
===#Block FH(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 179953541)
      goto FG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FH -> #FG
      -> TryCatch range: [FH...FG] -> ZM ([Ljava/lang/IllegalAccessException;])
      <- Switch[547700612] #MN -> #FH
      <- UnconditionalJump[GOTO] #MO -> #FH
===#Block FG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FH...FG] -> ZM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FH -> #FG
===#Block ZM(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1196619111:
      	 goto	#ZN
      case -644747798:
      	 goto	#ZO
      default:
      	 goto	#ZP
   }
      -> DefaultSwitch #ZM -> #ZP
      -> Switch[-644747798] #ZM -> #ZO
      -> Switch[-1196619111] #ZM -> #ZN
      <- TryCatch range: [FH...FG] -> ZM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FH...FG] -> ZM ([Ljava/lang/IllegalAccessException;])
===#Block ZN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 853498859);
   1. goto FI
      -> UnconditionalJump[GOTO] #ZN -> #FI
      <- Switch[-1196619111] #ZM -> #ZN
===#Block ZO(size=2, flags=10100)===
   0. lvar105 = {1593362181 ^ lvar105};
   1. goto FI
      -> UnconditionalJump[GOTO] #ZO -> #FI
      <- Switch[-644747798] #ZM -> #ZO
===#Block FI(size=2, flags=0)===
   0. _consume(catch());
   1. goto KR
      -> UnconditionalJump[GOTO] #FI -> #KR
      <- UnconditionalJump[GOTO] #ZN -> #FI
      <- UnconditionalJump[GOTO] #ZO -> #FI
===#Block KR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1285314244);
   1. goto CN
      -> UnconditionalJump[GOTO] #KR -> #CN
      <- UnconditionalJump[GOTO] #FI -> #KR
===#Block ZP(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ZM -> #ZP
===#Block VI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 428200494);
   1. goto AX
      -> UnconditionalJump[GOTO] #VI -> #AX
      <- Switch[74142296] #AH -> #VI
===#Block AX(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar45 = lvar101;
   2. lvar88 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.iwdhwfexgbzmsvo(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar46 = lvar45.equals(lvar88);
   4. if (lvar46 != {486755307 ^ lvar105})
      goto SI
   5. lvar105 = {586364005 ^ lvar105};
      -> Immediate #AX -> #AZ
      -> ConditionalJump[IF_ICMPNE] #AX -> #SI
      <- UnconditionalJump[GOTO] #VI -> #AX
===#Block SI(size=2, flags=10100)===
   0. lvar105 = {2093198263 ^ lvar105};
   1. goto JR
      -> UnconditionalJump[GOTO] #SI -> #JR
      <- ConditionalJump[IF_ICMPNE] #AX -> #SI
===#Block JR(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == 235340515)
      goto RN
   1. goto LB
      -> ConditionalJump[IF_ICMPEQ] #JR -> #RN
      -> UnconditionalJump[GOTO] #JR -> #LB
      <- UnconditionalJump[GOTO] #SI -> #JR
===#Block LB(size=2, flags=10100)===
   0. lvar105 = {982533366 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LB -> #JC
      <- UnconditionalJump[GOTO] #JR -> #LB
===#Block RN(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 40310433:
      	 goto	#AY
      case 224777074:
      	 goto	#RO
      case 453335180:
      	 goto	#JC
      case 614353060:
      	 goto	#RN
      default:
      	 goto	#JC
   }
      -> Switch[224777074] #RN -> #RO
      -> Switch[40310433] #RN -> #AY
      -> Immediate #RN -> #RO
      -> Switch[453335180] #RN -> #JC
      -> DefaultSwitch #RN -> #JC
      -> Switch[614353060] #RN -> #RN
      <- ConditionalJump[IF_ICMPEQ] #JR -> #RN
      <- Switch[614353060] #RN -> #RN
===#Block RO(size=2, flags=100)===
   0. lvar105 = {268276113 ^ lvar105};
   1. goto AY
      -> UnconditionalJump[GOTO] #RO -> #AY
      <- Switch[224777074] #RN -> #RO
      <- Immediate #RN -> #RO
===#Block AY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLACK_GLAZED_TERRACOTTA;
   2. goto KN
      -> UnconditionalJump[GOTO] #AY -> #KN
      <- UnconditionalJump[GOTO] #RO -> #AY
      <- Switch[40310433] #RN -> #AY
===#Block KN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1697912022);
   1. goto DF
      -> UnconditionalJump[GOTO] #KN -> #DF
      <- UnconditionalJump[GOTO] #AY -> #KN
===#Block DF(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 90323181)
      goto DE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DF -> #DE
      -> TryCatch range: [DF...DE] -> WS ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #KN -> #DF
===#Block DE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DF...DE] -> WS ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DF -> #DE
===#Block WS(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case 1140957931:
      	 goto	#WU
      case 1481320664:
      	 goto	#WT
      default:
      	 goto	#WV
   }
      -> Switch[1481320664] #WS -> #WT
      -> Switch[1140957931] #WS -> #WU
      -> DefaultSwitch #WS -> #WV
      <- TryCatch range: [DF...DE] -> WS ([Ljava/io/IOException;])
      <- TryCatch range: [DF...DE] -> WS ([Ljava/io/IOException;])
===#Block WV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #WS -> #WV
===#Block WU(size=2, flags=10100)===
   0. lvar105 = {1567122286 ^ lvar105};
   1. goto DG
      -> UnconditionalJump[GOTO] #WU -> #DG
      <- Switch[1140957931] #WS -> #WU
===#Block WT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1054945832);
   1. goto DG
      -> UnconditionalJump[GOTO] #WT -> #DG
      <- Switch[1481320664] #WS -> #WT
===#Block DG(size=2, flags=0)===
   0. _consume(catch());
   1. goto PX
      -> UnconditionalJump[GOTO] #DG -> #PX
      <- UnconditionalJump[GOTO] #WT -> #DG
      <- UnconditionalJump[GOTO] #WU -> #DG
===#Block PX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 2092890959);
   1. goto CN
      -> UnconditionalJump[GOTO] #PX -> #CN
      <- UnconditionalJump[GOTO] #DG -> #PX
===#Block AZ(size=1, flags=0)===
   0. goto KO
      -> UnconditionalJump[GOTO] #AZ -> #KO
      <- Immediate #AX -> #AZ
===#Block KO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1089689441);
   1. goto DL
      -> UnconditionalJump[GOTO] #KO -> #DL
      <- UnconditionalJump[GOTO] #AZ -> #KO
===#Block DL(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 102129740)
      goto DK
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DL -> #DK
      -> TryCatch range: [DL...DK] -> XA ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #KO -> #DL
===#Block DK(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DL...DK] -> XA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DL -> #DK
===#Block XA(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -2109099213:
      	 goto	#XC
      case -132561029:
      	 goto	#XB
      default:
      	 goto	#XD
   }
      -> Switch[-132561029] #XA -> #XB
      -> DefaultSwitch #XA -> #XD
      -> Switch[-2109099213] #XA -> #XC
      <- TryCatch range: [DL...DK] -> XA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DL...DK] -> XA ([Ljava/lang/RuntimeException;])
===#Block XC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 946207714);
   1. goto DM
      -> UnconditionalJump[GOTO] #XC -> #DM
      <- Switch[-2109099213] #XA -> #XC
===#Block XD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XA -> #XD
===#Block XB(size=2, flags=10100)===
   0. lvar105 = {925885803 ^ lvar105};
   1. goto DM
      -> UnconditionalJump[GOTO] #XB -> #DM
      <- Switch[-132561029] #XA -> #XB
===#Block DM(size=2, flags=0)===
   0. _consume(catch());
   1. goto ME
      -> UnconditionalJump[GOTO] #DM -> #ME
      <- UnconditionalJump[GOTO] #XC -> #DM
      <- UnconditionalJump[GOTO] #XB -> #DM
===#Block ME(size=2, flags=10100)===
   0. lvar105 = {1004338025 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #ME -> #BJ
      <- UnconditionalJump[GOTO] #DM -> #ME
===#Block VK(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 71185582:
      	 goto	#VL
      case 165168454:
      	 goto	#BJ
      case 205342837:
      	 goto	#VK
      case 871557488:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[165168454] #VK -> #BJ
      -> Switch[205342837] #VK -> #VK
      -> Switch[71185582] #VK -> #VL
      -> DefaultSwitch #VK -> #JC
      -> Switch[871557488] #VK -> #JC
      -> Immediate #VK -> #VL
      <- Switch[205342837] #VK -> #VK
      <- DefaultSwitch #AH -> #VK
===#Block VL(size=2, flags=100)===
   0. lvar105 = {2004266792 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #VL -> #BJ
      <- Switch[71185582] #VK -> #VL
      <- Immediate #VK -> #VL
===#Block VA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 67340893);
   1. goto BD
      -> UnconditionalJump[GOTO] #VA -> #BD
      <- Switch[74142274] #AH -> #VA
===#Block BD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar50 = lvar101;
   2. lvar90 = cn.acebrand.acedex.gui.PokeBallItemCreator.jigermynhl(cn.acebrand.acedex.gui.PokeBallItemCreator.hmurkkzzjnrgtei(), cn.acebrand.acedex.gui.PokeBallItemCreator.ifqfegjgdjhvdaw(), lvar105);
   3. lvar51 = lvar50.equals(lvar90);
   4. if (lvar51 != {8721304 ^ lvar105})
      goto TD
   5. lvar105 = {1449024158 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BD -> #TD
      -> Immediate #BD -> #BE
      <- UnconditionalJump[GOTO] #VA -> #BD
===#Block BE(size=1, flags=0)===
   0. goto QW
      -> UnconditionalJump[GOTO] #BE -> #QW
      <- Immediate #BD -> #BE
===#Block QW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1390701834);
   1. goto HG
      -> UnconditionalJump[GOTO] #QW -> #HG
      <- UnconditionalJump[GOTO] #BE -> #QW
===#Block HG(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 66794996)
      goto HF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HG -> #HF
      -> TryCatch range: [HG...HF] -> ACC ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #QW -> #HG
===#Block HF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HG...HF] -> ACC ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HG -> #HF
===#Block ACC(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -1279943597:
      	 goto	#ACE
      case 569974880:
      	 goto	#ACD
      default:
      	 goto	#ACF
   }
      -> DefaultSwitch #ACC -> #ACF
      -> Switch[569974880] #ACC -> #ACD
      -> Switch[-1279943597] #ACC -> #ACE
      <- TryCatch range: [HG...HF] -> ACC ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HG...HF] -> ACC ([Ljava/lang/IllegalAccessException;])
===#Block ACE(size=2, flags=10100)===
   0. lvar105 = {743291892 ^ lvar105};
   1. goto HH
      -> UnconditionalJump[GOTO] #ACE -> #HH
      <- Switch[-1279943597] #ACC -> #ACE
===#Block ACD(size=2, flags=10100)===
   0. lvar105 = {1577331826 ^ lvar105};
   1. goto HH
      -> UnconditionalJump[GOTO] #ACD -> #HH
      <- Switch[569974880] #ACC -> #ACD
===#Block HH(size=2, flags=0)===
   0. _consume(catch());
   1. goto MV
      -> UnconditionalJump[GOTO] #HH -> #MV
      <- UnconditionalJump[GOTO] #ACD -> #HH
      <- UnconditionalJump[GOTO] #ACE -> #HH
===#Block MV(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 132883164:
      	 goto	#MV
      case 179003886:
      	 goto	#BJ
      case 248658511:
      	 goto	#MW
      case 990024637:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #MV -> #JC
      -> Switch[179003886] #MV -> #BJ
      -> Switch[132883164] #MV -> #MV
      -> Switch[248658511] #MV -> #MW
      -> Switch[990024637] #MV -> #JC
      -> Immediate #MV -> #MW
      <- Switch[132883164] #MV -> #MV
      <- UnconditionalJump[GOTO] #HH -> #MV
===#Block MW(size=2, flags=100)===
   0. lvar105 = {701185683 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #MW -> #BJ
      <- Switch[248658511] #MV -> #MW
      <- Immediate #MV -> #MW
===#Block BJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.GRAY_GLAZED_TERRACOTTA;
   2. goto ML
      -> UnconditionalJump[GOTO] #BJ -> #ML
      <- UnconditionalJump[GOTO] #LQ -> #BJ
      <- UnconditionalJump[GOTO] #KU -> #BJ
      <- UnconditionalJump[GOTO] #VL -> #BJ
      <- Switch[165168454] #VK -> #BJ
      <- Switch[179003886] #MV -> #BJ
      <- UnconditionalJump[GOTO] #QD -> #BJ
      <- Switch[1693959582] #LP -> #BJ
      <- UnconditionalJump[GOTO] #QF -> #BJ
      <- UnconditionalJump[GOTO] #MW -> #BJ
      <- Switch[626582831] #OO -> #BJ
      <- UnconditionalJump[GOTO] #ME -> #BJ
      <- UnconditionalJump[GOTO] #OP -> #BJ
      <- UnconditionalJump[GOTO] #QE -> #BJ
      <- UnconditionalJump[GOTO] #QH -> #BJ
===#Block ML(size=2, flags=10100)===
   0. lvar105 = {2075288344 ^ lvar105};
   1. goto ES
      -> UnconditionalJump[GOTO] #ML -> #ES
      <- UnconditionalJump[GOTO] #BJ -> #ML
===#Block ES(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 36552)
      goto ER
   1. throw nullconst;
      -> TryCatch range: [ES...ER] -> YS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #ES -> #ER
      <- UnconditionalJump[GOTO] #ML -> #ES
===#Block ER(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [ES...ER] -> YS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #ES -> #ER
===#Block YS(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -292154149:
      	 goto	#YU
      case 1108520872:
      	 goto	#YT
      default:
      	 goto	#YV
   }
      -> DefaultSwitch #YS -> #YV
      -> Switch[1108520872] #YS -> #YT
      -> Switch[-292154149] #YS -> #YU
      <- TryCatch range: [ES...ER] -> YS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [ES...ER] -> YS ([Ljava/lang/IllegalAccessException;])
===#Block YU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 127855535);
   1. goto ET
      -> UnconditionalJump[GOTO] #YU -> #ET
      <- Switch[-292154149] #YS -> #YU
===#Block YT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1913552065);
   1. goto ET
      -> UnconditionalJump[GOTO] #YT -> #ET
      <- Switch[1108520872] #YS -> #YT
===#Block ET(size=2, flags=0)===
   0. _consume(catch());
   1. goto NT
      -> UnconditionalJump[GOTO] #ET -> #NT
      <- UnconditionalJump[GOTO] #YT -> #ET
      <- UnconditionalJump[GOTO] #YU -> #ET
===#Block NT(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 217314712:
      	 goto	#NU
      case 324582559:
      	 goto	#CN
      case 553174820:
      	 goto	#NT
      case 1601515356:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[217314712] #NT -> #NU
      -> Switch[553174820] #NT -> #NT
      -> Immediate #NT -> #NU
      -> Switch[324582559] #NT -> #CN
      -> Switch[1601515356] #NT -> #JC
      -> DefaultSwitch #NT -> #JC
      <- UnconditionalJump[GOTO] #ET -> #NT
      <- Switch[553174820] #NT -> #NT
===#Block NU(size=2, flags=100)===
   0. lvar105 = {857376072 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NU -> #CN
      <- Switch[217314712] #NT -> #NU
      <- Immediate #NT -> #NU
===#Block YV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YS -> #YV
===#Block ACF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ACC -> #ACF
===#Block TD(size=2, flags=10100)===
   0. lvar105 = {936187443 ^ lvar105};
   1. goto JG
      -> UnconditionalJump[GOTO] #TD -> #JG
      <- ConditionalJump[IF_ICMPNE] #BD -> #TD
===#Block JG(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.ldeqmdzcnkpynfka(lvar105) == -1170199207)
      goto RT
   1. goto NC
      -> ConditionalJump[IF_ICMPEQ] #JG -> #RT
      -> UnconditionalJump[GOTO] #JG -> #NC
      <- UnconditionalJump[GOTO] #TD -> #JG
===#Block NC(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 108876160:
      	 goto	#JC
      case 186367411:
      	 goto	#ND
      case 1163980645:
      	 goto	#JC
      case 1497036117:
      	 goto	#NC
      default:
      	 goto	#JC
   }
      -> Switch[186367411] #NC -> #ND
      -> Switch[1497036117] #NC -> #NC
      -> DefaultSwitch #NC -> #JC
      -> Immediate #NC -> #ND
      -> Switch[1163980645] #NC -> #JC
      <- Switch[1497036117] #NC -> #NC
      <- UnconditionalJump[GOTO] #JG -> #NC
===#Block ND(size=2, flags=100)===
   0. lvar105 = {1813553409 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #ND -> #JC
      <- Switch[186367411] #NC -> #ND
      <- Immediate #NC -> #ND
===#Block RT(size=1, flags=10100)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105)) {
      case 186367411:
      	 goto	#RU
      case 501126847:
      	 goto	#RT
      case 1242208681:
      	 goto	#BF
      case 1838059812:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #RT -> #JC
      -> Switch[1838059812] #RT -> #JC
      -> Switch[1242208681] #RT -> #BF
      -> Switch[501126847] #RT -> #RT
      -> Switch[186367411] #RT -> #RU
      -> Immediate #RT -> #RU
      <- Switch[501126847] #RT -> #RT
      <- ConditionalJump[IF_ICMPEQ] #JG -> #RT
===#Block RU(size=2, flags=100)===
   0. lvar105 = {492484737 ^ lvar105};
   1. goto BF
      -> UnconditionalJump[GOTO] #RU -> #BF
      <- Switch[186367411] #RT -> #RU
      <- Immediate #RT -> #RU
===#Block BF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar47 = org.bukkit.Material.BLUE_GLAZED_TERRACOTTA;
   2. goto NR
      -> UnconditionalJump[GOTO] #BF -> #NR
      <- Switch[1242208681] #RT -> #BF
      <- UnconditionalJump[GOTO] #RU -> #BF
===#Block NR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 579213030);
   1. goto IE
      -> UnconditionalJump[GOTO] #NR -> #IE
      <- UnconditionalJump[GOTO] #BF -> #NR
===#Block IE(size=2, flags=0)===
   0. if (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.laribtthekydhrqv(lvar105) == 10486863)
      goto ID
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IE -> #ID
      -> TryCatch range: [IE...ID] -> ADI ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #NR -> #IE
===#Block ID(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IE...ID] -> ADI ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IE -> #ID
===#Block ADI(size=1, flags=0)===
   0. switch (mantkpzccscvfpcc.fvsvtzhdrbvrtyuv.svznpxsdmuazavsh(lvar105)) {
      case -254046079:
      	 goto	#ADK
      case 1151688288:
      	 goto	#ADJ
      default:
      	 goto	#ADL
   }
      -> DefaultSwitch #ADI -> #ADL
      -> Switch[1151688288] #ADI -> #ADJ
      -> Switch[-254046079] #ADI -> #ADK
      <- TryCatch range: [IE...ID] -> ADI ([Ljava/io/IOException;])
      <- TryCatch range: [IE...ID] -> ADI ([Ljava/io/IOException;])
===#Block ADK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 434078099);
   1. goto IF
      -> UnconditionalJump[GOTO] #ADK -> #IF
      <- Switch[-254046079] #ADI -> #ADK
===#Block ADJ(size=2, flags=10100)===
   0. lvar105 = {793794639 ^ lvar105};
   1. goto IF
      -> UnconditionalJump[GOTO] #ADJ -> #IF
      <- Switch[1151688288] #ADI -> #ADJ
===#Block IF(size=2, flags=0)===
   0. _consume(catch());
   1. goto MK
      -> UnconditionalJump[GOTO] #IF -> #MK
      <- UnconditionalJump[GOTO] #ADK -> #IF
      <- UnconditionalJump[GOTO] #ADJ -> #IF
===#Block MK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.xiybxodfhvcrcjwp(lvar105, 1855086591);
   1. goto CN
      -> UnconditionalJump[GOTO] #MK -> #CN
      <- UnconditionalJump[GOTO] #IF -> #MK
===#Block CN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[1] [org/bukkit/Material]
   1. lvar7 = lvar47;
   2. lvar105 = {1155254416 ^ lvar105};
      -> Immediate #CN -> #CO
      <- UnconditionalJump[GOTO] #RF -> #CN
      <- UnconditionalJump[GOTO] #KI -> #CN
      <- UnconditionalJump[GOTO] #PM -> #CN
      <- Switch[1905056542] #OM -> #CN
      <- UnconditionalJump[GOTO] #OI -> #CN
      <- Switch[464687733] #RE -> #CN
      <- UnconditionalJump[GOTO] #LX -> #CN
      <- UnconditionalJump[GOTO] #ON -> #CN
      <- UnconditionalJump[GOTO] #MF -> #CN
      <- Switch[1642426341] #QN -> #CN
      <- Switch[165041697] #QI -> #CN
      <- UnconditionalJump[GOTO] #NK -> #CN
      <- UnconditionalJump[GOTO] #PS -> #CN
      <- UnconditionalJump[GOTO] #NV -> #CN
      <- UnconditionalJump[GOTO] #NS -> #CN
      <- UnconditionalJump[GOTO] #QK -> #CN
      <- Immediate #CL -> #CN
      <- Switch[1546718935] #NJ -> #CN
      <- UnconditionalJump[GOTO] #QJ -> #CN
      <- Switch[480753084] #OH -> #CN
      <- Switch[1379263213] #KH -> #CN
      <- UnconditionalJump[GOTO] #MM -> #CN
      <- UnconditionalJump[GOTO] #QO -> #CN
      <- Switch[324582559] #NT -> #CN
      <- UnconditionalJump[GOTO] #PI -> #CN
      <- UnconditionalJump[GOTO] #NA -> #CN
      <- UnconditionalJump[GOTO] #LO -> #CN
      <- Switch[2100908013] #PL -> #CN
      <- UnconditionalJump[GOTO] #NY -> #CN
      <- UnconditionalJump[GOTO] #NX -> #CN
      <- UnconditionalJump[GOTO] #OZ -> #CN
      <- UnconditionalJump[GOTO] #NU -> #CN
      <- Switch[1517690522] #PH -> #CN
      <- UnconditionalJump[GOTO] #MK -> #CN
      <- UnconditionalJump[GOTO] #PT -> #CN
      <- UnconditionalJump[GOTO] #QY -> #CN
      <- UnconditionalJump[GOTO] #PX -> #CN
      <- UnconditionalJump[GOTO] #KR -> #CN
      <- UnconditionalJump[GOTO] #NG -> #CN
      <- UnconditionalJump[GOTO] #QV -> #CN
===#Block CO(size=4, flags=0)===
   0. lvar14 = new org.bukkit.inventory.ItemStack;
   1. lvar6 = lvar7;
   2. _consume(lvar14.<init>(lvar6));
   3. return lvar14;
      <- Immediate #CN -> #CO
===#Block ADL(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADI -> #ADL
===#Block JC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      <- DefaultSwitch #NE -> #JC
      <- Switch[2039780132] #NE -> #JC
      <- DefaultSwitch #VB -> #JC
      <- DefaultSwitch #NC -> #JC
      <- Switch[1245325294] #MB -> #JC
      <- Switch[904722178] #VB -> #JC
      <- DefaultSwitch #MB -> #JC
      <- Switch[1163980645] #NC -> #JC
      <- Switch[1900878551] #UM -> #JC
      <- DefaultSwitch #UM -> #JC
      <- DefaultSwitch #NZ -> #JC
      <- DefaultSwitch #MG -> #JC
      <- Switch[1765631447] #NZ -> #JC
      <- Switch[1150434850] #MG -> #JC
      <- UnconditionalJump[GOTO] #OJ -> #JC
      <- Switch[1761061509] #NJ -> #JC
      <- DefaultSwitch #UF -> #JC
      <- DefaultSwitch #OO -> #JC
      <- Switch[610314873] #UF -> #JC
      <- Switch[1038535129] #OO -> #JC
      <- DefaultSwitch #NJ -> #JC
      <- Switch[435055237] #QT -> #JC
      <- DefaultSwitch #QT -> #JC
      <- UnconditionalJump[GOTO] #ND -> #JC
      <- DefaultSwitch #VK -> #JC
      <- Switch[871557488] #VK -> #JC
      <- DefaultSwitch #VD -> #JC
      <- DefaultSwitch #PH -> #JC
      <- DefaultSwitch #OE -> #JC
      <- UnconditionalJump[GOTO] #OA -> #JC
      <- Switch[1430060284] #OE -> #JC
      <- UnconditionalJump[GOTO] #LE -> #JC
      <- Switch[1914529424] #PH -> #JC
      <- Switch[378544618] #VD -> #JC
      <- UnconditionalJump[GOTO] #MP -> #JC
      <- Switch[1805284077] #PL -> #JC
      <- Switch[2101303664] #KX -> #JC
      <- DefaultSwitch #PL -> #JC
      <- DefaultSwitch #UJ -> #JC
      <- Switch[1601515356] #NT -> #JC
      <- Switch[2127003669] #UD -> #JC
      <- DefaultSwitch #KX -> #JC
      <- Switch[1046660488] #UJ -> #JC
      <- DefaultSwitch #NT -> #JC
      <- Switch[95526929] #SQ -> #JC
      <- UnconditionalJump[GOTO] #LL -> #JC
      <- DefaultSwitch #SQ -> #JC
      <- DefaultSwitch #UD -> #JC
      <- DefaultSwitch #OH -> #JC
      <- Switch[933539225] #OH -> #JC
      <- DefaultSwitch #VS -> #JC
      <- Switch[2031664399] #VS -> #JC
      <- DefaultSwitch #LR -> #JC
      <- Switch[482862063] #LR -> #JC
      <- Switch[1822833912] #QP -> #JC
      <- DefaultSwitch #SB -> #JC
      <- DefaultSwitch #QP -> #JC
      <- Switch[1385620124] #SB -> #JC
      <- UnconditionalJump[GOTO] #OY -> #JC
      <- DefaultSwitch #QZ -> #JC
      <- Switch[642835201] #QZ -> #JC
      <- Switch[220043151] #LT -> #JC
      <- DefaultSwitch #RT -> #JC
      <- Switch[1838059812] #RT -> #JC
      <- Switch[2001676707] #RZ -> #JC
      <- DefaultSwitch #MV -> #JC
      <- DefaultSwitch #RZ -> #JC
      <- DefaultSwitch #LT -> #JC
      <- DefaultSwitch #TG -> #JC
      <- Switch[1814607939] #TG -> #JC
      <- UnconditionalJump[GOTO] #NM -> #JC
      <- Switch[479780169] #OM -> #JC
      <- Switch[1382768470] #VQ -> #JC
      <- Switch[990024637] #MV -> #JC
      <- DefaultSwitch #OM -> #JC
      <- DefaultSwitch #TB -> #JC
      <- DefaultSwitch #QN -> #JC
      <- Switch[1675832967] #TB -> #JC
      <- Switch[1478464266] #QN -> #JC
      <- UnconditionalJump[GOTO] #NL -> #JC
      <- DefaultSwitch #VQ -> #JC
      <- DefaultSwitch #PD -> #JC
      <- Switch[153349665] #PD -> #JC
      <- Switch[2069429416] #UB -> #JC
      <- Switch[453335180] #RN -> #JC
      <- DefaultSwitch #UB -> #JC
      <- DefaultSwitch #RN -> #JC
      <- Switch[959515151] #LV -> #JC
      <- DefaultSwitch #LV -> #JC
      <- DefaultSwitch #SD -> #JC
      <- Switch[217122230] #SD -> #JC
      <- DefaultSwitch #OR -> #JC
      <- Switch[1840685497] #RJ -> #JC
      <- Switch[1444656472] #OR -> #JC
      <- DefaultSwitch #RJ -> #JC
      <- UnconditionalJump[GOTO] #MC -> #JC
      <- Switch[673482594] #UH -> #JC
      <- DefaultSwitch #UH -> #JC
      <- UnconditionalJump[GOTO] #RG -> #JC
      <- UnconditionalJump[GOTO] #NO -> #JC
      <- UnconditionalJump[GOTO] #PB -> #JC
      <- Switch[1725488817] #OB -> #JC
      <- UnconditionalJump[GOTO] #PA -> #JC
      <- UnconditionalJump[GOTO] #KM -> #JC
      <- UnconditionalJump[GOTO] #LS -> #JC
      <- Switch[666009838] #RE -> #JC
      <- Switch[1854773026] #RR -> #JC
      <- UnconditionalJump[GOTO] #LC -> #JC
      <- DefaultSwitch #LP -> #JC
      <- Switch[1443463500] #LP -> #JC
      <- DefaultSwitch #RE -> #JC
      <- DefaultSwitch #RR -> #JC
      <- DefaultSwitch #OB -> #JC
      <- Switch[2110769486] #KS -> #JC
      <- DefaultSwitch #KS -> #JC
      <- UnconditionalJump[GOTO] #KW -> #JC
      <- DefaultSwitch #TO -> #JC
      <- UnconditionalJump[GOTO] #PU -> #JC
      <- Switch[728412332] #SM -> #JC
      <- DefaultSwitch #SM -> #JC
      <- Switch[673124842] #TO -> #JC
      <- UnconditionalJump[GOTO] #MT -> #JC
      <- DefaultSwitch #LI -> #JC
      <- Switch[537876272] #LI -> #JC
      <- DefaultSwitch #QI -> #JC
      <- DefaultSwitch #RH -> #JC
      <- UnconditionalJump[GOTO] #OU -> #JC
      <- Switch[1167194937] #QI -> #JC
      <- Switch[923672462] #RH -> #JC
      <- DefaultSwitch #TI -> #JC
      <- Switch[2040990895] #TT -> #JC
      <- Switch[1849020325] #TI -> #JC
      <- DefaultSwitch #TT -> #JC
      <- DefaultSwitch #NP -> #JC
      <- Switch[1448091559] #NP -> #JC
      <- DefaultSwitch #MX -> #JC
      <- UnconditionalJump[GOTO] #MD -> #JC
      <- Switch[1065512853] #MX -> #JC
      <- Switch[1856209290] #PP -> #JC
      <- Switch[928822199] #TR -> #JC
      <- DefaultSwitch #TR -> #JC
      <- Switch[1318063051] #MR -> #JC
      <- DefaultSwitch #KZ -> #JC
      <- DefaultSwitch #PP -> #JC
      <- DefaultSwitch #MR -> #JC
      <- Switch[785646770] #KZ -> #JC
      <- UnconditionalJump[GOTO] #OS -> #JC
      <- DefaultSwitch #UV -> #JC
      <- Switch[2077877356] #PV -> #JC
      <- DefaultSwitch #PV -> #JC
      <- Switch[1197965418] #UV -> #JC
      <- Switch[1130477980] #UQ -> #JC
      <- Switch[114253960] #KH -> #JC
      <- Switch[800927982] #SY -> #JC
      <- Switch[637902977] #SO -> #JC
      <- DefaultSwitch #UQ -> #JC
      <- DefaultSwitch #KH -> #JC
      <- DefaultSwitch #SY -> #JC
      <- DefaultSwitch #SO -> #JC
      <- DefaultSwitch #MN -> #JC
      <- UnconditionalJump[GOTO] #PW -> #JC
      <- Switch[987125482] #MN -> #JC
      <- UnconditionalJump[GOTO] #LB -> #JC
      <- DefaultSwitch #SJ -> #JC
      <- UnconditionalJump[GOTO] #NQ -> #JC
      <- Switch[485375558] #SJ -> #JC
      <- UnconditionalJump[GOTO] #OK -> #JC
      <- DefaultSwitch #SU -> #JC
      <- DefaultSwitch #OV -> #JC
      <- UnconditionalJump[GOTO] #OG -> #JC
      <- Switch[388809052] #NH -> #JC
      <- Switch[280652411] #QA -> #JC
      <- Switch[*********] #OV -> #JC
      <- DefaultSwitch #NH -> #JC
      <- DefaultSwitch #QA -> #JC
      <- Switch[849621575] #KJ -> #JC
      <- UnconditionalJump[GOTO] #LU -> #JC
      <- DefaultSwitch #LF -> #JC
      <- DefaultSwitch #KJ -> #JC
      <- Switch[1857685002] #LF -> #JC
      <- Switch[2106601945] #RC -> #JC
      <- Switch[1820454188] #SU -> #JC
      <- DefaultSwitch #RC -> #JC
