package cn.acebrand.acedex.config

import cn.acebrand.acedex.AceDex
import org.bukkit.configuration.file.FileConfiguration
import org.bukkit.configuration.file.YamlConfiguration
import java.io.File

/**
 * 付费奖励配置管理器
 * 专门管理付费奖励系统的配置文件
 */
class PremiumRewardConfig(private val plugin: AceDex) {

    private lateinit var configFile: File
    private lateinit var config: FileConfiguration

    /**
     * 初始化付费奖励配置
     */
    fun initialize() {
        configFile = File(plugin.dataFolder, "premium-rewards.yml")
        
        // 如果配置文件不存在，从资源文件复制
        if (!configFile.exists()) {
            plugin.saveResource("premium-rewards.yml", false)
            plugin.logger.info("已创建付费奖励配置文件: premium-rewards.yml")
        }
        
        config = YamlConfiguration.loadConfiguration(configFile)
        plugin.logger.info("付费奖励配置文件加载完成")
    }

    /**
     * 重新加载配置文件
     */
    fun reload() {
        config = YamlConfiguration.loadConfiguration(configFile)
        plugin.logger.info("付费奖励配置文件已重新加载")
    }

    /**
     * 保存配置文件
     */
    fun save() {
        try {
            config.save(configFile)
        } catch (e: Exception) {
            plugin.logger.severe("保存付费奖励配置文件失败: ${e.message}")
        }
    }

    /**
     * 获取付费菜单的进度按钮材质配置
     */
    fun getPremiumProgressButtonMaterials(): Map<String, String> {
        val materialsMap = mutableMapOf<String, String>()
        val progressLevels = listOf("0-25", "25-50", "50-75", "75-99", "100")

        progressLevels.forEach { level ->
            val material = config.getString("premium-menu.progress-button.materials.$level")
            if (material != null) {
                materialsMap[level] = material
            }
        }

        // 如果没有配置，使用默认值
        if (materialsMap.isEmpty()) {
            materialsMap["0-25"] = "COBBLEMON_POKE_BALL"
            materialsMap["25-50"] = "COBBLEMON_GREAT_BALL"
            materialsMap["50-75"] = "COBBLEMON_ULTRA_BALL"
            materialsMap["75-99"] = "COBBLEMON_MASTER_BALL"
            materialsMap["100"] = "COBBLEMON_PREMIER_BALL"
        }

        return materialsMap
    }

    /**
     * 获取付费菜单的关闭按钮材质
     */
    fun getPremiumCloseButtonMaterial(): String {
        return config.getString("premium-menu.close-button.material") ?: "PAPER:10030"
    }

    /**
     * 获取配置对象
     */
    fun getConfig(): FileConfiguration = config

    /**
     * 检查付费奖励系统是否启用
     */
    fun isEnabled(): Boolean = config.getBoolean("enabled", true)

    /**
     * 获取付费世代完成奖励命令
     */
    fun getPremiumGenerationCompletionCommands(generationId: String): List<String> {
        return config.getStringList("generation.$generationId.completion.commands")
    }

    /**
     * 获取付费世代完成奖励描述
     */
    fun getPremiumGenerationCompletionDescriptions(generationId: String): List<String> {
        return config.getStringList("generation.$generationId.completion.descriptions")
    }

    /**
     * 检查付费世代完成奖励是否启用
     */
    fun isPremiumGenerationCompletionEnabled(generationId: String): Boolean {
        return config.getBoolean("generation.$generationId.completion.enabled", true)
    }

    /**
     * 获取付费世代进度奖励命令
     */
    fun getPremiumGenerationProgressCommands(generationId: String, percentage: Int): List<String> {
        return config.getStringList("generation.$generationId.progress.$percentage.commands")
    }

    /**
     * 获取付费世代进度奖励描述
     */
    fun getPremiumGenerationProgressDescriptions(generationId: String, percentage: Int): List<String> {
        return config.getStringList("generation.$generationId.progress.$percentage.descriptions")
    }

    /**
     * 获取付费全世代完成奖励命令
     */
    fun getPremiumAllGenerationsCompletionCommands(): List<String> {
        return config.getStringList("all-generations-completion.commands")
    }

    /**
     * 获取付费全世代完成奖励描述
     */
    fun getPremiumAllGenerationsCompletionDescriptions(): List<String> {
        return config.getStringList("all-generations-completion.descriptions")
    }

    /**
     * 检查付费全世代完成奖励是否启用
     */
    fun isPremiumAllGenerationsCompletionEnabled(): Boolean {
        return config.getBoolean("all-generations-completion.enabled", true)
    }

    /**
     * 获取付费全世代进度奖励命令
     */
    fun getPremiumOverallProgressCommands(percentage: Int): List<String> {
        return config.getStringList("overall-progress.$percentage.commands")
    }

    /**
     * 获取付费全世代进度奖励描述
     */
    fun getPremiumOverallProgressDescriptions(percentage: Int): List<String> {
        return config.getStringList("overall-progress.$percentage.descriptions")
    }

    /**
     * 获取付费全世代进度奖励名称
     */
    fun getPremiumOverallProgressName(percentage: Int): String {
        return config.getString("overall-progress.$percentage.name") ?: "付费进度奖励"
    }

    /**
     * 检查付费全世代进度奖励是否启用
     */
    fun isPremiumOverallProgressEnabled(): Boolean {
        return config.getBoolean("overall-progress.enabled", true)
    }

    /**
     * 获取权限配置
     */
    fun getViewPermission(): String = config.getString("permissions.view") ?: "acedex.premium.view"
    fun getClaimPermission(): String = config.getString("permissions.claim") ?: "acedex.premium.claim"

    /**
     * 获取显示配置
     */
    fun getMenuTitle(): String = config.getString("display.menu-title") ?: "§6§l付费奖励菜单"
    
    fun getCompletedPremiumButtonName(): String = config.getString("display.button-names.completed-premium") ?: "§6§l{generation} §a✓ §6[付费]"
    fun getAvailablePremiumButtonName(): String = config.getString("display.button-names.available-premium") ?: "§6§l{generation} §6★ §6[付费]"
    fun getLockedPremiumButtonName(): String = config.getString("display.button-names.locked-premium") ?: "§6§l{generation} §6[付费]"

    /**
     * 获取消息配置
     */
    fun getSuccessMessage(key: String): String {
        return config.getString("messages.success.$key") ?: "§a操作成功！"
    }

    fun getErrorMessage(key: String): String {
        return config.getString("messages.error.$key") ?: "§c操作失败！"
    }

    fun getInfoMessage(key: String): String {
        return config.getString("messages.info.$key") ?: "§7信息"
    }

    /**
     * 获取所有配置的付费世代进度百分比
     */
    fun getPremiumGenerationProgressPercentages(generationId: String): List<Int> {
        val progressSection = config.getConfigurationSection("generation.$generationId.progress")
        val percentages = mutableListOf<Int>()
        
        progressSection?.getKeys(false)?.forEach { key ->
            try {
                percentages.add(key.toInt())
            } catch (e: NumberFormatException) {
                // 忽略无效的百分比配置
            }
        }
        
        return percentages.sorted()
    }

    /**
     * 获取所有配置的付费全世代进度百分比
     */
    fun getPremiumOverallProgressPercentages(): List<Int> {
        val progressSection = config.getConfigurationSection("overall-progress")
        val percentages = mutableListOf<Int>()
        
        progressSection?.getKeys(false)?.forEach { key ->
            if (key != "enabled") {
                try {
                    percentages.add(key.toInt())
                } catch (e: NumberFormatException) {
                    // 忽略无效的百分比配置
                }
            }
        }
        
        return percentages.sorted()
    }

    /**
     * 检查指定世代是否有付费奖励配置
     */
    fun hasPremiumRewards(generationId: String): Boolean {
        return config.contains("generation.$generationId")
    }

    /**
     * 获取所有配置的付费世代ID
     */
    fun getAllPremiumGenerationIds(): List<String> {
        val generationSection = config.getConfigurationSection("generation")
        return generationSection?.getKeys(false)?.toList() ?: emptyList()
    }
}
