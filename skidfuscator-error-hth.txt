handler=Block #AH, types=[Ljava/io/IOException;], range=[Block #AG, Block #AF]
handler=Block #AK, types=[Ljava/lang/IllegalAccessException;], range=[Block #AJ, Block #AI]
handler=Block #AN, types=[Ljava/lang/IllegalAccessException;], range=[Block #AM, Block #AL]
handler=Block #AQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #AP, Block #AO]
handler=Block #AT, types=[Ljava/lang/RuntimeException;], range=[Block #AS, Block #AR]
handler=Block #AW, types=[Ljava/lang/IllegalAccessException;], range=[Block #AV, Block #AU]
handler=Block #AZ, types=[Ljava/io/IOException;], range=[Block #AY, Block #AX]
handler=Block #BC, types=[Ljava/lang/RuntimeException;], range=[Block #BB, Block #BA]
handler=Block #BF, types=[Ljava/lang/IllegalAccessException;], range=[Block #BE, Block #BD]
handler=Block #BI, types=[Ljava/lang/IllegalAccessException;], range=[Block #BH, Block #BG]
handler=Block #BL, types=[Ljava/lang/RuntimeException;], range=[Block #BK, Block #BJ]
handler=Block #BO, types=[Ljava/lang/RuntimeException;], range=[Block #BN, Block #BM]
handler=Block #BR, types=[Ljava/lang/RuntimeException;], range=[Block #BQ, Block #BP]
handler=Block #BU, types=[Ljava/io/IOException;], range=[Block #BT, Block #BS]
handler=Block #BX, types=[Ljava/lang/RuntimeException;], range=[Block #BW, Block #BV]
handler=Block #CA, types=[Ljava/io/IOException;], range=[Block #BZ, Block #BY]
handler=Block #CD, types=[Ljava/lang/IllegalAccessException;], range=[Block #CC, Block #CB]
handler=Block #CG, types=[Ljava/lang/RuntimeException;], range=[Block #CF, Block #CE]
===#Block A(size=2, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
      -> Immediate #A -> #B
===#Block B(size=6, flags=0)===
   0. lvar3 = lvar1;
   1. lvar5 = lvar3;
   2. lvar6 = lvar5;
   3. lvar7 = lvar6.hashCode();
   4. svar48 = {lvar7 ^ lvar46};
   5. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(svar48)) {
      case 45483012:
      	 goto	#C
      case 45483013:
      	 goto	#R
      case 45483059:
      	 goto	#AA
      case 45483064:
      	 goto	#U
      case 45483065:
      	 goto	#O
      case 45483068:
      	 goto	#X
      case 45483070:
      	 goto	#F
      case 45483071:
      	 goto	#I
      default:
      	 goto	#AC
   }
      -> Switch[45483059] #B -> #AA
      -> Switch[45483068] #B -> #X
      -> Switch[45483064] #B -> #U
      -> Switch[45483013] #B -> #R
      -> DefaultSwitch #B -> #AC
      -> Switch[45483065] #B -> #O
      -> Switch[45483071] #B -> #I
      -> Switch[45483070] #B -> #F
      -> Switch[45483012] #B -> #C
      <- Immediate #A -> #B
===#Block C(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar8 = lvar5;
   2. lvar4 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.jfprqgwjffzlpld(), lvar46);
   3. lvar9 = lvar8.equals(lvar4);
   4. if (lvar9 != {1817992025 ^ lvar46})
      goto CO
      -> ConditionalJump[IF_ICMPNE] #C -> #CO
      -> Immediate #C -> #D
      <- Switch[45483012] #B -> #C
===#Block D(size=1, flags=0)===
   0. goto BW
      -> UnconditionalJump[GOTO] #D -> #BW
      <- Immediate #C -> #D
===#Block BW(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 167592920)
      goto BV
   1. throw nullconst;
      -> TryCatch range: [BW...BV] -> BX ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #BW -> #BV
      <- UnconditionalJump[GOTO] #D -> #BW
===#Block BV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BW...BV] -> BX ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BW -> #BV
===#Block BX(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #BX -> #AC
      <- TryCatch range: [BW...BV] -> BX ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BW...BV] -> BX ([Ljava/lang/RuntimeException;])
===#Block CO(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -899468341)
      goto E
   1. goto CI
      -> UnconditionalJump[GOTO] #CO -> #CI
      -> ConditionalJump[IF_ICMPEQ] #CO -> #E
      <- ConditionalJump[IF_ICMPNE] #C -> #CO
===#Block E(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar11 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar11.getQUICK_BALL();
   3. goto BQ
      -> UnconditionalJump[GOTO] #E -> #BQ
      <- ConditionalJump[IF_ICMPEQ] #CO -> #E
===#Block BQ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 10332619)
      goto BP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BQ -> #BP
      -> TryCatch range: [BQ...BP] -> BR ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #E -> #BQ
===#Block BP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BQ...BP] -> BR ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BQ -> #BP
===#Block BR(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #BR -> #AE
      <- TryCatch range: [BQ...BP] -> BR ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BQ...BP] -> BR ([Ljava/lang/RuntimeException;])
===#Block F(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar5;
   2. lvar37 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.efdyklbpeajqtgq(), lvar46);
   3. lvar13 = lvar12.equals(lvar37);
   4. if (lvar13 != {1214021372 ^ lvar46})
      goto CM
      -> Immediate #F -> #G
      -> ConditionalJump[IF_ICMPNE] #F -> #CM
      <- Switch[45483070] #B -> #F
===#Block CM(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == 762517130)
      goto H
   1. goto CI
      -> UnconditionalJump[GOTO] #CM -> #CI
      -> ConditionalJump[IF_ICMPEQ] #CM -> #H
      <- ConditionalJump[IF_ICMPNE] #F -> #CM
===#Block H(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar14 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar14.getPREMIER_BALL();
   3. goto BB
      -> UnconditionalJump[GOTO] #H -> #BB
      <- ConditionalJump[IF_ICMPEQ] #CM -> #H
===#Block BB(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 185871359)
      goto BA
   1. throw nullconst;
      -> TryCatch range: [BB...BA] -> BC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #BB -> #BA
      <- UnconditionalJump[GOTO] #H -> #BB
===#Block BA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BB...BA] -> BC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BB -> #BA
===#Block BC(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #BC -> #AE
      <- TryCatch range: [BB...BA] -> BC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BB...BA] -> BC ([Ljava/lang/RuntimeException;])
===#Block G(size=1, flags=0)===
   0. goto AV
      -> UnconditionalJump[GOTO] #G -> #AV
      <- Immediate #F -> #G
===#Block AV(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 255871610)
      goto AU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AV -> #AU
      -> TryCatch range: [AV...AU] -> AW ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #G -> #AV
===#Block AU(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AV...AU] -> AW ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AV -> #AU
===#Block AW(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #AW -> #AC
      <- TryCatch range: [AV...AU] -> AW ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AV...AU] -> AW ([Ljava/lang/IllegalAccessException;])
===#Block I(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar15 = lvar5;
   2. lvar38 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.hgkdmuiepnqekqg(), lvar46);
   3. lvar16 = lvar15.equals(lvar38);
   4. if (lvar16 != {1394720432 ^ lvar46})
      goto CH
      -> ConditionalJump[IF_ICMPNE] #I -> #CH
      -> Immediate #I -> #J
      <- Switch[45483071] #B -> #I
===#Block J(size=1, flags=0)===
   0. goto AP
      -> UnconditionalJump[GOTO] #J -> #AP
      <- Immediate #I -> #J
===#Block AP(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 140999137)
      goto AO
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AP -> #AO
      -> TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #J -> #AP
===#Block AO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AP -> #AO
===#Block AQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #AQ -> #AC
      <- TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
===#Block CH(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -785958416)
      goto K
   1. goto CI
      -> UnconditionalJump[GOTO] #CH -> #CI
      -> ConditionalJump[IF_ICMPEQ] #CH -> #K
      <- ConditionalJump[IF_ICMPNE] #I -> #CH
===#Block K(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar17.getLUXURY_BALL();
   3. goto BK
      -> UnconditionalJump[GOTO] #K -> #BK
      <- ConditionalJump[IF_ICMPEQ] #CH -> #K
===#Block BK(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 102968955)
      goto BJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BK -> #BJ
      -> TryCatch range: [BK...BJ] -> BL ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #K -> #BK
===#Block BJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BK...BJ] -> BL ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BK -> #BJ
===#Block BL(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #BL -> #AE
      <- TryCatch range: [BK...BJ] -> BL ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BK...BJ] -> BL ([Ljava/lang/RuntimeException;])
===#Block O(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar5;
   2. lvar40 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.fufmafzfcwlfmxk(), lvar46);
   3. lvar22 = lvar21.equals(lvar40);
   4. if (lvar22 != {1596259404 ^ lvar46})
      goto CL
      -> Immediate #O -> #Q
      -> ConditionalJump[IF_ICMPNE] #O -> #CL
      <- Switch[45483065] #B -> #O
===#Block CL(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1840312247)
      goto P
   1. goto CI
      -> UnconditionalJump[GOTO] #CL -> #CI
      -> ConditionalJump[IF_ICMPEQ] #CL -> #P
      <- ConditionalJump[IF_ICMPNE] #O -> #CL
===#Block P(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar23.getMASTER_BALL();
   3. goto AG
      -> UnconditionalJump[GOTO] #P -> #AG
      <- ConditionalJump[IF_ICMPEQ] #CL -> #P
===#Block AG(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 66868004)
      goto AF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AG -> #AF
      -> TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #P -> #AG
===#Block AF(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AG -> #AF
===#Block AH(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #AH -> #AE
      <- TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
      <- TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
===#Block Q(size=1, flags=0)===
   0. goto AS
      -> UnconditionalJump[GOTO] #Q -> #AS
      <- Immediate #O -> #Q
===#Block AS(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 145469389)
      goto AR
   1. throw nullconst;
      -> TryCatch range: [AS...AR] -> AT ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AS -> #AR
      <- UnconditionalJump[GOTO] #Q -> #AS
===#Block AR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AS...AR] -> AT ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AS -> #AR
===#Block AT(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #AT -> #AC
      <- TryCatch range: [AS...AR] -> AT ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AS...AR] -> AT ([Ljava/lang/RuntimeException;])
===#Block R(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar24 = lvar5;
   2. lvar41 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.epjtqrdtqayqgvs(), lvar46);
   3. lvar25 = lvar24.equals(lvar41);
   4. if (lvar25 != {1803364152 ^ lvar46})
      goto CP
      -> Immediate #R -> #T
      -> ConditionalJump[IF_ICMPNE] #R -> #CP
      <- Switch[45483013] #B -> #R
===#Block CP(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == 1228163675)
      goto S
   1. goto CI
      -> UnconditionalJump[GOTO] #CP -> #CI
      -> ConditionalJump[IF_ICMPEQ] #CP -> #S
      <- ConditionalJump[IF_ICMPNE] #R -> #CP
===#Block S(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar26.getDUSK_BALL();
   3. goto BN
      -> UnconditionalJump[GOTO] #S -> #BN
      <- ConditionalJump[IF_ICMPEQ] #CP -> #S
===#Block BN(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 50722249)
      goto BM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BN -> #BM
      -> TryCatch range: [BN...BM] -> BO ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #S -> #BN
===#Block BM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BN...BM] -> BO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BN -> #BM
===#Block BO(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #BO -> #AE
      <- TryCatch range: [BN...BM] -> BO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BN...BM] -> BO ([Ljava/lang/RuntimeException;])
===#Block T(size=1, flags=0)===
   0. goto BT
      -> UnconditionalJump[GOTO] #T -> #BT
      <- Immediate #R -> #T
===#Block BT(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 240683849)
      goto BS
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BT -> #BS
      -> TryCatch range: [BT...BS] -> BU ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #T -> #BT
===#Block BS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BT...BS] -> BU ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BT -> #BS
===#Block BU(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #BU -> #AC
      <- TryCatch range: [BT...BS] -> BU ([Ljava/io/IOException;])
      <- TryCatch range: [BT...BS] -> BU ([Ljava/io/IOException;])
===#Block U(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = lvar5;
   2. lvar42 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.pvqtpbmzxaawgyr(), lvar46);
   3. lvar28 = lvar27.equals(lvar42);
   4. if (lvar28 != {1217857918 ^ lvar46})
      goto CN
      -> Immediate #U -> #W
      -> ConditionalJump[IF_ICMPNE] #U -> #CN
      <- Switch[45483064] #B -> #U
===#Block CN(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1646561408)
      goto V
   1. goto CI
      -> UnconditionalJump[GOTO] #CN -> #CI
      -> ConditionalJump[IF_ICMPEQ] #CN -> #V
      <- ConditionalJump[IF_ICMPNE] #U -> #CN
===#Block V(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar29.getTIMER_BALL();
   3. goto AJ
      -> UnconditionalJump[GOTO] #V -> #AJ
      <- ConditionalJump[IF_ICMPEQ] #CN -> #V
===#Block AJ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 159922713)
      goto AI
   1. throw nullconst;
      -> TryCatch range: [AJ...AI] -> AK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AJ -> #AI
      <- UnconditionalJump[GOTO] #V -> #AJ
===#Block AI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AJ...AI] -> AK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AJ -> #AI
===#Block AK(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #AK -> #AE
      <- TryCatch range: [AJ...AI] -> AK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AJ...AI] -> AK ([Ljava/lang/IllegalAccessException;])
===#Block W(size=1, flags=0)===
   0. goto CC
      -> UnconditionalJump[GOTO] #W -> #CC
      <- Immediate #U -> #W
===#Block CC(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 166061696)
      goto CB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CC -> #CB
      -> TryCatch range: [CC...CB] -> CD ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #W -> #CC
===#Block CB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CC...CB] -> CD ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CC -> #CB
===#Block CD(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #CD -> #AC
      <- TryCatch range: [CC...CB] -> CD ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CC...CB] -> CD ([Ljava/lang/IllegalAccessException;])
===#Block X(size=5, flags=0)===
   0. // Frame: locals[1] [java/lang/String] stack[0] []
   1. lvar31 = lvar5;
   2. lvar43 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.dmuhlmudvtqnhgp(), lvar46);
   3. lvar32 = lvar31.equals(lvar43);
   4. if (lvar32 != {1677238239 ^ lvar46})
      goto CJ
      -> Immediate #X -> #Z
      -> ConditionalJump[IF_ICMPNE] #X -> #CJ
      <- Switch[45483068] #B -> #X
===#Block CJ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1643735975)
      goto Y
   1. goto CI
      -> ConditionalJump[IF_ICMPEQ] #CJ -> #Y
      -> UnconditionalJump[GOTO] #CJ -> #CI
      <- ConditionalJump[IF_ICMPNE] #X -> #CJ
===#Block Y(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar33 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar33.getPOKE_BALL();
   3. goto BZ
      -> UnconditionalJump[GOTO] #Y -> #BZ
      <- ConditionalJump[IF_ICMPEQ] #CJ -> #Y
===#Block BZ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 236890671)
      goto BY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BZ -> #BY
      -> TryCatch range: [BZ...BY] -> CA ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #Y -> #BZ
===#Block BY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BZ...BY] -> CA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BZ -> #BY
===#Block CA(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #CA -> #AE
      <- TryCatch range: [BZ...BY] -> CA ([Ljava/io/IOException;])
      <- TryCatch range: [BZ...BY] -> CA ([Ljava/io/IOException;])
===#Block Z(size=1, flags=0)===
   0. goto BH
      -> UnconditionalJump[GOTO] #Z -> #BH
      <- Immediate #X -> #Z
===#Block BH(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 4572072)
      goto BG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BH -> #BG
      -> TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #Z -> #BH
===#Block BG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BH -> #BG
===#Block BI(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #BI -> #AC
      <- TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
===#Block AA(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar34 = lvar5;
   2. lvar44 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.tddtwlkzeopxwot(), lvar46);
   3. lvar35 = lvar34.equals(lvar44);
   4. if (lvar35 != {1935715589 ^ lvar46})
      goto CK
      -> ConditionalJump[IF_ICMPNE] #AA -> #CK
      -> Immediate #AA -> #AB
      <- Switch[45483059] #B -> #AA
===#Block AB(size=1, flags=0)===
   0. goto CF
      -> UnconditionalJump[GOTO] #AB -> #CF
      <- Immediate #AA -> #AB
===#Block CF(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 256629362)
      goto CE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CF -> #CE
      -> TryCatch range: [CF...CE] -> CG ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AB -> #CF
===#Block CE(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CF...CE] -> CG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CF -> #CE
===#Block CG(size=2, flags=0)===
   0. _consume(catch());
   1. goto AC
      -> UnconditionalJump[GOTO] #CG -> #AC
      <- TryCatch range: [CF...CE] -> CG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CF...CE] -> CG ([Ljava/lang/RuntimeException;])
===#Block AC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar10 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar10.getPOKE_BALL();
      -> Immediate #AC -> #AE
      <- UnconditionalJump[GOTO] #AT -> #AC
      <- UnconditionalJump[GOTO] #CD -> #AC
      <- UnconditionalJump[GOTO] #BI -> #AC
      <- UnconditionalJump[GOTO] #CG -> #AC
      <- DefaultSwitch #B -> #AC
      <- UnconditionalJump[GOTO] #AW -> #AC
      <- UnconditionalJump[GOTO] #BU -> #AC
      <- UnconditionalJump[GOTO] #AQ -> #AC
      <- UnconditionalJump[GOTO] #BF -> #AC
      <- UnconditionalJump[GOTO] #BX -> #AC
===#Block CK(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1401194669)
      goto AD
   1. goto CI
      -> ConditionalJump[IF_ICMPEQ] #CK -> #AD
      -> UnconditionalJump[GOTO] #CK -> #CI
      <- ConditionalJump[IF_ICMPNE] #AA -> #CK
===#Block CI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      <- UnconditionalJump[GOTO] #CM -> #CI
      <- UnconditionalJump[GOTO] #CH -> #CI
      <- UnconditionalJump[GOTO] #CL -> #CI
      <- UnconditionalJump[GOTO] #CQ -> #CI
      <- UnconditionalJump[GOTO] #CP -> #CI
      <- UnconditionalJump[GOTO] #CO -> #CI
      <- UnconditionalJump[GOTO] #CJ -> #CI
      <- UnconditionalJump[GOTO] #CN -> #CI
      <- UnconditionalJump[GOTO] #CK -> #CI
===#Block AD(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar36 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar36.getULTRA_BALL();
   3. goto AY
      -> UnconditionalJump[GOTO] #AD -> #AY
      <- ConditionalJump[IF_ICMPEQ] #CK -> #AD
===#Block AY(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 17628678)
      goto AX
   1. throw nullconst;
      -> TryCatch range: [AY...AX] -> AZ ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AY -> #AX
      <- UnconditionalJump[GOTO] #AD -> #AY
===#Block AX(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AY...AX] -> AZ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AY -> #AX
===#Block AZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto AE
      -> UnconditionalJump[GOTO] #AZ -> #AE
      <- TryCatch range: [AY...AX] -> AZ ([Ljava/io/IOException;])
      <- TryCatch range: [AY...AX] -> AZ ([Ljava/io/IOException;])
===#Block AE(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[1] [com/cobblemon/mod/common/pokeball/PokeBall]
   1. return lvar30;
      <- Immediate #AC -> #AE
      <- UnconditionalJump[GOTO] #BR -> #AE
      <- UnconditionalJump[GOTO] #CA -> #AE
      <- UnconditionalJump[GOTO] #AK -> #AE
      <- UnconditionalJump[GOTO] #AN -> #AE
      <- UnconditionalJump[GOTO] #AZ -> #AE
      <- UnconditionalJump[GOTO] #BO -> #AE
      <- UnconditionalJump[GOTO] #BC -> #AE
      <- UnconditionalJump[GOTO] #BL -> #AE
      <- UnconditionalJump[GOTO] #AH -> #AE
