{"input": "build/libs/AceDex-1.0.0.jar", "output": "obfuscated/AceDex-1.0.0-minimal-performance.jar", "libraries": ["libs/CobblemonBukkitEvent-1.0-SNAPSHOT (1).jar", "libs/PlaceholderAPI-2.11.6.jar", "libs/nmsSpigot.jar"], "exempts": ["cn.acebrand.acedex.AceDex", "cn.acebrand.acedex.AceDex.*", "cn.acebrand.acedex.command.*", "cn.acebrand.acedex.listener.*", "cn.acebrand.acedex.integration.*", "cn.acebrand.acedex.license.LicenseManager", "cn.acebrand.acedex.license.LicenseManager.*", "cn.acebrand.acedex.pokemon.PokemonDetector", "cn.acebrand.acedex.pokemon.PokemonDetector.*", "cn.acebrand.acedex.pokemon.PokemonNameMapping", "cn.acebrand.acedex.pokemon.PokemonNameMapping.*", "cn.acebrand.acedex.pokemon.generation.*", "cn.acebrand.acedex.config.*", "cn.acebrand.acedex.gui.*", "cn.acebrand.acedex.data.*", "cn.acebrand.acedex.util.*", "**$Companion", "**$Companion.*", "**$DefaultConstructorMarker", "**$DefaultConstructorMarker.*", "**$WhenMappings", "**$WhenMappings.*", "<init>(*)", "<clinit>(*)", "plugin.yml", "META-INF/**", "org.bukkit.**", "net.md_5.**", "io.papermc.**", "me.clip.**", "com.cobblemon.**", "org.jetbrains.**", "kotlin.**", "kotlin.jvm.internal.**"], "transformers": {"flow": {"enabled": false}, "string": {"enabled": false}, "number": {"enabled": false}, "reference": {"enabled": false}, "outlining": {"enabled": false}, "ahegao": {"enabled": true, "intensity": 1, "config": {"renameFields": false, "renameMethods": false, "renameClasses": true}}, "condition": {"enabled": false}, "bogus": {"enabled": false}, "switch": {"enabled": false}, "flatten": {"enabled": false}, "native": {"enabled": false}, "pure": {"enabled": false}}, "runtime": {"jvm": ["-Xmx4G", "-Xms2G"]}, "debug": false, "phantom": true, "verify": false, "computeFrames": false}