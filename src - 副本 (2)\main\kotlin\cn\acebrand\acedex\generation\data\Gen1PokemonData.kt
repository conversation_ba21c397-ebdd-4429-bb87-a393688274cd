/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第一代精灵数据 (关都地区)
 * 包含全国图鉴编号 1-151 的精灵数据
 */
object Gen1PokemonData {
    val data = mapOf(
        // 001-010
        "bulbasaur" to PokemonData(1, "草/毒", "种子精灵", "male", "丛林, 热带岛屿"),
        "ivysaur" to PokemonData(2, "草/毒", "种子精灵", "male", "丛林, 热带岛屿"),
        "venusaur" to PokemonData(3, "草/毒", "种子精灵", "male", "丛林, 热带岛屿"),
        "charmander" to PokemonData(4, "火", "蜥蜴精灵", "male", "丘陵, 火山"),
        "charmeleon" to PokemonData(5, "火", "火焰精灵", "male", "丘陵, 火山"),
        "charizard" to PokemonData(6, "火/飞行", "火焰精灵", "male", "丘陵, 火山"),
        "squirtle" to PokemonData(7, "水", "小龟精灵", "male", "淡水, 丘陵, 丛林"),
        "wartortle" to PokemonData(8, "水", "龟精灵", "male", "淡水, 丘陵, 丛林"),
        "blastoise" to PokemonData(9, "水", "甲壳精灵", "male", "淡水, 丘陵, 丛林"),
        "caterpie" to PokemonData(10, "虫", "芋虫精灵", "male", "森林, 平原"),
        
        // 011-020
        "metapod" to PokemonData(11, "虫", "蛹精灵", "male", "森林, 平原"),
        "butterfree" to PokemonData(12, "虫/飞行", "蝴蝶精灵", "male", "温带, 花田"),
        "weedle" to PokemonData(13, "虫/毒", "毛虫精灵", "male", "森林, 丛林"),
        "kakuna" to PokemonData(14, "虫/毒", "蛹精灵", "male", "森林, 丛林"),
        "beedrill" to PokemonData(15, "虫/毒", "毒蜂精灵", "male", "森林, 丛林"),
        "pidgey" to PokemonData(16, "一般/飞行", "小鸟精灵", "male", "温带, 天空"),
        "pidgeotto" to PokemonData(17, "一般/飞行", "鸟精灵", "male", "温带, 天空"),
        "pidgeot" to PokemonData(18, "一般/飞行", "鸟精灵", "male", "温带, 天空"),
        "rattata" to PokemonData(19, "一般", "鼠精灵", "male", "草原, 地下"),
        "raticate" to PokemonData(20, "一般", "鼠精灵", "male", "草原, 地下"),
        
        // 021-030
        "spearow" to PokemonData(21, "一般/飞行", "小鸟精灵", "male", "恶地, 热带草原"),
        "fearow" to PokemonData(22, "一般/飞行", "嘴钻精灵", "male", "恶地, 热带草原"),
        "ekans" to PokemonData(23, "毒", "蛇精灵", "male", "干旱地区"),
        "arbok" to PokemonData(24, "毒", "眼镜蛇精灵", "male", "干旱地区"),
        "pikachu" to PokemonData(25, "电", "鼠精灵", "male", "森林, 海滩"),
        "raichu" to PokemonData(26, "电", "鼠精灵", "male", "森林, 海滩"),
        "sandshrew" to PokemonData(27, "地面", "鼠精灵", "male", "恶地, 沙漠"),
        "sandslash" to PokemonData(28, "地面", "鼠精灵", "male", "恶地, 沙漠"),
        "nidoranf" to PokemonData(29, "毒", "毒针精灵", "female", "热带草原"),
        "nidorina" to PokemonData(30, "毒", "毒针精灵", "female", "热带草原"),
        
        // 031-040
        "nidoqueen" to PokemonData(31, "毒/地面", "钻锥精灵", "female", "热带草原"),
        "nidoranm" to PokemonData(32, "毒", "毒针精灵", "male", "热带草原"),
        "nidorino" to PokemonData(33, "毒", "毒针精灵", "male", "热带草原"),
        "nidoking" to PokemonData(34, "毒/地面", "钻锥精灵", "male", "热带草原"),
        "clefairy" to PokemonData(35, "妖精", "妖精精灵", "female", "滴水石洞, 丘陵"),
        "clefable" to PokemonData(36, "妖精", "妖精精灵", "female", "滴水石洞, 丘陵"),
        "vulpix" to PokemonData(37, "火", "狐狸精灵", "female", "森林, 针叶林"),
        "ninetales" to PokemonData(38, "火", "狐狸精灵", "female", "森林, 针叶林"),
        "jigglypuff" to PokemonData(39, "一般/妖精", "气球精灵", "female", "花田, 平原"),
        "wigglytuff" to PokemonData(40, "一般/妖精", "气球精灵", "female", "花田, 平原"),
        
        // 041-050
        "zubat" to PokemonData(41, "毒/飞行", "蝙蝠精灵", "male", "森林, 沼泽, 洞穴"),
        "golbat" to PokemonData(42, "毒/飞行", "蝙蝠精灵", "male", "森林, 沼泽, 洞穴"),
        "oddish" to PokemonData(43, "草/毒", "杂草精灵", "male", "丛林, 温带"),
        "gloom" to PokemonData(44, "草/毒", "杂草精灵", "male", "丛林, 温带"),
        "vileplume" to PokemonData(45, "草/毒", "花精灵", "male", "丛林, 温带"),
        "paras" to PokemonData(46, "虫/草", "蘑菇精灵", "male", "繁茂洞穴, 蘑菇岛"),
        "parasect" to PokemonData(47, "虫/草", "蘑菇精灵", "male", "繁茂洞穴, 蘑菇岛"),
        "venonat" to PokemonData(48, "虫/毒", "昆虫精灵", "male", "阴森森林, 沼泽"),
        "venomoth" to PokemonData(49, "虫/毒", "毒蛾精灵", "male", "阴森森林, 沼泽"),
        "diglett" to PokemonData(50, "地面", "鼹鼠精灵", "male", "地下"),
        
        // 051-060
        "dugtrio" to PokemonData(51, "地面", "鼹鼠精灵", "male", "地下"),
        "meowth" to PokemonData(52, "一般", "妖怪猫精灵", "male", "城市, 村庄, 干旱地区"),
        "persian" to PokemonData(53, "一般", "暹罗猫精灵", "male", "城市, 村庄, 干旱地区"),
        "psyduck" to PokemonData(54, "水", "鸭精灵", "male", "沼泽, 淡水, 森林, 草原"),
        "golduck" to PokemonData(55, "水", "鸭精灵", "male", "沼泽, 淡水, 森林, 草原"),
        "mankey" to PokemonData(56, "格斗", "猪猴精灵", "male", "丘陵, 丛林"),
        "primeape" to PokemonData(57, "格斗", "猪猴精灵", "male", "丘陵, 丛林"),
        "growlithe" to PokemonData(58, "火", "小狗精灵", "male", "城市, 村庄"),
        "arcanine" to PokemonData(59, "火", "传说精灵", "male", "城市, 村庄"),
        "poliwag" to PokemonData(60, "水", "蝌蚪精灵", "male", "沼泽, 淡水, 丘陵, 丛林"),
        
        // 061-070
        "poliwhirl" to PokemonData(61, "水", "蝌蚪精灵", "male", "沼泽, 淡水, 丘陵, 丛林"),
        "poliwrath" to PokemonData(62, "水/格斗", "蝌蚪精灵", "male", "沼泽, 淡水, 丘陵, 丛林"),
        "abra" to PokemonData(63, "超能力", "念力精灵", "male", "丘陵, 温带, 魔法森林"),
        "kadabra" to PokemonData(64, "超能力", "念力精灵", "male", "丘陵, 温带, 魔法森林"),
        "alakazam" to PokemonData(65, "超能力", "念力精灵", "male", "丘陵, 温带, 魔法森林"),
        "machop" to PokemonData(66, "格斗", "怪力精灵", "male", "丘陵"),
        "machoke" to PokemonData(67, "格斗", "怪力精灵", "male", "丘陵"),
        "machamp" to PokemonData(68, "格斗", "怪力精灵", "male", "丘陵"),
        "bellsprout" to PokemonData(69, "草/毒", "花精灵", "male", "丛林"),
        "weepinbell" to PokemonData(70, "草/毒", "捕蝇精灵", "male", "丛林"),
        
        // 071-080
        "victreebel" to PokemonData(71, "草/毒", "捕蝇精灵", "male", "丛林"),
        "tentacool" to PokemonData(72, "水/毒", "水母精灵", "male", "深海, 海洋"),
        "tentacruel" to PokemonData(73, "水/毒", "水母精灵", "male", "深海, 海洋"),
        "geodude" to PokemonData(74, "岩石/地面", "岩石精灵", "male", "山地, 地下"),
        "graveler" to PokemonData(75, "岩石/地面", "岩石精灵", "male", "山地, 地下"),
        "golem" to PokemonData(76, "岩石/地面", "巨石精灵", "male", "山地, 地下"),
        "ponyta" to PokemonData(77, "火", "火马精灵", "male", "草原, 下界荒地"),
        "rapidash" to PokemonData(78, "火", "火马精灵", "male", "草原, 下界荒地"),
        "slowpoke" to PokemonData(79, "水/超能力", "呆呆精灵", "male", "海滩, 河流, 淡水"),
        "slowbro" to PokemonData(80, "水/超能力", "寄居蟹精灵", "male", "海滩, 河流, 淡水"),
        
        // 081-090
        "magnemite" to PokemonData(81, "电/钢", "磁铁精灵", "genderless", "城市, 村庄"),
        "magneton" to PokemonData(82, "电/钢", "磁铁精灵", "genderless", "城市, 村庄"),
        "farfetchd" to PokemonData(83, "一般/飞行", "野鸭精灵", "male", "森林, 野外"),
        "doduo" to PokemonData(84, "一般/飞行", "双头鸟精灵", "male", "热带草原"),
        "dodrio" to PokemonData(85, "一般/飞行", "三头鸟精灵", "male", "热带草原"),
        "seel" to PokemonData(86, "水", "海狮精灵", "male", "冰冻海洋"),
        "dewgong" to PokemonData(87, "水/冰", "海狮精灵", "male", "冰冻海洋"),
        "grimer" to PokemonData(88, "毒", "污泥精灵", "male", "沼泽, 城市"),
        "muk" to PokemonData(89, "毒", "污泥精灵", "male", "沼泽, 城市"),
        "shellder" to PokemonData(90, "水", "双壳精灵", "male", "寒冷海洋, 冰冻海洋"),
        
        // 091-100
        "cloyster" to PokemonData(91, "水/冰", "双壳精灵", "male", "寒冷海洋, 冰冻海洋"),
        "gastly" to PokemonData(92, "幽灵/毒", "气体精灵", "male", "阴森森林, 黑暗森林"),
        "haunter" to PokemonData(93, "幽灵/毒", "气体精灵", "male", "阴森森林, 黑暗森林"),
        "gengar" to PokemonData(94, "幽灵/毒", "影子精灵", "male", "阴森森林, 黑暗森林"),
        "onix" to PokemonData(95, "岩石/地面", "岩蛇精灵", "male", "地下, 山地"),
        "drowzee" to PokemonData(96, "超能力", "催眠精灵", "male", "城市, 村庄"),
        "hypno" to PokemonData(97, "超能力", "催眠精灵", "male", "城市, 村庄"),
        "krabby" to PokemonData(98, "水", "河蟹精灵", "male", "海岸, 海洋"),
        "kingler" to PokemonData(99, "水", "钳子精灵", "male", "海岸, 海洋"),
        "voltorb" to PokemonData(100, "电", "球精灵", "genderless", "城市, 村庄"),
        
        // 101-110
        "electrode" to PokemonData(101, "电", "球精灵", "genderless", "城市, 村庄"),
        "exeggcute" to PokemonData(102, "草/超能力", "蛋精灵", "male", "丛林, 热带草原, 海滩"),
        "exeggutor" to PokemonData(103, "草/超能力", "椰蛋树精灵", "male", "丛林, 热带草原, 海滩"),
        "cubone" to PokemonData(104, "地面", "孤独精灵", "male", "恶地, 沙漠, 火山"),
        "marowak" to PokemonData(105, "地面", "骨头精灵", "male", "恶地, 沙漠, 火山"),
        "hitmonlee" to PokemonData(106, "格斗", "踢腿精灵", "male", "丘陵"),
        "hitmonchan" to PokemonData(107, "格斗", "拳击精灵", "male", "丘陵"),
        "lickitung" to PokemonData(108, "一般", "舔舌精灵", "male", "草原"),
        "koffing" to PokemonData(109, "毒", "毒气精灵", "male", "城市, 村庄"),
        "weezing" to PokemonData(110, "毒", "毒气精灵", "male", "城市, 村庄"),
        
        // 111-120
        "rhyhorn" to PokemonData(111, "地面/岩石", "尖刺精灵", "male", "山地, 热带草原"),
        "rhydon" to PokemonData(112, "地面/岩石", "钻锥精灵", "male", "山地, 热带草原"),
        "chansey" to PokemonData(113, "一般", "蛋精灵", "female", "城市, 村庄"),
        "tangela" to PokemonData(114, "草", "藤蔓精灵", "male", "丛林"),
        "kangaskhan" to PokemonData(115, "一般", "亲子精灵", "female", "热带草原"),
        "horsea" to PokemonData(116, "水", "龙精灵", "male", "温暖海洋"),
        "seadra" to PokemonData(117, "水", "龙精灵", "male", "温暖海洋"),
        "goldeen" to PokemonData(118, "水", "金鱼精灵", "female", "河流, 淡水"),
        "seaking" to PokemonData(119, "水", "金鱼精灵", "male", "河流, 淡水"),
        "staryu" to PokemonData(120, "水", "星形精灵", "genderless", "海岸, 海洋"),
        
        // 121-130
        "starmie" to PokemonData(121, "水/超能力", "神秘精灵", "genderless", "海岸, 海洋"),
        "mrmime" to PokemonData(122, "超能力/妖精", "屏障精灵", "male", "城市, 村庄"),
        "scyther" to PokemonData(123, "虫/飞行", "螳螂精灵", "male", "森林, 丘陵, 丛林"),
        "jynx" to PokemonData(124, "冰/超能力", "人形精灵", "female", "冰冻地区, 城市, 村庄"),
        "electabuzz" to PokemonData(125, "电", "电击精灵", "male", "丘陵, 平原"),
        "magmar" to PokemonData(126, "火", "喷火精灵", "male", "丘陵, 火山"),
        "pinsir" to PokemonData(127, "虫", "锹形虫精灵", "male", "丛林, 热带岛屿"),
        "tauros" to PokemonData(128, "一般", "野牛精灵", "male", "草原, 高地"),
        "magikarp" to PokemonData(129, "水", "鱼精灵", "male", "淡水, 海洋"),
        "gyarados" to PokemonData(130, "水/飞行", "凶恶精灵", "male", "淡水, 海洋"),
        
        // 131-140
        "lapras" to PokemonData(131, "水/冰", "乘骑精灵", "male", "冰冻海洋"),
        "ditto" to PokemonData(132, "一般", "变身精灵", "genderless", "平原, 温带"),
        "eevee" to PokemonData(133, "一般", "进化精灵", "male", "温带, 森林"),
        "vaporeon" to PokemonData(134, "水", "泡沫精灵", "male", "淡水, 海洋"),
        "jolteon" to PokemonData(135, "电", "雷精灵", "male", "平原, 草原"),
        "flareon" to PokemonData(136, "火", "火焰精灵", "male", "火山, 干旱地区"),
        "porygon" to PokemonData(137, "一般", "虚拟精灵", "genderless", "城市"),
        "omanyte" to PokemonData(138, "岩石/水", "螺旋精灵", "male", "海洋"),
        "omastar" to PokemonData(139, "岩石/水", "螺旋精灵", "male", "海洋"),
        "kabuto" to PokemonData(140, "岩石/水", "甲壳精灵", "male", "海洋"),
        
        // 141-151
        "kabutops" to PokemonData(141, "岩石/水", "甲壳精灵", "male", "海洋"),
        "aerodactyl" to PokemonData(142, "岩石/飞行", "化石翼龙精灵", "male", "山地"),
        "snorlax" to PokemonData(143, "一般", "瞌睡精灵", "male", "森林, 山地"),
        "articuno" to PokemonData(144, "冰/飞行", "冰冻精灵", "genderless", "冰冻地区, 雪山"),
        "zapdos" to PokemonData(145, "电/飞行", "电击精灵", "genderless", "山地, 高原"),
        "moltres" to PokemonData(146, "火/飞行", "火焰精灵", "genderless", "火山, 恶地"),
        "dratini" to PokemonData(147, "龙", "龙精灵", "male", "深海, 海洋"),
        "dragonair" to PokemonData(148, "龙", "龙精灵", "male", "深海, 海洋"),
        "dragonite" to PokemonData(149, "龙/飞行", "龙精灵", "male", "深海, 海洋"),
        "mewtwo" to PokemonData(150, "超能力", "基因精灵", "genderless", "洞穴"),
        "mew" to PokemonData(151, "超能力", "新种精灵", "genderless", "丛林")
    )
}
