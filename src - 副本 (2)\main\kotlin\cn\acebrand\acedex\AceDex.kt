/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex

import cn.acebrand.acedex.command.AceDexCommand
import cn.acebrand.acedex.listener.PokemonCaptureListener
import cn.acebrand.acedex.config.AceDexConfig
import cn.acebrand.acedex.config.PremiumRewardConfig
import cn.acebrand.acedex.data.PlayerDataManager
import cn.acebrand.acedex.generation.GenerationManager
import cn.acebrand.acedex.gui.DexMainGui
import cn.acebrand.acedex.gui.PremiumRewardGui
import cn.acebrand.acedex.gui.PokemonItemCreator
import cn.acebrand.acedex.gui.PokemonModelPreloader
import cn.acebrand.acedex.gui.AsyncGuiManager
import cn.acebrand.acedex.gui.GuiButtonCacheManager
import cn.acebrand.acedex.gui.PokeBallItemCreator
import cn.acebrand.acedex.integration.PlaceholderAPIExpansion
import cn.acebrand.acedex.license.LicenseManager
import cn.acebrand.acedex.listener.CobblemonEventListener
import cn.acebrand.acedex.listener.CobblemonListener
import cn.acebrand.acedex.pokemon.PokemonDetector
import cn.acebrand.acedex.reward.RewardManager
import org.bukkit.plugin.java.JavaPlugin

/**
 * AceDex 主类
 * 
 * 精灵图鉴插件的入口点，负责初始化所有模块和管理插件生命周期
 */
class AceDex : JavaPlugin() {
    
    companion object {
        lateinit var instance: AceDex
            private set
        
        const val PLUGIN_NAME = "AceDex"
        const val VERSION = "1.0.0"
    }
    
    // 配置管理器
    lateinit var config: AceDexConfig
        private set

    // 付费奖励配置管理器
    lateinit var premiumRewardConfig: PremiumRewardConfig
        private set
    
    // 许可证管理器
    lateinit var licenseManager: LicenseManager
        private set
        
    // 世代管理器
    lateinit var generationManager: GenerationManager
        private set
    
    // 奖励管理器
    lateinit var rewardManager: RewardManager
        private set

    // 精灵检测器
    lateinit var pokemonDetector: PokemonDetector
        private set

    // 数据存储管理器
    lateinit var dataStorage: PlayerDataManager
        private set

    // GUI管理器
    lateinit var mainGui: DexMainGui
        private set

    // 付费奖励GUI管理器
    lateinit var premiumRewardGui: PremiumRewardGui
        private set

    // 精灵物品创建器
    lateinit var pokemonItemCreator: PokemonItemCreator
        private set

    // 精灵球物品创建器
    lateinit var pokeBallItemCreator: PokeBallItemCreator
        private set

    // 精灵模型预加载管理器
    lateinit var pokemonModelPreloader: PokemonModelPreloader
        private set

    // 异步GUI管理器
    lateinit var asyncGuiManager: AsyncGuiManager
        private set

    // GUI按钮缓存管理器
    lateinit var guiButtonCacheManager: GuiButtonCacheManager
        private set

    // Cobblemon事件监听器
    lateinit var cobblemonEventListener: CobblemonEventListener
        private set

    // 精灵捕获监听器
    lateinit var pokemonCaptureListener: PokemonCaptureListener
        private set

    // PlaceholderAPI扩展
    private var placeholderExpansion: PlaceholderAPIExpansion? = null
    
    override fun onEnable() {
        instance = this
        
        logger.info("正在启用 $PLUGIN_NAME v$VERSION...")
        
        try {
            // 检查依赖
            if (!checkDependencies()) {
                logger.severe("缺少必要的依赖，插件将被禁用")
                server.pluginManager.disablePlugin(this)
                return
            }
            
            // 初始化配置
            initializeConfig()
            
            // 初始化许可证系统
            logger.info("╔══════════════════════════════════════════════════════════════╗")
            logger.info("║                    🔐 AceBrand 许可证系统 🔐                 ║")
            logger.info("╠══════════════════════════════════════════════════════════════╣")
            logger.info("║  🚀 正在启动 AceBrand 许可证验证...                          ║")
            logger.info("║  🌐 验证服务器: cn.AceBrand.com                              ║")
            logger.info("║  📞 技术支持 QQ: 337871509                                   ║")
            logger.info("╚══════════════════════════════════════════════════════════════╝")

            if (!initializeLicense()) {
                return
            }
            
            // 初始化管理器
            initializeManagers()
            
            // 注册命令
            registerCommands()
            
            // 注册事件监听器
            registerListeners()
            
            logger.info("$PLUGIN_NAME v$VERSION 启用成功！")
            
        } catch (e: Exception) {
            logger.severe("$PLUGIN_NAME 启用失败: ${e.message}")
            e.printStackTrace()
            server.pluginManager.disablePlugin(this)
        }
    }
    
    override fun onDisable() {
        logger.info("正在禁用 $PLUGIN_NAME...")

        try {
            // 关闭许可证管理器
            if (::licenseManager.isInitialized) {
                licenseManager.shutdown()
            }
            
            // 注销PlaceholderAPI扩展
            placeholderExpansion?.unregister()

            // 保存奖励数据
            if (::rewardManager.isInitialized) {
                rewardManager.saveRewardData()
                logger.info("奖励数据已保存")
            }

            // 保存精灵物品缓存到文件
            if (::pokemonItemCreator.isInitialized) {
                pokemonItemCreator.saveCache()
                logger.info("精灵物品缓存已保存")
            }

            // 保存GUI按钮缓存
            if (::guiButtonCacheManager.isInitialized) {
                // GUI按钮缓存不需要特殊保存，因为它们是从配置文件生成的
                logger.info("GUI按钮缓存已清理")
            }

            // 关闭精灵模型预加载管理器
            if (::pokemonModelPreloader.isInitialized) {
                pokemonModelPreloader.shutdown()
                logger.info("精灵模型预加载管理器已关闭")
            }

            // 关闭异步GUI管理器
            if (::asyncGuiManager.isInitialized) {
                asyncGuiManager.shutdown()
                logger.info("异步GUI管理器已关闭")
            }

            // 保存配置
            if (::config.isInitialized) {
                config.save()
            }

            logger.info("$PLUGIN_NAME 已成功禁用")
        } catch (e: Exception) {
            logger.severe("禁用插件时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 检查依赖
     */
    private fun checkDependencies(): Boolean {
        // 在Arclight环境下，Cobblemon作为模组运行，不是插件
        // 我们通过检查类是否存在来判断Cobblemon是否可用
        return try {
            Class.forName("com.cobblemon.mod.common.Cobblemon")
            logger.info("检测到 Cobblemon 模组已加载")
            true
        } catch (e: ClassNotFoundException) {
            logger.warning("未检测到 Cobblemon 模组，某些功能可能无法正常工作")
            // 即使没有Cobblemon也允许插件运行，只是功能受限
            true
        }
    }
    
    /**
     * 初始化配置
     */
    private fun initializeConfig() {
        config = AceDexConfig(this)
        config.load()
        logger.info("配置文件加载完成")

        // 初始化付费奖励配置
        premiumRewardConfig = PremiumRewardConfig(this)
        premiumRewardConfig.initialize()
        logger.info("付费奖励配置文件加载完成")
    }
    
    /**
     * 初始化许可证系统
     */
    private fun initializeLicense(): Boolean {
        return try {
            // 检查许可证密钥是否为空
            if (config.licenseKey.isBlank()) {
                logger.severe("╔══════════════════════════════════════════════════════════════╗")
                logger.severe("║                    🚫 许可证验证失败 🚫                      ║")
                logger.severe("╠══════════════════════════════════════════════════════════════╣")
                logger.severe("║  ❌ 错误：未配置许可证密钥！                                  ║")
                logger.severe("║                                                              ║")
                logger.severe("║  📝 解决方案：                                               ║")
                logger.severe("║     请在 config.yml 中的 license-key 字段填写您的许可证密钥  ║")
                logger.severe("║                                                              ║")
                logger.severe("║  📋 示例配置：                                               ║")
                logger.severe("║     license-key: \"857ff611-618f-40db-8fe2-291b388f0cf0\"     ║")
                logger.severe("║                                                              ║")
                logger.severe("║  ⚠️  插件将被禁用，直到配置有效的许可证密钥                   ║")
                logger.severe("╚══════════════════════════════════════════════════════════════╝")
                server.pluginManager.disablePlugin(this)
                return false
            }

            // 初始化许可证管理器
            licenseManager = LicenseManager(this)
            val isValid = licenseManager.validateLicenseKey(config.licenseKey)

            if (!isValid) {
                logger.severe("╔══════════════════════════════════════════════════════════════╗")
                logger.severe("║                    🚫 许可证验证失败 🚫                      ║")
                logger.severe("╠══════════════════════════════════════════════════════════════╣")
                logger.severe("║  ❌ 错误：提供的许可证密钥无效或已过期                        ║")
                logger.severe("║                                                              ║")
                logger.severe("║  🔍 可能的原因：                                             ║")
                logger.severe("║     • 许可证密钥输入错误                                     ║")
                logger.severe("║     • 许可证已过期                                           ║")
                logger.severe("║     • 网络连接问题                                           ║")
                logger.severe("║     • 服务器无法访问 AceBrand 许可证服务                     ║")
                logger.severe("║                                                              ║")
                logger.severe("║  📞 联系支持：                                               ║")
                logger.severe("║     QQ: 337871509 (AceBrand 技术支持)                       ║")
                logger.severe("║                                                              ║")
                logger.severe("║  ⚠️  插件将被禁用                                            ║")
                logger.severe("╚══════════════════════════════════════════════════════════════╝")
                server.pluginManager.disablePlugin(this)
                return false
            }

            logger.info("╔══════════════════════════════════════════════════════════════╗")
            logger.info("║                    ✅ 许可证验证成功 ✅                      ║")
            logger.info("╠══════════════════════════════════════════════════════════════╣")
            logger.info("║  🎉 恭喜！您的许可证已通过 AceBrand 服务器验证                ║")
            logger.info("║  🔒 插件功能已解锁，可以正常使用                              ║")
            logger.info("║  💓 心跳监控已启动，每15分钟自动验证                          ║")
            logger.info("╚══════════════════════════════════════════════════════════════╝")
            true

        } catch (e: Exception) {
            logger.severe("╔══════════════════════════════════════════════════════════════╗")
            logger.severe("║                    💥 系统初始化错误 💥                      ║")
            logger.severe("╠══════════════════════════════════════════════════════════════╣")
            logger.severe("║  ❌ 初始化许可证系统时发生错误: ${e.message}")
            logger.severe("║                                                              ║")
            logger.severe("║  🔧 可能的解决方案：                                         ║")
            logger.severe("║     • 检查网络连接                                           ║")
            logger.severe("║     • 重启服务器                                             ║")
            logger.severe("║     • 联系技术支持 QQ: 337871509                             ║")
            logger.severe("║                                                              ║")
            logger.severe("║  ⚠️  插件将被禁用                                            ║")
            logger.severe("╚══════════════════════════════════════════════════════════════╝")
            e.printStackTrace()
            server.pluginManager.disablePlugin(this)
            false
        }
    }
    
    /**
     * 初始化管理器
     */
    private fun initializeManagers() {
        // 按依赖顺序初始化管理器
        generationManager = GenerationManager(this)
        rewardManager = RewardManager(this)
        dataStorage = PlayerDataManager(this)
        pokemonDetector = PokemonDetector(this)

        // 初始化精灵模型预加载管理器（在精灵物品创建器之前）
        pokemonModelPreloader = PokemonModelPreloader(this)

        pokemonItemCreator = PokemonItemCreator(this)
        pokeBallItemCreator = PokeBallItemCreator(this)

        // 初始化GUI按钮缓存管理器（在主GUI之前）
        guiButtonCacheManager = GuiButtonCacheManager(this)

        // 初始化异步GUI管理器（在主GUI之前）
        asyncGuiManager = AsyncGuiManager(this)

        mainGui = DexMainGui(this)
        premiumRewardGui = PremiumRewardGui(this)
        cobblemonEventListener = CobblemonEventListener(this)
        pokemonCaptureListener = PokemonCaptureListener(this)

        // 初始化基础管理器
        generationManager.initialize()
        rewardManager.initialize()

        // 初始化付费菜单
        premiumRewardGui.initialize()

        // 启动精灵模型预加载（异步）
        logger.info("启动精灵模型预加载...")
        pokemonModelPreloader.startPreloading().thenRun {
            logger.info("精灵模型预加载完成！")

            // 精灵模型预加载完成后，启动GUI按钮缓存预加载
            logger.info("启动GUI按钮缓存预加载...")
            guiButtonCacheManager.preloadAllButtons()

        }.exceptionally { throwable ->
            logger.severe("精灵模型预加载失败: ${throwable.message}")
            throwable.printStackTrace()
            null
        }

        // 注册Cobblemon事件监听器
        cobblemonEventListener.registerEvents()

        // 调试Cobblemon API
        logger.info("正在检查Cobblemon API...")
        cn.acebrand.acedex.util.CobblemonApiHelper.debugCobblemonStorage()

        // 注册PlaceholderAPI扩展
        initializePlaceholderAPI()

        logger.info("管理器初始化完成")
    }
    
    /**
     * 注册命令
     */
    private fun registerCommands() {
        getCommand("acedex")?.setExecutor(AceDexCommand(this))
        getCommand("dex")?.setExecutor(AceDexCommand(this))

        logger.info("命令注册完成")
    }
    
    /**
     * 注册事件监听器
     */
    private fun registerListeners() {
        server.pluginManager.registerEvents(CobblemonListener(this), this)
        server.pluginManager.registerEvents(pokemonCaptureListener, this)

        // 启动数据文件维护任务（每小时检查一次数据文件状态）
        server.scheduler.runTaskTimerAsynchronously(this, Runnable {
            val files = pokemonDetector.getAllPlayerDataFiles()
            logger.info("当前有 ${files.size} 个玩家数据文件")
        }, 72000L, 72000L) // 1小时 = 72000 ticks

        // 启动数据变化检测任务（每30秒检测一次打开图鉴界面的玩家）
        server.scheduler.runTaskTimerAsynchronously(this, Runnable {
            // 只检测当前打开图鉴界面的玩家
            server.onlinePlayers.filter { player ->
                val currentGui = mainGui.openGuis[player]
                currentGui == "main" || currentGui?.startsWith("generation:") == true
            }.forEach { player ->
                pokemonDetector.checkAndUpdatePlayerData(player)
            }
        }, 600L, 600L) // 30秒 = 600 ticks

        logger.info("事件监听器注册完成")
    }

    /**
     * 初始化PlaceholderAPI扩展
     */
    private fun initializePlaceholderAPI() {
        if (server.pluginManager.getPlugin("PlaceholderAPI") != null) {
            try {
                placeholderExpansion = PlaceholderAPIExpansion(this)
                if (placeholderExpansion!!.register()) {
                    logger.info("PlaceholderAPI 扩展注册成功")
                } else {
                    logger.warning("PlaceholderAPI 扩展注册失败")
                }
            } catch (e: Exception) {
                logger.warning("注册 PlaceholderAPI 扩展时发生错误: ${e.message}")
            }
        } else {
            logger.info("未检测到 PlaceholderAPI，跳过占位符注册")
        }
    }

    /**
     * 重新加载插件
     */
    fun reload() {
        try {
            logger.info("正在重新加载 $PLUGIN_NAME...")

            // 清理精灵物品缓存
            pokemonItemCreator.clearItemCache()

            // 清理GUI按钮缓存
            if (::guiButtonCacheManager.isInitialized) {
                guiButtonCacheManager.clearCache()
            }

            // 重新加载配置
            config.reload()
            
            // 重新验证许可证
            if (::licenseManager.isInitialized) {
                if (config.licenseKey.isBlank()) {
                    logger.severe("许可证密钥为空，重新加载失败")
                    return
                }

                if (!licenseManager.validateLicenseKey(config.licenseKey)) {
                    logger.severe("许可证验证失败，重新加载失败")
                    return
                }
            }

            // 重新初始化管理器
            generationManager.reload()
            rewardManager.reload()

            // 重新预加载GUI按钮缓存
            if (::guiButtonCacheManager.isInitialized) {
                logger.info("重新预加载GUI按钮缓存...")
                guiButtonCacheManager.preloadAllButtons()
            }

            // 重新预加载付费菜单按钮缓存
            if (::premiumRewardGui.isInitialized) {
                logger.info("重新预加载付费菜单按钮缓存...")
                premiumRewardGui.initialize()
            }

            // 延迟刷新所有正在查看菜单的玩家界面，确保缓存已重新加载
            if (::mainGui.isInitialized) {
                logger.info("刷新所有在线玩家的菜单界面...")
                server.scheduler.runTaskLater(this, Runnable {
                    server.onlinePlayers.forEach { player ->
                        if (mainGui.isPlayerInGui(player)) {
                            // 强制清除所有相关缓存
                            mainGui.clearMainInventoryCache(player)
                            mainGui.clearGenerationInventoryCache(player)
                            // 刷新界面
                            mainGui.refreshCurrentGui(player)
                        }
                    }
                }, 5L) // 延迟5tick确保缓存已重新加载
            }

            logger.info("$PLUGIN_NAME 重新加载完成")
        } catch (e: Exception) {
            logger.severe("重新加载失败: ${e.message}")
            throw e
        }
    }
    
    /**
     * 检查许可证是否有效
     */
    fun isLicenseValid(): Boolean {
        if (!::licenseManager.isInitialized) return false
        return licenseManager.isValid()
    }
}
