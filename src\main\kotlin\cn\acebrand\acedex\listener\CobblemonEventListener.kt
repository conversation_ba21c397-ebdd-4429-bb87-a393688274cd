/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.listener

import cn.acebrand.acedex.AceDex

/**
 * Cobblemon事件监听器
 * 由于插件直接检测玩家PC和背包中的精灵，不需要监听事件
 */
class CobblemonEventListener(private val plugin: AceDex) {

    /**
     * 注册事件监听器（当前为空，因为使用直接检测方式）
     */
    fun registerEvents() {
        // 不再监听Cobblemon事件，因为插件直接检测玩家PC和背包中的精灵
    }
}
