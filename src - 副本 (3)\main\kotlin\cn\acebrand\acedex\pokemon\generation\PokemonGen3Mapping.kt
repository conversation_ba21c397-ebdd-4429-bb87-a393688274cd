/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第三世代精灵名称映射 (252-386)
 * 包含丰缘地区的所有精灵
 */
object PokemonGen3Mapping {
    
    /**
     * 获取第三世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第三世代 (252-386)
            "treecko" to "木守宫",
            "grovyle" to "森林蜥蜴",
            "sceptile" to "蜥蜴王",
            "torchic" to "火稚鸡",
            "combusken" to "力壮鸡",
            "blaziken" to "火焰鸡",
            "mudkip" to "水跃鱼",
            "marshtomp" to "沼跃鱼",
            "swampert" to "巨沼怪",
            "poochyena" to "土狼犬",
            "mightyena" to "大狼犬",
            "zigzagoon" to "蛇纹熊",
            "linoone" to "直冲熊",
            "wurmple" to "刺尾虫",
            "silcoon" to "甲壳茧",
            "beautifly" to "狩猎凤蝶",
            "cascoon" to "盾甲茧",
            "dustox" to "毒粉蛾",
            "lotad" to "莲叶童子",
            "lombre" to "莲帽小童",
            "ludicolo" to "乐天河童",
            "seedot" to "橡实果",
            "nuzleaf" to "长鼻叶",
            "shiftry" to "狡猾天狗",
            "taillow" to "傲骨燕",
            "swellow" to "大王燕",
            "wingull" to "长翅鸥",
            "pelipper" to "大嘴鸥",
            "ralts" to "拉鲁拉丝",
            "kirlia" to "奇鲁莉安",
            "gardevoir" to "沙奈朵",
            "surskit" to "溜溜糖球",
            "masquerain" to "雨翅蛾",
            "shroomish" to "蘑蘑菇",
            "breloom" to "斗笠菇",
            "slakoth" to "懒人獭",
            "vigoroth" to "过动猿",
            "slaking" to "请假王",
            "nincada" to "土居忍士",
            "ninjask" to "铁面忍者",
            "shedinja" to "脱壳忍者",
            "whismur" to "咕妞妞",
            "loudred" to "吼爆弹",
            "exploud" to "爆音怪",
            "makuhita" to "幕下力士",
            "hariyama" to "铁掌力士",
            "azurill" to "露力丽",
            "nosepass" to "朝北鼻",
            "skitty" to "向尾喵",
            "delcatty" to "优雅猫",
            "sableye" to "勾魂眼",
            "mawile" to "大嘴娃",
            "aron" to "可可多拉",
            "lairon" to "可多拉",
            "aggron" to "波士可多拉",
            "meditite" to "玛沙那",
            "medicham" to "恰雷姆",
            "electrike" to "落雷兽",
            "manectric" to "雷电兽",
            "plusle" to "正电拍拍",
            "minun" to "负电拍拍",
            "volbeat" to "电萤虫",
            "illumise" to "甜甜萤",
            "roselia" to "毒蔷薇",
            "gulpin" to "溶食兽",
            "swalot" to "吞食兽",
            "carvanha" to "利牙鱼",
            "sharpedo" to "巨牙鲨",
            "wailmer" to "吼吼鲸",
            "wailord" to "吼鲸王",
            "numel" to "呆火驼",
            "camerupt" to "喷火驼",
            "torkoal" to "煤炭龟",
            "spoink" to "跳跳猪",
            "grumpig" to "噗噗猪",
            "spinda" to "晃晃斑",
            "trapinch" to "大颚蚁",
            "vibrava" to "超音波幼虫",
            "flygon" to "沙漠蜻蜓",
            "cacnea" to "刺球仙人掌",
            "cacturne" to "梦歌仙人掌",
            "swablu" to "青绵鸟",
            "altaria" to "七夕青鸟",
            "zangoose" to "猫鼬斩",
            "seviper" to "饭匙蛇",
            "lunatone" to "月石",
            "solrock" to "太阳岩",
            "barboach" to "泥泥鳅",
            "whiscash" to "鲶鱼王",
            "corphish" to "龙虾小兵",
            "crawdaunt" to "铁螯龙虾",
            "baltoy" to "天秤偶",
            "claydol" to "念力土偶",
            "lileep" to "触手百合",
            "cradily" to "摇篮百合",
            "anorith" to "太古羽虫",
            "armaldo" to "太古盔甲",
            "feebas" to "丑丑鱼",
            "milotic" to "美纳斯",
            "castform" to "飘浮泡泡",
            "kecleon" to "变隐龙",
            "shuppet" to "怨影娃娃",
            "banette" to "诅咒娃娃",
            "duskull" to "夜巡灵",
            "dusclops" to "彷徨夜灵",
            "tropius" to "热带龙",
            "chimecho" to "风铃铃",
            "absol" to "阿勃梭鲁",
            "wynaut" to "小果然",
            "snorunt" to "雪童子",
            "glalie" to "冰鬼护",
            "spheal" to "海豹球",
            "sealeo" to "海魔狮",
            "walrein" to "帝牙海狮",
            "clamperl" to "珍珠贝",
            "huntail" to "猎斑鱼",
            "gorebyss" to "樱花鱼",
            "relicanth" to "古空棘鱼",
            "luvdisc" to "爱心鱼",
            "bagon" to "宝贝龙",
            "shelgon" to "甲壳龙",
            "salamence" to "暴飞龙",
            "beldum" to "铁哑铃",
            "metang" to "金属怪",
            "metagross" to "巨金怪",
            "regirock" to "雷吉洛克",
            "regice" to "雷吉艾斯",
            "registeel" to "雷吉斯奇鲁",
            "latias" to "拉帝亚斯",
            "latios" to "拉帝欧斯",
            "kyogre" to "盖欧卡",
            "groudon" to "固拉多",
            "rayquaza" to "烈空坐",
            "jirachi" to "基拉祈",
            "deoxys" to "代欧奇希斯",
    )
}
