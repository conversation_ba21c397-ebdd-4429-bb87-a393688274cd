/**
 * AceDex - 精灵图鉴插件
 * LGPokemonMenu 集成工具类
 */

package cn.acebrand.acedex.util

import cn.acebrand.acedex.AceDex
import com.cobblemon.mod.common.api.pokemon.PokemonSpecies
import com.cobblemon.mod.common.pokemon.Pokemon
import com.cobblemon.mod.common.pokemon.Species
import org.bukkit.inventory.ItemStack

/**
 * LGPokemonMenu 集成工具类
 * 专门用于与 LGPokemonMenu 插件集成，使用其 API 创建精灵物品
 */
object LGMenuIntegration {
    
    private var isLGMenuAvailable: Boolean? = null
    
    /**
     * 检查 LGPokemonMenu 是否可用
     */
    fun isLGMenuAvailable(): Boolean {
        if (isLGMenuAvailable == null) {
            isLGMenuAvailable = try {
                Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
                println("✓ LGPokemonMenu 类找到，集成可用")
                true
            } catch (e: ClassNotFoundException) {
                println("✗ LGPokemonMenu 类未找到: ${e.message}")
                false
            }
        }
        return isLGMenuAvailable!!
    }
    
    /**
     * 使用 LGPokemonMenu 创建精灵物品
     */
    fun createPokemonItem(pokemon: Pokemon): ItemStack? {
        return if (isLGMenuAvailable()) {
            try {
                val lgMenuClass = Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
                val getPokemonItemMethod = lgMenuClass.getMethod("getPokemonItem", Pokemon::class.java)
                getPokemonItemMethod.invoke(null, pokemon) as ItemStack
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    /**
     * 根据精灵名称创建精灵物品
     */
    fun createPokemonItemByName(pokemonName: String, level: Int = 1): ItemStack? {
        return try {
            println("🔍 尝试创建精灵物品: $pokemonName")

            if (!isLGMenuAvailable()) {
                println("✗ LGPokemonMenu 不可用")
                return null
            }

            val species = getSpeciesByName(pokemonName)
            if (species == null) {
                println("✗ 无法找到精灵种类: $pokemonName")
                return null
            }

            println("✓ 找到精灵种类: ${species.name}")
            val pokemon = createPokemon(species, false, level)
            println("✓ 创建精灵对象成功")

            val item = createPokemonItem(pokemon)
            if (item != null) {
                println("✓ 成功创建精灵物品: $pokemonName")
            } else {
                println("✗ 精灵物品创建失败: $pokemonName")
            }

            item
        } catch (e: Exception) {
            println("✗ 创建精灵物品异常: $pokemonName - ${e.message}")
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 根据图鉴编号创建精灵物品
     */
    fun createPokemonItemByDexNumber(dexNumber: Int, isShiny: Boolean = false, level: Int = 1): ItemStack? {
        return try {
            val species = getSpeciesByDexNumber(dexNumber) ?: return null
            val pokemon = createPokemon(species, isShiny, level)
            createPokemonItem(pokemon)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据精灵名称获取 Species
     */
    fun getSpeciesByName(pokemonName: String): Species? {
        return try {
            val resourceIdentifier = com.cobblemon.mod.common.util.cobblemonResource(pokemonName.lowercase())
            PokemonSpecies.getByIdentifier(resourceIdentifier)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据图鉴编号获取 Species
     */
    fun getSpeciesByDexNumber(dexNumber: Int): Species? {
        return try {
            PokemonSpecies.getByPokedexNumber(dexNumber)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 创建精灵对象
     */
    fun createPokemon(species: Species, isShiny: Boolean = false, level: Int = 1): Pokemon {
        val pokemon = Pokemon()
        pokemon.species = species
        pokemon.shiny = isShiny
        pokemon.level = level
        pokemon.initialize()
        return pokemon
    }
    
    /**
     * 检查精灵是否存在
     */
    fun isPokemonExists(pokemonName: String): Boolean {
        return getSpeciesByName(pokemonName) != null
    }
    
    /**
     * 检查图鉴编号的精灵是否存在
     */
    fun isPokemonExists(dexNumber: Int): Boolean {
        return getSpeciesByDexNumber(dexNumber) != null
    }
    
    /**
     * 获取精灵的显示名称
     */
    fun getPokemonDisplayName(species: Species): String {
        return try {
            species.translatedName.string
        } catch (e: Exception) {
            species.name
        }
    }
    
    /**
     * 获取精灵的类型信息
     */
    fun getPokemonTypes(species: Species): Pair<String, String?> {
        return try {
            val primaryType = species.primaryType.displayName.string
            val secondaryType = species.secondaryType?.displayName?.string
            Pair(primaryType, secondaryType)
        } catch (e: Exception) {
            Pair("未知", null)
        }
    }

    /**
     * 创建精灵球物品
     */
    fun createPokeBallItem(pokeBall: com.cobblemon.mod.common.pokeball.PokeBall): ItemStack? {
        return try {
            if (!isLGMenuAvailable()) {
                return null
            }

            // 获取精灵球的 ItemStack
            val cobblemonStack = pokeBall.stack(1)

            // 使用 LGPokemonMenu 的转换方法
            val lgMenuClass = Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
            val toItemStackMethod = lgMenuClass.getMethod("toItemStack", Class.forName("net.minecraft.world.item.ItemStack"))

            val bukkitItem = toItemStackMethod.invoke(null, cobblemonStack) as? ItemStack
            bukkitItem
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 使用 LGPokemonMenu 的占位符系统
     */
    fun buildPokemonPlaceholders(pokemon: Pokemon): Map<String, String>? {
        return if (isLGMenuAvailable()) {
            try {
                val lgMenuClass = Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
                val buildPlaceholdersMethod = lgMenuClass.getMethod("buildPokemonInfoPlaceholders", Pokemon::class.java)
                @Suppress("UNCHECKED_CAST")
                buildPlaceholdersMethod.invoke(null, pokemon) as Map<String, String>
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    /**
     * 使用 LGPokemonMenu 的占位符替换
     */
    fun replacePlaceholders(template: String, placeholders: Map<String, String>): String? {
        return if (isLGMenuAvailable()) {
            try {
                val lgMenuClass = Class.forName("lg.minecraft.plugin.pokemonmenu.LGPokemonMenu")
                val replaceMethod = lgMenuClass.getMethod("replacePlaceholders", String::class.java, Map::class.java)
                replaceMethod.invoke(null, template, placeholders) as String
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    /**
     * 测试 LGPokemonMenu 集成
     */
    fun testIntegration(): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()
        
        results["LGMenu可用"] = isLGMenuAvailable()
        
        if (isLGMenuAvailable()) {
            // 测试创建精灵物品
            try {
                val testItem = createPokemonItemByName("pikachu", 1)
                results["创建精灵物品"] = testItem != null
            } catch (e: Exception) {
                results["创建精灵物品"] = false
            }
            
            // 测试获取精灵信息
            try {
                val species = getSpeciesByName("pikachu")
                results["获取精灵信息"] = species != null
            } catch (e: Exception) {
                results["获取精灵信息"] = false
            }
            
            // 测试占位符系统
            try {
                val species = getSpeciesByName("pikachu")
                if (species != null) {
                    val pokemon = createPokemon(species, false, 1)
                    val placeholders = buildPokemonPlaceholders(pokemon)
                    results["占位符系统"] = placeholders != null && placeholders.isNotEmpty()
                } else {
                    results["占位符系统"] = false
                }
            } catch (e: Exception) {
                results["占位符系统"] = false
            }
        }
        
        return results
    }
    
    /**
     * 获取集成状态信息
     */
    fun getIntegrationInfo(): String {
        return if (isLGMenuAvailable()) {
            "LGPokemonMenu 集成已启用，可以使用原生精灵物品创建功能"
        } else {
            "LGPokemonMenu 未找到，将使用备用方案"
        }
    }
}
