/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第二世代精灵名称映射 (152-251)
 * 包含城都地区的所有精灵
 */
object PokemonGen2Mapping {
    
    /**
     * 获取第二世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第二世代 (152-251)
            "chikorita" to "菊草叶",
            "bayleef" to "月桂叶",
            "meganium" to "大竺葵",
            "cyndaquil" to "火球鼠",
            "quilava" to "火岩鼠",
            "typhlosion" to "火爆兽",
            "totodile" to "小锯鳄",
            "croconaw" to "蓝鳄",
            "feraligatr" to "大力鳄",
            "sentret" to "尾立",
            "furret" to "大尾立",
            "hoothoot" to "咕咕",
            "noctowl" to "猫头夜鹰",
            "ledyba" to "芭瓢虫",
            "ledian" to "安瓢虫",
            "spinarak" to "圆丝蛛",
            "ariados" to "阿利多斯",
            "crobat" to "叉字蝠",
            "chinchou" to "灯笼鱼",
            "lanturn" to "电灯怪",
            "pichu" to "皮丘",
            "cleffa" to "皮宝宝",
            "igglybuff" to "宝宝丁",
            "togepi" to "波克比",
            "togetic" to "波克基古",
            "natu" to "天然雀",
            "xatu" to "天然鸟",
            "mareep" to "咩利羊",
            "flaaffy" to "茸茸羊",
            "ampharos" to "电龙",
            "bellossom" to "美丽花",
            "marill" to "玛力露",
            "azumarill" to "玛力露丽",
            "sudowoodo" to "树才怪",
            "politoed" to "蚊香蛙皇",
            "hoppip" to "毽子草",
            "skiploom" to "毽子花",
            "jumpluff" to "毽子棉",
            "aipom" to "长尾怪手",
            "sunkern" to "向日种子",
            "sunflora" to "向日花怪",
            "yanma" to "蜻蜻蜓",
            "wooper" to "乌波",
            "quagsire" to "沼王",
            "espeon" to "太阳伊布",
            "umbreon" to "月亮伊布",
            "murkrow" to "黑暗鸦",
            "slowking" to "呆呆王",
            "misdreavus" to "梦妖",
            "unown" to "未知图腾",
            "wobbuffet" to "果然翁",
            "girafarig" to "麒麟奇",
            "pineco" to "榛果球",
            "forretress" to "佛烈托斯",
            "dunsparce" to "土龙弟弟",
            "gligar" to "天蝎",
            "steelix" to "大钢蛇",
            "snubbull" to "布鲁",
            "granbull" to "布鲁皇",
            "qwilfish" to "千针鱼",
            "scizor" to "巨钳螳螂",
            "shuckle" to "壶壶",
            "heracross" to "赫拉克罗斯",
            "sneasel" to "狃拉",
            "teddiursa" to "熊宝宝",
            "ursaring" to "圈圈熊",
            "slugma" to "熔岩虫",
            "magcargo" to "熔岩蜗牛",
            "swinub" to "小山猪",
            "piloswine" to "长毛猪",
            "corsola" to "太阳珊瑚",
            "remoraid" to "铁炮鱼",
            "octillery" to "章鱼桶",
            "delibird" to "信使鸟",
            "mantine" to "巨翅飞鱼",
            "skarmory" to "盔甲鸟",
            "houndour" to "戴鲁比",
            "houndoom" to "黑鲁加",
            "kingdra" to "刺龙王",
            "phanpy" to "小小象",
            "donphan" to "顿甲",
            "porygon2" to "多边兽Ⅱ",
            "stantler" to "惊角鹿",
            "smeargle" to "图图犬",
            "tyrogue" to "无畏小子",
            "hitmontop" to "战舞郎",
            "smoochum" to "迷唇娃",
            "elekid" to "电击怪",
            "magby" to "鸭嘴宝宝",
            "miltank" to "大奶罐",
            "blissey" to "幸福蛋",
            "raikou" to "雷公",
            "entei" to "炎帝",
            "suicune" to "水君",
            "larvitar" to "幼基拉斯",
            "pupitar" to "沙基拉斯",
            "tyranitar" to "班基拉斯",
            "lugia" to "洛奇亚",
            "hooh" to "凤王",
            "celebi" to "时拉比",
    )
}
