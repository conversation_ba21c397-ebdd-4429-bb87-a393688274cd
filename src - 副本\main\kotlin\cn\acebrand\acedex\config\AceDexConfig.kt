/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.config

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.util.VariableReplacer
import org.bukkit.Material
import org.bukkit.configuration.file.FileConfiguration
import org.bukkit.configuration.file.YamlConfiguration
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack
import java.io.File

/**
 * AceDex 配置管理器
 */
class AceDexConfig(private val plugin: AceDex) {

    private lateinit var config: FileConfiguration
    private lateinit var configFile: File
    private lateinit var guiConfig: FileConfiguration
    private lateinit var guiConfigFile: File
    
    // 配置项
    var enableRewards: Boolean = true
        private set
    
    var enableGui: Boolean = true
        private set
    
    var defaultRewardCooldown: Long = 3600000 // 1小时
        private set

    var enableDebug: Boolean = false
        private set
    
    var guiTitle: String = "§6§lAceDex §7- 精灵图鉴"
        private set

    // 世代标题配置 - 每个世代独立设置
    var generationTitles: Map<String, String> = mapOf()
        private set

    // 世代精灵球配置 - 每个世代的位置和材质
    var generationSlots: Map<String, Int> = mapOf()
        private set
    var generationMaterials: Map<String, String> = mapOf()
        private set

    // 主菜单按钮配置 - 统计按钮和关闭按钮的位置和材质
    var statsButtonSlot: Int = 40
        private set
    var statsButtonMaterial: String = "ENCHANTED_BOOK"
        private set
    var closeButtonSlot: Int = 49
        private set
    var closeButtonMaterial: String = "BARRIER"
        private set

    // 全世界进度按钮配置
    var progressButtonSlot: Int = 4
        private set
    var progressButtonMaterials: Map<String, String> = mapOf()
        private set
    
    var enableGenerationRewards: Boolean = true
        private set
    

    
    var enableCompletionRewards: Boolean = true
        private set

    // 许可证设置
    var licenseKey: String = ""
        private set

    // 精灵显示设置
    var useCustomMaterialForCaught: Boolean = false
        private set

    var customCaughtMaterial: String = "EMERALD"
        private set

    // GUI装饰设置
    var guiDecorationMaterial: String = "GRAY_STAINED_GLASS_PANE"
        private set

    // 分页按钮设置
    var previousPageMaterial: String = "ARROW"
        private set

    var nextPageMaterial: String = "ARROW"
        private set

    // GUI导航物品设置
    var pageIndicatorMaterial: String = "PAPER"
        private set

    var backToMainMenuMaterial: String = "BARRIER"
        private set

    var progressInfoMaterial: String = "EXPERIENCE_BOTTLE"
        private set



    // 点击冷却设置
    var statsButtonCooldown: Long = 2000  // 2秒
        private set

    var progressButtonCooldown: Long = 3000  // 3秒
        private set

    var generationRewardCooldown: Long = 2500  // 2.5秒
        private set
    
    /**
     * 加载配置
     */
    fun load() {
        // 创建主配置文件
        configFile = File(plugin.dataFolder, "config.yml")

        if (!configFile.exists()) {
            plugin.saveDefaultConfig()
        }

        config = YamlConfiguration.loadConfiguration(configFile)

        // 创建GUI配置文件
        guiConfigFile = File(plugin.dataFolder, "gui.yml")

        if (!guiConfigFile.exists()) {
            plugin.saveResource("gui.yml", false)
        }

        guiConfig = YamlConfiguration.loadConfiguration(guiConfigFile)

        // 加载配置项
        loadConfigValues()

    }
    
    /**
     * 保存配置
     */
    fun save() {
        try {
            config.save(configFile)
            guiConfig.save(guiConfigFile)
            plugin.logger.info("配置文件保存完成")
        } catch (e: Exception) {
        }
    }
    
    /**
     * 重新加载配置
     */
    fun reload() {
        config = YamlConfiguration.loadConfiguration(configFile)
        guiConfig = YamlConfiguration.loadConfiguration(guiConfigFile)
        loadConfigValues()

        // 清理精灵物品缓存，确保自定义材质设置生效
        plugin.pokemonItemCreator.clearItemCache()

        // 清理GUI按钮缓存，确保新的按钮配置生效
        try {
            plugin.guiButtonCacheManager.clearCache()
        } catch (e: UninitializedPropertyAccessException) {
            // GUI按钮缓存管理器未初始化，跳过
        }

        // 清理GUI界面缓存，确保新的标题和配置生效
        plugin.mainGui.clearAllMainInventoryCache()
        plugin.mainGui.clearAllGenerationInventoryCache()

    }
    
    /**
     * 加载配置值
     */
    private fun loadConfigValues() {
        // 先设置默认值
        setDefaults()

        // 基础设置
        enableRewards = config.getBoolean("rewards.enabled", true)
        enableGui = config.getBoolean("gui.enabled", true)
        defaultRewardCooldown = config.getLong("rewards.default-cooldown", 3600000)
        enableDebug = config.getBoolean("debug.enabled", false)

        // GUI设置 - 从gui.yml文件加载
        guiTitle = guiConfig.getString("gui.title") ?: "§6§lAceDex §7- 精灵图鉴"

        // 加载每个世代的标题配置
        val generationTitlesMap = mutableMapOf<String, String>()
        val generationSlotsMap = mutableMapOf<String, Int>()
        val generationMaterialsMap = mutableMapOf<String, String>()
        val generationIds = listOf("gen1", "gen2", "gen3", "gen4", "gen5", "gen6", "gen7", "gen8", "gen9")

        generationIds.forEach { genId ->
            // 加载标题
            val title = guiConfig.getString("gui.generation-titles.$genId")
            if (title != null) {
                generationTitlesMap[genId] = title
            }

            // 加载位置
            val slot = guiConfig.getInt("gui.generation-slots.$genId", -1)
            if (slot >= 0) {
                generationSlotsMap[genId] = slot
            }

            // 加载材质
            val material = guiConfig.getString("gui.generation-materials.$genId")
            if (material != null) {
                generationMaterialsMap[genId] = material
            }
        }

        generationTitles = generationTitlesMap
        generationSlots = generationSlotsMap
        generationMaterials = generationMaterialsMap

        // 加载主菜单按钮配置
        statsButtonSlot = guiConfig.getInt("gui.main-menu-buttons.stats-button.slot", 40)
        statsButtonMaterial = guiConfig.getString("gui.main-menu-buttons.stats-button.material") ?: "ENCHANTED_BOOK"
        closeButtonSlot = guiConfig.getInt("gui.main-menu-buttons.close-button.slot", 49)
        closeButtonMaterial = guiConfig.getString("gui.main-menu-buttons.close-button.material") ?: "BARRIER"

        // 加载全世界进度按钮配置
        progressButtonSlot = guiConfig.getInt("gui.main-menu-buttons.progress-button.slot", 4)
        val progressMaterialsMap = mutableMapOf<String, String>()
        val progressLevels = listOf("0-25", "25-50", "50-75", "75-99", "100")
        progressLevels.forEach { level ->
            val material = guiConfig.getString("gui.main-menu-buttons.progress-button.materials.$level")
            if (material != null) {
                progressMaterialsMap[level] = material
            }
        }
        progressButtonMaterials = progressMaterialsMap

        // 奖励设置
        enableGenerationRewards = config.getBoolean("rewards.generation.enabled", true)
        enableCompletionRewards = config.getBoolean("rewards.completion.enabled", true)

        // 许可证设置
        licenseKey = config.getString("license-key", "") ?: ""

        // 精灵显示设置 - 从gui.yml文件加载
        useCustomMaterialForCaught = guiConfig.getBoolean("pokemon.display.use-custom-material-for-caught", false)
        customCaughtMaterial = guiConfig.getString("pokemon.display.custom-caught-material") ?: "EMERALD"

        // GUI装饰设置 - 从gui.yml文件加载
        guiDecorationMaterial = guiConfig.getString("gui.decoration-material") ?: "GRAY_STAINED_GLASS_PANE"

        // 分页按钮设置 - 从gui.yml文件加载
        previousPageMaterial = guiConfig.getString("gui.previous-page-material") ?: "ARROW"
        nextPageMaterial = guiConfig.getString("gui.next-page-material") ?: "ARROW"

        // GUI导航物品设置 - 从gui.yml文件加载
        pageIndicatorMaterial = guiConfig.getString("gui.page-indicator-material") ?: "PAPER"
        backToMainMenuMaterial = guiConfig.getString("gui.back-to-main-menu-material") ?: "BARRIER"
        progressInfoMaterial = guiConfig.getString("gui.progress-info-material") ?: "EXPERIENCE_BOTTLE"

        // 主菜单物品设置 - 从gui.yml文件加载（保持向后兼容）
        // 优先使用新的配置路径，如果没有则使用旧的配置路径
        closeButtonMaterial = guiConfig.getString("gui.main-menu-buttons.close-button.material")
            ?: guiConfig.getString("gui.close-button-material") ?: "BARRIER"
        statsButtonMaterial = guiConfig.getString("gui.main-menu-buttons.stats-button.material")
            ?: guiConfig.getString("gui.stats-button-material") ?: "ENCHANTED_BOOK"

        // 点击冷却设置 - 从gui.yml文件加载
        statsButtonCooldown = guiConfig.getLong("gui.click-cooldown.stats-button", 2000)
        progressButtonCooldown = guiConfig.getLong("gui.click-cooldown.progress-button", 3000)
        generationRewardCooldown = guiConfig.getLong("gui.click-cooldown.generation-reward", 2500)
    }
    
    /**
     * 设置默认配置值
     */
    private fun setDefaults() {
        // 基础设置
        config.addDefault("rewards.enabled", true)
        config.addDefault("gui.enabled", true)
        config.addDefault("rewards.default-cooldown", 3600000)
        config.addDefault("debug.enabled", false)
        
        // GUI设置 - 设置到gui.yml文件
        guiConfig.addDefault("gui.title", "§6§lAceDex §7- 精灵图鉴%img_offset_-48%")
        guiConfig.addDefault("gui.size", 54)

        // 为9个世代设置默认配置
        // 标题配置
        guiConfig.addDefault("gui.generation-titles.gen1", "§c§l关都地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen2", "§6§l城都地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen3", "§e§l丰缘地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen4", "§b§l神奥地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen5", "§5§l合众地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen6", "§d§l卡洛斯地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen7", "§a§l阿罗拉地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen8", "§9§l伽勒尔地区%img_offset_-32%")
        guiConfig.addDefault("gui.generation-titles.gen9", "§f§l帕底亚地区%img_offset_-32%")

        // 位置配置 (3x3居中布局)
        guiConfig.addDefault("gui.generation-slots.gen1", 11)  // 第2行第3列
        guiConfig.addDefault("gui.generation-slots.gen2", 13)  // 第2行第5列
        guiConfig.addDefault("gui.generation-slots.gen3", 15)  // 第2行第7列
        guiConfig.addDefault("gui.generation-slots.gen4", 20)  // 第3行第3列
        guiConfig.addDefault("gui.generation-slots.gen5", 22)  // 第3行第5列
        guiConfig.addDefault("gui.generation-slots.gen6", 24)  // 第3行第7列
        guiConfig.addDefault("gui.generation-slots.gen7", 29)  // 第4行第3列
        guiConfig.addDefault("gui.generation-slots.gen8", 31)  // 第4行第5列
        guiConfig.addDefault("gui.generation-slots.gen9", 33)  // 第4行第7列

        // 材质配置 (使用 Cobblemon 精灵球)
        guiConfig.addDefault("gui.generation-materials.gen1", "COBBLEMON_POKE_BALL")     // 关都 - 精灵球
        guiConfig.addDefault("gui.generation-materials.gen2", "COBBLEMON_GREAT_BALL")    // 城都 - 超级球
        guiConfig.addDefault("gui.generation-materials.gen3", "COBBLEMON_ULTRA_BALL")    // 丰缘 - 高级球
        guiConfig.addDefault("gui.generation-materials.gen4", "COBBLEMON_MASTER_BALL")   // 神奥 - 大师球
        guiConfig.addDefault("gui.generation-materials.gen5", "COBBLEMON_TIMER_BALL")    // 合众 - 计时球
        guiConfig.addDefault("gui.generation-materials.gen6", "COBBLEMON_LUXURY_BALL")   // 卡洛斯 - 豪华球
        guiConfig.addDefault("gui.generation-materials.gen7", "COBBLEMON_PREMIER_BALL")  // 阿罗拉 - 纪念球
        guiConfig.addDefault("gui.generation-materials.gen8", "COBBLEMON_DUSK_BALL")     // 伽勒尔 - 黄昏球
        guiConfig.addDefault("gui.generation-materials.gen9", "COBBLEMON_QUICK_BALL")    // 帕底亚 - 先机球

        // 主菜单按钮配置
        guiConfig.addDefault("gui.main-menu-buttons.stats-button.slot", 40)
        guiConfig.addDefault("gui.main-menu-buttons.stats-button.material", "PAPER:10030")
        guiConfig.addDefault("gui.main-menu-buttons.close-button.slot", 49)
        guiConfig.addDefault("gui.main-menu-buttons.close-button.material", "PAPER:10030")

        // 全世界进度按钮配置
        guiConfig.addDefault("gui.main-menu-buttons.progress-button.slot", 4)
        guiConfig.addDefault("gui.main-menu-buttons.progress-button.materials.0-25", "COBBLEMON_POKE_BALL")     // 0-25% - 精灵球
        guiConfig.addDefault("gui.main-menu-buttons.progress-button.materials.25-50", "COBBLEMON_GREAT_BALL")   // 25-50% - 超级球
        guiConfig.addDefault("gui.main-menu-buttons.progress-button.materials.50-75", "COBBLEMON_ULTRA_BALL")   // 50-75% - 高级球
        guiConfig.addDefault("gui.main-menu-buttons.progress-button.materials.75-99", "COBBLEMON_MASTER_BALL")  // 75-99% - 大师球
        guiConfig.addDefault("gui.main-menu-buttons.progress-button.materials.100", "COBBLEMON_PREMIER_BALL")             // 100% - 纪念球
        guiConfig.addDefault("gui.decoration-material", "GRAY_STAINED_GLASS_PANE")
        guiConfig.addDefault("gui.previous-page-material", "ARROW")
        guiConfig.addDefault("gui.next-page-material", "ARROW")
        guiConfig.addDefault("gui.page-indicator-material", "PAPER")
        guiConfig.addDefault("gui.back-to-main-menu-material", "BARRIER")
        guiConfig.addDefault("gui.progress-info-material", "EXPERIENCE_BOTTLE")
        // 旧的配置路径已弃用，使用新的 main-menu-buttons 配置

        // 点击冷却设置
        guiConfig.addDefault("gui.click-cooldown.stats-button", 2000)
        guiConfig.addDefault("gui.click-cooldown.progress-button", 3000)
        guiConfig.addDefault("gui.click-cooldown.generation-reward", 2500)

        // 奖励设置
        config.addDefault("rewards.generation.enabled", true)
        config.addDefault("rewards.completion.enabled", true)
        
        // 世代奖励配置 - 包含奖励描述（100%完成才能领取）
        // 第一世代 - 关都地区
        config.addDefault("rewards.generation.gen1.commands", listOf(
            "give {player} minecraft:diamond 1",
            "give {player} minecraft:emerald 2",
            "give {player} minecraft:gold_ingot 5",
            "give {player} minecraft:netherite_ingot 1"
        ))
        config.addDefault("rewards.generation.gen1.descriptions", listOf(
            "§7• §b钻石 x1",
            "§7• §a绿宝石 x2",
            "§7• §6金锭 x5",
            "§7• §5下界合金锭 x1"
        ))

        // 第二世代 - 城都地区
        config.addDefault("rewards.generation.gen2.commands", listOf(
            "give {player} minecraft:diamond 1",
            "give {player} minecraft:emerald 3",
            "give {player} minecraft:gold_ingot 8",
            "give {player} minecraft:netherite_ingot 1"
        ))
        config.addDefault("rewards.generation.gen2.descriptions", listOf(
            "§7• §b钻石 x1",
            "§7• §a绿宝石 x3",
            "§7• §6金锭 x8",
            "§7• §5下界合金锭 x1"
        ))

        // 第三世代 - 丰缘地区
        config.addDefault("rewards.generation.gen3.commands", listOf(
            "give {player} minecraft:diamond 2",
            "give {player} minecraft:emerald 3",
            "give {player} minecraft:gold_ingot 10",
            "give {player} minecraft:netherite_ingot 1"
        ))
        config.addDefault("rewards.generation.gen3.descriptions", listOf(
            "§7• §b钻石 x2",
            "§7• §a绿宝石 x3",
            "§7• §6金锭 x10",
            "§7• §5下界合金锭 x1"
        ))

        // 第四世代 - 神奥地区
        config.addDefault("rewards.generation.gen4.commands", listOf(
            "give {player} minecraft:diamond 2",
            "give {player} minecraft:emerald 4",
            "give {player} minecraft:gold_ingot 12",
            "give {player} minecraft:netherite_ingot 2"
        ))
        config.addDefault("rewards.generation.gen4.descriptions", listOf(
            "§7• §b钻石 x2",
            "§7• §a绿宝石 x4",
            "§7• §6金锭 x12",
            "§7• §5下界合金锭 x2"
        ))

        // 第五世代 - 合众地区
        config.addDefault("rewards.generation.gen5.commands", listOf(
            "give {player} minecraft:diamond 3",
            "give {player} minecraft:emerald 5",
            "give {player} minecraft:gold_ingot 15",
            "give {player} minecraft:netherite_ingot 2"
        ))
        config.addDefault("rewards.generation.gen5.descriptions", listOf(
            "§7• §b钻石 x3",
            "§7• §a绿宝石 x5",
            "§7• §6金锭 x15",
            "§7• §5下界合金锭 x2"
        ))

        // 第六世代 - 卡洛斯地区
        config.addDefault("rewards.generation.gen6.commands", listOf(
            "give {player} minecraft:diamond 3",
            "give {player} minecraft:emerald 6",
            "give {player} minecraft:gold_ingot 18",
            "give {player} minecraft:netherite_ingot 2"
        ))
        config.addDefault("rewards.generation.gen6.descriptions", listOf(
            "§7• §b钻石 x3",
            "§7• §a绿宝石 x6",
            "§7• §6金锭 x18",
            "§7• §5下界合金锭 x2"
        ))

        // 第七世代 - 阿罗拉地区
        config.addDefault("rewards.generation.gen7.commands", listOf(
            "give {player} minecraft:diamond 4",
            "give {player} minecraft:emerald 7",
            "give {player} minecraft:gold_ingot 20",
            "give {player} minecraft:netherite_ingot 3"
        ))
        config.addDefault("rewards.generation.gen7.descriptions", listOf(
            "§7• §b钻石 x4",
            "§7• §a绿宝石 x7",
            "§7• §6金锭 x20",
            "§7• §5下界合金锭 x3"
        ))

        // 第八世代 - 伽勒尔地区
        config.addDefault("rewards.generation.gen8.commands", listOf(
            "give {player} minecraft:diamond 4",
            "give {player} minecraft:emerald 8",
            "give {player} minecraft:gold_ingot 25",
            "give {player} minecraft:netherite_ingot 3"
        ))
        config.addDefault("rewards.generation.gen8.descriptions", listOf(
            "§7• §b钻石 x4",
            "§7• §a绿宝石 x8",
            "§7• §6金锭 x25",
            "§7• §5下界合金锭 x3"
        ))

        // 第九世代 - 帕底亚地区
        config.addDefault("rewards.generation.gen9.commands", listOf(
            "give {player} minecraft:diamond 5",
            "give {player} minecraft:emerald 10",
            "give {player} minecraft:gold_ingot 30",
            "give {player} minecraft:netherite_ingot 4"
        ))
        config.addDefault("rewards.generation.gen9.descriptions", listOf(
            "§7• §b钻石 x5",
            "§7• §a绿宝石 x10",
            "§7• §6金锭 x30",
            "§7• §5下界合金锭 x4"
        ))

        // 全世代完成奖励描述配置
        config.addDefault("rewards.completion.all-generations.descriptions", listOf(
            "§7• §b钻石 x100",
            "§7• §5下界合金锭 x20",
            "§7• §e信标 x5",
            "§7• §d龙蛋 x1",
            "§7• §6附魔金苹果 x10",
            "§7• §a不死图腾 x3",
            "§7• §5精灵大师称号"
        ))


        // 许可证配置
        config.addDefault("license-key", "")

        // 精灵显示设置 - 设置到gui.yml文件
        guiConfig.addDefault("pokemon.display.use-custom-material-for-caught", false)
        guiConfig.addDefault("pokemon.display.custom-caught-material", "EMERALD")
        guiConfig.addDefault("pokemon.display.custom-material-examples", listOf(
            "# === 普通 Minecraft 材质 ===",
            "# EMERALD - 绿宝石（默认）",
        ))
        
        // 许可证配置
        config.addDefault("license-key", "")

        config.options().copyDefaults(true)
        guiConfig.options().copyDefaults(true)
    }
    
    /**
     * 获取处理变量替换后的GUI标题
     */
    fun getProcessedGuiTitle(player: Player? = null): String {
        return VariableReplacer.replaceVariables(guiTitle, player, plugin)
    }

    /**
     * 获取处理变量替换后的世代标题
     */
    fun getProcessedGenerationTitle(
        player: Player? = null,
        generation: cn.acebrand.acedex.generation.Generation
    ): String {
        // 获取该世代的自定义标题，如果没有则使用默认格式
        val title = generationTitles[generation.id] ?: "${generation.color}§l${generation.name}地区%img_offset_-32%"

        // 使用 VariableReplacer 处理变量（包括 img_offset）
        return VariableReplacer.replaceVariables(title, player, plugin)
    }

    /**
     * 获取世代在主菜单中的位置
     */
    fun getGenerationSlot(generationId: String): Int {
        return generationSlots[generationId] ?: getDefaultGenerationSlot(generationId)
    }

    /**
     * 获取世代的默认位置 - 3x3居中布局
     */
    private fun getDefaultGenerationSlot(generationId: String): Int {
        return when (generationId) {
            "gen1" -> 11  // 第2行第3列
            "gen2" -> 13  // 第2行第5列
            "gen3" -> 15  // 第2行第7列
            "gen4" -> 20  // 第3行第3列
            "gen5" -> 22  // 第3行第5列
            "gen6" -> 24  // 第3行第7列
            "gen7" -> 29  // 第4行第3列
            "gen8" -> 31  // 第4行第5列
            "gen9" -> 33  // 第4行第7列
            else -> 22    // 默认位置（中心）
        }
    }

    /**
     * 获取世代的材质配置
     */
    fun getGenerationMaterial(generationId: String): String {
        return generationMaterials[generationId] ?: getDefaultGenerationMaterial(generationId)
    }

    /**
     * 获取世代的默认材质 - 使用 Cobblemon 精灵球
     */
    private fun getDefaultGenerationMaterial(generationId: String): String {
        return when (generationId) {
            "gen1" -> "COBBLEMON_POKE_BALL"     // 关都 - 精灵球
            "gen2" -> "COBBLEMON_GREAT_BALL"    // 城都 - 超级球
            "gen3" -> "COBBLEMON_ULTRA_BALL"    // 丰缘 - 高级球
            "gen4" -> "COBBLEMON_MASTER_BALL"   // 神奥 - 大师球
            "gen5" -> "COBBLEMON_TIMER_BALL"    // 合众 - 计时球
            "gen6" -> "COBBLEMON_LUXURY_BALL"   // 卡洛斯 - 豪华球
            "gen7" -> "COBBLEMON_PREMIER_BALL"  // 阿罗拉 - 纪念球
            "gen8" -> "COBBLEMON_DUSK_BALL"     // 伽勒尔 - 黄昏球
            "gen9" -> "COBBLEMON_QUICK_BALL"    // 帕底亚 - 先机球
            else -> "ENDER_PEARL"               // 默认材质
        }
    }

    /**
     * 创建统计按钮物品
     */
    fun createStatsButtonItem(): ItemStack {
        if (enableDebug) {
            plugin.logger.info("创建统计按钮，使用材质: $statsButtonMaterial")
        }
        return parseButtonMaterial(statsButtonMaterial)
    }

    /**
     * 创建关闭按钮物品
     */
    fun createCloseButtonItem(): ItemStack {
        if (enableDebug) {
            plugin.logger.info("创建关闭按钮，使用材质: $closeButtonMaterial")
        }
        return parseButtonMaterial(closeButtonMaterial)
    }

    /**
     * 根据进度百分比创建进度按钮物品
     */
    fun createProgressButtonItem(progressPercentage: Double): ItemStack {
        val materialKey = when {
            progressPercentage >= 100.0 -> "100"
            progressPercentage >= 75.0 -> "75-99"
            progressPercentage >= 50.0 -> "50-75"
            progressPercentage >= 25.0 -> "25-50"
            else -> "0-25"
        }

        val material = progressButtonMaterials[materialKey] ?: getDefaultProgressMaterial(materialKey)
        return parseButtonMaterial(material)
    }

    /**
     * 获取默认的进度材质 - 使用 Cobblemon 精灵球
     */
    private fun getDefaultProgressMaterial(materialKey: String): String {
        return when (materialKey) {
            "100" -> "COBBLEMON_PREMIER_BALL"             // 100% - 纪念球
            "75-99" -> "COBBLEMON_MASTER_BALL"  // 75-99% - 大师球
            "50-75" -> "COBBLEMON_ULTRA_BALL"   // 50-75% - 高级球
            "25-50" -> "COBBLEMON_GREAT_BALL"   // 25-50% - 超级球
            "0-25" -> "COBBLEMON_POKE_BALL"     // 0-25% - 精灵球
            else -> "EXPERIENCE_BOTTLE"         // 默认材质
        }
    }

    /**
     * 获取世代奖励描述
     */
    fun getGenerationRewardDescriptions(generationId: String): List<String> {
        val descriptions = config.getStringList("rewards.generation.$generationId.completion.descriptions")

        if (enableDebug) {
            plugin.logger.info("获取世代奖励描述 - $generationId: 找到 ${descriptions?.size ?: 0} 个描述")
        }

        return descriptions ?: listOf(
            "§7• §b钻石 x1",
            "§7• §a绿宝石 x2",
            "§7• §6金锭 x5",
            "§7• §5下界合金锭 x1"
        )
    }

    /**
     * 获取全世代完成奖励描述
     */
    fun getAllGenerationsRewardDescriptions(): List<String> {
        return config.getStringList("rewards.completion.all-generations.descriptions") ?: listOf(
            "§7• §b钻石 x100",
            "§7• §5下界合金锭 x20",
            "§7• §e信标 x5",
            "§7• §d龙蛋 x1",
            "§7• §6附魔金苹果 x10",
            "§7• §a不死图腾 x3",
            "§7• §5精灵大师称号"
        )
    }

    /**
     * 解析按钮材质配置
     */
    private fun parseButtonMaterial(materialConfig: String): ItemStack {
        return when {
            // Paper CustomModelData 格式: PAPER:CustomModelData数字
            materialConfig.startsWith("PAPER:", ignoreCase = true) -> {
                val parts = materialConfig.split(":")
                val item = ItemStack(org.bukkit.Material.PAPER)
                if (parts.size >= 2) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }

            // Cobblemon 材质格式: COBBLEMON_POKE_BALL
            materialConfig.startsWith("COBBLEMON_", ignoreCase = true) -> {
                createCobblemonButtonItem(materialConfig)
            }

            // 普通 Minecraft 材质，支持 CustomModelData
            else -> {
                val parts = materialConfig.split(":")
                val material = try {
                    org.bukkit.Material.valueOf(parts[0].uppercase())
                } catch (e: Exception) {
                    // 记录无效材质的警告
                    plugin.logger.warning("无效的材质名称: ${parts[0]}, 使用默认材质 BARRIER")
                    org.bukkit.Material.BARRIER
                }
                val item = ItemStack(material)

                if (parts.size > 1) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }
        }
    }

    /**
     * 创建 Cobblemon 按钮物品
     * 尝试创建真正的Cobblemon物品，失败时使用备用方案
     */
    private fun createCobblemonButtonItem(materialConfig: String): ItemStack {
        // 方法1：尝试直接通过Bukkit Material获取
        try {
            val material = org.bukkit.Material.valueOf(materialConfig.uppercase())
            val item = ItemStack(material)
            if (enableDebug) {
                plugin.logger.info("成功通过Bukkit Material创建Cobblemon按钮物品: $materialConfig -> ${material.name}")
            }
            return item
        } catch (e: IllegalArgumentException) {
            if (enableDebug) {
                plugin.logger.info("Material不存在: $materialConfig，使用备用方案")
            }
        } catch (e: Exception) {
            if (enableDebug) {
                plugin.logger.warning("Bukkit Material方法失败: ${e.message}")
            }
        }

        // 方法2：使用备用方案
        if (enableDebug) {
            plugin.logger.warning("无法创建Cobblemon按钮物品 $materialConfig，使用备用方案")
        }
        return when (materialConfig.uppercase()) {
            "COBBLEMON_POKE_BALL" -> ItemStack(org.bukkit.Material.RED_CONCRETE_POWDER)      // 红色 - 精灵球
            "COBBLEMON_GREAT_BALL" -> ItemStack(org.bukkit.Material.BLUE_CONCRETE_POWDER)    // 蓝色 - 超级球
            "COBBLEMON_ULTRA_BALL" -> ItemStack(org.bukkit.Material.YELLOW_CONCRETE_POWDER)  // 黄色 - 高级球
            "COBBLEMON_MASTER_BALL" -> ItemStack(org.bukkit.Material.PURPLE_CONCRETE_POWDER) // 紫色 - 大师球
            "COBBLEMON_TIMER_BALL" -> ItemStack(org.bukkit.Material.BLACK_CONCRETE_POWDER)   // 黑色 - 计时球
            "COBBLEMON_LUXURY_BALL" -> ItemStack(org.bukkit.Material.GREEN_CONCRETE_POWDER)  // 绿色 - 豪华球
            "COBBLEMON_PREMIER_BALL" -> ItemStack(org.bukkit.Material.WHITE_CONCRETE_POWDER) // 白色 - 纪念球
            "COBBLEMON_DUSK_BALL" -> ItemStack(org.bukkit.Material.MAGENTA_CONCRETE_POWDER)  // 品红 - 黄昏球
            "COBBLEMON_QUICK_BALL" -> ItemStack(org.bukkit.Material.CYAN_CONCRETE_POWDER)    // 青色 - 先机球
            else -> ItemStack(org.bukkit.Material.GRAY_CONCRETE_POWDER)                      // 灰色 - 默认
        }
    }

    /**
     * 创建GUI装饰物品
     */
    fun createDecorationItem(): ItemStack {
        return parseDecorationMaterial(guiDecorationMaterial)
    }

    /**
     * 创建上一页按钮物品
     */
    fun createPreviousPageItem(): ItemStack {
        return parsePaginationMaterial(previousPageMaterial)
    }

    /**
     * 创建下一页按钮物品
     */
    fun createNextPageItem(): ItemStack {
        return parsePaginationMaterial(nextPageMaterial)
    }

    /**
     * 创建页码指示器物品
     */
    fun createPageIndicatorItem(): ItemStack {
        return parseNavigationMaterial(pageIndicatorMaterial, Material.PAPER)
    }

    /**
     * 创建返回主菜单物品
     */
    fun createBackToMainMenuItem(): ItemStack {
        return parseNavigationMaterial(backToMainMenuMaterial, Material.BARRIER)
    }

    /**
     * 创建收集进度物品
     */
    fun createProgressInfoItem(): ItemStack {
        return parseNavigationMaterial(progressInfoMaterial, Material.EXPERIENCE_BOTTLE)
    }



    /**
     * 解析装饰材质配置
     * 支持多种格式：
     * 1. 普通材质: "GRAY_STAINED_GLASS_PANE"
     * 2. 普通材质+CustomModelData: "GRAY_STAINED_GLASS_PANE:12345"
     * 3. Paper CustomModelData: "PAPER:12345"
     * 4. Cobblemon 物品: "COBBLEMON_POKE_BALL"
     */
    private fun parseDecorationMaterial(materialConfig: String): ItemStack {
        return when {
            // Paper CustomModelData 格式: PAPER:CustomModelData数字
            materialConfig.startsWith("PAPER:", ignoreCase = true) -> {
                createPaperCustomModelDataItem(materialConfig)
            }

            // Cobblemon 材质格式: COBBLEMON_POKE_BALL
            materialConfig.startsWith("COBBLEMON_", ignoreCase = true) -> {
                val item = createCobblemonButtonItem(materialConfig)
                // 设置显示名称为空格，隐藏物品名称
                val meta = item.itemMeta
                if (meta != null) {
                    meta.setDisplayName(" ")
                    item.itemMeta = meta
                }
                item
            }

            // 普通 Minecraft 材质，支持 CustomModelData
            else -> {
                // 支持 "GRAY_STAINED_GLASS_PANE:1234" 这种格式
                val parts = materialConfig.split(":")
                val material = try { Material.valueOf(parts[0].uppercase()) } catch (e: Exception) { Material.GRAY_STAINED_GLASS_PANE }
                val item = ItemStack(material)

                // 设置显示名称为空格，隐藏物品名称
                val meta = item.itemMeta
                if (meta != null) {
                    meta.setDisplayName(" ")

                    // 如果有CustomModelData，设置它
                    if (parts.size > 1) {
                        val customModelData = parts[1].toIntOrNull()
                        if (customModelData != null) {
                            meta.setCustomModelData(customModelData)
                        }
                    }

                    item.itemMeta = meta
                }
                item
            }
        }
    }

    /**
     * 创建 Paper CustomModelData 装饰物品
     */
    private fun createPaperCustomModelDataItem(config: String): ItemStack {
        return try {
            // 解析格式: PAPER:CustomModelData数字
            val parts = config.split(":")
            if (parts.size >= 2) {
                val customModelData = parts[1].toIntOrNull()

                if (customModelData != null) {
                    // 使用 PAPER 材质
                    val item = ItemStack(Material.PAPER)
                    val meta = item.itemMeta

                    if (meta != null) {
                        // 设置显示名称为空格，隐藏物品名称
                        meta.setDisplayName(" ")
                        // 设置 CustomModelData
                        meta.setCustomModelData(customModelData)
                        item.itemMeta = meta

                        return item
                    }
                }
            }

            // 如果解析失败，返回默认装饰物品
            val defaultItem = ItemStack(Material.GRAY_STAINED_GLASS_PANE)
            val defaultMeta = defaultItem.itemMeta
            defaultMeta?.setDisplayName(" ")
            defaultItem.itemMeta = defaultMeta
            return defaultItem

        } catch (e: Exception) {
            val defaultItem = ItemStack(Material.GRAY_STAINED_GLASS_PANE)
            val defaultMeta = defaultItem.itemMeta
            defaultMeta?.setDisplayName(" ")
            defaultItem.itemMeta = defaultMeta
            return defaultItem
        }
    }

    /**
     * 解析分页按钮材质配置
     * 支持多种格式：
     * 1. 普通材质: "ARROW"
     * 2. 普通材质+CustomModelData: "ARROW:12345"
     * 3. Paper CustomModelData: "PAPER:12345"
     * 4. Cobblemon 物品: "COBBLEMON_POKE_BALL"
     */
    private fun parsePaginationMaterial(materialConfig: String): ItemStack {
        return when {
            // Paper CustomModelData 格式: PAPER:CustomModelData数字
            materialConfig.startsWith("PAPER:", ignoreCase = true) -> {
                createPaperPaginationItem(materialConfig)
            }

            // Cobblemon 材质格式: COBBLEMON_POKE_BALL
            materialConfig.startsWith("COBBLEMON_", ignoreCase = true) -> {
                createCobblemonButtonItem(materialConfig)
            }

            // 普通 Minecraft 材质，支持 CustomModelData
            else -> {
                // 支持 "ARROW:1234" 这种格式
                val parts = materialConfig.split(":")
                val material = try { Material.valueOf(parts[0].uppercase()) } catch (e: Exception) { Material.ARROW }
                val item = ItemStack(material)
                if (parts.size > 1) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }
        }
    }

    /**
     * 创建 Paper CustomModelData 分页按钮物品
     */
    private fun createPaperPaginationItem(config: String): ItemStack {
        return try {
            // 解析格式: PAPER:CustomModelData数字
            val parts = config.split(":")
            if (parts.size >= 2) {
                val customModelData = parts[1].toIntOrNull()

                if (customModelData != null) {
                    // 使用 PAPER 材质
                    val item = ItemStack(Material.PAPER)
                    val meta = item.itemMeta

                    if (meta != null) {
                        // 设置 CustomModelData
                        meta.setCustomModelData(customModelData)
                        item.itemMeta = meta

                        return item
                    }
                }
            }

            // 如果解析失败，返回默认箭头
            return ItemStack(Material.ARROW)

        } catch (e: Exception) {
            return ItemStack(Material.ARROW)
        }
    }

    /**
     * 解析导航物品材质配置
     * 支持多种格式：
     * 1. 普通材质: "PAPER", "BARRIER", "EXPERIENCE_BOTTLE"
     * 2. 普通材质+CustomModelData: "PAPER:12345"
     * 3. Paper CustomModelData: "PAPER:12345"
     * 4. Cobblemon 物品: "COBBLEMON_POKE_BALL"
     */
    private fun parseNavigationMaterial(materialConfig: String, defaultMaterial: Material = Material.PAPER): ItemStack {
        return when {
            // Paper CustomModelData 格式: PAPER:CustomModelData数字
            materialConfig.startsWith("PAPER:", ignoreCase = true) -> {
                createPaperNavigationItem(materialConfig)
            }

            // Cobblemon 材质格式: COBBLEMON_POKE_BALL
            materialConfig.startsWith("COBBLEMON_", ignoreCase = true) -> {
                createCobblemonButtonItem(materialConfig)
            }

            // 普通 Minecraft 材质，支持 CustomModelData
            else -> {
                // 支持 "MATERIAL:1234" 这种格式
                val parts = materialConfig.split(":")
                val material = try {
                    Material.valueOf(parts[0].uppercase())
                } catch (e: Exception) {
                    // 使用传入的默认材质
                    defaultMaterial
                }
                val item = ItemStack(material)

                // 如果有CustomModelData，设置它
                if (parts.size > 1) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }
        }
    }

    /**
     * 创建 Paper CustomModelData 导航物品
     */
    private fun createPaperNavigationItem(config: String): ItemStack {
        return try {
            // 解析格式: PAPER:CustomModelData数字
            val parts = config.split(":")
            if (parts.size >= 2) {
                val customModelData = parts[1].toIntOrNull()

                if (customModelData != null) {
                    // 使用 PAPER 材质
                    val item = ItemStack(Material.PAPER)
                    val meta = item.itemMeta

                    if (meta != null) {
                        // 设置 CustomModelData
                        meta.setCustomModelData(customModelData)
                        item.itemMeta = meta

                        return item
                    }
                }
            }

            // 如果解析失败，返回默认纸张
            return ItemStack(Material.PAPER)

        } catch (e: Exception) {
            return ItemStack(Material.PAPER)
        }
    }

    /**
     * 获取原始配置对象
     */
    fun getConfig(): FileConfiguration = config
}
