/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon.generation

/**
 * 第九世代精灵名称映射 (906-1025)
 * 包含帕底亚地区的所有精灵
 */
object PokemonGen9Mapping {
    
    /**
     * 获取第九世代精灵英文名到中文名的映射
     */
    fun getMapping(): Map<String, String> = mapOf(
            // 第九世代 (906-1025)
            "sprigatito" to "新叶喵",
            "floragato" to "蒂蕾喵",
            "meowscarada" to "魅力喵",
            "fuecoco" to "呆火鳄",
            "crocalor" to "炙烫鳄",
            "skeledirge" to "骨纹巨声鳄",
            "quaxly" to "润水鸭",
            "quaxwell" to "涌跃鸭",
            "quaquaval" to "狂欢浪舞鸭",
            "lechonk" to "爱吃豚",
            "oinkologne" to "飘香豚",
            "tarountula" to "团珠蛛",
            "spidops" to "操陷蛛",
            "nymble" to "豆蟋蟀",
            "lokix" to "烈腿蝗",
            "pawmi" to "布拨",
            "pawmo" to "布土拨",
            "pawmot" to "巴布土拨",
            "tandemaus" to "一对鼠",
            "maushold" to "一家鼠",
            "fidough" to "狗仔包",
            "dachsbun" to "麻花犬",
            "smoliv" to "迷你芙",
            "dolliv" to "奥利纽",
            "arboliva" to "奥利瓦",
            "squawkabilly" to "怒鹦哥",
            "nacli" to "盐石宝",
            "naclstack" to "盐石垒",
            "garganacl" to "盐石巨灵",
            "charcadet" to "炭小侍",
            "armarouge" to "红莲铠骑",
            "ceruledge" to "苍炎刃鬼",
            "tadbulb" to "光蚪仔",
            "bellibolt" to "电肚蛙",
            "wattrel" to "电海燕",
            "kilowattrel" to "大电海燕",
            "maschiff" to "偶叫獒",
            "mabosstiff" to "獒教父",
            "shroodle" to "涂标客",
            "grafaiai" to "涂标猿",
            "bramblin" to "纳噬草",
            "brambleghast" to "怖纳噬草",
            "toedscool" to "走鲸",
            "toedscruel" to "踏鲸",
            "klawf" to "拳蟹",
            "capsakid" to "热辣娃",
            "scovillain" to "狠辣椒",
            "rellor" to "虫滚泥",
            "rabsca" to "虫甲圣",
            "flittle" to "飞翩翩",
            "espathra" to "超翩翩",
            "tinkatink" to "小锻匠",
            "tinkatuff" to "大锻匠",
            "tinkaton" to "巨锻匠",
            "wiglett" to "海地鼠",
            "wugtrio" to "三海地鼠",
            "bombirdier" to "下石鸟",
            "finizen" to "海豚侠",
            "palafin" to "海豚人",
            "varoom" to "噗隆隆",
            "revavroom" to "普隆隆姆",
            "cyclizar" to "摩托蜥",
            "orthworm" to "拖拖蚓",
            "glimmet" to "晶光芽",
            "glimmora" to "晶光花",
            "greavard" to "墓仔狗",
            "houndstone" to "墓扬犬",
            "flamigo" to "火烈鸟",
            "cetoddle" to "巨鲸宝",
            "cetitan" to "浩大鲸",
            "veluza" to "轻身鳕",
            "dondozo" to "吃吼霸",
            "tatsugiri" to "米立龙",
            "annihilape" to "弃世猴",
            "clodsire" to "土王",
            "farigiraf" to "奇麒麟",
            "dudunsparce" to "土龙节节",
            "kingambit" to "仆刀将军",
            "greattusk" to "雄伟牙",
            "screamtail" to "吼叫尾",
            "brutebonnet" to "猛恶菇",
            "fluttermane" to "振翼发",
            "slitherwing" to "爬地翅",
            "sandyshocks" to "沙铁皮",
            "irontreads" to "铁辙迹",
            "ironbundle" to "铁包袱",
            "ironhands" to "铁臂膀",
            "ironjugulis" to "铁脖颈",
            "ironmoth" to "铁毒蛾",
            "ironthorns" to "铁荆棘",
            "frigibax" to "凉脊龙",
            "arctibax" to "冻脊龙",
            "baxcalibur" to "戟脊龙",
            "gimmighoul" to "索财灵",
            "gholdengo" to "赛富豪",
            "wochien" to "古简蜗",
            "chienpao" to "古剑豹",
            "tinglu" to "古鼎鹿",
            "chiyu" to "古玉鱼",
            "roaringmoon" to "轰鸣月",
            "ironvaliant" to "铁武者",
            "koraidon" to "故勒顿",
            "miraidon" to "密勒顿",
            "walkingwake" to "踏浪",
            "ironleaves" to "铁斑叶",
            "dipplin" to "裹蜜虫",
            "poltchageist" to "斯魅茶",
            "sinistcha" to "来悲粗茶",
            "okidogi" to "够赞狗",
            "munkidori" to "愿增猿",
            "fezandipiti" to "吉雉鸡",
            "ogerpon" to "厄诡椪",
            "archaludon" to "铝钢桥龙",
            "hydrapple" to "蜜集大蛇",
            "gougingfire" to "破空焰",
            "ragingbolt" to "奔雷号",
            "ironboulder" to "铁磐岩",
            "ironcrown" to "铁头壳",
            "terapagos" to "太乐巴戈斯",
            "pecharunt" to "桃歹郎",

            // 特殊精灵 (Let's Go)
            "meltan" to "美录坦",
            "melmetal" to "美录梅塔"
    )
}
