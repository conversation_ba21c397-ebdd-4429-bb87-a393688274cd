/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.util

import cn.acebrand.acedex.AceDex
import me.clip.placeholderapi.PlaceholderAPI
import org.bukkit.Bukkit
import org.bukkit.entity.Player

/**
 * 变量替换工具类
 * 支持自定义变量和PlaceholderAPI变量
 */
object VariableReplacer {
    
    /**
     * 替换字符串中的变量
     * @param text 原始文本
     * @param player 玩家对象（可选）
     * @param plugin AceDex插件实例（可选，用于AceDex内置变量）
     * @return 替换后的文本
     */
    fun replaceVariables(text: String, player: Player? = null, plugin: AceDex? = null): String {
        var result = text

        // 替换自定义变量
        result = replaceCustomVariables(result)

        // 替换AceDex内置变量
        if (player != null && plugin != null) {
            result = replaceAceDexVariables(result, player, plugin)
        }

        // 如果有PlaceholderAPI，替换PAPI变量
        if (player != null && Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            try {
                result = PlaceholderAPI.setPlaceholders(player, result)
            } catch (e: Exception) {
                // 如果PAPI处理失败，继续使用原文本
            }
        }

        return result
    }
    
    /**
     * 替换自定义变量
     * @param text 原始文本
     * @return 替换后的文本
     */
    private fun replaceCustomVariables(text: String): String {
        var result = text

        // 替换图片偏移变量 %img_offset_数字%
        val imgOffsetPattern = Regex("(§[0-9a-fk-or])?%img_offset_(-?\\d+)%")
        result = imgOffsetPattern.replace(result) { matchResult ->
            val colorCode = matchResult.groupValues[1]  // 捕获前面的颜色代码
            val offset = matchResult.groupValues[2].toInt()

            // 使用 ItemsAdder 标准的偏移格式 :offset_数字:
            // 这是 ItemsAdder 官方支持的偏移方式
            if (offset == 0) {
                colorCode  // 保持原有颜色代码
            } else {
                // 如果有颜色代码，在偏移后重新应用；否则使用白色
                val resetColor = if (colorCode.isNotEmpty()) colorCode else "§f"
                "${colorCode}:offset_${offset}:${resetColor}"
            }
        }

        // 可以在这里添加更多自定义变量的处理
        // 例如：
        // result = result.replace("%server_name%", Bukkit.getServerName())
        // result = result.replace("%online_players%", Bukkit.getOnlinePlayers().size.toString())

        return result
    }

    /**
     * 替换AceDex内置变量
     * @param text 原始文本
     * @param player 玩家对象
     * @param plugin AceDex插件实例
     * @return 替换后的文本
     */
    private fun replaceAceDexVariables(text: String, player: Player, plugin: AceDex): String {
        var result = text

        try {
            // 获取玩家数据
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            // 基础统计变量
            result = result.replace("%acedex_total_caught%", playerData.totalCaught.toString())
            result = result.replace("%acedex_party_count%", playerData.partyPokemon.size.toString())
            result = result.replace("%acedex_pc_count%", playerData.pcPokemon.size.toString())

            // 总体进度变量
            result = result.replace("%acedex_overall_percentage%", "${allProgress.overallPercentage}%")
            result = result.replace("%acedex_overall_caught%", allProgress.caughtPokemon.toString())
            result = result.replace("%acedex_overall_total%", allProgress.totalPokemon.toString())
            result = result.replace("%acedex_overall_progress%", "${allProgress.caughtPokemon}/${allProgress.totalPokemon}")



        } catch (e: Exception) {
            // 如果获取数据失败，保持原文本
            plugin.logger.warning("替换AceDex变量时发生错误: ${e.message}")
        }

        return result
    }
    
    /**
     * 检查文本是否包含变量
     * @param text 要检查的文本
     * @return 是否包含变量
     */
    fun containsVariables(text: String): Boolean {
        // 检查是否包含自定义变量
        if (containsCustomVariables(text)) {
            return true
        }
        
        // 检查是否包含PAPI变量
        if (text.contains("%") && text.contains("_")) {
            return true
        }
        
        return false
    }
    
    /**
     * 检查是否包含自定义变量
     * @param text 要检查的文本
     * @return 是否包含自定义变量
     */
    private fun containsCustomVariables(text: String): Boolean {
        // 检查图片偏移变量
        val imgOffsetPattern = Regex("%img_offset_(-?\\d+)%")
        if (imgOffsetPattern.containsMatchIn(text)) {
            return true
        }
        
        // 可以在这里添加更多自定义变量的检查
        
        return false
    }
    
    /**
     * 获取所有支持的自定义变量列表
     * @return 变量列表及其说明
     */
    fun getSupportedVariables(): Map<String, String> {
        return mapOf(
            // 自定义变量
            "%img_offset_数字%" to "图片偏移变量，例如 %img_offset_-90% 会被替换为对应的Unicode字符 (基础值0xE000+偏移值)",

            // AceDex内置变量
            "%acedex_total_caught%" to "玩家已收集的精灵总数",
            "%acedex_party_count%" to "玩家队伍中的精灵数量",
            "%acedex_pc_count%" to "玩家PC中的精灵数量",
            "%acedex_overall_percentage%" to "总体收集进度百分比",
            "%acedex_overall_caught%" to "总体已收集精灵数量",
            "%acedex_overall_total%" to "总体精灵总数",
            "%acedex_overall_progress%" to "总体进度（已收集/总数）",

            // 系统变量
            "%server_name%" to "服务器名称",
            "%online_players%" to "在线玩家数量"
        )
    }
}
