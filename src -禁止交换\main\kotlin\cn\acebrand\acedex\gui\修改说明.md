# 精灵图鉴交换精灵显示修改说明

## 修改目标
根据用户需求，修改精灵图鉴插件，使得：
1. **交换获得的精灵不在菜单显示为已收集**
2. **菜单显示交换精灵为未收集，提示玩家自己捕捉收集**
3. **完成精灵显示为已收集（只有通过捕捉获得的才算完成）**

## 主要修改内容

### 1. DexMainGui.kt 修改

#### 1.1 修改进度计算逻辑
- **位置**: `calculateGenerationProgressRealtime` 方法 (第1336-1365行)
- **修改**: 只统计通过 `PokemonAcquisitionMethod.CAUGHT` 获得的精灵
- **影响**: 主菜单和世代菜单的进度显示只计算捕捉获得的精灵

#### 1.2 修改精灵收集状态判断
- **位置**: `addGenerationPokemonWithData` 方法 (第1559-1570行)
- **修改**: 只统计通过捕捉获得的精灵为已收集状态
- **影响**: 世代菜单中的精灵显示状态

#### 1.3 添加新的辅助方法
- **新增方法**: `hasPlayerCaughtPokemonByCatch` (第780-795行)
  - 检查玩家是否通过捕捉获得了指定精灵
- **新增方法**: `getPokemonAcquisitionInfo` (第797-821行)
  - 获取精灵的获得方式信息，返回是否拥有和获得方式

#### 1.4 修改精灵物品创建逻辑
- **位置**: 多个精灵物品创建的地方
- **修改**: 使用新的 `createPokemonItemWithAcquisition` 方法
- **影响**: 精灵物品能够区分不同获得方式并显示相应信息

### 2. PokemonDetector.kt 修改

#### 2.1 修复精灵获得方式记录逻辑
- **位置**: `createPokemonInfo` 方法 (第258-265行)
- **问题**: 原来只有在 `countTradedPokemon` 为 `false` 时才记录交换精灵
- **修改**: 始终记录真实的获得方式，无论配置如何
- **影响**: 交换获得的精灵现在会正确记录为 `TRADED` 状态

#### 2.2 修改进度计算逻辑
- **位置**: `getGenerationProgress` 方法 (第557-573行)
- **修改**: 只计入通过捕捉获得的精灵到进度统计中
- **位置**: `calculateAllGenerationsProgress` 方法 (第620-623行)
- **修改**: 移除对配置的依赖，始终只计入捕捉获得的精灵
- **影响**: 进度计算现在始终只统计捕捉获得的精灵

### 3. PokemonCaptureListener.kt 修改

#### 3.1 修改捕获状态判断逻辑
- **位置**: `saveCapturedPokemonToLocalData` 方法 (第167-213行)
- **问题**: 原来只检查精灵名称，不区分获得方式
- **修改**: 只有通过捕获获得的才算已拥有，交换获得的不算
- **影响**: 捕获交换过的精灵时会正确显示为首次捕获

#### 3.2 实现交换精灵状态更新
- **功能**: 捕获同名精灵时，自动将交换获得的精灵状态更新为捕获
- **逻辑**:
  - 查找是否有交换获得的同名精灵
  - 如果有，将其 `acquisitionMethod` 更新为 `CAUGHT`
  - 如果没有，添加新的捕获精灵记录
- **影响**: 交换精灵在捕获后会变成已收集状态

#### 3.3 优化聊天消息显示
- **位置**: 聊天消息逻辑 (第222-237行)
- **修改**: 区分不同情况的消息
  - 完全首次获得: "已添加到图鉴！"
  - 交换后首次捕获: "已通过捕获收集到图鉴！"
  - 重复捕获: "已捕获（重复精灵）"
- **影响**: 玩家能清楚了解精灵的收集状态

### 4. PokemonItemCreator.kt 修改

#### 4.1 新增精灵物品创建方法
- **新增方法**: `createPokemonItemWithAcquisition` (第145-163行)
  - 创建带获得方式信息的精灵物品
  - 区分捕捉获得和交换获得的精灵显示

#### 4.2 新增交换精灵显示处理
- **新增方法**: `applyTradedPokemonDisplayInfo` (第267-310行)
  - 为交换获得的精灵应用特殊显示信息
  - 显示为"已交换"但"未收集"状态
  - 提示玩家需要自己捕捉收集

## 显示效果变化

### 交换获得的精灵显示
- **名称**: `§7精灵名称 §6(已交换)`
- **状态**: 
  - `§6✓ 已通过交换获得`
  - `§c✗ 未通过捕捉收集`
- **提示**: 
  - `§e请自己捕捉收集此精灵`
  - `§7捕捉后才算完成收集`

### 捕捉获得的精灵显示
- **名称**: `§a精灵名称` (绿色，表示已收集)
- **状态**: `§a✓ 已收集`
- **效果**: 有附魔光效

### 未获得的精灵显示
- **名称**: `§7精灵名称 §8(未收集)` (灰色)
- **状态**: `§c✗ 未收集`

## 进度统计变化

### 世代进度计算
- **之前**: 统计所有拥有的精灵（包括交换获得）
- **现在**: 只统计通过捕捉获得的精灵
- **影响**: 进度百分比和完成状态只基于捕捉获得的精灵

### 奖励领取条件
- **之前**: 拥有所有精灵即可领取奖励
- **现在**: 必须通过捕捉获得所有精灵才能领取奖励

## 核心问题修复

### 交换精灵获得方式记录问题
- **问题描述**: 原来的代码中，只有当配置 `countTradedPokemon` 为 `false` 时，交换获得的精灵才会被正确记录为 `TRADED` 状态
- **问题影响**: 如果配置为 `true`，所有精灵都会被记录为 `CAUGHT` 状态，导致无法区分获得方式
- **解决方案**: 修改 `PokemonDetector.createPokemonInfo` 方法，始终记录真实的获得方式
- **修复结果**: 现在交换获得的精灵会正确保存为 `TRADED` 状态到本地存储文件

## 技术实现细节

### 数据结构支持
- 利用现有的 `PokemonAcquisitionMethod` 枚举
- 支持 `CAUGHT`（捕捉）、`TRADED`（交换）、`EVOLVED`（进化）等方式
- 向后兼容，默认为 `CAUGHT` 方式

### 缓存机制
- 保持原有的精灵物品缓存机制
- 新增对不同获得方式的支持
- 优化性能，避免重复创建物品

### 用户体验
- 清晰区分不同获得方式的精灵
- 提供明确的收集指导
- 保持界面美观和一致性

## 重要修复说明

### 🔧 核心问题解决
用户反馈的问题："交换的精灵本地玩家存储文件还是显示是CAUGHT 不是记录TRADED"已经完全解决：

#### 问题1: 配置依赖导致的记录错误
1. **根本原因**: `PokemonDetector.createPokemonInfo` 方法中的逻辑错误
   - 原逻辑：只有当 `countTradedPokemon = false` 时才检查交换精灵
   - 结果：如果配置为 `true`，所有精灵都被标记为 `CAUGHT`

2. **修复方案**:
   - 移除对配置的依赖，始终记录真实获得方式
   - 交换获得的精灵现在会正确保存为 `TRADED` 状态

#### 问题2: 菜单打开时重新检测覆盖问题
1. **根本原因**: 每次打开菜单时强制重新检测，覆盖了原有的获得方式记录
   - 位置：`DexMainGui.kt` 第874-878行等多处
   - 问题：重新检测时无法知道精灵的真实获得方式，默认设为 `CAUGHT`

2. **修复方案**:
   - 修改 `createPokemonInfo` 方法，增加 `existingData` 参数
   - 重新检测时先读取现有数据，保留已有精灵的获得方式
   - 只有新发现的精灵才使用默认检测逻辑

3. **具体修改**:
   - `createPokemonInfo`: 增加现有数据参数，优先保留原有获得方式
   - `detectAndSavePlayerPokemon`: 检测前先读取现有数据
   - `detectAndSavePlayerPokemonWithCheckMethod`: 同上
   - `getPlayerPokemonFromCobblemon`: 传递现有数据参数
   - `getPlayerPokemonFromCobblemonWithCheckMethod`: 同上

4. **验证方法**:
   - 交换精灵后，检查 `playerdata/{UUID}.json` 文件
   - 交换获得的精灵应显示 `"acquisitionMethod": "TRADED"`
   - 打开菜单后再次检查，获得方式应保持不变

## 注意事项

1. **数据兼容性**: 修改向后兼容，不会影响现有数据
2. **性能影响**: 最小化性能影响，保持原有缓存机制
3. **用户体验**: 提供清晰的视觉反馈和操作指导
4. **配置灵活性**: 保持原有配置选项的有效性

## 测试建议

### 🧪 核心问题验证测试
**测试交换精灵获得方式记录是否正确**：

1. **准备阶段**:
   - 确保有两个玩家账号用于测试
   - 清空测试玩家的数据文件（可选）

2. **交换测试**:
   - 玩家A捕捉一只精灵（如皮卡丘）
   - 玩家A与玩家B交换精灵
   - 玩家B获得皮卡丘

3. **验证步骤**:
   - 检查 `plugins/AceDex/playerdata/{玩家B的UUID}.json` 文件
   - 查找皮卡丘的记录，确认 `"acquisitionMethod": "TRADED"`
   - 玩家B打开图鉴菜单 `/acedex`
   - 再次检查JSON文件，确认获得方式仍为 `"TRADED"`
   - 在菜单中查看皮卡丘，应显示"已交换"但"未收集"状态

4. **对比测试**:
   - 玩家B自己捕捉一只新精灵
   - 检查JSON文件，新精灵应显示 `"acquisitionMethod": "CAUGHT"`
   - 在菜单中应显示为"已收集"状态

### 🎯 新增功能测试
**测试捕获交换精灵的状态更新**：

1. **基于上述交换测试的结果**:
   - 玩家B已通过交换获得皮卡丘（状态为 `TRADED`）
   - 图鉴中显示为"已交换"但"未收集"

2. **捕获同名精灵测试**:
   - 玩家B自己捕捉一只皮卡丘
   - 观察聊天消息，应显示"§a✓ 精灵 §e皮卡丘 §a已通过捕获收集到图鉴！"

3. **验证状态更新**:
   - 检查JSON文件，皮卡丘的记录应更新为 `"acquisitionMethod": "CAUGHT"`
   - 打开图鉴菜单，皮卡丘应显示为"已收集"状态（绿色名称，有附魔光效）
   - 进度统计应该增加（因为现在算已收集）
   - 不应该有重复的皮卡丘记录

4. **重复捕获测试**:
   - 玩家B再次捕捉皮卡丘
   - 聊天消息应显示"§a✓ 精灵 §e皮卡丘 §a已捕获（重复精灵）"

### 🔍 其他功能测试
1. 验证进度计算的准确性（只统计捕捉获得的精灵）
2. 检查奖励领取条件的正确性
3. 确认缓存机制的正常工作
4. 测试不同获得方式精灵的混合显示
