package cn.acebrand.acebiomespawner.command

import cn.acebrand.acebiomespawner.AceBiomeSpawner
import cn.acebrand.acebiomespawner.util.AnnouncementUtil
import org.bukkit.Bukkit
import org.bukkit.ChatColor
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import org.bukkit.entity.Player

/**
 * AceSpawner 命令处理器
 */
class AceSpawnerCommand : CommandExecutor, TabCompleter {

    override fun onCommand(sender: CommandSender, command: Command, label: String, args: Array<out String>): Bo<PERSON>an {
        if (!sender.hasPermission("acebiomespawner.admin")) {
            sender.sendMessage("${ChatColor.RED}你没有权限使用此命令！")
            return true
        }

        when (args.getOrNull(0)?.lowercase()) {
            "reload" -> executeReload(sender)
            "status" -> executeStatus(sender)
            "toggle" -> executeToggle(sender, args)
            "spawn" -> executeSpawn(sender, args)
            "info" -> executeInfo(sender)
            "config" -> executeConfig(sender, args)
            else -> executeHelp(sender)
        }

        return true
    }

    override fun onTabComplete(sender: CommandSender, command: Command, alias: String, args: Array<out String>): List<String> {
        if (!sender.hasPermission("acebiomespawner.admin")) {
            return emptyList()
        }

        return when (args.size) {
            1 -> listOf("reload", "status", "toggle", "spawn", "info", "config").filter {
                it.startsWith(args[0], ignoreCase = true)
            }
            2 -> when (args[0].lowercase()) {
                "toggle" -> listOf("true", "false")
                "spawn" -> Bukkit.getOnlinePlayers().map { it.name }
                "config" -> listOf("interval", "range", "players")
                else -> emptyList()
            }
            3 -> when (args[0].lowercase()) {
                "config" -> when (args[1].lowercase()) {
                    "interval" -> listOf("300", "600", "900")
                    "range" -> listOf("64", "128", "256")
                    "players" -> listOf("1", "2", "3")
                    else -> emptyList()
                }
                else -> emptyList()
            }
            else -> emptyList()
        }
    }

    private fun executeReload(sender: CommandSender) {
        try {
            AceBiomeSpawner.INSTANCE.reloadPluginConfig()
            sender.sendMessage("${ChatColor.GREEN}配置重新加载完成！")
            AceBiomeSpawner.INSTANCE.logger.info("管理员 ${sender.name} 重新加载了配置")
        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}配置重新加载失败: ${e.message}")
            AceBiomeSpawner.INSTANCE.logger.severe("重新加载配置时发生错误: ${e.message}")
        }
    }

    private fun executeStatus(sender: CommandSender) {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        sender.sendMessage("${ChatColor.GOLD}=== AceBiomeSpawner 状态 ===")

        val statusColor = if (config.enabled) ChatColor.GREEN else ChatColor.RED
        val statusText = if (config.enabled) "已启用" else "已禁用"
        sender.sendMessage("${ChatColor.WHITE}启用状态: $statusColor$statusText")

        sender.sendMessage("${ChatColor.WHITE}生成间隔: ${ChatColor.YELLOW}${config.spawnInterval} 秒")
        sender.sendMessage("${ChatColor.WHITE}检测范围: ${ChatColor.YELLOW}${config.detectionRange} 方块")
        sender.sendMessage("${ChatColor.WHITE}每次选择玩家: ${ChatColor.YELLOW}${config.playersPerSpawn} 人")

        val onlinePlayers = Bukkit.getOnlinePlayers().size
        sender.sendMessage("${ChatColor.WHITE}在线玩家: ${ChatColor.AQUA}$onlinePlayers")

        sender.sendMessage("${ChatColor.WHITE}配置生物群系: ${ChatColor.AQUA}${config.biomeConfigs.size}")
        sender.sendMessage("${ChatColor.WHITE}目标世界: ${ChatColor.AQUA}${config.targetWorlds.joinToString(", ")}")
    }

    private fun executeToggle(sender: CommandSender, args: Array<out String>) {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        val newState = if (args.size > 1) {
            args[1].toBoolean()
        } else {
            !config.enabled
        }

        config.enabled = newState
        AceBiomeSpawner.CONFIG_MANAGER.saveConfig()

        val statusText = if (newState) "启用" else "禁用"
        val color = if (newState) ChatColor.GREEN else ChatColor.RED

        sender.sendMessage("${color}插件已${statusText}！")

        if (newState) {
            AceBiomeSpawner.INSTANCE.restartSpawnerTask()
        }

        AnnouncementUtil.sendInfoAnnouncement("生物群系精灵生成系统已${statusText}")

        AceBiomeSpawner.INSTANCE.logger.info("管理员 ${sender.name} ${statusText}了插件")
    }

    private fun executeSpawn(sender: CommandSender, args: Array<out String>) {
        try {
            val spawnerManager = AceBiomeSpawner.INSTANCE.getSpawnerManager()

            if (args.size > 1) {
                val targetPlayer = Bukkit.getPlayer(args[1])
                if (targetPlayer != null) {
                    // 为指定玩家触发生成，忽略生物群系概率，并传递触发者信息
                    val success = spawnerManager.executeSpawnForPlayer(
                        targetPlayer,
                        ignoreChance = true,
                        triggerPlayerName = sender.name
                    )
                    if (success) {
                        sender.sendMessage("${ChatColor.GREEN}已为玩家 ${targetPlayer.name} 触发精灵生成并发送公告")
                        AceBiomeSpawner.INSTANCE.logger.info("管理员 ${sender.name} 为玩家 ${targetPlayer.name} 触发了精灵生成")
                    } else {
                        sender.sendMessage("${ChatColor.YELLOW}为玩家 ${targetPlayer.name} 生成精灵失败，可能是生物群系配置问题或位置不合适")
                    }
                } else {
                    sender.sendMessage("${ChatColor.RED}玩家 ${args[1]} 不在线！")
                }
            } else {
                spawnerManager.executeSpawnCycle()
                sender.sendMessage("${ChatColor.GREEN}已触发随机精灵生成")
                AceBiomeSpawner.INSTANCE.logger.info("管理员 ${sender.name} 触发了随机精灵生成")
            }

        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}执行生成时发生错误: ${e.message}")
            AceBiomeSpawner.INSTANCE.logger.severe("手动触发生成时发生错误: ${e.message}")
        }
    }

    private fun executeInfo(sender: CommandSender) {
        sender.sendMessage("${ChatColor.GOLD}=== AceBiomeSpawner 信息 ===")
        sender.sendMessage("${ChatColor.WHITE}版本: ${ChatColor.YELLOW}${AceBiomeSpawner.VERSION}")
        sender.sendMessage("${ChatColor.WHITE}作者: ${ChatColor.AQUA}AceBrand")
        sender.sendMessage("${ChatColor.WHITE}描述: ${ChatColor.GREEN}生物群系随机精灵生成插件")
        sender.sendMessage("${ChatColor.WHITE}支持: ${ChatColor.LIGHT_PURPLE}Minecraft 1.21.1 + Cobblemon")
    }

    private fun executeConfig(sender: CommandSender, args: Array<out String>) {
        if (args.size < 3) {
            sender.sendMessage("${ChatColor.RED}用法: /acespawner config <选项> <值>")
            sender.sendMessage("${ChatColor.YELLOW}可用选项: interval, range, players")
            return
        }

        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()

        when (args[1].lowercase()) {
            "interval" -> {
                val seconds = args[2].toIntOrNull()
                if (seconds != null && seconds in 10..3600) {
                    config.spawnInterval = seconds
                    AceBiomeSpawner.CONFIG_MANAGER.saveConfig()
                    AceBiomeSpawner.INSTANCE.restartSpawnerTask()
                    sender.sendMessage("${ChatColor.GREEN}生成间隔已设置为 $seconds 秒")
                } else {
                    sender.sendMessage("${ChatColor.RED}间隔时间必须在10-3600秒之间！")
                }
            }
            "range" -> {
                val blocks = args[2].toIntOrNull()
                if (blocks != null && blocks in 16..256) {
                    config.detectionRange = blocks
                    AceBiomeSpawner.CONFIG_MANAGER.saveConfig()
                    sender.sendMessage("${ChatColor.GREEN}检测范围已设置为 $blocks 方块")
                } else {
                    sender.sendMessage("${ChatColor.RED}检测范围必须在16-256方块之间！")
                }
            }
            "players" -> {
                val players = args[2].toIntOrNull()
                if (players != null && players in 1..10) {
                    config.playersPerSpawn = players
                    AceBiomeSpawner.CONFIG_MANAGER.saveConfig()
                    sender.sendMessage("${ChatColor.GREEN}每次选择玩家数量已设置为 $players 人")
                } else {
                    sender.sendMessage("${ChatColor.RED}玩家数量必须在1-10之间！")
                }
            }
            else -> {
                sender.sendMessage("${ChatColor.RED}未知的配置选项: ${args[1]}")
            }
        }
    }

    private fun executeHelp(sender: CommandSender) {
        sender.sendMessage("${ChatColor.GOLD}=== AceBiomeSpawner 命令帮助 ===")
        sender.sendMessage("${ChatColor.YELLOW}/acespawner reload ${ChatColor.WHITE}- 重新加载配置")
        sender.sendMessage("${ChatColor.YELLOW}/acespawner status ${ChatColor.WHITE}- 查看状态")
        sender.sendMessage("${ChatColor.YELLOW}/acespawner toggle [true/false] ${ChatColor.WHITE}- 切换启用状态")
        sender.sendMessage("${ChatColor.YELLOW}/acespawner spawn [player] ${ChatColor.WHITE}- 手动触发生成")
        sender.sendMessage("${ChatColor.YELLOW}/acespawner info ${ChatColor.WHITE}- 查看插件信息")
        sender.sendMessage("${ChatColor.YELLOW}/acespawner config <选项> <值> ${ChatColor.WHITE}- 修改配置")
        sender.sendMessage("${ChatColor.GRAY}别名: /abs")
    }
}
