package cn.acebrand.acebiomespawner.util

/**
 * 生物群系名称工具类
 * 提供英文生物群系名称到中文名称的映射
 */
object BiomeNameUtil {

    private val biomeNameMap = mapOf(
        // ========== 主世界生物群系 ==========

        // 平原类
        "minecraft:plains" to "平原",
        "minecraft:sunflower_plains" to "向日葵平原",

        // 森林类
        "minecraft:forest" to "森林",
        "minecraft:birch_forest" to "桦木森林",
        "minecraft:dark_forest" to "黑森林",
        "minecraft:flower_forest" to "繁花森林",
        "minecraft:old_growth_birch_forest" to "原始桦木森林",

        // 针叶林类
        "minecraft:taiga" to "针叶林",
        "minecraft:snowy_taiga" to "积雪针叶林",
        "minecraft:old_growth_pine_taiga" to "原始松木针叶林",
        "minecraft:old_growth_spruce_taiga" to "原始云杉针叶林",

        // 沙漠类
        "minecraft:desert" to "沙漠",

        // 热带草原类
        "minecraft:savanna" to "热带草原",
        "minecraft:savanna_plateau" to "热带草原高原",
        "minecraft:windswept_savanna" to "风袭热带草原",

        // 恶地类
        "minecraft:badlands" to "恶地",
        "minecraft:eroded_badlands" to "风蚀恶地",
        "minecraft:wooded_badlands" to "繁茂恶地",

        // 山地类
        "minecraft:windswept_hills" to "风袭丘陵",
        "minecraft:windswept_forest" to "风袭森林",
        "minecraft:windswept_gravelly_hills" to "风袭砂砾丘陵",
        "minecraft:meadow" to "草甸",
        "minecraft:grove" to "雪林",
        "minecraft:snowy_slopes" to "积雪山坡",
        "minecraft:frozen_peaks" to "冰冻山峰",
        "minecraft:jagged_peaks" to "尖峭山峰",
        "minecraft:stony_peaks" to "裸岩山峰",

        // 沼泽类
        "minecraft:swamp" to "沼泽",
        "minecraft:mangrove_swamp" to "红树林沼泽",

        // 丛林类
        "minecraft:jungle" to "丛林",
        "minecraft:sparse_jungle" to "稀疏丛林",
        "minecraft:bamboo_jungle" to "竹林",

        // 冰雪类
        "minecraft:snowy_plains" to "积雪平原",
        "minecraft:ice_spikes" to "冰刺平原",
        "minecraft:frozen_ocean" to "冰冻海洋",
        "minecraft:deep_frozen_ocean" to "深冰冻海洋",

        // 海洋类
        "minecraft:ocean" to "海洋",
        "minecraft:deep_ocean" to "深海",
        "minecraft:lukewarm_ocean" to "温海",
        "minecraft:deep_lukewarm_ocean" to "深温海",
        "minecraft:warm_ocean" to "暖海",
        "minecraft:cold_ocean" to "冷海",
        "minecraft:deep_cold_ocean" to "深冷海",

        // 河流类
        "minecraft:river" to "河流",
        "minecraft:frozen_river" to "冰冻河流",

        // 海滩类
        "minecraft:beach" to "海滩",
        "minecraft:snowy_beach" to "积雪海滩",
        "minecraft:stony_shore" to "石岸",

        // 蘑菇岛
        "minecraft:mushroom_fields" to "蘑菇岛",

        // 洞穴类
        "minecraft:dripstone_caves" to "溶洞",
        "minecraft:lush_caves" to "繁茂洞穴",

        // 深暗之域
        "minecraft:deep_dark" to "深暗之域",

        // ========== 下界生物群系 ==========
        "minecraft:nether_wastes" to "下界荒地",
        "minecraft:crimson_forest" to "绯红森林",
        "minecraft:warped_forest" to "诡异森林",
        "minecraft:soul_sand_valley" to "灵魂沙峡谷",
        "minecraft:basalt_deltas" to "玄武岩三角洲",

        // ========== 末地生物群系 ==========
        "minecraft:the_end" to "末地",
        "minecraft:end_highlands" to "末地高地",
        "minecraft:end_midlands" to "末地中地",
        "minecraft:small_end_islands" to "末地小岛",
        "minecraft:end_barrens" to "末地荒地",

        // ========== 其他/变种生物群系 ==========
        "minecraft:void" to "虚空",

        // 旧版本兼容
        "minecraft:mountains" to "山地",
        "minecraft:mountain_edge" to "山地边缘",
        "minecraft:wooded_mountains" to "繁茂山地",
        "minecraft:gravelly_mountains" to "砂砾山地",
        "minecraft:modified_jungle" to "丛林变种",
        "minecraft:modified_jungle_edge" to "丛林边缘变种",
        "minecraft:tall_birch_forest" to "高桦木森林",
        "minecraft:tall_birch_hills" to "高桦木丘陵",
        "minecraft:dark_forest_hills" to "黑森林丘陵",
        "minecraft:snowy_tundra" to "积雪苔原",
        "minecraft:snowy_mountains" to "积雪山地",
        "minecraft:giant_tree_taiga" to "巨型针叶林",
        "minecraft:giant_tree_taiga_hills" to "巨型针叶林丘陵",
        "minecraft:giant_spruce_taiga" to "巨型云杉针叶林",
        "minecraft:giant_spruce_taiga_hills" to "巨型云杉针叶林丘陵",
        "minecraft:stone_shore" to "石岸",
        "minecraft:snowy_beach" to "积雪海滩",
        "minecraft:desert_hills" to "沙漠丘陵",
        "minecraft:wooded_hills" to "繁茂丘陵",
        "minecraft:taiga_hills" to "针叶林丘陵",
        "minecraft:mountain_edge" to "山地边缘",
        "minecraft:jungle_hills" to "丛林丘陵",
        "minecraft:jungle_edge" to "丛林边缘",
        "minecraft:birch_forest_hills" to "桦木森林丘陵",
        "minecraft:snowy_taiga_hills" to "积雪针叶林丘陵",
        "minecraft:snowy_taiga_mountains" to "积雪针叶林山地",
        "minecraft:swamp_hills" to "沼泽丘陵",

        // ========== Terralith 生物群系 (完整版本) ==========

        // 高山类生物群系
        "terralith:alpine_grove" to "高山林地",
        "terralith:alpine_highlands" to "高山高地",
        "terralith:frozen_cliffs" to "冰冻悬崖",
        "terralith:glacial_chasm" to "冰川峡谷",
        "terralith:rocky_mountains" to "岩石山脉",
        "terralith:snowy_badlands" to "雪地恶地",
        "terralith:snowy_maple_forest" to "雪地枫林",
        "terralith:wintry_forest" to "寒冬森林",
        "terralith:wintry_lowlands" to "寒冬低地",
        "terralith:bryce_canyon" to "布莱斯峡谷",
        "terralith:snowy_cherry_grove" to "雪地樱花林",
        "terralith:snowy_shield" to "雪地护盾",
        "terralith:emerald_peaks" to "翡翠峰",
        "terralith:volcanic_peaks" to "火山峰",
        "terralith:scarlet_mountains" to "猩红山脉",
        "terralith:painted_mountains" to "彩绘山脉",
        "terralith:haze_mountain" to "雾霾山",
        "terralith:white_cliffs" to "白色悬崖",
        "terralith:granite_cliffs" to "花岗岩悬崖",
        "terralith:windswept_spires" to "风蚀尖塔",
        "terralith:stony_spires" to "石质尖塔",

        // 森林类生物群系
        "terralith:birch_taiga" to "桦木针叶林",
        "terralith:siberian_grove" to "西伯利亚林地",
        "terralith:siberian_taiga" to "西伯利亚针叶林",
        "terralith:cloud_forest" to "云雾森林",
        "terralith:forested_highlands" to "森林高地",
        "terralith:lavender_forest" to "薰衣草森林",
        "terralith:sakura_grove" to "樱花林地",
        "terralith:moonlight_grove" to "月光林地",
        "terralith:shield" to "护盾林",
        "terralith:shield_clearing" to "护盾林空地",
        "terralith:temperate_highlands" to "温带高地",
        "terralith:tropical_jungle" to "热带丛林",
        "terralith:jungle_mountains" to "丛林山脉",
        "terralith:rocky_jungle" to "岩石丛林",
        "terralith:amethyst_rainforest" to "紫水晶雨林",

        // 峡谷和山谷类生物群系
        "terralith:blooming_plateau" to "花开高原",
        "terralith:blooming_valley" to "花开峡谷",
        "terralith:lavender_valley" to "薰衣草峡谷",
        "terralith:lush_valley" to "繁茂峡谷",
        "terralith:moonlight_valley" to "月光峡谷",
        "terralith:sakura_valley" to "樱花峡谷",
        "terralith:valley_clearing" to "峡谷空地",
        "terralith:sandstone_valley" to "砂岩峡谷",
        "terralith:amethyst_canyon" to "紫水晶峡谷",
        "terralith:desert_canyon" to "沙漠峡谷",
        "terralith:yosemite_cliffs" to "约塞米蒂悬崖",
        "terralith:yosemite_lowlands" to "约塞米蒂低地",

        // 平原和草原类生物群系
        "terralith:brushland" to "灌木地",
        "terralith:shrubland" to "灌木地",
        "terralith:cold_shrubland" to "寒冷灌木地",
        "terralith:hot_shrubland" to "炎热灌木地",
        "terralith:rocky_shrubland" to "岩石灌木地",
        "terralith:highlands" to "高地",
        "terralith:temperate_highlands" to "温带高地",
        "terralith:steppe" to "草原",
        "terralith:ashen_savanna" to "灰烬热带草原",
        "terralith:savanna_badlands" to "热带草原恶地",
        "terralith:savanna_slopes" to "热带草原坡地",
        "terralith:fractured_savanna" to "破碎热带草原",
        "terralith:yellowstone" to "黄石公园",

        // 沙漠类生物群系
        "terralith:ancient_sands" to "远古沙地",
        "terralith:arid_highlands" to "干旱高地",
        "terralith:terralith_arid_highlands" to "干旱高地",  // 重复前缀格式
        "terralith:desert_spires" to "沙漠尖塔",
        "terralith:gravel_desert" to "砾石沙漠",
        "terralith:lush_desert" to "繁茂沙漠",
        "terralith:desert_oasis" to "沙漠绿洲",
        "terralith:red_oasis" to "红色绿洲",

        // 火山类生物群系
        "terralith:caldera" to "火山口",
        "terralith:volcanic_crater" to "火山坑",
        "terralith:basalt_cliffs" to "玄武岩悬崖",

        // 海滩和海洋类生物群系
        "terralith:gravel_beach" to "砾石海滩",
        "terralith:warm_river" to "温暖河流",
        "terralith:deep_warm_ocean" to "深层温暖海洋",

        // 沼泽类生物群系
        "terralith:orchid_swamp" to "兰花沼泽",
        "terralith:ice_marsh" to "冰沼泽",

        // 天空岛类生物群系
        "terralith:skylands_autumn" to "天空岛秋林",
        "terralith:skylands_spring" to "天空岛春林",
        "terralith:skylands_summer" to "天空岛夏林",
        "terralith:skylands_winter" to "天空岛冬林",
        "terralith:alpha_islands" to "阿尔法群岛",
        "terralith:alpha_islands_winter" to "阿尔法群岛冬季",
        "terralith:mirage_isles" to "海市蜃楼岛",

        // 台地类生物群系
        "terralith:white_mesa" to "白色台地",
        "terralith:warped_mesa" to "扭曲台地",

        // 洞穴类生物群系
        "terralith:andesite_caves" to "安山岩洞穴",
        "terralith:diorite_caves" to "闪长岩洞穴",
        "terralith:granite_caves" to "花岗岩洞穴",
        "terralith:tuff_caves" to "凝灰岩洞穴",
        "terralith:deep_caves" to "深层洞穴",
        "terralith:mantle_caves" to "地幔洞穴",
        "terralith:frostfire_caves" to "霜火洞穴",
        "terralith:thermal_caves" to "热力洞穴",
        "terralith:infested_caves" to "虫蛀洞穴",
        "terralith:fungal_caves" to "真菌洞穴",
        "terralith:underground_jungle" to "地下丛林",

        // 重复前缀格式的常见Terralith生物群系
        "terralith:terralith_alpine_grove" to "高山林地",
        "terralith:terralith_sakura_grove" to "樱花林地",
        "terralith:terralith_moonlight_valley" to "月光峡谷",
        "terralith:terralith_volcanic_crater" to "火山坑",
        "terralith:terralith_ancient_sands" to "远古沙地",
        "terralith:terralith_desert_spires" to "沙漠尖塔",
        "terralith:terralith_tropical_jungle" to "热带丛林",
        "terralith:terralith_temperate_highlands" to "温带高地",
        "terralith:terralith_skylands_autumn" to "天空岛秋林",
        "terralith:terralith_skylands_spring" to "天空岛春林",
        "terralith:terralith_skylands_summer" to "天空岛夏林",
        "terralith:terralith_skylands_winter" to "天空岛冬林",
        "terralith:terralith_brushland" to "灌木地",
        "terralith:terralith_cavegranite_caves" to "花岗岩洞穴",
        "terralith:terralith_cavedeep_caves" to "深层洞穴",
        "terralith:terralith_caveandesite_caves" to "安山岩洞穴",
        "terralith:terralith_cavediorite_caves" to "闪长岩洞穴",
        "terralith:terralith_cavetuff_caves" to "凝灰岩洞穴",
        "terralith:terralith_cavethermal_caves" to "热力洞穴",
        "terralith:terralith_cavefrostfire_caves" to "霜火洞穴",
        "terralith:terralith_cavemantle_caves" to "地幔洞穴",
        "terralith:terralith_caveinfested_caves" to "虫蛀洞穴",
        "terralith:terralith_cavefungal_caves" to "真菌洞穴",

        // 斜杠格式的洞穴生物群系
        "terralith:cave/mantle_caves" to "地幔洞穴",
        "terralith:cave/granite_caves" to "花岗岩洞穴",
        "terralith:cave/deep_caves" to "深层洞穴",
        "terralith:cave/andesite_caves" to "安山岩洞穴",
        "terralith:cave/diorite_caves" to "闪长岩洞穴",
        "terralith:cave/tuff_caves" to "凝灰岩洞穴",
        "terralith:cave/thermal_caves" to "热力洞穴",
        "terralith:cave/frostfire_caves" to "霜火洞穴",
        "terralith:cave/infested_caves" to "虫蛀洞穴",
        "terralith:cave/fungal_caves" to "真菌洞穴"
    )

    /**
     * 获取生物群系的中文名称
     * @param biomeName 英文生物群系名称
     * @return 中文名称，如果没有找到则返回格式化后的英文名称
     */
    fun getChineseName(biomeName: String): String {
        return biomeNameMap[biomeName] ?: formatEnglishName(biomeName)
    }

    /**
     * 格式化英文生物群系名称
     * 移除命名空间前缀并将下划线替换为空格，首字母大写
     */
    private fun formatEnglishName(biomeName: String): String {
        val simpleName = biomeName.substringAfter(":")
        return simpleName.replace("_", " ").split(" ").joinToString(" ") { word ->
            word.lowercase().replaceFirstChar {
                if (it.isLowerCase()) it.titlecase() else it.toString()
            }
        }
    }

    /**
     * 检查是否有该生物群系的中文名称
     */
    fun hasChineseName(biomeName: String): Boolean {
        return biomeNameMap.containsKey(biomeName)
    }

    /**
     * 获取所有支持的生物群系列表
     */
    fun getAllSupportedBiomes(): Set<String> {
        return biomeNameMap.keys
    }
}
