package cn.acebrand.acedex.integration

import cn.acebrand.acedex.AceDex
import me.clip.placeholderapi.expansion.PlaceholderExpansion
import org.bukkit.entity.Player

/**
 * PlaceholderAPI 扩展
 * 提供精灵图鉴相关的占位符
 *
 * 可用占位符列表：
 *
 * === 基础统计 ===
 * %acedex_total_caught% - 玩家总捕获精灵数
 * %acedex_party_count% - 队伍中精灵数量
 * %acedex_pc_count% - PC中精灵数量
 *
 * === 总体进度 ===
 * %acedex_overall_percentage% - 总体完成百分比
 * %acedex_overall_caught% - 总体已捕获数量
 * %acedex_overall_total% - 总体精灵总数
 * %acedex_overall_progress% - 总体进度 (已捕获/总数)
 * %acedex_overall_progressbar% - 总体进度条
 * %acedex_overall_progressbar_short% - 短进度条
 * %acedex_overall_progressbar_long% - 长进度条
 * %acedex_overall_progressbar_with_percent% - 带百分比的进度条
 *
 * === 世代完成情况 ===
 * %acedex_completed_generations% - 已完成世代数
 * %acedex_total_generations% - 总世代数
 * %acedex_generation_progress% - 世代完成进度 (已完成/总数)
 * %acedex_generation_percentage% - 世代完成百分比
 * %acedex_generation_progressbar% - 世代完成进度条
 *
 * === 奖励状态 ===
 * %acedex_can_claim_overall_reward% - 是否可领取全世代奖励 (true/false)
 * %acedex_has_claimed_overall_reward% - 是否已领取全世代奖励 (true/false)
 *
 * === 单个世代进度 ===
 * %acedex_gen1_percentage% - 第一世代完成百分比
 * %acedex_gen1_caught% - 第一世代已捕获数量
 * %acedex_gen1_total% - 第一世代精灵总数
 * %acedex_gen1_progress% - 第一世代进度 (已捕获/总数)
 * %acedex_gen1_name% - 第一世代名称
 * %acedex_gen1_progressbar% - 第一世代进度条
 * (支持 gen1 到 gen9，以及 _short, _long, _with_percent 变体)
 *
 * === 进度奖励状态 ===
 * %acedex_gen1_progress_25_claimed% - 第一世代25%进度奖励是否已领取 (true/false)
 * %acedex_gen1_progress_25_available% - 第一世代25%进度奖励是否可领取 (true/false)
 * %acedex_gen1_progress_25_status% - 第一世代25%进度奖励状态 (已领取/可领取/未达成)
 * %acedex_gen1_next_progress_percentage% - 第一世代下一个进度奖励百分比
 * %acedex_gen1_next_progress_reward% - 第一世代下一个进度奖励描述
 * (支持 gen1 到 gen9，以及任意百分比数值)
 *
 * === 全世代进度奖励状态 ===
 * %acedex_overall_progress_25_claimed% - 全世代25%进度奖励是否已领取 (true/false)
 * %acedex_overall_progress_25_available% - 全世代25%进度奖励是否可领取 (true/false)
 * %acedex_overall_progress_25_status% - 全世代25%进度奖励状态 (已领取/可领取/未达成)
 * %acedex_overall_next_progress_percentage% - 下一个全世代进度奖励百分比
 * %acedex_overall_next_progress_reward% - 下一个全世代进度奖励描述
 * (支持任意百分比数值)
 *
 * === 付费奖励状态 ===
 * %acedex_premium_can_claim_overall_reward% - 是否可领取付费全世代奖励 (true/false)
 * %acedex_premium_has_claimed_overall_reward% - 是否已领取付费全世代奖励 (true/false)
 * %acedex_premium_gen1_progress_25_claimed% - 第一世代25%付费进度奖励是否已领取 (true/false)
 * %acedex_premium_gen1_progress_25_available% - 第一世代25%付费进度奖励是否可领取 (true/false)
 * %acedex_premium_gen1_progress_25_status% - 第一世代25%付费进度奖励状态 (已领取/可领取/未达成)
 * %acedex_premium_overall_progress_25_claimed% - 付费全世代25%进度奖励是否已领取 (true/false)
 * %acedex_premium_overall_progress_25_available% - 付费全世代25%进度奖励是否可领取 (true/false)
 * %acedex_premium_overall_progress_25_status% - 付费全世代25%进度奖励状态 (已领取/可领取/未达成)
 * (支持 gen1 到 gen9，以及任意百分比数值)
 */
class PlaceholderAPIExpansion(private val plugin: AceDex) : PlaceholderExpansion() {

    override fun getIdentifier(): String = "acedex"

    override fun getAuthor(): String = "AceBrand"

    override fun getVersion(): String = plugin.description.version

    override fun persist(): Boolean = true

    override fun canRegister(): Boolean = true

    override fun onPlaceholderRequest(player: Player?, params: String): String? {
        if (player == null) return null

        return when (params.lowercase()) {
            // 基础统计
            "total_caught" -> {
                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                playerData.totalCaught.toString()
            }
            "party_count" -> {
                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                playerData.partyPokemon.size.toString()
            }
            "pc_count" -> {
                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                playerData.pcPokemon.size.toString()
            }
            
            // 总体进度
            "overall_percentage" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                "${allProgress.overallPercentage}%"
            }
            "overall_caught" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                allProgress.caughtPokemon.toString()
            }
            "overall_total" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                allProgress.totalPokemon.toString()
            }
            "overall_progress" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                "${allProgress.caughtPokemon}/${allProgress.totalPokemon}"
            }
            
            // 世代完成情况
            "completed_generations" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                allProgress.completedGenerations.toString()
            }
            "total_generations" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                allProgress.totalGenerations.toString()
            }
            "generation_progress" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                "${allProgress.completedGenerations}/${allProgress.totalGenerations}"
            }
            "generation_percentage" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                "${generationPercentage}%"
            }
            
            // 奖励状态
            "can_claim_overall_reward" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val canClaim = allProgress.completedGenerations >= 9 &&
                              !plugin.rewardManager.hasClaimedAllGenerationsReward(player)
                if (canClaim) "true" else "false"
            }
            "has_claimed_overall_reward" -> {
                if (plugin.rewardManager.hasClaimedAllGenerationsReward(player)) "true" else "false"
            }

            // 付费奖励状态
            "premium_can_claim_overall_reward" -> {
                val canClaim = plugin.rewardManager.canClaimPremiumAllGenerationsReward(player)
                if (canClaim) "true" else "false"
            }
            "premium_has_claimed_overall_reward" -> {
                if (plugin.rewardManager.hasClaimedPremiumAllGenerationsReward(player)) "true" else "false"
            }

            // 总体进度条
            "overall_progressbar" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                createProgressBar(allProgress.overallPercentage, 20)
            }
            "overall_progressbar_short" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                createProgressBar(allProgress.overallPercentage, 10)
            }
            "overall_progressbar_long" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                createProgressBar(allProgress.overallPercentage, 30)
            }
            "overall_progressbar_with_percent" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                createProgressBarWithPercentage(allProgress.overallPercentage, 20)
            }
            "overall_progressbar_short_with_percent" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                createProgressBarWithPercentage(allProgress.overallPercentage, 10)
            }
            "overall_progressbar_long_with_percent" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                createProgressBarWithPercentage(allProgress.overallPercentage, 30)
            }

            // 世代完成进度条
            "generation_progressbar" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                createProgressBar(generationPercentage, 20)
            }
            "generation_progressbar_short" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                createProgressBar(generationPercentage, 10)
            }
            "generation_progressbar_long" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                createProgressBar(generationPercentage, 30)
            }
            "generation_progressbar_with_percent" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                createProgressBarWithPercentage(generationPercentage, 20)
            }
            "generation_progressbar_short_with_percent" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                createProgressBarWithPercentage(generationPercentage, 10)
            }
            "generation_progressbar_long_with_percent" -> {
                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                val generationPercentage = if (allProgress.totalGenerations > 0) {
                    (allProgress.completedGenerations * 100) / allProgress.totalGenerations
                } else 0
                createProgressBarWithPercentage(generationPercentage, 30)
            }

            // 各世代进度 (gen1_percentage, gen1_caught, gen1_total 等)
            else -> {
                handleGenerationPlaceholder(player, params) ?: handleProgressRewardPlaceholder(player, params)
            }
        }
    }
    
    /**
     * 处理世代相关的占位符
     */
    private fun handleGenerationPlaceholder(player: Player, params: String): String? {
        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

        // 匹配格式: gen1_percentage, gen1_caught, gen1_total, gen1_progress
        val genRegex = Regex("gen(\\d+)_(percentage|caught|total|progress)")
        val matchResult = genRegex.matchEntire(params.lowercase())

        if (matchResult != null) {
            val genNumber = matchResult.groupValues[1].toIntOrNull() ?: return null
            val type = matchResult.groupValues[2]

            // 获取对应世代的进度
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" } ?: return null
            val progress = allProgress.generationProgresses[generation.id] ?: return null

            return when (type) {
                "percentage" -> "${progress.percentage}%"
                "caught" -> progress.caught.toString()
                "total" -> progress.total.toString()
                "progress" -> "${progress.caught}/${progress.total}"
                else -> null
            }
        }

        // 匹配格式: gen1_name (返回世代中文名称)
        val genNameRegex = Regex("gen(\\d+)_name")
        val nameMatchResult = genNameRegex.matchEntire(params.lowercase())

        if (nameMatchResult != null) {
            val genNumber = nameMatchResult.groupValues[1].toIntOrNull() ?: return null
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" }
            return generation?.name ?: null
        }

        // 匹配格式: gen1_progressbar, gen1_progressbar_short, gen1_progressbar_long
        val genProgressBarRegex = Regex("gen(\\d+)_progressbar(?:_(short|long))?")
        val progressBarMatchResult = genProgressBarRegex.matchEntire(params.lowercase())

        if (progressBarMatchResult != null) {
            val genNumber = progressBarMatchResult.groupValues[1].toIntOrNull() ?: return null
            val size = when (progressBarMatchResult.groupValues[2]) {
                "short" -> 10
                "long" -> 30
                else -> 20 // 默认长度
            }

            // 获取对应世代的进度
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" } ?: return null
            val progress = allProgress.generationProgresses[generation.id] ?: return null

            return createProgressBar(progress.percentage, size)
        }

        // 匹配格式: gen1_progressbar_with_percent, gen1_progressbar_short_with_percent, gen1_progressbar_long_with_percent
        val genProgressBarWithPercentRegex = Regex("gen(\\d+)_progressbar(?:_(short|long))?_with_percent")
        val progressBarWithPercentMatchResult = genProgressBarWithPercentRegex.matchEntire(params.lowercase())

        if (progressBarWithPercentMatchResult != null) {
            val genNumber = progressBarWithPercentMatchResult.groupValues[1].toIntOrNull() ?: return null
            val size = when (progressBarWithPercentMatchResult.groupValues[2]) {
                "short" -> 10
                "long" -> 30
                else -> 20 // 默认长度
            }

            // 获取对应世代的进度
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" } ?: return null
            val progress = allProgress.generationProgresses[generation.id] ?: return null

            return createProgressBarWithPercentage(progress.percentage, size)
        }

        return null
    }

    /**
     * 处理进度奖励相关的占位符
     */
    private fun handleProgressRewardPlaceholder(player: Player, params: String): String? {
        // 匹配付费奖励格式: premium_gen1_progress_25_claimed, premium_gen1_progress_50_status, premium_gen1_progress_75_available
        val premiumProgressRewardRegex = Regex("premium_gen(\\d+)_progress_(\\d+)_(claimed|status|available)")
        val premiumMatchResult = premiumProgressRewardRegex.matchEntire(params.lowercase())

        if (premiumMatchResult != null) {
            val genNumber = premiumMatchResult.groupValues[1].toIntOrNull() ?: return null
            val percentage = premiumMatchResult.groupValues[2].toIntOrNull() ?: return null
            val type = premiumMatchResult.groupValues[3]

            // 获取对应世代
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" } ?: return null

            // 获取当前进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val currentProgress = allProgress.generationProgresses[generation.id] ?: return null

            val rewardKey = "premium_progress_${generation.id}_$percentage"
            val hasClaimedProgress = plugin.rewardManager.hasClaimedPremiumProgressReward(player, rewardKey)
            val canClaim = currentProgress.percentage >= percentage && !hasClaimedProgress && player.hasPermission("acedex.premium.claim")

            return when (type) {
                "claimed" -> if (hasClaimedProgress) "true" else "false"
                "available" -> if (canClaim) "true" else "false"
                "status" -> when {
                    hasClaimedProgress -> "已领取"
                    canClaim -> "可领取"
                    !player.hasPermission("acedex.premium.claim") -> "无权限"
                    else -> "未达成"
                }
                else -> null
            }
        }

        // 匹配付费全世代进度奖励格式: premium_overall_progress_25_claimed, premium_overall_progress_50_status, premium_overall_progress_75_available
        val premiumOverallProgressRewardRegex = Regex("premium_overall_progress_(\\d+)_(claimed|status|available)")
        val premiumOverallMatchResult = premiumOverallProgressRewardRegex.matchEntire(params.lowercase())

        if (premiumOverallMatchResult != null) {
            val percentage = premiumOverallMatchResult.groupValues[1].toIntOrNull() ?: return null
            val type = premiumOverallMatchResult.groupValues[2]

            // 获取当前全世代进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            val rewardKey = "premium_overall_$percentage"
            val hasClaimedOverall = plugin.rewardManager.hasClaimedPremiumOverallProgressReward(player, rewardKey)
            val canClaim = allProgress.overallPercentage >= percentage && !hasClaimedOverall && player.hasPermission("acedex.premium.claim")

            return when (type) {
                "claimed" -> if (hasClaimedOverall) "true" else "false"
                "available" -> if (canClaim) "true" else "false"
                "status" -> when {
                    hasClaimedOverall -> "已领取"
                    canClaim -> "可领取"
                    !player.hasPermission("acedex.premium.claim") -> "无权限"
                    else -> "未达成"
                }
                else -> null
            }
        }
        // 匹配格式: gen1_progress_25_claimed, gen1_progress_50_status, gen1_progress_75_available
        val progressRewardRegex = Regex("gen(\\d+)_progress_(\\d+)_(claimed|status|available)")
        val matchResult = progressRewardRegex.matchEntire(params.lowercase())

        if (matchResult != null) {
            val genNumber = matchResult.groupValues[1].toIntOrNull() ?: return null
            val percentage = matchResult.groupValues[2].toIntOrNull() ?: return null
            val type = matchResult.groupValues[3]

            // 获取对应世代
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" } ?: return null

            // 获取当前进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val currentProgress = allProgress.generationProgresses[generation.id] ?: return null

            val rewardKey = "progress_${generation.id}_$percentage"
            val hasClaimedProgress = plugin.rewardManager.hasClaimedProgressReward(player, rewardKey)
            val canClaim = currentProgress.percentage >= percentage && !hasClaimedProgress

            return when (type) {
                "claimed" -> if (hasClaimedProgress) "true" else "false"
                "available" -> if (canClaim) "true" else "false"
                "status" -> when {
                    hasClaimedProgress -> "已领取"
                    canClaim -> "可领取"
                    else -> "未达成"
                }
                else -> null
            }
        }

        // 匹配格式: overall_progress_25_claimed, overall_progress_50_status, overall_progress_75_available
        val overallProgressRewardRegex = Regex("overall_progress_(\\d+)_(claimed|status|available)")
        val overallMatchResult = overallProgressRewardRegex.matchEntire(params.lowercase())

        if (overallMatchResult != null) {
            val percentage = overallMatchResult.groupValues[1].toIntOrNull() ?: return null
            val type = overallMatchResult.groupValues[2]

            // 获取当前全世代进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            val rewardKey = "overall_$percentage"
            val hasClaimedOverall = plugin.rewardManager.hasClaimedOverallProgressReward(player, rewardKey)
            val canClaim = allProgress.overallPercentage >= percentage && !hasClaimedOverall

            return when (type) {
                "claimed" -> if (hasClaimedOverall) "true" else "false"
                "available" -> if (canClaim) "true" else "false"
                "status" -> when {
                    hasClaimedOverall -> "已领取"
                    canClaim -> "可领取"
                    else -> "未达成"
                }
                else -> null
            }
        }

        // 匹配格式: gen1_next_progress_reward, gen1_next_progress_percentage
        val nextProgressRewardRegex = Regex("gen(\\d+)_next_progress_(reward|percentage)")
        val nextMatchResult = nextProgressRewardRegex.matchEntire(params.lowercase())

        if (nextMatchResult != null) {
            val genNumber = nextMatchResult.groupValues[1].toIntOrNull() ?: return null
            val type = nextMatchResult.groupValues[2]

            // 获取对应世代
            val generations = plugin.generationManager.getAllGenerations()
            val generation = generations.find { it.id == "gen$genNumber" } ?: return null

            // 获取当前进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val currentProgress = allProgress.generationProgresses[generation.id] ?: return null

            // 查找下一个可领取的进度奖励
            val config = plugin.config.getConfig()
            val progressSection = config.getConfigurationSection("rewards.generation.${generation.id}.progress")

            if (progressSection != null) {
                val availablePercentages = mutableListOf<Int>()

                for (percentageKey in progressSection.getKeys(false)) {
                    try {
                        val percentage = percentageKey.toInt()
                        val rewardKey = "progress_${generation.id}_$percentage"

                        // 找到未领取且未达成的进度奖励
                        if (!plugin.rewardManager.hasClaimedProgressReward(player, rewardKey) &&
                            currentProgress.percentage < percentage) {
                            availablePercentages.add(percentage)
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                    }
                }

                val nextPercentage = availablePercentages.minOrNull()

                return when (type) {
                    "percentage" -> nextPercentage?.toString() ?: "无"
                    "reward" -> if (nextPercentage != null) "${nextPercentage}%进度奖励" else "无"
                    else -> null
                }
            }
        }

        // 匹配格式: overall_next_progress_reward, overall_next_progress_percentage
        val nextOverallProgressRewardRegex = Regex("overall_next_progress_(reward|percentage)")
        val nextOverallMatchResult = nextOverallProgressRewardRegex.matchEntire(params.lowercase())

        if (nextOverallMatchResult != null) {
            val type = nextOverallMatchResult.groupValues[1]

            // 获取当前全世代进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            // 查找下一个可领取的全世代进度奖励
            val config = plugin.config.getConfig()
            val overallProgressSection = config.getConfigurationSection("rewards.overall-progress")

            if (overallProgressSection != null && config.getBoolean("rewards.overall-progress.enabled", false)) {
                val availablePercentages = mutableListOf<Int>()

                for (percentageKey in overallProgressSection.getKeys(false)) {
                    if (percentageKey == "enabled") continue

                    try {
                        val percentage = percentageKey.toInt()
                        val rewardKey = "overall_$percentage"

                        // 找到未领取且未达成的全世代进度奖励
                        if (!plugin.rewardManager.hasClaimedOverallProgressReward(player, rewardKey) &&
                            allProgress.overallPercentage < percentage) {
                            availablePercentages.add(percentage)
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                    }
                }

                val nextPercentage = availablePercentages.minOrNull()

                return when (type) {
                    "percentage" -> nextPercentage?.toString() ?: "无"
                    "reward" -> if (nextPercentage != null) "${nextPercentage}%全世代进度奖励" else "无"
                    else -> null
                }
            }
        }

        return null
    }

    /**
     * 创建进度条
     * @param percentage 完成百分比 (0-100)
     * @param length 进度条长度
     * @return 格式化的进度条字符串
     */
    private fun createProgressBar(percentage: Int, length: Int): String {
        // 确保百分比在0-100范围内
        val safePercentage = percentage.coerceIn(0, 100)
        val filledLength = (safePercentage * length / 100.0).toInt()
        val emptyLength = (length - filledLength).coerceAtLeast(0)

        val filledChar = "█"  // 已完成部分 - 实心方块
        val emptyChar = "█"   // 未完成部分 - 实心方块

        val filled = filledChar.repeat(filledLength)
        val empty = emptyChar.repeat(emptyLength)

        // 简化颜色逻辑：有进展显示绿色，没有进展显示灰色
        return if (safePercentage > 0) {
            "§a$filled§8$empty"  // 绿色已完成部分 + 深灰色未完成部分
        } else {
            "§8${filledChar.repeat(length)}"  // 全部显示深灰色实心方块
        }
    }

    /**
     * 创建带百分比的进度条
     * @param percentage 完成百分比 (0-100)
     * @param length 进度条长度
     * @return 格式化的进度条字符串（包含百分比）
     */
    private fun createProgressBarWithPercentage(percentage: Int, length: Int): String {
        val progressBar = createProgressBar(percentage, length)
        val percentageColor = if (percentage > 0) "§a" else "§8"  // 有进展显示绿色，没有显示灰色
        return "$progressBar $percentageColor$percentage%"
    }
}
