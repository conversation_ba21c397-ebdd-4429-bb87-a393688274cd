package cn.acebrand.acebiomespawner.config

import org.bukkit.configuration.file.YamlConfiguration
import org.bukkit.plugin.java.JavaPlugin
import java.io.File

/**
 * 配置管理器
 */
class ConfigManager(private val plugin: JavaPlugin) {
    
    private var config: SpawnerConfig = SpawnerConfig()
    private val configFile = File(plugin.dataFolder, "config.yml")
    
    /**
     * 加载配置
     */
    fun loadConfig() {
        if (!plugin.dataFolder.exists()) {
            plugin.dataFolder.mkdirs()
        }
        
        if (!configFile.exists()) {
            createDefaultConfig()
        }
        
        try {
            val yamlConfig = YamlConfiguration.loadConfiguration(configFile)
            config = SpawnerConfig.fromYaml(yamlConfig)
            plugin.logger.info("配置文件加载完成")
        } catch (e: Exception) {
            plugin.logger.severe("配置文件加载失败，使用默认配置: ${e.message}")
            config = SpawnerConfig()
        }
    }
    
    /**
     * 创建默认配置文件
     */
    private fun createDefaultConfig() {
        try {
            val yamlConfig = YamlConfiguration()
            val defaultConfig = SpawnerConfig()
            defaultConfig.saveToYaml(yamlConfig)
            yamlConfig.save(configFile)
            plugin.logger.info("已创建默认配置文件")
        } catch (e: Exception) {
            plugin.logger.severe("创建默认配置文件失败: ${e.message}")
        }
    }
    
    /**
     * 保存配置
     */
    fun saveConfig() {
        try {
            val yamlConfig = YamlConfiguration()
            config.saveToYaml(yamlConfig)
            yamlConfig.save(configFile)
        } catch (e: Exception) {
            plugin.logger.severe("保存配置文件失败: ${e.message}")
        }
    }
    
    /**
     * 获取配置
     */
    fun getConfig(): SpawnerConfig {
        return config
    }
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: SpawnerConfig) {
        this.config = newConfig
        saveConfig()
    }
}
