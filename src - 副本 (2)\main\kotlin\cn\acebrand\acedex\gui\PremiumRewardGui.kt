package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.data.GenerationProgress
import cn.acebrand.acedex.generation.Generation
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.InventoryClickEvent
import org.bukkit.event.inventory.InventoryCloseEvent
import org.bukkit.event.player.PlayerQuitEvent
import org.bukkit.inventory.Inventory
import org.bukkit.inventory.ItemStack
import org.bukkit.Sound

/**
 * 付费奖励菜单GUI
 * 专门用于显示和领取付费奖励
 */
class PremiumRewardGui(private val plugin: AceDex) : Listener {

    val openGuis = mutableMapOf<Player, String>()

    // 存储玩家点击冷却时间
    private val playerCooldowns = mutableMapOf<Player, MutableMap<String, Long>>()

    // 缓存玩家的付费菜单Inventory，避免重复创建 - 线程安全
    private val playerPremiumInventories = java.util.concurrent.ConcurrentHashMap<Player, Inventory>()

    init {
        plugin.server.pluginManager.registerEvents(this, plugin)
    }

    /**
     * 初始化付费菜单（插件启动时调用）
     */
    fun initialize() {
        plugin.logger.info("正在初始化付费菜单...")

        // 预生成付费按钮缓存
        preloadPremiumButtonCache()

        plugin.logger.info("付费菜单初始化完成")
    }

    /**
     * 获取或创建付费菜单（优化版本，使用缓存）
     */
    private fun getOrCreatePremiumInventoryOptimized(player: Player): Inventory {
        // 检查是否已有缓存的付费菜单
        val existingInventory = playerPremiumInventories[player]
        if (existingInventory != null) {
            return existingInventory
        }

        // 创建新的付费菜单
        val rawTitle = plugin.config.getPremiumMenuTitle()
        val title = cn.acebrand.acedex.util.VariableReplacer.replaceVariables(rawTitle, player, plugin)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景（这些不会变化）
        fillBackground(inventory)

        // 缓存菜单
        playerPremiumInventories[player] = inventory
        return inventory
    }

    /**
     * 清除玩家的付费菜单缓存（当需要重新创建菜单时使用）
     */
    fun clearPremiumInventoryCache(player: Player) {
        playerPremiumInventories.remove(player)
    }

    /**
     * 清除所有玩家的付费菜单缓存
     */
    fun clearAllPremiumInventoryCache() {
        playerPremiumInventories.clear()
    }

    /**
     * 异步更新付费菜单数据（不重新打开界面）
     */
    private fun updatePremiumGuiDataAsync(player: Player) {
        val inventory = playerPremiumInventories[player] ?: return

        // 检查玩家是否还在付费菜单中
        if (openGuis[player] != "premium_main" || player.openInventory.topInventory != inventory) {
            return
        }

        // 异步更新数据，避免阻塞主线程
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            try {
                // 强制重新检测并保存最新数据到本地文件
                val latestPlayerData = plugin.pokemonDetector.detectAndSavePlayerPokemon(player)
                    ?: plugin.pokemonDetector.getPlayerPokemon(player)

                // 重新计算所有世代进度
                val latestAllProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

                // 回到主线程更新界面
                plugin.server.scheduler.runTask(plugin, Runnable {
                    // 再次检查玩家是否还在付费菜单中
                    if (openGuis[player] == "premium_main" && player.openInventory.topInventory == inventory) {
                        // 使用最新数据更新界面内容
                        addPremiumGenerationButtons(inventory, player)
                        addPremiumOverallProgressButton(inventory, player)
                        addCloseButton(inventory)

                        if (plugin.config.enableDebug) {
                            plugin.logger.info("付费菜单异步更新完成 - 玩家 ${player.name} 总体进度: ${latestAllProgress.overallPercentage}%")
                        }
                    }
                })
            } catch (e: Exception) {
                plugin.logger.warning("付费菜单异步数据更新失败: ${e.message}")

                // 如果异步检测失败，尝试使用CheckPokemon方法
                plugin.server.scheduler.runTask(plugin, Runnable {
                    if (openGuis[player] == "premium_main") {
                        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                            try {
                                val success = plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)
                                if (success) {
                                    plugin.server.scheduler.runTask(plugin, Runnable {
                                        if (openGuis[player] == "premium_main" && player.openInventory.topInventory == inventory) {
                                            addPremiumGenerationButtons(inventory, player)
                                            addPremiumOverallProgressButton(inventory, player)
                                            addCloseButton(inventory)
                                        }
                                    })
                                }
                            } catch (e2: Exception) {
                                plugin.logger.warning("付费菜单备用数据检测也失败: ${e2.message}")
                            }
                        })
                    }
                })
            }
        })
    }

    /**
     * 根据进度百分比创建付费专用进度按钮物品
     */
    private fun createPremiumProgressButtonItem(progressPercentage: Double): ItemStack {
        val materialKey = when {
            progressPercentage >= 100.0 -> "100"
            progressPercentage >= 75.0 -> "75-99"
            progressPercentage >= 50.0 -> "50-75"
            progressPercentage >= 25.0 -> "25-50"
            else -> "0-25"
        }

        val premiumMaterials = plugin.premiumRewardConfig.getPremiumProgressButtonMaterials()
        val material = premiumMaterials[materialKey] ?: getDefaultPremiumProgressMaterial(materialKey)
        return parseButtonMaterial(material)
    }

    /**
     * 获取默认的付费进度材质
     */
    private fun getDefaultPremiumProgressMaterial(materialKey: String): String {
        return when (materialKey) {
            "100" -> "COBBLEMON_PREMIER_BALL"   // 100% - 纪念球
            "75-99" -> "COBBLEMON_MASTER_BALL"  // 75-99% - 大师球
            "50-75" -> "COBBLEMON_ULTRA_BALL"   // 50-75% - 高级球
            "25-50" -> "COBBLEMON_GREAT_BALL"   // 25-50% - 超级球
            "0-25" -> "COBBLEMON_POKE_BALL"     // 0-25% - 精灵球
            else -> "EXPERIENCE_BOTTLE"         // 默认材质
        }
    }

    /**
     * 解析按钮材质（复制自AceDexConfig的逻辑）
     */
    private fun parseButtonMaterial(materialString: String): ItemStack {
        return try {
            if (materialString.contains(":")) {
                val parts = materialString.split(":")
                val materialName = parts[0]
                val customModelData = parts.getOrNull(1)?.toIntOrNull()

                val material = org.bukkit.Material.valueOf(materialName)
                val item = ItemStack(material)

                if (customModelData != null) {
                    val meta = item.itemMeta
                    meta?.setCustomModelData(customModelData)
                    item.itemMeta = meta
                }

                item
            } else {
                val material = org.bukkit.Material.valueOf(materialString)
                ItemStack(material)
            }
        } catch (e: Exception) {
            plugin.logger.warning("解析付费按钮材质失败: $materialString - ${e.message}")
            ItemStack(org.bukkit.Material.EXPERIENCE_BOTTLE)
        }
    }

    /**
     * 预加载付费按钮缓存（插件启动时生成所有可能的按钮状态）
     */
    private fun preloadPremiumButtonCache() {
        try {
            plugin.logger.info("开始预加载付费按钮缓存...")

            // 确保gui_cache文件夹存在
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            // 预加载付费世代按钮（所有可能的状态组合）
            preloadPremiumGenerationButtons()

            // 预加载付费全世代按钮（所有可能的状态组合）
            preloadPremiumOverallButtons()

            plugin.logger.info("付费按钮缓存预加载完成")
        } catch (e: Exception) {
            plugin.logger.warning("预加载付费按钮缓存失败: ${e.message}")
        }
    }

    /**
     * 预加载付费世代按钮（所有状态组合）
     */
    private fun preloadPremiumGenerationButtons() {
        try {
            val generations = plugin.generationManager.getAllGenerations().toList()
            var buttonCount = 0

            generations.forEach { generation ->
                // 为每个世代生成所有可能的状态组合
                val statusCombinations = listOf(
                    Pair(false, false), // 未捕获，未完成
                    Pair(true, false),  // 已捕获，未完成
                    Pair(false, true),  // 未捕获，已完成（理论上不可能，但为了完整性）
                    Pair(true, true)    // 已捕获，已完成
                )

                statusCombinations.forEach { (hasCaught, isCompleted) ->
                    val cacheKey = "premium_gen_${generation.id}_${hasCaught}_${isCompleted}"
                    val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
                    val cacheFile = java.io.File(cacheDir, "$cacheKey.yml")

                    // 如果缓存文件不存在，则创建
                    if (!cacheFile.exists()) {
                        val button = createPremiumGenerationButtonForCache(generation, hasCaught, isCompleted)
                        if (button != null) {
                            val config = org.bukkit.configuration.file.YamlConfiguration()
                            config.set("item", button.serialize())
                            config.set("timestamp", System.currentTimeMillis())
                            config.save(cacheFile)
                            buttonCount++
                        }
                    }
                }
            }

            plugin.logger.info("预加载了 $buttonCount 个付费世代按钮")
        } catch (e: Exception) {
            plugin.logger.warning("预加载付费世代按钮失败: ${e.message}")
        }
    }

    /**
     * 预加载付费全世代按钮（所有状态组合）
     */
    private fun preloadPremiumOverallButtons() {
        try {
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            var buttonCount = 0

            // 为付费全世代按钮生成所有可能的状态组合
            val statusCombinations = listOf(
                Pair(false, false), // 不可领取，未领取
                Pair(true, false),  // 可领取，未领取
                Pair(false, true),  // 不可领取，已领取（理论上不可能，但为了完整性）
                Pair(true, true)    // 可领取，已领取（理论上不可能，但为了完整性）
            )

            statusCombinations.forEach { (canClaim, hasClaimed) ->
                val cacheKey = "premium_overall_${canClaim}_${hasClaimed}"
                val cacheFile = java.io.File(cacheDir, "$cacheKey.yml")

                // 如果缓存文件不存在，则创建
                if (!cacheFile.exists()) {
                    val button = createPremiumOverallButtonForCache(canClaim, hasClaimed)
                    if (button != null) {
                        val config = org.bukkit.configuration.file.YamlConfiguration()
                        config.set("item", button.serialize())
                        config.set("timestamp", System.currentTimeMillis())
                        config.save(cacheFile)
                        buttonCount++
                    }
                }
            }

            plugin.logger.info("预加载了 $buttonCount 个付费全世代按钮")
        } catch (e: Exception) {
            plugin.logger.warning("预加载付费全世代按钮失败: ${e.message}")
        }
    }

    /**
     * 为缓存创建付费世代按钮（不依赖玩家数据）
     */
    private fun createPremiumGenerationButtonForCache(generation: Generation, hasCaught: Boolean, isCompleted: Boolean): ItemStack? {
        return try {
            // 创建基础按钮（使用世代固定材质，和普通菜单一样）
            val item = plugin.pokeBallItemCreator.createGenerationPokeBallItem(
                generationId = generation.id,
                hasCaught = hasCaught,
                isCompleted = isCompleted
            )
            val meta = item.itemMeta ?: return null

            // 设置显示名称
            val displayName = when {
                isCompleted -> "§a§l${generation.name} §6✦ §a✓"
                hasCaught -> "§e§l${generation.name} §6✦"
                else -> "§7§l${generation.name} §6✦"
            }
            meta.setDisplayName(displayName)

            // 设置基础lore（不包含具体进度数据）
            val lore = mutableListOf<String>()
            lore.add("§7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            lore.add("§6§l付费奖励状态:")

            when {
                isCompleted -> {
                    lore.add("§a✓ 已完成世代收集")
                    lore.add("§a✓ 付费奖励已领取")
                }
                hasCaught -> {
                    lore.add("§e⚡ 正在收集中...")
                    lore.add("§7○ 付费奖励未领取")
                }
                else -> {
                    lore.add("§7○ 尚未开始收集")
                    lore.add("§7○ 付费奖励未领取")
                }
            }

            lore.add("§7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            lore.add("§c注意: 需要付费权限才能领取奖励")
            lore.add("§7权限: acedex.premium.claim")
            lore.add("§7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

            meta.lore = lore
            item.itemMeta = meta
            item
        } catch (e: Exception) {
            plugin.logger.warning("创建付费世代按钮缓存失败: ${generation.id} - ${e.message}")
            null
        }
    }

    /**
     * 为缓存创建付费全世代按钮（不依赖玩家数据）
     */
    private fun createPremiumOverallButtonForCache(canClaim: Boolean, hasClaimed: Boolean): ItemStack? {
        return try {
            // 根据状态选择合适的进度百分比来确定材质
            val progressPercentage = when {
                hasClaimed -> 100.0  // 已领取 - 使用100%材质
                canClaim -> 75.0     // 可领取 - 使用75%材质
                else -> 25.0         // 不可领取 - 使用25%材质
            }

            // 创建基础按钮（使用付费专用进度材质）
            val item = createPremiumProgressButtonItem(progressPercentage)
            val meta = item.itemMeta ?: return null

            // 设置显示名称
            val displayName = when {
                hasClaimed -> "§6§l✦ 付费精灵大师 ✦ §a✓"
                canClaim -> "§6§l✦ 付费精灵大师 ✦ §6★"
                else -> "§6§l✦ 付费精灵大师 ✦"
            }
            meta.setDisplayName(displayName)

            // 设置基础lore（不包含具体进度数据）
            val lore = mutableListOf<String>()
            lore.add("§7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            lore.add("§6§l付费精灵大师奖励:")

            when {
                hasClaimed -> {
                    lore.add("§a✓ 已完成所有世代收集")
                    lore.add("§a✓ 付费精灵大师奖励已领取")
                }
                canClaim -> {
                    lore.add("§a✓ 已完成所有世代收集")
                    lore.add("§6★ 可领取付费精灵大师奖励")
                    lore.add("§e点击领取付费奖励")
                }
                else -> {
                    lore.add("§7○ 尚未完成所有世代收集")
                    lore.add("§7○ 付费精灵大师奖励未领取")
                }
            }

            lore.add("§7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            lore.add("§c注意: 需要付费权限才能领取奖励")
            lore.add("§7权限: acedex.premium.claim")
            lore.add("§7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

            meta.lore = lore
            item.itemMeta = meta
            item
        } catch (e: Exception) {
            plugin.logger.warning("创建付费全世代按钮缓存失败: ${e.message}")
            null
        }
    }

    /**
     * 打开付费奖励主菜单（异步优化版本）
     * 先立即打开菜单，然后异步更新数据，避免阻塞主线程
     */
    fun openPremiumRewardGui(player: Player) {
        // 检查权限
        if (!player.hasPermission("acedex.premium.view")) {
            player.sendMessage("§c你没有权限查看付费奖励菜单！")
            return
        }

        // 获取付费菜单标题并处理变量（使用和普通菜单相同的方式）
        val rawTitle = plugin.config.getPremiumMenuTitle()
        val title = cn.acebrand.acedex.util.VariableReplacer.replaceVariables(rawTitle, player, plugin)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景
        fillBackground(inventory)

        // 先使用缓存数据快速创建界面，立即打开给玩家
        addPremiumGenerationButtons(inventory, player)
        addPremiumOverallProgressButton(inventory, player)
        addCloseButton(inventory)

        // 立即打开菜单，不等待数据检测
        player.openInventory(inventory)
        openGuis[player] = "premium_main"

        // 异步更新菜单内容，不阻塞主线程
        updatePremiumGuiDataAsync(player)
    }

    /**
     * 存储付费按钮信息到本地缓存（保存到gui_cache文件夹）
     */
    private fun storePremiumButtonInfo(player: Player, inventory: Inventory) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val generations = plugin.generationManager.getAllGenerations().toList()

            // 确保gui_cache文件夹存在
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            // 存储世代按钮信息到缓存
            generations.forEach { generation ->
                val slot = plugin.config.getGenerationSlot(generation.id)
                val item = inventory.getItem(slot)
                if (item != null) {
                    val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)
                    val isCompleted = progress.percentage >= 100
                    val hasClaimedReward = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generation.id)
                    val hasCaught = progress.caught > 0
                    val finalCompleted = isCompleted && hasClaimedReward

                    // 保存付费世代按钮到gui_cache
                    savePremiumButtonToCache(generation.id, hasCaught, finalCompleted, item)
                }
            }

            // 存储全世代按钮信息到缓存
            val overallSlot = plugin.config.progressButtonSlot
            val overallItem = inventory.getItem(overallSlot)
            if (overallItem != null) {
                val canClaimPremiumReward = plugin.rewardManager.canClaimPremiumAllGenerationsReward(player)
                val hasClaimedPremiumReward = plugin.rewardManager.hasClaimedPremiumAllGenerationsReward(player)

                // 保存付费全世代按钮到gui_cache
                savePremiumOverallButtonToCache(canClaimPremiumReward, hasClaimedPremiumReward, overallItem)
            }
        } catch (e: Exception) {
            plugin.logger.warning("存储付费按钮信息失败: ${e.message}")
        }
    }

    /**
     * 保存付费世代按钮到gui_cache文件夹
     */
    private fun savePremiumButtonToCache(generationId: String, hasCaught: Boolean, isCompleted: Boolean, item: ItemStack) {
        try {
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            val cacheKey = "premium_gen_${generationId}_${hasCaught}_${isCompleted}"
            val cacheFile = java.io.File(cacheDir, "$cacheKey.yml")

            val config = org.bukkit.configuration.file.YamlConfiguration()
            config.set("item", item.serialize())
            config.set("timestamp", System.currentTimeMillis())
            config.save(cacheFile)
        } catch (e: Exception) {
            plugin.logger.warning("保存付费世代按钮缓存失败: $generationId - ${e.message}")
        }
    }

    /**
     * 保存付费全世代按钮到gui_cache文件夹
     */
    private fun savePremiumOverallButtonToCache(canClaim: Boolean, hasClaimed: Boolean, item: ItemStack) {
        try {
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            val cacheKey = "premium_overall_${canClaim}_${hasClaimed}"
            val cacheFile = java.io.File(cacheDir, "$cacheKey.yml")

            val config = org.bukkit.configuration.file.YamlConfiguration()
            config.set("item", item.serialize())
            config.set("timestamp", System.currentTimeMillis())
            config.save(cacheFile)
        } catch (e: Exception) {
            plugin.logger.warning("保存付费全世代按钮缓存失败: ${e.message}")
        }
    }

    /**
     * 从缓存获取付费按钮（从gui_cache文件夹读取）
     */
    private fun getCachedPremiumButton(player: Player, generation: Generation): ItemStack? {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)
            val isCompleted = progress.percentage >= 100
            val hasClaimedReward = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generation.id)
            val hasCaught = progress.caught > 0
            val finalCompleted = isCompleted && hasClaimedReward

            // 构建缓存文件路径
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val cacheKey = "premium_gen_${generation.id}_${hasCaught}_${finalCompleted}"
            val cacheFile = java.io.File(cacheDir, "$cacheKey.yml")

            if (cacheFile.exists()) {
                val config = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(cacheFile)
                val itemMap = config.getConfigurationSection("item")?.getValues(false)
                if (itemMap != null) {
                    return ItemStack.deserialize(itemMap as Map<String, Any>)
                }
            }
        } catch (e: Exception) {
            plugin.logger.warning("从缓存读取付费按钮失败: ${e.message}")
        }
        return null
    }

    /**
     * 从缓存获取付费全世代按钮（从gui_cache文件夹读取）
     */
    private fun getCachedPremiumOverallButton(player: Player): ItemStack? {
        try {
            val canClaimPremiumReward = plugin.rewardManager.canClaimPremiumAllGenerationsReward(player)
            val hasClaimedPremiumReward = plugin.rewardManager.hasClaimedPremiumAllGenerationsReward(player)

            // 构建缓存文件路径
            val cacheDir = java.io.File(plugin.dataFolder, "gui_cache")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val cacheKey = "premium_overall_${canClaimPremiumReward}_${hasClaimedPremiumReward}"
            val cacheFile = java.io.File(cacheDir, "$cacheKey.yml")

            if (cacheFile.exists()) {
                val config = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(cacheFile)
                val itemMap = config.getConfigurationSection("item")?.getValues(false)
                if (itemMap != null) {
                    return ItemStack.deserialize(itemMap as Map<String, Any>)
                }
            }
        } catch (e: Exception) {
            plugin.logger.warning("从缓存读取付费全世代按钮失败: ${e.message}")
        }
        return null
    }

    /**
     * 更新付费世代按钮的数据（不重新创建按钮，只更新lore等数据）
     */
    private fun updatePremiumButtonData(cachedItem: ItemStack, generation: Generation, progress: GenerationProgress, player: Player): ItemStack {
        try {
            val item = cachedItem.clone()
            val meta = item.itemMeta ?: return item

            // 重新生成lore，保持按钮基本信息不变
            val lore = mutableListOf<String>()

            // 添加基本信息
            lore.add("§7世代: §e${generation.name}")
            lore.add("§7当前进度: §a${progress.caught}§7/§e${progress.total} §7(§b${progress.percentage}%§7)")
            lore.add("")

            // 添加付费进度奖励信息
            addPremiumProgressRewardInfo(lore, generation.id, progress.percentage, player)

            // 添加操作提示
            lore.add("")
            lore.add("§e左键点击查看详情")
            lore.add("§6右键点击领取奖励")

            meta.lore = lore
            item.itemMeta = meta

            return item
        } catch (e: Exception) {
            plugin.logger.warning("更新付费按钮数据失败: ${e.message}")
            return cachedItem
        }
    }

    /**
     * 更新付费全世代按钮的数据（不重新创建按钮，只更新lore等数据）
     */
    private fun updatePremiumOverallButtonData(cachedItem: ItemStack, player: Player): ItemStack {
        try {
            val item = cachedItem.clone()
            val meta = item.itemMeta ?: return item

            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val canClaimPremiumReward = plugin.rewardManager.canClaimPremiumAllGenerationsReward(player)
            val hasClaimedPremiumReward = plugin.rewardManager.hasClaimedPremiumAllGenerationsReward(player)

            // 重新生成lore，保持按钮基本信息不变
            val lore = mutableListOf<String>()

            // 添加全世代进度信息
            lore.add("§7全世代进度: §b${allProgress.overallPercentage}%")
            lore.add("§7总计: §a${allProgress.caughtPokemon}§7/§e${allProgress.totalPokemon}")
            lore.add("")

            // 添加各世代详细信息
            val generations = plugin.generationManager.getAllGenerations().toList()
            generations.forEach { generation ->
                val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)
                val status = if (progress.percentage >= 100) "§a✓" else "§c✗"
                lore.add("$status §7${generation.name}: §b${progress.percentage}% §7(§a${progress.caught}§7/§e${progress.total}§7)")
            }

            lore.add("")

            // 添加付费全世代进度奖励状态
            addPremiumOverallProgressRewardStatus(lore, player, allProgress.overallPercentage)

            // 添加付费全世代完成奖励状态
            if (canClaimPremiumReward) {
                lore.add("§6§l[可领取] §e付费精灵大师奖励")
                lore.add("§a右键点击领取付费精灵大师奖励！")
            } else if (hasClaimedPremiumReward) {
                lore.add("§a§l[已领取] §7付费精灵大师奖励")
            } else {
                lore.add("§7§l[未完成] §c需要完成所有1-9世代")
            }

            lore.add("")
            lore.add("§e左键点击查看详情")
            if (canClaimPremiumReward) {
                lore.add("§6右键点击领取付费精灵大师奖励")
            }

            meta.lore = lore
            item.itemMeta = meta

            return item
        } catch (e: Exception) {
            plugin.logger.warning("更新付费全世代按钮数据失败: ${e.message}")
            return cachedItem
        }
    }



    /**
     * 填充背景 - 付费菜单无边框
     */
    private fun fillBackground(inventory: Inventory) {
        // 付费菜单不使用边框，保持空白背景
        // 这样可以让付费菜单看起来更加简洁和高端
    }

    /**
     * 添加付费世代奖励按钮（从缓存读取，只更新数据）
     */
    private fun addPremiumGenerationButtons(inventory: Inventory, player: Player) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val generations = plugin.generationManager.getAllGenerations().toList()

            generations.forEach { generation: Generation ->
                val slot = plugin.config.getGenerationSlot(generation.id)
                val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)

                // 计算按钮状态
                val isCompleted = progress.percentage >= 100
                val hasClaimedReward = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generation.id)
                val hasCaught = progress.caught > 0
                val finalCompleted = isCompleted && hasClaimedReward

                // 付费菜单需要特殊的按钮信息，不能直接使用普通缓存
                // 创建付费专用按钮，包含完整的付费奖励信息
                var item = createPremiumGenerationButton(generation, progress, player)

                inventory.setItem(slot, item)
            }
        } catch (e: Exception) {
            plugin.logger.warning("添加付费世代奖励按钮失败: ${e.message}")
        }
    }

    /**
     * 创建付费世代奖励按钮
     */
    private fun createPremiumGenerationButton(generation: Generation, progress: GenerationProgress, player: Player): ItemStack {
        val isCompleted = progress.percentage >= 100
        val hasClaimedPremiumReward = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generation.id)
        val hasCaught = progress.caught > 0

        // 使用精灵球物品创建器
        val pokeBallItemCreator = PokeBallItemCreator(plugin)
        val item = pokeBallItemCreator.createGenerationPokeBallItem(
            generationId = generation.id,
            hasCaught = hasCaught,
            isCompleted = isCompleted  // 付费菜单根据完成度显示，不管是否已领取
        )

        val meta = item.itemMeta
        meta?.let { itemMeta ->
            // 设置显示名称
            val displayName = when {
                isCompleted && hasClaimedPremiumReward -> "${generation.color}§l${generation.name} §a✓ §6[付费]"
                isCompleted -> "${generation.color}§l${generation.name} §6★ §6[付费]"
                else -> "${generation.color}§l${generation.name} §6[付费]"
            }
            itemMeta.setDisplayName(displayName)

            val lore = mutableListOf<String>()

            // 添加世代基本信息
            lore.add("§7地区: §f${generation.region}")
            lore.add("§7图鉴范围: §f${generation.pokemonRange.first}-${generation.pokemonRange.last}")
            lore.add("§6§l付费奖励专区")
            lore.add("")

            // 添加进度信息
            lore.add("§e你的进度:")
            lore.add("§7已收集: §a${progress.caught}§7/§f${progress.total}")
            lore.add("§7完成度: ${progress.getProgressBar()}")
            lore.add("")

            // 添加付费奖励状态信息
            when {
                isCompleted && hasClaimedPremiumReward -> {
                    lore.add("§a§l已完成并领取付费奖励！")
                    lore.add("§6✦ 付费大师级收集家 ✦")
                    lore.add("")
                    lore.add("§e左键: §7查看付费奖励详情")
                }
                isCompleted -> {
                    lore.add("§6§l恭喜完成！可以领取付费奖励")
                    lore.add("§6◇ 准备成为付费大师 ◇")
                    lore.add("")
                    lore.add("§6付费完成奖励:")
                    // 从配置文件获取付费奖励描述
                    val premiumRewardDescriptions = plugin.premiumRewardConfig.getPremiumGenerationCompletionDescriptions(generation.id)
                    premiumRewardDescriptions.forEach { description ->
                        lore.add(description)
                    }
                    lore.add("")
                    lore.add("§e左键: §7查看付费奖励详情")
                    lore.add("§a右键: §7领取付费完成奖励")
                }
                else -> {
                    val remaining = progress.total - progress.caught
                    lore.add("§7还需收集 §c$remaining §7只精灵")
                    lore.add("§7完成后可领取付费奖励")
                    lore.add("")

                    // 显示付费进度奖励信息
                    addPremiumProgressRewardInfo(lore, generation.id, progress.percentage, player)

                    lore.add("§e点击查看付费奖励详情")
                }
            }

            // 添加权限提示
            if (!player.hasPermission("acedex.premium.claim")) {
                lore.add("")
                lore.add("§c注意: 需要付费权限才能领取奖励")
                lore.add("§7权限: acedex.premium.claim")
            }

            itemMeta.lore = lore
            item.itemMeta = itemMeta
        }

        return item
    }

    /**
     * 添加付费进度奖励信息到lore中 - 显示全部奖励信息
     */
    private fun addPremiumProgressRewardInfo(lore: MutableList<String>, generationId: String, currentPercentage: Int, player: Player) {
        try {
            // 使用新的付费奖励配置
            val progressPercentages = plugin.premiumRewardConfig.getPremiumGenerationProgressPercentages(generationId)

            if (progressPercentages.isNotEmpty()) {
                val progressRewards = mutableListOf<Triple<Int, List<String>, List<String>>>()

                // 收集所有付费进度奖励
                for (percentage in progressPercentages) {
                    val commands = plugin.premiumRewardConfig.getPremiumGenerationProgressCommands(generationId, percentage)
                    val descriptions = plugin.premiumRewardConfig.getPremiumGenerationProgressDescriptions(generationId, percentage)
                    if (descriptions.isNotEmpty()) {
                        progressRewards.add(Triple(percentage, commands, descriptions))
                    }
                }

                // 按百分比排序
                progressRewards.sortBy { it.first }

                if (progressRewards.isNotEmpty()) {
                    lore.add("§6§l付费进度奖励:")

                    // 显示所有付费进度奖励信息，让玩家看到全部奖励内容
                    for ((percentage, commands, descriptions) in progressRewards) {
                        val rewardKey = "premium_progress_${generationId}_${percentage}"
                        val hasClaimedProgress = plugin.rewardManager.hasClaimedPremiumProgressReward(player, rewardKey)

                        // 根据状态显示不同的图标和颜色
                        val statusIcon = when {
                            hasClaimedProgress -> "§a✓"  // 已领取
                            currentPercentage >= percentage -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedProgress -> "§a已领取"
                            currentPercentage >= percentage -> "§6可领取"
                            else -> "§c需要${percentage}%进度"
                        }

                        lore.add("$statusIcon §e${percentage}%付费进度奖励 §7- $statusText")
                        descriptions.forEach { description ->
                            lore.add("  $description")
                        }
                        lore.add("")
                    }

                    // 添加100%付费完成奖励
                    val completionDescriptions = plugin.premiumRewardConfig.getPremiumGenerationCompletionDescriptions(generationId)
                    if (completionDescriptions.isNotEmpty()) {
                        val hasClaimedCompletion = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generationId)

                        val statusIcon = when {
                            hasClaimedCompletion -> "§a✓"  // 已领取
                            currentPercentage >= 100 -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedCompletion -> "§a已领取"
                            currentPercentage >= 100 -> "§6可领取"
                            else -> "§c需要100%进度"
                        }

                        lore.add("$statusIcon §e100%付费完成奖励 §7- $statusText")
                        completionDescriptions.forEach { description ->
                            lore.add("  $description")
                        }
                        lore.add("")
                    }
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("添加付费进度奖励信息失败: ${e.message}")
        }
    }

    /**
     * 添加付费全世代奖励按钮（从缓存读取，只更新数据）
     */
    private fun addPremiumOverallProgressButton(inventory: Inventory, player: Player) {
        try {
            // 获取进度数据
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val canClaimPremiumReward = plugin.rewardManager.canClaimPremiumAllGenerationsReward(player)
            val hasClaimedPremiumReward = plugin.rewardManager.hasClaimedPremiumAllGenerationsReward(player)
            val progressPercentage = allProgress.overallPercentage.toDouble()

            // 根据进度和状态选择材质
            val materialProgressPercentage = when {
                hasClaimedPremiumReward -> 100.0  // 已领取 - 使用100%材质
                canClaimPremiumReward -> 75.0     // 可领取 - 使用75%材质
                progressPercentage >= 50.0 -> progressPercentage  // 使用实际进度
                else -> 25.0  // 低进度 - 使用25%材质
            }

            // 优先使用GuiButtonCacheManager的内存缓存（和普通菜单一样，瞬间响应）
            var item = plugin.guiButtonCacheManager.getCachedProgressButtonByPercentage(materialProgressPercentage)

            if (item == null) {
                // 内存缓存中没有，创建新按钮
                item = createPremiumProgressButtonItem(materialProgressPercentage)
            } else {
                // 从内存缓存获取到按钮，克隆一份避免修改原缓存
                item = item.clone()
            }

            val meta = item.itemMeta
            if (meta != null) {
                
                val displayName = when {
                    hasClaimedPremiumReward -> "§6§l✦ 付费精灵大师 ✦ §a✓"
                    canClaimPremiumReward -> "§6§l✦ 付费精灵大师 ✦ §6★"
                    else -> "§6§l✦ 付费精灵大师 ✦"
                }
                meta.setDisplayName(displayName)

                // 设置描述
                val lore = mutableListOf<String>()
                lore.add("§7═══════════════════")
                lore.add("§7总进度: §a${String.format("%.1f", progressPercentage)}%")
                lore.add("§7总收集: §b${allProgress.caughtPokemon}/${allProgress.totalPokemon}")
                lore.add("§6§l付费奖励专区")
                lore.add("§7═══════════════════")
                lore.add("")

                // 添加各世代进度
                allProgress.generationProgresses.forEach { (generationId, genProgress) ->
                    val generation = plugin.generationManager.getGeneration(generationId)
                    if (generation != null) {
                        val statusIcon = if (genProgress.percentage >= 100) "§a✓" else "§7○"
                        lore.add("$statusIcon §f${generation.displayName}: §e${genProgress.percentage}% §7(${genProgress.caught}/${genProgress.total})")
                    }
                }

                lore.add("")
                if (hasClaimedPremiumReward) {
                    lore.add("§a§l✓ 已领取付费精灵大师奖励")
                    lore.add("§6◆ 付费精灵大师 ◆")
                } else if (canClaimPremiumReward) {
                    lore.add("§6§l可领取付费精灵大师奖励:")
                    // 从配置文件获取付费全世代奖励描述
                    val premiumAllGenRewardDescriptions = plugin.premiumRewardConfig.getPremiumAllGenerationsCompletionDescriptions()
                    premiumAllGenRewardDescriptions.forEach { description ->
                        lore.add(description)
                    }
                    lore.add("")
                    lore.add("§e§l点击领取付费精灵大师奖励！")
                } else {
                    lore.add("§7完成所有世代收集来解锁付费奖励:")
                    // 显示付费奖励预览
                    val premiumAllGenRewardDescriptions = plugin.premiumRewardConfig.getPremiumAllGenerationsCompletionDescriptions()
                    premiumAllGenRewardDescriptions.forEach { description ->
                        lore.add(description)
                    }

                    // 显示付费全世代进度奖励状态
                    lore.add("")
                    addPremiumOverallProgressRewardStatus(lore, player, allProgress.overallPercentage)

                    lore.add("§e左键: §7领取付费全世代完成奖励")
                    lore.add("§a右键: §7查看付费详细进度")
                }

                // 添加权限提示
                if (!player.hasPermission("acedex.premium.claim")) {
                    lore.add("")
                    lore.add("§c注意: 需要付费权限才能领取奖励")
                    lore.add("§7权限: acedex.premium.claim")
                }

                    meta.lore = lore
                    item.itemMeta = meta
                }

            inventory.setItem(plugin.config.progressButtonSlot, item)
        } catch (e: Exception) {
            plugin.logger.warning("创建付费全世代奖励按钮失败: ${e.message}")
        }
    }

    /**
     * 添加付费全世代进度奖励状态到lore中
     */
    private fun addPremiumOverallProgressRewardStatus(lore: MutableList<String>, player: Player, currentOverallPercentage: Int) {
        try {
            // 使用新的付费奖励配置
            if (plugin.premiumRewardConfig.isPremiumOverallProgressEnabled()) {
                val overallPercentages = plugin.premiumRewardConfig.getPremiumOverallProgressPercentages()
                val overallRewards = mutableListOf<Triple<Int, String, List<String>>>()

                // 收集所有付费全世代进度奖励
                for (percentage in overallPercentages) {
                    val name = plugin.premiumRewardConfig.getPremiumOverallProgressName(percentage)
                    val descriptions = plugin.premiumRewardConfig.getPremiumOverallProgressDescriptions(percentage)
                    if (descriptions.isNotEmpty()) {
                        overallRewards.add(Triple(percentage, name, descriptions))
                    }
                }

                // 按百分比排序
                overallRewards.sortBy { it.first }

                if (overallRewards.isNotEmpty()) {
                    lore.add("§6§l付费全世代进度奖励:")

                    // 显示所有付费全世代进度奖励信息，让玩家看到全部奖励内容
                    for ((percentage, name, descriptions) in overallRewards) {
                        val rewardKey = "premium_overall_$percentage"
                        val hasClaimedOverall = plugin.rewardManager.hasClaimedPremiumOverallProgressReward(player, rewardKey)

                        // 根据状态显示不同的图标和颜色
                        val statusIcon = when {
                            hasClaimedOverall -> "§a✓"  // 已领取
                            currentOverallPercentage >= percentage -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedOverall -> "§a已领取"
                            currentOverallPercentage >= percentage -> "§6可领取"
                            else -> "§c需要${percentage}%全世代进度"
                        }

                        lore.add("$statusIcon §e${percentage}%付费全世代进度 - §6$name §7- $statusText")
                        descriptions.forEach { desc ->
                            lore.add("  $desc")
                        }
                        lore.add("")
                    }
                }
            }
        } catch (e: Exception) {
            plugin.logger.warning("添加付费全世代进度奖励状态失败: ${e.message}")
        }
    }

    /**
     * 添加关闭按钮（使用缓存优化）
     */
    private fun addCloseButton(inventory: Inventory) {
        // 优先使用GuiButtonCacheManager的内存缓存（和普通菜单一样，瞬间响应）
        var item = plugin.guiButtonCacheManager.getCachedButton("close_button")

        if (item == null) {
            // 内存缓存中没有，检查是否有付费专用配置
            val premiumCloseMaterial = plugin.premiumRewardConfig.getPremiumCloseButtonMaterial()
            item = if (premiumCloseMaterial != "PAPER:10030") {
                // 使用付费专用材质
                parseButtonMaterial(premiumCloseMaterial)
            } else {
                // 使用普通菜单的关闭按钮材质
                plugin.config.createCloseButtonItem()
            }
        } else {
            // 从内存缓存获取到按钮，克隆一份避免修改原缓存
            item = item.clone()
        }

        val meta = item.itemMeta
        if (meta != null) {
            meta.setDisplayName("§c关闭菜单")
            meta.lore = listOf("§7点击关闭付费奖励菜单")
            item.itemMeta = meta
        }
        inventory.setItem(plugin.config.closeButtonSlot, item)
    }

    /**
     * 处理GUI点击事件
     */
    @EventHandler
    fun onInventoryClick(event: InventoryClickEvent) {
        val player = event.whoClicked as? Player ?: return
        val guiType = openGuis[player]

        // 只处理付费菜单的点击事件
        if (guiType != "premium_main") {
            return
        }

        event.isCancelled = true

        val clickedItem = event.currentItem ?: return
        val slot = event.slot

        when {
            guiType == "premium_main" -> handlePremiumMainGuiClick(player, slot, clickedItem, event.click)
        }
    }

    /**
     * 处理付费主界面点击
     */
    private fun handlePremiumMainGuiClick(player: Player, slot: Int, item: ItemStack, clickType: org.bukkit.event.inventory.ClickType) {
        // 检查权限
        if (!player.hasPermission("acedex.premium.view")) {
            player.sendMessage("§c你没有权限使用付费奖励功能！")
            return
        }

        when (slot) {
            plugin.config.progressButtonSlot -> {
                // 付费全世代奖励按钮
                if (isPlayerOnCooldown(player, "premium-progress")) {
                    val remaining = getRemainingCooldown(player, "premium-progress")
                    player.sendMessage("§c请等待 ${remaining} 秒后再操作！")
                    return
                }
                setPlayerCooldown(player, "premium-progress")

                when (clickType) {
                    org.bukkit.event.inventory.ClickType.LEFT -> {
                        handlePremiumAllGenerationsRewardClaim(player)
                    }
                    org.bukkit.event.inventory.ClickType.RIGHT -> {
                        // 右键：查看付费详细进度
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.0f)
                        showPremiumAllGenerationsProgress(player)
                    }
                    else -> {
                        handlePremiumAllGenerationsRewardClaim(player)
                    }
                }
            }
            plugin.config.closeButtonSlot -> {
                // 关闭按钮
                player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.5f, 0.8f)
                player.closeInventory()
                openGuis.remove(player)
            }
            else -> {
                // 检查是否是世代按钮位置
                val generations = plugin.generationManager.getAllGenerations().toList()
                val generation = generations.find { plugin.config.getGenerationSlot(it.id) == slot }

                if (generation != null) {
                    when (clickType) {
                        org.bukkit.event.inventory.ClickType.LEFT -> {
                            // 左键：查看付费奖励详情
                            player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.1f)
                            showPremiumGenerationDetails(player, generation)
                        }
                        org.bukkit.event.inventory.ClickType.RIGHT -> {
                            // 右键：尝试领取付费奖励
                            handlePremiumGenerationRewardClaim(player, generation)
                        }
                        else -> {
                            // 其他点击类型默认查看详情
                            player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.1f)
                            showPremiumGenerationDetails(player, generation)
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理付费全世代奖励领取
     */
    private fun handlePremiumAllGenerationsRewardClaim(player: Player) {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            player.sendMessage("§c你没有权限领取付费奖励！")
            return
        }

        val canClaim = plugin.rewardManager.canClaimPremiumAllGenerationsReward(player)
        val hasClaimed = plugin.rewardManager.hasClaimedPremiumAllGenerationsReward(player)

        when {
            hasClaimed -> {
                // 播放已领取提示音效
                player.playSound(player.location, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 0.5f, 0.8f)
                player.sendMessage("§e你已经领取过付费精灵大师奖励了！")
                showPremiumAllGenerationsProgress(player)
            }
            !canClaim -> {
                // 尝试领取可用的付费全世代进度奖励
                val claimedOverallPercentages = plugin.rewardManager.claimAvailablePremiumOverallProgressRewards(player)

                if (claimedOverallPercentages.isNotEmpty()) {
                    // 成功领取了付费全世代进度奖励
                    player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.2f)
                    player.sendMessage("§6§l[付费全世代进度奖励] §f成功领取付费全世代进度奖励！")
                    player.sendMessage("§7领取的进度: §e${claimedOverallPercentages.joinToString("%, ", "", "%")}付费全世代进度奖励")

                    // 显示具体的付费全世代进度奖励内容
                    showClaimedPremiumOverallProgressRewardsDetails(player, claimedOverallPercentages)

                    // 刷新菜单显示
                    openPremiumRewardGui(player)
                } else {
                    // 没有可领取的付费全世代进度奖励，显示详细信息
                    try {
                        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                        val remaining = allProgress.totalGenerations - allProgress.completedGenerations

                        // 播放未完成音效
                        player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.6f, 1.0f)
                        player.sendMessage("§c你还需要完成 $remaining 个世代才能领取付费精灵大师奖励！")
                        player.sendMessage("§7当前进度: §a${allProgress.completedGenerations}§7/§f${allProgress.totalGenerations}")
                        showPremiumAllGenerationsProgress(player)
                    } catch (e: Exception) {
                        player.sendMessage("§c读取进度数据失败: ${e.message}")
                    }
                }
            }
            else -> {
                // 可以领取付费全世代奖励
                val success = plugin.rewardManager.claimPremiumAllGenerationsReward(player)
                if (success) {
                    // 刷新菜单显示
                    openPremiumRewardGui(player)

                    // 播放成功音效
                    player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.2f)
                } else {
                    // 播放失败音效
                    player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.8f, 0.7f)
                    player.sendMessage("§c付费精灵大师奖励发放失败，请联系管理员！")
                }
            }
        }
    }

    /**
     * 处理付费世代奖励领取
     */
    private fun handlePremiumGenerationRewardClaim(player: Player, generation: Generation) {
        // 检查权限
        if (!player.hasPermission("acedex.premium.claim")) {
            player.sendMessage("§c你没有权限领取付费奖励！")
            return
        }

        // 检查冷却时间，防止刷屏
        if (isPlayerOnCooldown(player, "premium-generation-reward")) {
            val remaining = getRemainingCooldown(player, "premium-generation-reward")
            player.sendMessage("§c请等待 ${remaining} 秒后再查看${generation.name}付费信息！")
            return
        }
        setPlayerCooldown(player, "premium-generation-reward")

        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)
            val isCompleted = progress.percentage >= 100
            val hasClaimedPremiumReward = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generation.id)

            when {
                !isCompleted -> {
                    // 尝试领取可用的付费进度奖励
                    val claimedPercentages = plugin.rewardManager.claimAvailablePremiumProgressRewards(player, generation.id)

                    if (claimedPercentages.isNotEmpty()) {
                        // 成功领取了付费进度奖励
                        player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.2f)
                        player.sendMessage("§6§l[付费进度奖励] §f成功领取${generation.name}付费进度奖励！")
                        player.sendMessage("§7领取的进度: §e${claimedPercentages.joinToString("%, ", "", "%")}")

                        // 刷新菜单显示
                        openPremiumRewardGui(player)
                    } else {
                        // 没有可领取的付费进度奖励，显示详细信息
                        player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.6f, 1.0f)
                        val remaining = progress.total - progress.caught
                        player.sendMessage("§c你还需要收集 $remaining 只精灵才能完成${generation.name}！")
                        player.sendMessage("§7当前进度: §a${progress.caught}§7/§f${progress.total}")

                        // 显示按顺序的付费进度奖励信息
                        showPremiumProgressRewardsInOrder(player, generation.id, progress.percentage)
                    }
                }
                hasClaimedPremiumReward -> {
                    // 播放已领取提示音效
                    player.playSound(player.location, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 0.5f, 0.8f)
                    player.sendMessage("§e你已经领取过${generation.name}的付费完成奖励了！")
                }
                else -> {
                    // 先尝试领取所有可用的付费进度奖励
                    val claimedProgressPercentages = plugin.rewardManager.claimAvailablePremiumProgressRewards(player, generation.id)

                    // 然后领取付费完成奖励
                    val success = plugin.rewardManager.givePremiumGenerationReward(player, generation.id)
                    if (success) {
                        // 显示领取信息
                        if (claimedProgressPercentages.isNotEmpty()) {
                            player.sendMessage("§6§l[批量领取] §f同时领取了以下付费奖励：")
                            player.sendMessage("§6§l付费进度奖励: §e${claimedProgressPercentages.joinToString("%, ", "", "%")}进度奖励")
                            player.sendMessage("§a§l付费完成奖励: §f${generation.name}世代付费完成奖励")

                            // 显示具体的付费进度奖励内容
                            showClaimedPremiumProgressRewardsDetails(player, generation.id, claimedProgressPercentages)
                        }

                        // 刷新菜单显示
                        openPremiumRewardGui(player)

                        // 播放成功音效
                        player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f)
                    } else {
                        // 播放失败音效
                        player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.8f, 0.7f)
                        player.sendMessage("§c付费奖励发放失败，请联系管理员！")
                    }
                }
            }
        } catch (e: Exception) {
            player.sendMessage("§c读取世代进度数据失败: ${e.message}")
            plugin.logger.warning("读取玩家 ${player.name} 世代进度失败: ${e.message}")
        }
    }

    // 冷却时间管理方法
    private fun isPlayerOnCooldown(player: Player, action: String): Boolean {
        val playerCooldownMap = playerCooldowns[player] ?: return false
        val lastAction = playerCooldownMap[action] ?: return false
        val cooldownTime = 2000L // 2秒冷却
        return System.currentTimeMillis() - lastAction < cooldownTime
    }

    private fun setPlayerCooldown(player: Player, action: String) {
        val playerCooldownMap = playerCooldowns.getOrPut(player) { mutableMapOf() }
        playerCooldownMap[action] = System.currentTimeMillis()
    }

    private fun getRemainingCooldown(player: Player, action: String): Int {
        val playerCooldownMap = playerCooldowns[player] ?: return 0
        val lastAction = playerCooldownMap[action] ?: return 0
        val cooldownTime = 2000L // 2秒冷却
        val remaining = cooldownTime - (System.currentTimeMillis() - lastAction)
        return (remaining / 1000).toInt().coerceAtLeast(0)
    }

    /**
     * 显示付费全世代进度详情
     */
    private fun showPremiumAllGenerationsProgress(player: Player) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            player.sendMessage("")
            player.sendMessage("§6§l=== 付费全世代进度详情 ===")
            player.sendMessage("§7总体进度: §e${allProgress.overallPercentage}% §7(§a${allProgress.caughtPokemon}§7/§f${allProgress.totalPokemon}§7)")
            player.sendMessage("§7完成世代: §a${allProgress.completedGenerations}§7/§f${allProgress.totalGenerations}")
            player.sendMessage("")

            // 显示各世代详细进度
            allProgress.generationProgresses.forEach { (generationId, progress) ->
                val generation = plugin.generationManager.getGeneration(generationId)
                if (generation != null) {
                    val statusIcon = if (progress.percentage >= 100) "§a✓" else "§7○"
                    val progressBar = progress.getProgressBar(15)

                    player.sendMessage("$statusIcon §f${generation.displayName}:")
                    player.sendMessage("  §7进度: $progressBar")
                    player.sendMessage("  §7收集: §e${progress.caught}§7/§f${progress.total} §7(${progress.percentage}%)")

                    // 显示付费奖励状态
                    val hasClaimedPremium = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generationId)
                    val premiumStatus = if (progress.percentage >= 100) {
                        if (hasClaimedPremium) "§a已领取付费奖励" else "§6可领取付费奖励"
                    } else {
                        "§7付费奖励未解锁"
                    }
                    player.sendMessage("  §7付费状态: $premiumStatus")
                    player.sendMessage("")
                }
            }

        } catch (e: Exception) {
            player.sendMessage("§c获取付费全世代进度失败: ${e.message}")
            plugin.logger.warning("显示付费全世代进度失败: ${e.message}")
        }
    }

    /**
     * 显示已领取的付费全世代进度奖励详细信息
     */
    private fun showClaimedPremiumOverallProgressRewardsDetails(player: Player, claimedPercentages: List<Int>) {
        try {
            player.sendMessage("")
            player.sendMessage("§6§l领取的付费全世代进度奖励详情:")

            claimedPercentages.sorted().forEach { percentage ->
                val name = plugin.premiumRewardConfig.getPremiumOverallProgressName(percentage)
                val descriptions = plugin.premiumRewardConfig.getPremiumOverallProgressDescriptions(percentage)

                player.sendMessage("§a✓ §e${percentage}%付费全世代进度 - §6$name:")
                descriptions.forEach { description ->
                    player.sendMessage("  $description")
                }
                player.sendMessage("")
            }

        } catch (e: Exception) {
            plugin.logger.warning("显示已领取付费全世代进度奖励详情失败: ${e.message}")
        }
    }

    /**
     * 显示付费世代奖励详情
     */
    private fun showPremiumGenerationDetails(player: Player, generation: Generation) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)

            player.sendMessage("")
            player.sendMessage("§6§l=== ${generation.name}付费奖励详情 ===")
            player.sendMessage("§7地区: §f${generation.region}")
            player.sendMessage("§7图鉴范围: §f${generation.pokemonRange.first}-${generation.pokemonRange.last}")
            player.sendMessage("")
            player.sendMessage("§e当前进度:")
            player.sendMessage("§7已收集: §a${progress.caught}§7/§f${progress.total}")
            player.sendMessage("§7完成度: ${progress.getProgressBar()}")
            player.sendMessage("")

            // 显示付费完成奖励
            if (progress.percentage >= 100) {
                val hasClaimedPremium = plugin.rewardManager.hasClaimedPremiumGenerationReward(player, generation.id)
                if (hasClaimedPremium) {
                    player.sendMessage("§a§l✓ 已领取付费完成奖励")
                } else {
                    player.sendMessage("§6§l★ 可领取付费完成奖励:")
                    val premiumRewardDescriptions = plugin.premiumRewardConfig.getPremiumGenerationCompletionDescriptions(generation.id)
                    premiumRewardDescriptions.forEach { description ->
                        player.sendMessage("  $description")
                    }
                }
            } else {
                player.sendMessage("§7§l○ 付费完成奖励 (需要100%完成度):")
                val premiumRewardDescriptions = plugin.premiumRewardConfig.getPremiumGenerationCompletionDescriptions(generation.id)
                premiumRewardDescriptions.forEach { description ->
                    player.sendMessage("  $description")
                }
            }

            player.sendMessage("")

            // 显示付费进度奖励详情
            showPremiumProgressRewardsInOrder(player, generation.id, progress.percentage)

        } catch (e: Exception) {
            player.sendMessage("§c获取${generation.name}付费奖励详情失败: ${e.message}")
            plugin.logger.warning("显示${generation.name}付费奖励详情失败: ${e.message}")
        }
    }
    /**
     * 按顺序显示付费进度奖励信息到聊天栏
     */
    private fun showPremiumProgressRewardsInOrder(player: Player, generationId: String, currentPercentage: Int) {
        try {
            // 使用新的付费奖励配置
            val progressPercentages = plugin.premiumRewardConfig.getPremiumGenerationProgressPercentages(generationId)

            if (progressPercentages.isNotEmpty()) {
                val progressRewards = mutableListOf<Triple<Int, List<String>, List<String>>>()

                // 收集所有付费进度奖励
                for (percentage in progressPercentages) {
                    val commands = plugin.premiumRewardConfig.getPremiumGenerationProgressCommands(generationId, percentage)
                    val descriptions = plugin.premiumRewardConfig.getPremiumGenerationProgressDescriptions(generationId, percentage)
                    if (descriptions.isNotEmpty()) {
                        progressRewards.add(Triple(percentage, commands, descriptions))
                    }
                }

                // 按百分比排序
                progressRewards.sortBy { it.first }

                if (progressRewards.isNotEmpty()) {
                    player.sendMessage("§6§l付费进度奖励详情:")

                    // 找到下一个需要显示的进度奖励
                    var hasShownNext = false

                    for ((percentage, commands, descriptions) in progressRewards) {
                        val rewardKey = "premium_progress_${generationId}_${percentage}"
                        val hasClaimedProgress = plugin.rewardManager.hasClaimedPremiumProgressReward(player, rewardKey)

                        when {
                            // 已经领取的付费奖励
                            hasClaimedProgress -> {
                                player.sendMessage("§a✓ §e${percentage}%付费进度奖励 §7- §a已领取")
                                descriptions.forEach { description ->
                                    player.sendMessage("  $description")
                                }
                                player.sendMessage("")
                            }
                            // 可以领取的付费奖励
                            currentPercentage >= percentage -> {
                                player.sendMessage("§6★ §e${percentage}%付费进度奖励 §7- §6可领取")
                                descriptions.forEach { description ->
                                    player.sendMessage("  $description")
                                }
                                player.sendMessage("")
                            }
                            // 下一个需要达到的进度（只显示第一个）
                            !hasShownNext -> {
                                player.sendMessage("§7○ §e${percentage}%付费进度奖励 §7- §c需要${percentage}%进度 §7(当前${currentPercentage}%)")
                                descriptions.forEach { description ->
                                    player.sendMessage("  $description")
                                }
                                player.sendMessage("")
                                hasShownNext = true
                            }
                        }
                    }
                } else {
                    player.sendMessage("§7该世代暂无付费进度奖励配置")
                }
            } else {
                player.sendMessage("§7该世代暂无付费进度奖励配置")
            }

        } catch (e: Exception) {
            plugin.logger.warning("显示付费进度奖励信息失败: ${e.message}")
            player.sendMessage("§c获取付费进度奖励信息失败")
        }
    }

    /**
     * 显示已领取的付费进度奖励详细信息
     */
    private fun showClaimedPremiumProgressRewardsDetails(player: Player, generationId: String, claimedPercentages: List<Int>) {
        try {
            val generation = plugin.generationManager.getGeneration(generationId)

            player.sendMessage("")
            player.sendMessage("§6§l领取的${generation?.name ?: generationId}付费进度奖励详情:")

            claimedPercentages.sorted().forEach { percentage ->
                val descriptions = plugin.premiumRewardConfig.getPremiumGenerationProgressDescriptions(generationId, percentage)

                player.sendMessage("§a✓ §e${percentage}%付费进度奖励:")
                descriptions.forEach { description ->
                    player.sendMessage("  $description")
                }
                player.sendMessage("")
            }

        } catch (e: Exception) {
            plugin.logger.warning("显示已领取付费进度奖励详情失败: ${e.message}")
        }
    }

    /**
     * 同步更新玩家的付费奖励数据（当输入指令时调用）
     * 手动触发付费菜单数据更新检测（当玩家执行特定操作时调用）
     */
    fun triggerPremiumDataUpdateCheck(player: Player) {
        // 使用强制检测方法异步检测并保存数据，然后通知界面更新
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            try {
                plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

                // 回到主线程通知界面更新
                plugin.server.scheduler.runTask(plugin, Runnable {
                    onPremiumPlayerDataUpdated(player)
                })
            } catch (e: Exception) {
                plugin.logger.warning("付费菜单数据更新检测失败: ${e.message}")
            }
        })
    }

    /**
     * 当玩家数据更新时的回调（付费菜单版本）
     */
    private fun onPremiumPlayerDataUpdated(player: Player) {
        // 如果玩家当前正在查看付费菜单，刷新显示
        if (openGuis[player] == "premium_main") {
            try {
                // 重新打开付费菜单以显示最新数据
                openPremiumRewardGui(player)
            } catch (e: Exception) {
                plugin.logger.warning("刷新付费菜单显示失败: ${e.message}")
            }
        }
    }

    /**
     * 只刷新数据，不清除按钮缓存
     */
    fun syncPremiumRewardData(player: Player) {
        try {
            // 强制重新检测并保存最新数据
            plugin.pokemonDetector.detectAndSavePlayerPokemon(player)

            // 获取最新的进度数据
            plugin.pokemonDetector.getAllGenerationsProgress(player)

            // 不清除缓存，保持按钮结构，只在打开菜单时更新数据
            if (plugin.config.enableDebug) {
                plugin.logger.info("已同步玩家 ${player.name} 的付费奖励数据（保持按钮缓存）")
            }
        } catch (e: Exception) {
            plugin.logger.warning("同步玩家 ${player.name} 付费奖励数据失败: ${e.message}")
        }
    }

    /**
     * 批量同步所有在线玩家的付费奖励数据（只刷新数据，保持缓存）
     */
    fun syncAllPlayersData() {
        try {
            plugin.server.onlinePlayers.forEach { player ->
                syncPremiumRewardData(player)
            }

            plugin.logger.info("已同步所有在线玩家的付费奖励数据（保持按钮缓存）")
        } catch (e: Exception) {
            plugin.logger.warning("批量同步付费奖励数据失败: ${e.message}")
        }
    }

    /**
     * 处理菜单关闭事件 - 清理玩家GUI状态
     */
    @EventHandler
    fun onInventoryClose(event: InventoryCloseEvent) {
        val player = event.player as? Player ?: return
        val currentGuiType = openGuis[player]

        // 清理付费菜单的GUI状态
        if (currentGuiType == "premium_main") {
            openGuis.remove(player)
        }
    }

    /**
     * 玩家退出时清理缓存（防止内存泄漏）
     */
    @EventHandler
    fun onPlayerQuit(event: org.bukkit.event.player.PlayerQuitEvent) {
        val player = event.player
        // 清理玩家数据，防止内存泄漏
        openGuis.remove(player)
        playerCooldowns.remove(player)
        playerPremiumInventories.remove(player)
    }
}
