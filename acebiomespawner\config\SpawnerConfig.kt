package cn.acebrand.acebiomespawner.config

import org.bukkit.configuration.file.YamlConfiguration

/**
 * 生成器配置类
 */
data class SpawnerConfig(
    // 基础设置
    var enabled: Boolean = true,
    var spawnInterval: Int = 300, // 5分钟
    var initialDelay: Int = 60,   // 1分钟
    var targetWorlds: List<String> = listOf("world"),

    // 玩家设置
    var playersPerSpawn: Int = 1,
    var detectionRange: Int = 64,
    var minOnlinePlayers: Int = 1,

    // 生成设置
    var spawnCountMin: Int = 1,
    var spawnCountMax: Int = 3,
    var levelMin: Int = 10,
    var levelMax: Int = 50,
    var allowShiny: Boolean = true,
    var shinyChance: Double = 0.001, // 0.1%

    // 公告设置
    var enableAnnouncement: Boolean = true,
    var announcementPrefix: String = "§6✨[精灵生成]",
    var showCoordinates: Boolean = true,
    var showBiomeName: Boolean = true,
    var showPokemonCount: Boolean = true,

    // 调试设置
    var verboseLogging: Boolean = false,
    var showSpawnProcess: Boolean = false,
    var showReflectionDetails: Boolean = false,

    // 生物群系配置
    var biomeConfigs: Map<String, BiomeConfig> = getDefaultBiomeConfigs()
) {

    companion object {
        /**
         * 从YAML配置加载
         */
        fun fromYaml(yaml: YamlConfiguration): SpawnerConfig {
            val config = SpawnerConfig()

            // 基础设置
            config.enabled = yaml.getBoolean("general.enabled", config.enabled)
            config.spawnInterval = yaml.getInt("general.spawn_interval", config.spawnInterval)
            config.initialDelay = yaml.getInt("general.initial_delay", config.initialDelay)
            config.targetWorlds = yaml.getStringList("general.target_worlds").takeIf { it.isNotEmpty() } ?: config.targetWorlds

            // 玩家设置
            config.playersPerSpawn = yaml.getInt("players.players_per_spawn", config.playersPerSpawn)
            config.detectionRange = yaml.getInt("players.detection_range", config.detectionRange)
            config.minOnlinePlayers = yaml.getInt("players.min_online_players", config.minOnlinePlayers)

            // 生成设置
            config.spawnCountMin = yaml.getInt("spawning.spawn_count_min", config.spawnCountMin)
            config.spawnCountMax = yaml.getInt("spawning.spawn_count_max", config.spawnCountMax)
            config.levelMin = yaml.getInt("spawning.level_min", config.levelMin)
            config.levelMax = yaml.getInt("spawning.level_max", config.levelMax)
            config.allowShiny = yaml.getBoolean("spawning.allow_shiny", config.allowShiny)
            config.shinyChance = yaml.getDouble("spawning.shiny_chance", config.shinyChance)

            // 公告设置
            config.enableAnnouncement = yaml.getBoolean("announcement.enabled", config.enableAnnouncement)
            config.announcementPrefix = yaml.getString("announcement.prefix", config.announcementPrefix) ?: config.announcementPrefix
            config.showCoordinates = yaml.getBoolean("announcement.show_coordinates", config.showCoordinates)
            config.showBiomeName = yaml.getBoolean("announcement.show_biome_name", config.showBiomeName)
            config.showPokemonCount = yaml.getBoolean("announcement.show_pokemon_count", config.showPokemonCount)

            // 调试设置
            config.verboseLogging = yaml.getBoolean("debug.verbose_logging", config.verboseLogging)
            config.showSpawnProcess = yaml.getBoolean("debug.show_spawn_process", config.showSpawnProcess)
            config.showReflectionDetails = yaml.getBoolean("debug.show_reflection_details", config.showReflectionDetails)

            // 生物群系配置
            config.biomeConfigs = loadBiomeConfigs(yaml)

            return config
        }

        /**
         * 加载生物群系配置
         */
        private fun loadBiomeConfigs(yaml: YamlConfiguration): Map<String, BiomeConfig> {
            val biomeConfigs = mutableMapOf<String, BiomeConfig>()

            val biomesSection = yaml.getConfigurationSection("biomes")
            if (biomesSection != null) {
                for (biomeName in biomesSection.getKeys(false)) {
                    val biomeSection = biomesSection.getConfigurationSection(biomeName)
                    if (biomeSection != null) {
                        val biomeConfig = BiomeConfig.fromYaml(biomeSection)
                        biomeConfigs[biomeName] = biomeConfig
                    }
                }
            }

            return biomeConfigs.takeIf { it.isNotEmpty() } ?: getDefaultBiomeConfigs()
        }

        /**
         * 获取默认生物群系配置
         */
        private fun getDefaultBiomeConfigs(): Map<String, BiomeConfig> {
            return mapOf(
                "minecraft:plains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("pikachu", 30.0),
                        PokemonEntry("eevee", 20.0),
                        PokemonEntry("rattata", 25.0),
                        PokemonEntry("pidgey", 25.0)
                    )
                ),
                "minecraft:forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("caterpie", 30.0),
                        PokemonEntry("weedle", 30.0),
                        PokemonEntry("oddish", 15.0)
                    )
                ),
                "minecraft:desert" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 40.0),
                        PokemonEntry("diglett", 35.0),
                        PokemonEntry("cubone", 25.0)
                    )
                ),
                "minecraft:ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.5,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 50.0),
                        PokemonEntry("tentacool", 30.0),
                        PokemonEntry("staryu", 15.0),
                        PokemonEntry("horsea", 5.0)
                    )
                ),

                // ========== 更多原版生物群系 ==========

                // 向日葵平原 - 草系精灵
                "minecraft:sunflower_plains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("sunflora", 35.0),
                        PokemonEntry("sunkern", 30.0),
                        PokemonEntry("pikachu", 20.0),
                        PokemonEntry("eevee", 15.0)
                    )
                ),

                // 桦木森林 - 草系和普通系精灵
                "minecraft:birch_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 25.0),
                        PokemonEntry("pidgey", 25.0)
                    )
                ),

                // 黑森林 - 恶系和幽灵系精灵
                "minecraft:dark_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("gastly", 30.0),
                        PokemonEntry("haunter", 20.0),
                        PokemonEntry("gengar", 10.0),
                        PokemonEntry("murkrow", 25.0),
                        PokemonEntry("umbreon", 15.0)
                    )
                ),

                // 繁花森林 - 草系和妖精系精灵
                "minecraft:flower_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.5,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("oddish", 20.0),
                        PokemonEntry("gloom", 15.0),
                        PokemonEntry("vileplume", 10.0),
                        PokemonEntry("clefairy", 15.0),
                        PokemonEntry("sylveon", 5.0)
                    )
                ),

                // 针叶林 - 草系精灵
                "minecraft:taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("torterra", 15.0),
                        PokemonEntry("seedot", 25.0),
                        PokemonEntry("nuzleaf", 15.0)
                    )
                ),

                // 积雪针叶林 - 冰系和草系精灵
                "minecraft:snowy_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 20.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 15.0),
                        PokemonEntry("glaceon", 10.0)
                    )
                ),

                // 热带草原 - 火系和地面系精灵
                "minecraft:savanna" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 30.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("girafarig", 20.0),
                        PokemonEntry("zebstrika", 15.0),
                        PokemonEntry("pyroar", 10.0)
                    )
                ),

                // 丛林 - 草系和虫系精灵
                "minecraft:jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.5,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("venusaur", 10.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 20.0),
                        PokemonEntry("tropius", 10.0)
                    )
                ),

                // 沼泽 - 水系和毒系精灵
                "minecraft:swamp" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("poliwag", 30.0),
                        PokemonEntry("poliwhirl", 20.0),
                        PokemonEntry("poliwrath", 10.0),
                        PokemonEntry("grimer", 25.0),
                        PokemonEntry("muk", 15.0)
                    )
                ),

                // 红树林沼泽 - 水系和草系精灵
                "minecraft:mangrove_swamp" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("lotad", 30.0),
                        PokemonEntry("lombre", 20.0),
                        PokemonEntry("ludicolo", 15.0),
                        PokemonEntry("mudkip", 20.0),
                        PokemonEntry("marshtomp", 15.0)
                    )
                ),

                // 山地 - 岩石系和地面系精灵
                "minecraft:windswept_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("machop", 20.0),
                        PokemonEntry("machoke", 5.0)
                    )
                ),

                // 积雪平原 - 冰系精灵
                "minecraft:snowy_plains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 20.0),
                        PokemonEntry("sneasel", 25.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("articuno", 10.0)
                    )
                ),

                // ========== Terralith 生物群系示例配置 ==========

                // 高山林地 - 冰系和草系精灵
                "terralith:alpine_grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 15.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 15.0),
                        PokemonEntry("mamoswine", 10.0),
                        PokemonEntry("glaceon", 5.0)
                    )
                ),

                // 樱花林地 - 草系和妖精系精灵
                "terralith:sakura_grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("cherubi", 35.0),
                        PokemonEntry("cherrim", 20.0),
                        PokemonEntry("roselia", 25.0),
                        PokemonEntry("roserade", 10.0),
                        PokemonEntry("sylveon", 8.0),
                        PokemonEntry("clefairy", 2.0)
                    )
                ),

                // 月光林地 - 超能力系和妖精系精灵
                "terralith:moonlight_grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("clefairy", 25.0),
                        PokemonEntry("clefable", 15.0),
                        PokemonEntry("jigglypuff", 20.0),
                        PokemonEntry("wigglytuff", 10.0),
                        PokemonEntry("espeon", 15.0),
                        PokemonEntry("umbreon", 15.0)
                    )
                ),

                // 远古沙地 - 地面系和岩石系精灵
                "terralith:ancient_sands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 20.0),
                        PokemonEntry("trapinch", 25.0),
                        PokemonEntry("vibrava", 15.0),
                        PokemonEntry("flygon", 8.0),
                        PokemonEntry("hippopotas", 2.0)
                    )
                ),

                // 火山口 - 火系精灵
                "terralith:volcanic_crater" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("arcanine", 15.0),
                        PokemonEntry("ponyta", 20.0),
                        PokemonEntry("rapidash", 15.0),
                        PokemonEntry("magmar", 15.0),
                        PokemonEntry("magmortar", 8.0),
                        PokemonEntry("heatran", 2.0)
                    )
                ),

                // 水晶洞穴 - 岩石系和钢系精灵
                "terralith:crystal_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("steelix", 10.0),
                        PokemonEntry("carbink", 5.0)
                    )
                ),

                // 热带丛林 - 草系和虫系精灵
                "terralith:tropical_jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.5,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("venusaur", 10.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("metapod", 15.0),
                        PokemonEntry("butterfree", 10.0),
                        PokemonEntry("tropius", 5.0)
                    )
                ),

                // 天空岛 - 飞行系精灵
                "terralith:skylands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("pidgeot", 15.0),
                        PokemonEntry("spearow", 20.0),
                        PokemonEntry("fearow", 15.0),
                        PokemonEntry("rayquaza", 5.0)
                    )
                ),

                // ========== 更多Terralith生物群系 ==========

                // 干旱高地 - 地面系和岩石系精灵
                "terralith:arid_highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 20.0),
                        PokemonEntry("cubone", 25.0),
                        PokemonEntry("marowak", 15.0),
                        PokemonEntry("trapinch", 10.0)
                    )
                ),

                // 温带高地 - 草系和普通系精灵
                "terralith:temperate_highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("eevee", 20.0),
                        PokemonEntry("leafeon", 15.0),
                        PokemonEntry("deerling", 20.0)
                    )
                ),

                // 月光峡谷 - 超能力系和妖精系精灵
                "terralith:moonlight_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("clefairy", 25.0),
                        PokemonEntry("clefable", 15.0),
                        PokemonEntry("espeon", 20.0),
                        PokemonEntry("umbreon", 20.0),
                        PokemonEntry("cresselia", 10.0),
                        PokemonEntry("lunatone", 10.0)
                    )
                ),

                // 樱花峡谷 - 草系和妖精系精灵
                "terralith:sakura_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("cherubi", 30.0),
                        PokemonEntry("cherrim", 25.0),
                        PokemonEntry("roselia", 20.0),
                        PokemonEntry("roserade", 15.0),
                        PokemonEntry("sylveon", 10.0)
                    )
                ),

                // 薰衣草森林 - 草系和妖精系精灵
                "terralith:lavender_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("gloom", 20.0),
                        PokemonEntry("vileplume", 15.0),
                        PokemonEntry("roselia", 20.0),
                        PokemonEntry("flabebe", 15.0),
                        PokemonEntry("floette", 5.0)
                    )
                ),

                // 云雾森林 - 飞行系和草系精灵
                "terralith:cloud_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("swablu", 25.0),
                        PokemonEntry("altaria", 15.0),
                        PokemonEntry("tropius", 15.0)
                    )
                ),

                // 花岗岩洞穴 - 岩石系和钢系精灵
                "terralith:granite_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("steelix", 10.0)
                    )
                ),

                // 安山岩洞穴 - 岩石系精灵
                "terralith:andesite_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("roggenrola", 20.0),
                        PokemonEntry("boldore", 15.0)
                    )
                ),

                // 热力洞穴 - 火系精灵
                "terralith:thermal_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 15.0),
                        PokemonEntry("slugma", 30.0),
                        PokemonEntry("magcargo", 20.0),
                        PokemonEntry("torchic", 10.0)
                    )
                ),

                // 霜火洞穴 - 冰系和火系精灵
                "terralith:frostfire_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("sneasel", 25.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("glaceon", 20.0),
                        PokemonEntry("flareon", 15.0)
                    )
                ),

                // 沙漠尖塔 - 地面系和岩石系精灵
                "terralith:desert_spires" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 20.0),
                        PokemonEntry("diglett", 25.0),
                        PokemonEntry("dugtrio", 15.0),
                        PokemonEntry("cacnea", 10.0)
                    )
                ),

                // 繁茂沙漠 - 草系和地面系精灵
                "terralith:lush_desert" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("cacnea", 30.0),
                        PokemonEntry("cacturne", 20.0),
                        PokemonEntry("maractus", 25.0),
                        PokemonEntry("sandshrew", 15.0),
                        PokemonEntry("tropius", 10.0)
                    )
                ),

                // 沙漠绿洲 - 水系和草系精灵
                "terralith:desert_oasis" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("lotad", 25.0),
                        PokemonEntry("lombre", 20.0),
                        PokemonEntry("ludicolo", 15.0),
                        PokemonEntry("cacnea", 20.0),
                        PokemonEntry("maractus", 20.0)
                    )
                ),

                // 天空岛春林 - 飞行系和草系精灵
                "terralith:skylands_spring" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("swablu", 25.0),
                        PokemonEntry("altaria", 15.0),
                        PokemonEntry("tropius", 15.0)
                    )
                ),

                // 天空岛夏林 - 飞行系和火系精灵
                "terralith:skylands_summer" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 20.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("fearow", 20.0),
                        PokemonEntry("charizard", 15.0),
                        PokemonEntry("moltres", 10.0),
                        PokemonEntry("ho_oh", 15.0)
                    )
                ),

                // 天空岛秋林 - 飞行系精灵
                "terralith:skylands_autumn" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("pidgeot", 15.0),
                        PokemonEntry("spearow", 20.0),
                        PokemonEntry("fearow", 15.0),
                        PokemonEntry("noctowl", 5.0)
                    )
                ),

                // 天空岛冬林 - 飞行系和冰系精灵
                "terralith:skylands_winter" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 20.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("articuno", 15.0),
                        PokemonEntry("delibird", 25.0),
                        PokemonEntry("glaceon", 20.0)
                    )
                ),

                // 兰花沼泽 - 水系和草系精灵
                "terralith:orchid_swamp" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("poliwag", 25.0),
                        PokemonEntry("poliwhirl", 20.0),
                        PokemonEntry("lotad", 25.0),
                        PokemonEntry("lombre", 15.0),
                        PokemonEntry("vileplume", 15.0)
                    )
                ),

                // 黄石公园 - 火系和地面系精灵
                "terralith:yellowstone" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("arcanine", 20.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("rapidash", 15.0),
                        PokemonEntry("entei", 10.0),
                        PokemonEntry("heatran", 5.0)
                    )
                ),

                // ========== 更多原版生物群系 ==========

                // 冰刺平原 - 冰系精灵
                "minecraft:ice_spikes" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 25.0),
                        PokemonEntry("sneasel", 20.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("articuno", 10.0)
                    )
                ),

                // 蘑菇岛 - 妖精系和草系精灵
                "minecraft:mushroom_fields" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.6,
                    pokemonList = listOf(
                        PokemonEntry("shroomish", 35.0),
                        PokemonEntry("breloom", 25.0),
                        PokemonEntry("paras", 25.0),
                        PokemonEntry("parasect", 15.0)
                    )
                ),

                // 溶洞 - 岩石系精灵
                "minecraft:dripstone_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("onix", 25.0),
                        PokemonEntry("carbink", 20.0)
                    )
                ),

                // 繁茂洞穴 - 草系精灵
                "minecraft:lush_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 25.0),
                        PokemonEntry("tangela", 25.0)
                    )
                ),

                // 深暗之域 - 恶系和幽灵系精灵
                "minecraft:deep_dark" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.15,
                    pokemonList = listOf(
                        PokemonEntry("gastly", 25.0),
                        PokemonEntry("haunter", 20.0),
                        PokemonEntry("gengar", 15.0),
                        PokemonEntry("sableye", 20.0),
                        PokemonEntry("spiritomb", 15.0),
                        PokemonEntry("darkrai", 5.0)
                    )
                ),

                // 河流 - 水系精灵
                "minecraft:river" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 40.0),
                        PokemonEntry("goldeen", 30.0),
                        PokemonEntry("seaking", 15.0),
                        PokemonEntry("psyduck", 10.0),
                        PokemonEntry("golduck", 5.0)
                    )
                ),

                // 海滩 - 水系和地面系精灵
                "minecraft:beach" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("krabby", 30.0),
                        PokemonEntry("kingler", 20.0),
                        PokemonEntry("sandshrew", 25.0),
                        PokemonEntry("sandslash", 15.0),
                        PokemonEntry("staryu", 10.0)
                    )
                ),

                // 冰冻河流 - 冰系和水系精灵
                "minecraft:frozen_river" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 25.0),
                        PokemonEntry("magikarp", 25.0),
                        PokemonEntry("goldeen", 15.0),
                        PokemonEntry("articuno", 5.0)
                    )
                ),

                // 积雪海滩 - 冰系精灵
                "minecraft:snowy_beach" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("seel", 35.0),
                        PokemonEntry("dewgong", 30.0),
                        PokemonEntry("sneasel", 20.0),
                        PokemonEntry("weavile", 15.0)
                    )
                ),

                // 石岸 - 岩石系和水系精灵
                "minecraft:stony_shore" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("krabby", 25.0),
                        PokemonEntry("kingler", 15.0),
                        PokemonEntry("onix", 5.0)
                    )
                ),

                // 原始桦木森林 - 草系精灵
                "minecraft:old_growth_birch_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 原始松木针叶林 - 草系精灵
                "minecraft:old_growth_pine_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("torterra", 15.0),
                        PokemonEntry("seedot", 25.0),
                        PokemonEntry("nuzleaf", 15.0)
                    )
                ),

                // 原始云杉针叶林 - 草系精灵
                "minecraft:old_growth_spruce_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("torterra", 15.0),
                        PokemonEntry("seedot", 25.0),
                        PokemonEntry("shiftry", 15.0)
                    )
                ),

                // 热带草原高原 - 火系和地面系精灵
                "minecraft:savanna_plateau" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 30.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("girafarig", 20.0),
                        PokemonEntry("zebstrika", 15.0),
                        PokemonEntry("pyroar", 10.0)
                    )
                ),

                // 风袭热带草原 - 火系和飞行系精灵
                "minecraft:windswept_savanna" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("fearow", 10.0)
                    )
                ),

                // 恶地 - 地面系和岩石系精灵
                "minecraft:badlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 25.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 15.0),
                        PokemonEntry("rhyhorn", 5.0)
                    )
                ),

                // 风蚀恶地 - 地面系和岩石系精灵
                "minecraft:eroded_badlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 25.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 15.0),
                        PokemonEntry("aerodactyl", 5.0)
                    )
                ),

                // 繁茂恶地 - 地面系和草系精灵
                "minecraft:wooded_badlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 25.0),
                        PokemonEntry("sandslash", 20.0),
                        PokemonEntry("cacnea", 25.0),
                        PokemonEntry("cacturne", 20.0),
                        PokemonEntry("maractus", 10.0)
                    )
                ),

                // 风袭森林 - 草系和飞行系精灵
                "minecraft:windswept_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("fearow", 20.0)
                    )
                ),

                // 风袭砂砾丘陵 - 岩石系和飞行系精灵
                "minecraft:windswept_gravelly_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("skarmory", 5.0)
                    )
                ),

                // 草甸 - 草系和普通系精灵
                "minecraft:meadow" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 25.0),
                        PokemonEntry("eevee", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 雪林 - 冰系和草系精灵
                "minecraft:grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 25.0),
                        PokemonEntry("turtwig", 20.0),
                        PokemonEntry("grotle", 15.0),
                        PokemonEntry("glaceon", 10.0)
                    )
                ),

                // 积雪山坡 - 冰系精灵
                "minecraft:snowy_slopes" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("swinub", 30.0),
                        PokemonEntry("piloswine", 25.0),
                        PokemonEntry("sneasel", 25.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("glaceon", 5.0)
                    )
                ),

                // 冰冻山峰 - 冰系精灵
                "minecraft:frozen_peaks" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 20.0),
                        PokemonEntry("mamoswine", 15.0),
                        PokemonEntry("articuno", 20.0),
                        PokemonEntry("regice", 20.0)
                    )
                ),

                // 尖峭山峰 - 岩石系和冰系精灵
                "minecraft:jagged_peaks" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 20.0),
                        PokemonEntry("aerodactyl", 10.0)
                    )
                ),

                // 裸岩山峰 - 岩石系精灵
                "minecraft:stony_peaks" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("golem", 20.0),
                        PokemonEntry("onix", 15.0)
                    )
                ),

                // 稀疏丛林 - 草系和虫系精灵
                "minecraft:sparse_jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 20.0),
                        PokemonEntry("scyther", 10.0)
                    )
                ),

                // 竹林 - 草系和普通系精灵
                "minecraft:bamboo_jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 25.0),
                        PokemonEntry("tropius", 15.0)
                    )
                ),

                // 深海 - 水系精灵
                "minecraft:deep_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 30.0),
                        PokemonEntry("tentacool", 25.0),
                        PokemonEntry("tentacruel", 20.0),
                        PokemonEntry("staryu", 15.0),
                        PokemonEntry("starmie", 10.0)
                    )
                ),

                // 温海 - 水系精灵
                "minecraft:lukewarm_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 25.0),
                        PokemonEntry("goldeen", 25.0),
                        PokemonEntry("seaking", 20.0),
                        PokemonEntry("horsea", 20.0),
                        PokemonEntry("seadra", 10.0)
                    )
                ),

                // 深温海 - 水系精灵
                "minecraft:deep_lukewarm_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 25.0),
                        PokemonEntry("tentacool", 25.0),
                        PokemonEntry("horsea", 25.0),
                        PokemonEntry("seadra", 15.0),
                        PokemonEntry("kingdra", 10.0)
                    )
                ),

                // 暖海 - 水系精灵
                "minecraft:warm_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.5,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 20.0),
                        PokemonEntry("goldeen", 25.0),
                        PokemonEntry("seaking", 20.0),
                        PokemonEntry("corsola", 25.0),
                        PokemonEntry("mantine", 10.0)
                    )
                ),

                // ========== 更多Terralith生物群系 ==========

                // 西伯利亚针叶林 - 冰系和草系精灵
                "terralith:siberian_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 20.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 15.0),
                        PokemonEntry("glaceon", 10.0)
                    )
                ),

                // 森林高地 - 草系精灵
                "terralith:forested_highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("torterra", 15.0),
                        PokemonEntry("leafeon", 20.0),
                        PokemonEntry("deerling", 20.0)
                    )
                ),

                // 花开高原 - 草系和妖精系精灵
                "terralith:blooming_plateau" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("oddish", 20.0),
                        PokemonEntry("gloom", 15.0),
                        PokemonEntry("vileplume", 10.0),
                        PokemonEntry("clefairy", 15.0),
                        PokemonEntry("sylveon", 5.0)
                    )
                ),

                // 花开峡谷 - 草系和妖精系精灵
                "terralith:blooming_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.5,
                    pokemonList = listOf(
                        PokemonEntry("cherubi", 25.0),
                        PokemonEntry("cherrim", 20.0),
                        PokemonEntry("roselia", 25.0),
                        PokemonEntry("roserade", 15.0),
                        PokemonEntry("flabebe", 10.0),
                        PokemonEntry("floette", 5.0)
                    )
                ),

                // 薰衣草峡谷 - 草系和妖精系精灵
                "terralith:lavender_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("gloom", 20.0),
                        PokemonEntry("vileplume", 15.0),
                        PokemonEntry("roselia", 20.0),
                        PokemonEntry("flabebe", 15.0),
                        PokemonEntry("floette", 5.0)
                    )
                ),

                // 繁茂峡谷 - 草系精灵
                "terralith:lush_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("venusaur", 15.0),
                        PokemonEntry("oddish", 20.0),
                        PokemonEntry("bellsprout", 20.0)
                    )
                ),

                // 灌木地 - 草系和普通系精灵
                "terralith:brushland" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("rattata", 30.0),
                        PokemonEntry("raticate", 20.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("eevee", 10.0)
                    )
                ),

                // 寒冷灌木地 - 冰系和草系精灵
                "terralith:cold_shrubland" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("snover", 25.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 20.0),
                        PokemonEntry("glaceon", 15.0),
                        PokemonEntry("articuno", 15.0)
                    )
                ),

                // 炎热灌木地 - 火系和地面系精灵
                "terralith:hot_shrubland" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("sandshrew", 25.0),
                        PokemonEntry("diglett", 20.0),
                        PokemonEntry("flareon", 5.0)
                    )
                ),

                // 岩石灌木地 - 岩石系和地面系精灵
                "terralith:rocky_shrubland" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("rhyhorn", 15.0),
                        PokemonEntry("rhydon", 10.0)
                    )
                ),

                // ========== 高山类生物群系 ==========

                // 高山高地 - 冰系和岩石系精灵
                "terralith:alpine_highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("snover", 25.0),
                        PokemonEntry("abomasnow", 20.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 15.0),
                        PokemonEntry("glaceon", 15.0)
                    )
                ),

                // 冰冻悬崖 - 冰系精灵
                "terralith:frozen_cliffs" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("seel", 25.0),
                        PokemonEntry("dewgong", 20.0),
                        PokemonEntry("sneasel", 25.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("articuno", 15.0)
                    )
                ),

                // 冰川峡谷 - 冰系精灵
                "terralith:glacial_chasm" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("swinub", 30.0),
                        PokemonEntry("piloswine", 25.0),
                        PokemonEntry("mamoswine", 15.0),
                        PokemonEntry("glaceon", 20.0),
                        PokemonEntry("regice", 10.0)
                    )
                ),

                // 岩石山脉 - 岩石系和地面系精灵
                "terralith:rocky_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("rhyhorn", 20.0),
                        PokemonEntry("rhydon", 10.0)
                    )
                ),

                // 雪地恶地 - 冰系和地面系精灵
                "terralith:snowy_badlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("snover", 25.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 20.0),
                        PokemonEntry("sandshrew", 15.0),
                        PokemonEntry("glaceon", 15.0)
                    )
                ),

                // 雪地枫林 - 冰系和草系精灵
                "terralith:snowy_maple_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 20.0),
                        PokemonEntry("turtwig", 20.0),
                        PokemonEntry("grotle", 15.0),
                        PokemonEntry("glaceon", 15.0)
                    )
                ),

                // 寒冬森林 - 冰系和草系精灵
                "terralith:wintry_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 25.0),
                        PokemonEntry("swinub", 20.0),
                        PokemonEntry("piloswine", 15.0),
                        PokemonEntry("glaceon", 10.0)
                    )
                ),

                // 寒冬低地 - 冰系精灵
                "terralith:wintry_lowlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 25.0),
                        PokemonEntry("sneasel", 25.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("articuno", 5.0)
                    )
                ),

                // 布莱斯峡谷 - 岩石系和地面系精灵
                "terralith:bryce_canyon" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("rhyhorn", 15.0),
                        PokemonEntry("aerodactyl", 10.0)
                    )
                ),

                // 雪地樱花林 - 冰系和草系精灵
                "terralith:snowy_cherry_grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("cherubi", 25.0),
                        PokemonEntry("cherrim", 20.0),
                        PokemonEntry("snover", 25.0),
                        PokemonEntry("abomasnow", 15.0),
                        PokemonEntry("glaceon", 15.0)
                    )
                ),

                // 雪地护盾 - 冰系精灵
                "terralith:snowy_shield" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("swinub", 30.0),
                        PokemonEntry("piloswine", 25.0),
                        PokemonEntry("mamoswine", 15.0),
                        PokemonEntry("glaceon", 20.0),
                        PokemonEntry("regice", 10.0)
                    )
                ),

                // 翡翠峰 - 岩石系和草系精灵
                "terralith:emerald_peaks" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("carbink", 15.0)
                    )
                ),

                // 火山峰 - 火系和岩石系精灵
                "terralith:volcanic_peaks" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("arcanine", 20.0),
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 15.0),
                        PokemonEntry("heatran", 15.0)
                    )
                ),

                // 猩红山脉 - 火系和岩石系精灵
                "terralith:scarlet_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("rapidash", 20.0),
                        PokemonEntry("geodude", 20.0),
                        PokemonEntry("graveler", 10.0)
                    )
                ),

                // 彩绘山脉 - 岩石系精灵
                "terralith:painted_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("carbink", 10.0)
                    )
                ),

                // 雾霾山 - 毒系和岩石系精灵
                "terralith:haze_mountain" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("grimer", 30.0),
                        PokemonEntry("muk", 25.0),
                        PokemonEntry("koffing", 25.0),
                        PokemonEntry("weezing", 15.0),
                        PokemonEntry("crobat", 5.0)
                    )
                ),

                // 白色悬崖 - 岩石系和飞行系精灵
                "terralith:white_cliffs" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("aerodactyl", 10.0)
                    )
                ),

                // 花岗岩悬崖 - 岩石系精灵
                "terralith:granite_cliffs" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("golem", 20.0),
                        PokemonEntry("onix", 15.0)
                    )
                ),

                // 风蚀尖塔 - 岩石系和飞行系精灵
                "terralith:windswept_spires" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("skarmory", 15.0)
                    )
                ),

                // 石质尖塔 - 岩石系精灵
                "terralith:stony_spires" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("onix", 25.0),
                        PokemonEntry("steelix", 10.0)
                    )
                ),

                // ========== 森林类生物群系 ==========

                // 桦木针叶林 - 草系精灵
                "terralith:birch_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("torterra", 15.0),
                        PokemonEntry("seedot", 25.0),
                        PokemonEntry("nuzleaf", 15.0)
                    )
                ),

                // 西伯利亚林地 - 冰系和草系精灵
                "terralith:siberian_grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 20.0),
                        PokemonEntry("turtwig", 20.0),
                        PokemonEntry("grotle", 15.0),
                        PokemonEntry("glaceon", 15.0)
                    )
                ),

                // 护盾林 - 草系和普通系精灵
                "terralith:shield" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 20.0),
                        PokemonEntry("eevee", 10.0)
                    )
                ),

                // 护盾林空地 - 草系和普通系精灵
                "terralith:shield_clearing" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 25.0),
                        PokemonEntry("eevee", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 丛林山脉 - 草系和虫系精灵
                "terralith:jungle_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 25.0),
                        PokemonEntry("scyther", 15.0)
                    )
                ),

                // 岩石丛林 - 草系和岩石系精灵
                "terralith:rocky_jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("onix", 20.0)
                    )
                ),

                // 紫水晶雨林 - 草系和超能力系精灵
                "terralith:amethyst_rainforest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 10.0),
                        PokemonEntry("carbink", 10.0)
                    )
                ),

                // ========== 洞穴类生物群系 ==========

                // 闪长岩洞穴 - 岩石系精灵
                "terralith:diorite_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("carbink", 15.0)
                    )
                ),

                // 凝灰岩洞穴 - 岩石系精灵
                "terralith:tuff_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("roggenrola", 20.0),
                        PokemonEntry("boldore", 15.0)
                    )
                ),

                // 深层洞穴 - 岩石系和恶系精灵
                "terralith:deep_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("sableye", 20.0),
                        PokemonEntry("mawile", 15.0)
                    )
                ),

                // 地幔洞穴 - 火系和岩石系精灵
                "terralith:mantle_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 15.0),
                        PokemonEntry("slugma", 30.0),
                        PokemonEntry("magcargo", 20.0),
                        PokemonEntry("heatran", 10.0)
                    )
                ),

                // 虫蛀洞穴 - 虫系和毒系精灵
                "terralith:infested_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 25.0),
                        PokemonEntry("paras", 25.0),
                        PokemonEntry("parasect", 15.0),
                        PokemonEntry("scyther", 10.0)
                    )
                ),

                // 真菌洞穴 - 草系和毒系精灵
                "terralith:fungal_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("shroomish", 30.0),
                        PokemonEntry("breloom", 20.0),
                        PokemonEntry("paras", 25.0),
                        PokemonEntry("parasect", 20.0),
                        PokemonEntry("foongus", 5.0)
                    )
                ),

                // 地下丛林 - 草系和虫系精灵
                "terralith:underground_jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 25.0),
                        PokemonEntry("scyther", 15.0)
                    )
                ),

                // ========== 峡谷和山谷类生物群系 ==========

                // 峡谷空地 - 草系和普通系精灵
                "terralith:valley_clearing" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 25.0),
                        PokemonEntry("eevee", 20.0),
                        PokemonEntry("leafeon", 5.0)
                    )
                ),

                // 砂岩峡谷 - 岩石系和地面系精灵
                "terralith:sandstone_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("sandshrew", 25.0),
                        PokemonEntry("sandslash", 15.0),
                        PokemonEntry("onix", 5.0)
                    )
                ),

                // 紫水晶峡谷 - 岩石系和超能力系精灵
                "terralith:amethyst_canyon" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("carbink", 10.0)
                    )
                ),

                // 沙漠峡谷 - 地面系和岩石系精灵
                "terralith:desert_canyon" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 25.0),
                        PokemonEntry("diglett", 25.0),
                        PokemonEntry("dugtrio", 15.0),
                        PokemonEntry("trapinch", 5.0)
                    )
                ),

                // 约塞米蒂悬崖 - 岩石系和飞行系精灵
                "terralith:yosemite_cliffs" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("aerodactyl", 10.0)
                    )
                ),

                // 约塞米蒂低地 - 草系和普通系精灵
                "terralith:yosemite_lowlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("eevee", 25.0),
                        PokemonEntry("deerling", 20.0),
                        PokemonEntry("leafeon", 5.0)
                    )
                ),

                // ========== 平原和草原类生物群系 ==========

                // 灌木地 - 草系和普通系精灵
                "terralith:shrubland" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("rattata", 30.0),
                        PokemonEntry("raticate", 20.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("eevee", 10.0)
                    )
                ),

                // 高地 - 草系和普通系精灵
                "terralith:highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0),
                        PokemonEntry("eevee", 25.0),
                        PokemonEntry("deerling", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 草原 - 草系和普通系精灵
                "terralith:steppe" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("rapidash", 20.0),
                        PokemonEntry("eevee", 25.0),
                        PokemonEntry("deerling", 20.0),
                        PokemonEntry("sawsbuck", 10.0)
                    )
                ),

                // 灰烬热带草原 - 火系和地面系精灵
                "terralith:ashen_savanna" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 30.0),
                        PokemonEntry("arcanine", 20.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("rapidash", 15.0),
                        PokemonEntry("entei", 10.0)
                    )
                ),

                // 热带草原恶地 - 火系和地面系精灵
                "terralith:savanna_badlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("sandshrew", 25.0),
                        PokemonEntry("sandslash", 20.0),
                        PokemonEntry("pyroar", 5.0)
                    )
                ),

                // 热带草原坡地 - 火系和地面系精灵
                "terralith:savanna_slopes" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 30.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("girafarig", 20.0),
                        PokemonEntry("zebstrika", 15.0),
                        PokemonEntry("pyroar", 10.0)
                    )
                ),

                // 破碎热带草原 - 火系和岩石系精灵
                "terralith:fractured_savanna" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("ponyta", 25.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("rhyhorn", 5.0)
                    )
                ),

                // ========== 沙漠和火山类生物群系 ==========

                // 砾石沙漠 - 地面系和岩石系精灵
                "terralith:gravel_desert" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 25.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 15.0),
                        PokemonEntry("onix", 5.0)
                    )
                ),

                // 红色绿洲 - 水系和草系精灵
                "terralith:red_oasis" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("lotad", 25.0),
                        PokemonEntry("lombre", 20.0),
                        PokemonEntry("ludicolo", 15.0),
                        PokemonEntry("cacnea", 25.0),
                        PokemonEntry("cacturne", 15.0)
                    )
                ),

                // 火山口 - 火系精灵
                "terralith:caldera" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 30.0),
                        PokemonEntry("magmortar", 20.0),
                        PokemonEntry("slugma", 25.0),
                        PokemonEntry("magcargo", 20.0),
                        PokemonEntry("heatran", 5.0)
                    )
                ),

                // 玄武岩悬崖 - 岩石系和火系精灵
                "terralith:basalt_cliffs" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("slugma", 20.0),
                        PokemonEntry("magcargo", 10.0)
                    )
                ),

                // ========== 海滩和水域类生物群系 ==========

                // 砾石海滩 - 岩石系和水系精灵
                "terralith:gravel_beach" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("krabby", 25.0),
                        PokemonEntry("kingler", 15.0),
                        PokemonEntry("staryu", 5.0)
                    )
                ),

                // 温暖河流 - 水系精灵
                "terralith:warm_river" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 30.0),
                        PokemonEntry("goldeen", 25.0),
                        PokemonEntry("seaking", 20.0),
                        PokemonEntry("horsea", 20.0),
                        PokemonEntry("seadra", 5.0)
                    )
                ),

                // 深层温暖海洋 - 水系精灵
                "terralith:deep_warm_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 25.0),
                        PokemonEntry("tentacool", 25.0),
                        PokemonEntry("tentacruel", 20.0),
                        PokemonEntry("corsola", 20.0),
                        PokemonEntry("mantine", 10.0)
                    )
                ),

                // 冰沼泽 - 冰系和水系精灵
                "terralith:ice_marsh" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 25.0),
                        PokemonEntry("poliwag", 25.0),
                        PokemonEntry("poliwhirl", 15.0),
                        PokemonEntry("glaceon", 5.0)
                    )
                ),

                // ========== 天空岛和特殊生物群系 ==========

                // 阿尔法群岛 - 飞行系和普通系精灵
                "terralith:alpha_islands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("pidgeot", 15.0),
                        PokemonEntry("spearow", 20.0),
                        PokemonEntry("fearow", 15.0),
                        PokemonEntry("rayquaza", 5.0)
                    )
                ),

                // 阿尔法群岛冬季 - 飞行系和冰系精灵
                "terralith:alpha_islands_winter" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("pidgey", 20.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("articuno", 20.0),
                        PokemonEntry("delibird", 25.0),
                        PokemonEntry("glaceon", 15.0)
                    )
                ),

                // 海市蜃楼岛 - 超能力系和飞行系精灵
                "terralith:mirage_isles" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 15.0),
                        PokemonEntry("pidgey", 20.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("mewtwo", 5.0)
                    )
                ),

                // 白色台地 - 岩石系和普通系精灵
                "terralith:white_mesa" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("steelix", 10.0)
                    )
                ),

                // 扭曲台地 - 超能力系和岩石系精灵
                "terralith:warped_mesa" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 10.0)
                    )
                ),

                // ========== 下界生物群系 ==========

                // 下界荒地 - 火系和恶系精灵
                "minecraft:nether_wastes" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 20.0),
                        PokemonEntry("houndour", 25.0),
                        PokemonEntry("houndoom", 20.0),
                        PokemonEntry("moltres", 10.0)
                    )
                ),

                // 绯红森林 - 火系和草系精灵
                "minecraft:crimson_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 20.0),
                        PokemonEntry("magmortar", 15.0),
                        PokemonEntry("shroomish", 25.0),
                        PokemonEntry("breloom", 20.0),
                        PokemonEntry("foongus", 20.0)
                    )
                ),

                // 诡异森林 - 超能力系和毒系精灵
                "minecraft:warped_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 15.0),
                        PokemonEntry("grimer", 25.0),
                        PokemonEntry("muk", 15.0)
                    )
                ),

                // 灵魂沙峡谷 - 幽灵系和恶系精灵
                "minecraft:soul_sand_valley" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("gastly", 30.0),
                        PokemonEntry("haunter", 25.0),
                        PokemonEntry("gengar", 20.0),
                        PokemonEntry("sableye", 20.0),
                        PokemonEntry("spiritomb", 5.0)
                    )
                ),

                // 玄武岩三角洲 - 火系和岩石系精灵
                "minecraft:basalt_deltas" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 20.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("heatran", 10.0)
                    )
                ),

                // ========== 末地生物群系 ==========

                // 末地 - 超能力系和龙系精灵
                "minecraft:the_end" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.15,
                    pokemonList = listOf(
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 15.0),
                        PokemonEntry("dratini", 20.0),
                        PokemonEntry("dragonair", 15.0),
                        PokemonEntry("dragonite", 5.0)
                    )
                ),

                // 末地高地 - 超能力系和龙系精灵
                "minecraft:end_highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("abra", 20.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 15.0),
                        PokemonEntry("dratini", 25.0),
                        PokemonEntry("dragonair", 15.0),
                        PokemonEntry("rayquaza", 5.0)
                    )
                ),

                // 末地中地 - 超能力系精灵
                "minecraft:end_midlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("abra", 30.0),
                        PokemonEntry("kadabra", 25.0),
                        PokemonEntry("alakazam", 20.0),
                        PokemonEntry("espeon", 20.0),
                        PokemonEntry("mewtwo", 5.0)
                    )
                ),

                // 末地小岛 - 超能力系和飞行系精灵
                "minecraft:small_end_islands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.2,
                    pokemonList = listOf(
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 20.0),
                        PokemonEntry("lugia", 10.0)
                    )
                ),

                // 末地荒地 - 超能力系精灵
                "minecraft:end_barrens" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.15,
                    pokemonList = listOf(
                        PokemonEntry("abra", 35.0),
                        PokemonEntry("kadabra", 30.0),
                        PokemonEntry("alakazam", 25.0),
                        PokemonEntry("deoxys", 10.0)
                    )
                ),

                // ========== 更多海洋生物群系 ==========

                // 冷海 - 水系和冰系精灵
                "minecraft:cold_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 25.0),
                        PokemonEntry("tentacool", 25.0),
                        PokemonEntry("seel", 25.0),
                        PokemonEntry("dewgong", 20.0),
                        PokemonEntry("articuno", 5.0)
                    )
                ),

                // 深冷海 - 水系和冰系精灵
                "minecraft:deep_cold_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 20.0),
                        PokemonEntry("tentacool", 25.0),
                        PokemonEntry("tentacruel", 20.0),
                        PokemonEntry("seel", 25.0),
                        PokemonEntry("dewgong", 10.0)
                    )
                ),

                // 冰海 - 冰系和水系精灵
                "minecraft:frozen_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("seel", 35.0),
                        PokemonEntry("dewgong", 30.0),
                        PokemonEntry("magikarp", 20.0),
                        PokemonEntry("articuno", 15.0)
                    )
                ),

                // 深冰海 - 冰系和水系精灵
                "minecraft:deep_frozen_ocean" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 25.0),
                        PokemonEntry("tentacool", 25.0),
                        PokemonEntry("tentacruel", 15.0),
                        PokemonEntry("regice", 5.0)
                    )
                ),

                // ========== 旧版本兼容生物群系 ==========

                // 虚空 - 超能力系和幽灵系精灵
                "minecraft:void" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.1,
                    pokemonList = listOf(
                        PokemonEntry("abra", 25.0),
                        PokemonEntry("kadabra", 20.0),
                        PokemonEntry("alakazam", 15.0),
                        PokemonEntry("gastly", 25.0),
                        PokemonEntry("haunter", 15.0)
                    )
                ),

                // 山地 - 岩石系和地面系精灵
                "minecraft:mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("golem", 15.0),
                        PokemonEntry("machop", 20.0),
                        PokemonEntry("machoke", 5.0)
                    )
                ),

                // 山地边缘 - 岩石系和普通系精灵
                "minecraft:mountain_edge" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 30.0),
                        PokemonEntry("graveler", 25.0),
                        PokemonEntry("pidgey", 25.0),
                        PokemonEntry("pidgeotto", 15.0),
                        PokemonEntry("eevee", 5.0)
                    )
                ),

                // 繁茂山地 - 岩石系和草系精灵
                "minecraft:wooded_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 砂砾山地 - 岩石系和地面系精灵
                "minecraft:gravelly_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("golem", 20.0),
                        PokemonEntry("onix", 15.0)
                    )
                ),

                // 丛林变种 - 草系和虫系精灵
                "minecraft:modified_jungle" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.45,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("venusaur", 10.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 25.0),
                        PokemonEntry("scyther", 5.0)
                    )
                ),

                // 丛林边缘变种 - 草系和虫系精灵
                "minecraft:modified_jungle_edge" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 25.0),
                        PokemonEntry("scyther", 5.0)
                    )
                ),

                // 高桦木森林 - 草系精灵
                "minecraft:tall_birch_forest" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 高桦木丘陵 - 草系精灵
                "minecraft:tall_birch_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 20.0),
                        PokemonEntry("leafeon", 10.0)
                    )
                ),

                // 黑森林丘陵 - 草系和恶系精灵
                "minecraft:dark_forest_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 20.0),
                        PokemonEntry("ivysaur", 15.0),
                        PokemonEntry("poochyena", 25.0),
                        PokemonEntry("mightyena", 20.0),
                        PokemonEntry("umbreon", 20.0)
                    )
                ),

                // 积雪苔原 - 冰系精灵
                "minecraft:snowy_tundra" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("seel", 30.0),
                        PokemonEntry("dewgong", 25.0),
                        PokemonEntry("sneasel", 25.0),
                        PokemonEntry("weavile", 15.0),
                        PokemonEntry("glaceon", 5.0)
                    )
                ),

                // 积雪山地 - 冰系和岩石系精灵
                "minecraft:snowy_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 20.0),
                        PokemonEntry("geodude", 25.0),
                        PokemonEntry("graveler", 20.0),
                        PokemonEntry("articuno", 10.0)
                    )
                ),

                // 巨型针叶林 - 草系精灵
                "minecraft:giant_tree_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 30.0),
                        PokemonEntry("grotle", 25.0),
                        PokemonEntry("torterra", 20.0),
                        PokemonEntry("seedot", 20.0),
                        PokemonEntry("nuzleaf", 5.0)
                    )
                ),

                // 巨型针叶林丘陵 - 草系精灵
                "minecraft:giant_tree_taiga_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 30.0),
                        PokemonEntry("grotle", 25.0),
                        PokemonEntry("torterra", 20.0),
                        PokemonEntry("seedot", 20.0),
                        PokemonEntry("nuzleaf", 5.0)
                    )
                ),

                // 巨型云杉针叶林 - 草系精灵
                "minecraft:giant_spruce_taiga" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 30.0),
                        PokemonEntry("grotle", 25.0),
                        PokemonEntry("torterra", 20.0),
                        PokemonEntry("seedot", 20.0),
                        PokemonEntry("shiftry", 5.0)
                    )
                ),

                // 巨型云杉针叶林丘陵 - 草系精灵
                "minecraft:giant_spruce_taiga_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 30.0),
                        PokemonEntry("grotle", 25.0),
                        PokemonEntry("torterra", 20.0),
                        PokemonEntry("seedot", 20.0),
                        PokemonEntry("shiftry", 5.0)
                    )
                ),

                // 沙漠丘陵 - 地面系精灵
                "minecraft:desert_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 35.0),
                        PokemonEntry("sandslash", 30.0),
                        PokemonEntry("diglett", 25.0),
                        PokemonEntry("dugtrio", 10.0)
                    )
                ),

                // 繁茂丘陵 - 草系精灵
                "minecraft:wooded_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 30.0),
                        PokemonEntry("ivysaur", 25.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 20.0)
                    )
                ),

                // 针叶林丘陵 - 草系精灵
                "minecraft:taiga_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("turtwig", 30.0),
                        PokemonEntry("grotle", 25.0),
                        PokemonEntry("seedot", 25.0),
                        PokemonEntry("nuzleaf", 20.0)
                    )
                ),

                // 丛林丘陵 - 草系和虫系精灵
                "minecraft:jungle_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 25.0),
                        PokemonEntry("ivysaur", 20.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 25.0),
                        PokemonEntry("scyther", 5.0)
                    )
                ),

                // 丛林边缘 - 草系和虫系精灵
                "minecraft:jungle_edge" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 30.0),
                        PokemonEntry("ivysaur", 25.0),
                        PokemonEntry("caterpie", 25.0),
                        PokemonEntry("weedle", 20.0)
                    )
                ),

                // 桦木森林丘陵 - 草系精灵
                "minecraft:birch_forest_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("bulbasaur", 30.0),
                        PokemonEntry("ivysaur", 25.0),
                        PokemonEntry("oddish", 25.0),
                        PokemonEntry("bellsprout", 20.0)
                    )
                ),

                // 积雪针叶林丘陵 - 冰系和草系精灵
                "minecraft:snowy_taiga_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 25.0),
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 20.0)
                    )
                ),

                // 积雪针叶林山地 - 冰系和草系精灵
                "minecraft:snowy_taiga_mountains" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 25.0),
                        PokemonEntry("swinub", 25.0),
                        PokemonEntry("piloswine", 20.0)
                    )
                ),

                // 沼泽丘陵 - 水系和毒系精灵
                "minecraft:swamp_hills" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("poliwag", 30.0),
                        PokemonEntry("poliwhirl", 25.0),
                        PokemonEntry("grimer", 25.0),
                        PokemonEntry("muk", 20.0)
                    )
                ),

                // ========== 缺少的Terralith生物群系 ==========

                // 海滩 - 水系和地面系精灵
                "terralith:beach" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.35,
                    pokemonList = listOf(
                        PokemonEntry("krabby", 30.0),
                        PokemonEntry("kingler", 20.0),
                        PokemonEntry("sandshrew", 25.0),
                        PokemonEntry("sandslash", 15.0),
                        PokemonEntry("staryu", 10.0)
                    )
                ),

                // 河流 - 水系精灵
                "terralith:river" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.4,
                    pokemonList = listOf(
                        PokemonEntry("magikarp", 40.0),
                        PokemonEntry("goldeen", 30.0),
                        PokemonEntry("seaking", 15.0),
                        PokemonEntry("psyduck", 10.0),
                        PokemonEntry("golduck", 5.0)
                    )
                ),

                // 干旱高地 - 地面系和火系精灵
                "terralith:arid_highlands" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("sandshrew", 30.0),
                        PokemonEntry("sandslash", 25.0),
                        PokemonEntry("growlithe", 25.0),
                        PokemonEntry("arcanine", 15.0),
                        PokemonEntry("entei", 5.0)
                    )
                ),

                // 高山林地 - 冰系和草系精灵
                "terralith:alpine_grove" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("snover", 30.0),
                        PokemonEntry("abomasnow", 25.0),
                        PokemonEntry("turtwig", 25.0),
                        PokemonEntry("grotle", 15.0),
                        PokemonEntry("glaceon", 5.0)
                    )
                ),

                // 安山岩洞穴 - 岩石系精灵
                "terralith:andesite_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("geodude", 35.0),
                        PokemonEntry("graveler", 30.0),
                        PokemonEntry("onix", 20.0),
                        PokemonEntry("carbink", 15.0)
                    )
                ),

                // 热力洞穴 - 火系和岩石系精灵
                "terralith:thermal_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 15.0),
                        PokemonEntry("slugma", 30.0),
                        PokemonEntry("magcargo", 20.0),
                        PokemonEntry("heatran", 10.0)
                    )
                ),

                // 霜火洞穴 - 冰系和火系精灵
                "terralith:frostfire_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.25,
                    pokemonList = listOf(
                        PokemonEntry("seel", 25.0),
                        PokemonEntry("dewgong", 20.0),
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("slugma", 20.0),
                        PokemonEntry("glaceon", 10.0)
                    )
                ),

                // 地幔洞穴（斜杠格式） - 火系和岩石系精灵
                "terralith:cave/mantle_caves" to BiomeConfig(
                    enabled = true,
                    spawnChance = 0.3,
                    pokemonList = listOf(
                        PokemonEntry("magmar", 25.0),
                        PokemonEntry("magmortar", 15.0),
                        PokemonEntry("slugma", 30.0),
                        PokemonEntry("magcargo", 20.0),
                        PokemonEntry("heatran", 10.0)
                    )
                )
            )
        }
    }

    /**
     * 保存到YAML配置
     */
    fun saveToYaml(yaml: YamlConfiguration) {
        // 基础设置
        yaml.set("general.enabled", enabled)
        yaml.set("general.spawn_interval", spawnInterval)
        yaml.set("general.initial_delay", initialDelay)
        yaml.set("general.target_worlds", targetWorlds)

        // 玩家设置
        yaml.set("players.players_per_spawn", playersPerSpawn)
        yaml.set("players.detection_range", detectionRange)
        yaml.set("players.min_online_players", minOnlinePlayers)

        // 生成设置
        yaml.set("spawning.spawn_count_min", spawnCountMin)
        yaml.set("spawning.spawn_count_max", spawnCountMax)
        yaml.set("spawning.level_min", levelMin)
        yaml.set("spawning.level_max", levelMax)
        yaml.set("spawning.allow_shiny", allowShiny)
        yaml.set("spawning.shiny_chance", shinyChance)

        // 公告设置
        yaml.set("announcement.enabled", enableAnnouncement)
        yaml.set("announcement.prefix", announcementPrefix)
        yaml.set("announcement.show_coordinates", showCoordinates)
        yaml.set("announcement.show_biome_name", showBiomeName)
        yaml.set("announcement.show_pokemon_count", showPokemonCount)

        // 调试设置
        yaml.set("debug.verbose_logging", verboseLogging)
        yaml.set("debug.show_spawn_process", showSpawnProcess)
        yaml.set("debug.show_reflection_details", showReflectionDetails)

        // 生物群系配置
        for ((biomeName, biomeConfig) in biomeConfigs) {
            biomeConfig.saveToYaml(yaml, "biomes.$biomeName")
        }

        // 添加注释
        addComments(yaml)
    }

    /**
     * 添加配置注释
     */
    private fun addComments(yaml: YamlConfiguration) {
        yaml.setComments("general", listOf("基础设置"))
        yaml.setComments("general.enabled", listOf("是否启用插件"))
        yaml.setComments("general.spawn_interval", listOf("生成间隔（秒）"))
        yaml.setComments("general.initial_delay", listOf("初始延迟（秒）"))
        yaml.setComments("general.target_worlds", listOf("目标世界列表"))

        yaml.setComments("players", listOf("玩家相关设置"))
        yaml.setComments("players.players_per_spawn", listOf("每次随机选择的玩家数量"))
        yaml.setComments("players.detection_range", listOf("玩家附近的检测范围（方块）"))
        yaml.setComments("players.min_online_players", listOf("最小在线玩家数量"))

        yaml.setComments("spawning", listOf("精灵生成设置"))
        yaml.setComments("spawning.spawn_count_min", listOf("每次生成的精灵最小数量"))
        yaml.setComments("spawning.spawn_count_max", listOf("每次生成的精灵最大数量"))
        yaml.setComments("spawning.level_min", listOf("精灵最小等级"))
        yaml.setComments("spawning.level_max", listOf("精灵最大等级"))
        yaml.setComments("spawning.allow_shiny", listOf("是否允许闪光精灵"))
        yaml.setComments("spawning.shiny_chance", listOf("闪光精灵概率（0.0-1.0）"))

        yaml.setComments("announcement", listOf("公告设置"))
        yaml.setComments("announcement.enabled", listOf("是否发送公告"))
        yaml.setComments("announcement.prefix", listOf("公告前缀"))
        yaml.setComments("announcement.show_coordinates", listOf("是否显示坐标"))
        yaml.setComments("announcement.show_biome_name", listOf("是否显示生物群系名称"))
        yaml.setComments("announcement.show_pokemon_count", listOf("是否显示精灵数量"))

        yaml.setComments("debug", listOf("调试设置"))
        yaml.setComments("debug.verbose_logging", listOf("是否显示详细的API调用日志"))
        yaml.setComments("debug.show_spawn_process", listOf("是否显示精灵生成过程日志"))
        yaml.setComments("debug.show_reflection_details", listOf("是否显示反射调用详情"))

        yaml.setComments("biomes", listOf("生物群系精灵配置"))

        // 添加生物群系注释
        addBiomeComments(yaml)
    }

    /**
     * 添加生物群系配置注释
     */
    private fun addBiomeComments(yaml: YamlConfiguration) {
        // 原版Minecraft生物群系注释
        yaml.setComments("biomes.minecraft:plains", listOf("平原 - 草系和普通系精灵"))
        yaml.setComments("biomes.minecraft:sunflower_plains", listOf("向日葵平原 - 草系精灵"))
        yaml.setComments("biomes.minecraft:forest", listOf("森林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:birch_forest", listOf("桦木森林 - 草系和普通系精灵"))
        yaml.setComments("biomes.minecraft:dark_forest", listOf("黑森林 - 恶系和幽灵系精灵"))
        yaml.setComments("biomes.minecraft:flower_forest", listOf("繁花森林 - 草系和妖精系精灵"))
        yaml.setComments("biomes.minecraft:taiga", listOf("针叶林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:snowy_taiga", listOf("积雪针叶林 - 冰系和草系精灵"))
        yaml.setComments("biomes.minecraft:desert", listOf("沙漠 - 地面系精灵"))
        yaml.setComments("biomes.minecraft:savanna", listOf("热带草原 - 火系和地面系精灵"))
        yaml.setComments("biomes.minecraft:jungle", listOf("丛林 - 草系和虫系精灵"))
        yaml.setComments("biomes.minecraft:swamp", listOf("沼泽 - 水系和毒系精灵"))
        yaml.setComments("biomes.minecraft:mangrove_swamp", listOf("红树林沼泽 - 水系和草系精灵"))
        yaml.setComments("biomes.minecraft:windswept_hills", listOf("山地 - 岩石系和地面系精灵"))
        yaml.setComments("biomes.minecraft:snowy_plains", listOf("积雪平原 - 冰系精灵"))
        yaml.setComments("biomes.minecraft:ocean", listOf("海洋 - 水系精灵"))
        yaml.setComments("biomes.minecraft:ice_spikes", listOf("冰刺平原 - 冰系精灵"))
        yaml.setComments("biomes.minecraft:mushroom_fields", listOf("蘑菇岛 - 妖精系和草系精灵"))
        yaml.setComments("biomes.minecraft:dripstone_caves", listOf("溶洞 - 岩石系精灵"))
        yaml.setComments("biomes.minecraft:lush_caves", listOf("繁茂洞穴 - 草系精灵"))
        yaml.setComments("biomes.minecraft:deep_dark", listOf("深暗之域 - 恶系和幽灵系精灵"))
        yaml.setComments("biomes.minecraft:river", listOf("河流 - 水系精灵"))
        yaml.setComments("biomes.minecraft:beach", listOf("海滩 - 水系和地面系精灵"))
        yaml.setComments("biomes.minecraft:frozen_river", listOf("冰冻河流 - 冰系和水系精灵"))
        yaml.setComments("biomes.minecraft:snowy_beach", listOf("积雪海滩 - 冰系精灵"))
        yaml.setComments("biomes.minecraft:stony_shore", listOf("石岸 - 岩石系和水系精灵"))
        yaml.setComments("biomes.minecraft:old_growth_birch_forest", listOf("原始桦木森林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:old_growth_pine_taiga", listOf("原始松木针叶林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:old_growth_spruce_taiga", listOf("原始云杉针叶林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:savanna_plateau", listOf("热带草原高原 - 火系和地面系精灵"))
        yaml.setComments("biomes.minecraft:windswept_savanna", listOf("风袭热带草原 - 火系和飞行系精灵"))
        yaml.setComments("biomes.minecraft:badlands", listOf("恶地 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.minecraft:eroded_badlands", listOf("风蚀恶地 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.minecraft:wooded_badlands", listOf("繁茂恶地 - 地面系和草系精灵"))
        yaml.setComments("biomes.minecraft:windswept_forest", listOf("风袭森林 - 草系和飞行系精灵"))
        yaml.setComments("biomes.minecraft:windswept_gravelly_hills", listOf("风袭砂砾丘陵 - 岩石系和飞行系精灵"))
        yaml.setComments("biomes.minecraft:meadow", listOf("草甸 - 草系和普通系精灵"))
        yaml.setComments("biomes.minecraft:grove", listOf("雪林 - 冰系和草系精灵"))
        yaml.setComments("biomes.minecraft:snowy_slopes", listOf("积雪山坡 - 冰系精灵"))
        yaml.setComments("biomes.minecraft:frozen_peaks", listOf("冰冻山峰 - 冰系精灵"))
        yaml.setComments("biomes.minecraft:jagged_peaks", listOf("尖峭山峰 - 岩石系和冰系精灵"))
        yaml.setComments("biomes.minecraft:stony_peaks", listOf("裸岩山峰 - 岩石系精灵"))
        yaml.setComments("biomes.minecraft:sparse_jungle", listOf("稀疏丛林 - 草系和虫系精灵"))
        yaml.setComments("biomes.minecraft:bamboo_jungle", listOf("竹林 - 草系和普通系精灵"))
        yaml.setComments("biomes.minecraft:deep_ocean", listOf("深海 - 水系精灵"))
        yaml.setComments("biomes.minecraft:lukewarm_ocean", listOf("温海 - 水系精灵"))
        yaml.setComments("biomes.minecraft:deep_lukewarm_ocean", listOf("深温海 - 水系精灵"))
        yaml.setComments("biomes.minecraft:warm_ocean", listOf("暖海 - 水系精灵"))

        // Terralith生物群系注释
        yaml.setComments("biomes.terralith:alpine_grove", listOf("高山林地 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:sakura_grove", listOf("樱花林地 - 草系和妖精系精灵"))
        yaml.setComments("biomes.terralith:moonlight_grove", listOf("月光林地 - 超能力系和妖精系精灵"))
        yaml.setComments("biomes.terralith:ancient_sands", listOf("远古沙地 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.terralith:volcanic_crater", listOf("火山口 - 火系精灵"))
        yaml.setComments("biomes.terralith:crystal_caves", listOf("水晶洞穴 - 岩石系和钢系精灵"))
        yaml.setComments("biomes.terralith:tropical_jungle", listOf("热带丛林 - 草系和虫系精灵"))
        yaml.setComments("biomes.terralith:skylands", listOf("天空岛 - 飞行系精灵"))
        yaml.setComments("biomes.terralith:arid_highlands", listOf("干旱高地 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.terralith:temperate_highlands", listOf("温带高地 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:moonlight_valley", listOf("月光峡谷 - 超能力系和妖精系精灵"))
        yaml.setComments("biomes.terralith:sakura_valley", listOf("樱花峡谷 - 草系和妖精系精灵"))
        yaml.setComments("biomes.terralith:lavender_forest", listOf("薰衣草森林 - 草系和妖精系精灵"))
        yaml.setComments("biomes.terralith:cloud_forest", listOf("云雾森林 - 飞行系和草系精灵"))
        yaml.setComments("biomes.terralith:granite_caves", listOf("花岗岩洞穴 - 岩石系和钢系精灵"))
        yaml.setComments("biomes.terralith:andesite_caves", listOf("安山岩洞穴 - 岩石系精灵"))
        yaml.setComments("biomes.terralith:thermal_caves", listOf("热力洞穴 - 火系精灵"))
        yaml.setComments("biomes.terralith:frostfire_caves", listOf("霜火洞穴 - 冰系和火系精灵"))
        yaml.setComments("biomes.terralith:desert_spires", listOf("沙漠尖塔 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.terralith:lush_desert", listOf("繁茂沙漠 - 草系和地面系精灵"))
        yaml.setComments("biomes.terralith:desert_oasis", listOf("沙漠绿洲 - 水系和草系精灵"))
        yaml.setComments("biomes.terralith:skylands_spring", listOf("天空岛春林 - 飞行系和草系精灵"))
        yaml.setComments("biomes.terralith:skylands_summer", listOf("天空岛夏林 - 飞行系和火系精灵"))
        yaml.setComments("biomes.terralith:skylands_autumn", listOf("天空岛秋林 - 飞行系精灵"))
        yaml.setComments("biomes.terralith:skylands_winter", listOf("天空岛冬林 - 飞行系和冰系精灵"))
        yaml.setComments("biomes.terralith:orchid_swamp", listOf("兰花沼泽 - 水系和草系精灵"))
        yaml.setComments("biomes.terralith:yellowstone", listOf("黄石公园 - 火系和地面系精灵"))
        yaml.setComments("biomes.terralith:siberian_taiga", listOf("西伯利亚针叶林 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:forested_highlands", listOf("森林高地 - 草系精灵"))
        yaml.setComments("biomes.terralith:blooming_plateau", listOf("花开高原 - 草系和妖精系精灵"))
        yaml.setComments("biomes.terralith:blooming_valley", listOf("花开峡谷 - 草系和妖精系精灵"))
        yaml.setComments("biomes.terralith:lavender_valley", listOf("薰衣草峡谷 - 草系和妖精系精灵"))
        yaml.setComments("biomes.terralith:lush_valley", listOf("繁茂峡谷 - 草系精灵"))
        yaml.setComments("biomes.terralith:brushland", listOf("灌木地 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:cold_shrubland", listOf("寒冷灌木地 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:hot_shrubland", listOf("炎热灌木地 - 火系和地面系精灵"))
        yaml.setComments("biomes.terralith:rocky_shrubland", listOf("岩石灌木地 - 岩石系和地面系精灵"))

        // 高山类生物群系注释
        yaml.setComments("biomes.terralith:alpine_highlands", listOf("高山高地 - 冰系和岩石系精灵"))
        yaml.setComments("biomes.terralith:frozen_cliffs", listOf("冰冻悬崖 - 冰系精灵"))
        yaml.setComments("biomes.terralith:glacial_chasm", listOf("冰川峡谷 - 冰系精灵"))
        yaml.setComments("biomes.terralith:rocky_mountains", listOf("岩石山脉 - 岩石系和地面系精灵"))
        yaml.setComments("biomes.terralith:snowy_badlands", listOf("雪地恶地 - 冰系和地面系精灵"))
        yaml.setComments("biomes.terralith:snowy_maple_forest", listOf("雪地枫林 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:wintry_forest", listOf("寒冬森林 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:wintry_lowlands", listOf("寒冬低地 - 冰系精灵"))
        yaml.setComments("biomes.terralith:bryce_canyon", listOf("布莱斯峡谷 - 岩石系和地面系精灵"))
        yaml.setComments("biomes.terralith:snowy_cherry_grove", listOf("雪地樱花林 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:snowy_shield", listOf("雪地护盾 - 冰系精灵"))
        yaml.setComments("biomes.terralith:emerald_peaks", listOf("翡翠峰 - 岩石系和草系精灵"))
        yaml.setComments("biomes.terralith:volcanic_peaks", listOf("火山峰 - 火系和岩石系精灵"))
        yaml.setComments("biomes.terralith:scarlet_mountains", listOf("猩红山脉 - 火系和岩石系精灵"))
        yaml.setComments("biomes.terralith:painted_mountains", listOf("彩绘山脉 - 岩石系精灵"))
        yaml.setComments("biomes.terralith:haze_mountain", listOf("雾霾山 - 毒系和岩石系精灵"))
        yaml.setComments("biomes.terralith:white_cliffs", listOf("白色悬崖 - 岩石系和飞行系精灵"))
        yaml.setComments("biomes.terralith:granite_cliffs", listOf("花岗岩悬崖 - 岩石系精灵"))
        yaml.setComments("biomes.terralith:windswept_spires", listOf("风蚀尖塔 - 岩石系和飞行系精灵"))
        yaml.setComments("biomes.terralith:stony_spires", listOf("石质尖塔 - 岩石系精灵"))

        // 森林类生物群系注释
        yaml.setComments("biomes.terralith:birch_taiga", listOf("桦木针叶林 - 草系精灵"))
        yaml.setComments("biomes.terralith:siberian_grove", listOf("西伯利亚林地 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:shield", listOf("护盾林 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:shield_clearing", listOf("护盾林空地 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:jungle_mountains", listOf("丛林山脉 - 草系和虫系精灵"))
        yaml.setComments("biomes.terralith:rocky_jungle", listOf("岩石丛林 - 草系和岩石系精灵"))
        yaml.setComments("biomes.terralith:amethyst_rainforest", listOf("紫水晶雨林 - 草系和超能力系精灵"))

        // 洞穴类生物群系注释
        yaml.setComments("biomes.terralith:diorite_caves", listOf("闪长岩洞穴 - 岩石系精灵"))
        yaml.setComments("biomes.terralith:tuff_caves", listOf("凝灰岩洞穴 - 岩石系精灵"))
        yaml.setComments("biomes.terralith:deep_caves", listOf("深层洞穴 - 岩石系和恶系精灵"))
        yaml.setComments("biomes.terralith:mantle_caves", listOf("地幔洞穴 - 火系和岩石系精灵"))
        yaml.setComments("biomes.terralith:infested_caves", listOf("虫蛀洞穴 - 虫系和毒系精灵"))
        yaml.setComments("biomes.terralith:fungal_caves", listOf("真菌洞穴 - 草系和毒系精灵"))
        yaml.setComments("biomes.terralith:underground_jungle", listOf("地下丛林 - 草系和虫系精灵"))

        // 峡谷和山谷类生物群系注释
        yaml.setComments("biomes.terralith:valley_clearing", listOf("峡谷空地 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:sandstone_valley", listOf("砂岩峡谷 - 岩石系和地面系精灵"))
        yaml.setComments("biomes.terralith:amethyst_canyon", listOf("紫水晶峡谷 - 岩石系和超能力系精灵"))
        yaml.setComments("biomes.terralith:desert_canyon", listOf("沙漠峡谷 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.terralith:yosemite_cliffs", listOf("约塞米蒂悬崖 - 岩石系和飞行系精灵"))
        yaml.setComments("biomes.terralith:yosemite_lowlands", listOf("约塞米蒂低地 - 草系和普通系精灵"))

        // 平原和草原类生物群系注释
        yaml.setComments("biomes.terralith:shrubland", listOf("灌木地 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:highlands", listOf("高地 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:steppe", listOf("草原 - 草系和普通系精灵"))
        yaml.setComments("biomes.terralith:ashen_savanna", listOf("灰烬热带草原 - 火系和地面系精灵"))
        yaml.setComments("biomes.terralith:savanna_badlands", listOf("热带草原恶地 - 火系和地面系精灵"))
        yaml.setComments("biomes.terralith:savanna_slopes", listOf("热带草原坡地 - 火系和地面系精灵"))
        yaml.setComments("biomes.terralith:fractured_savanna", listOf("破碎热带草原 - 火系和岩石系精灵"))

        // 沙漠和火山类生物群系注释
        yaml.setComments("biomes.terralith:gravel_desert", listOf("砾石沙漠 - 地面系和岩石系精灵"))
        yaml.setComments("biomes.terralith:red_oasis", listOf("红色绿洲 - 水系和草系精灵"))
        yaml.setComments("biomes.terralith:caldera", listOf("火山口 - 火系精灵"))
        yaml.setComments("biomes.terralith:basalt_cliffs", listOf("玄武岩悬崖 - 岩石系和火系精灵"))

        // 海滩和水域类生物群系注释
        yaml.setComments("biomes.terralith:gravel_beach", listOf("砾石海滩 - 岩石系和水系精灵"))
        yaml.setComments("biomes.terralith:warm_river", listOf("温暖河流 - 水系精灵"))
        yaml.setComments("biomes.terralith:deep_warm_ocean", listOf("深层温暖海洋 - 水系精灵"))
        yaml.setComments("biomes.terralith:ice_marsh", listOf("冰沼泽 - 冰系和水系精灵"))

        // 天空岛和特殊生物群系注释
        yaml.setComments("biomes.terralith:alpha_islands", listOf("阿尔法群岛 - 飞行系和普通系精灵"))
        yaml.setComments("biomes.terralith:alpha_islands_winter", listOf("阿尔法群岛冬季 - 飞行系和冰系精灵"))
        yaml.setComments("biomes.terralith:mirage_isles", listOf("海市蜃楼岛 - 超能力系和飞行系精灵"))
        yaml.setComments("biomes.terralith:white_mesa", listOf("白色台地 - 岩石系和普通系精灵"))
        yaml.setComments("biomes.terralith:warped_mesa", listOf("扭曲台地 - 超能力系和岩石系精灵"))

        // 下界生物群系注释
        yaml.setComments("biomes.minecraft:nether_wastes", listOf("下界荒地 - 火系和恶系精灵"))
        yaml.setComments("biomes.minecraft:crimson_forest", listOf("绯红森林 - 火系和草系精灵"))
        yaml.setComments("biomes.minecraft:warped_forest", listOf("诡异森林 - 超能力系和毒系精灵"))
        yaml.setComments("biomes.minecraft:soul_sand_valley", listOf("灵魂沙峡谷 - 幽灵系和恶系精灵"))
        yaml.setComments("biomes.minecraft:basalt_deltas", listOf("玄武岩三角洲 - 火系和岩石系精灵"))

        // 末地生物群系注释
        yaml.setComments("biomes.minecraft:the_end", listOf("末地 - 超能力系和龙系精灵"))
        yaml.setComments("biomes.minecraft:end_highlands", listOf("末地高地 - 超能力系和龙系精灵"))
        yaml.setComments("biomes.minecraft:end_midlands", listOf("末地中地 - 超能力系精灵"))
        yaml.setComments("biomes.minecraft:small_end_islands", listOf("末地小岛 - 超能力系和飞行系精灵"))
        yaml.setComments("biomes.minecraft:end_barrens", listOf("末地荒地 - 超能力系精灵"))

        // 更多海洋生物群系注释
        yaml.setComments("biomes.minecraft:cold_ocean", listOf("冷海 - 水系和冰系精灵"))
        yaml.setComments("biomes.minecraft:deep_cold_ocean", listOf("深冷海 - 水系和冰系精灵"))
        yaml.setComments("biomes.minecraft:frozen_ocean", listOf("冰海 - 冰系和水系精灵"))
        yaml.setComments("biomes.minecraft:deep_frozen_ocean", listOf("深冰海 - 冰系和水系精灵"))

        // 旧版本兼容生物群系注释
        yaml.setComments("biomes.minecraft:void", listOf("虚空 - 超能力系和幽灵系精灵"))
        yaml.setComments("biomes.minecraft:mountains", listOf("山地 - 岩石系和地面系精灵"))
        yaml.setComments("biomes.minecraft:mountain_edge", listOf("山地边缘 - 岩石系和普通系精灵"))
        yaml.setComments("biomes.minecraft:wooded_mountains", listOf("繁茂山地 - 岩石系和草系精灵"))
        yaml.setComments("biomes.minecraft:gravelly_mountains", listOf("砂砾山地 - 岩石系和地面系精灵"))
        yaml.setComments("biomes.minecraft:modified_jungle", listOf("丛林变种 - 草系和虫系精灵"))
        yaml.setComments("biomes.minecraft:modified_jungle_edge", listOf("丛林边缘变种 - 草系和虫系精灵"))
        yaml.setComments("biomes.minecraft:tall_birch_forest", listOf("高桦木森林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:tall_birch_hills", listOf("高桦木丘陵 - 草系精灵"))
        yaml.setComments("biomes.minecraft:dark_forest_hills", listOf("黑森林丘陵 - 草系和恶系精灵"))
        yaml.setComments("biomes.minecraft:snowy_tundra", listOf("积雪苔原 - 冰系精灵"))
        yaml.setComments("biomes.minecraft:snowy_mountains", listOf("积雪山地 - 冰系和岩石系精灵"))
        yaml.setComments("biomes.minecraft:giant_tree_taiga", listOf("巨型针叶林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:giant_tree_taiga_hills", listOf("巨型针叶林丘陵 - 草系精灵"))
        yaml.setComments("biomes.minecraft:giant_spruce_taiga", listOf("巨型云杉针叶林 - 草系精灵"))
        yaml.setComments("biomes.minecraft:giant_spruce_taiga_hills", listOf("巨型云杉针叶林丘陵 - 草系精灵"))
        yaml.setComments("biomes.minecraft:desert_hills", listOf("沙漠丘陵 - 地面系精灵"))
        yaml.setComments("biomes.minecraft:wooded_hills", listOf("繁茂丘陵 - 草系精灵"))
        yaml.setComments("biomes.minecraft:taiga_hills", listOf("针叶林丘陵 - 草系精灵"))
        yaml.setComments("biomes.minecraft:jungle_hills", listOf("丛林丘陵 - 草系和虫系精灵"))
        yaml.setComments("biomes.minecraft:jungle_edge", listOf("丛林边缘 - 草系和虫系精灵"))
        yaml.setComments("biomes.minecraft:birch_forest_hills", listOf("桦木森林丘陵 - 草系精灵"))
        yaml.setComments("biomes.minecraft:snowy_taiga_hills", listOf("积雪针叶林丘陵 - 冰系和草系精灵"))
        yaml.setComments("biomes.minecraft:snowy_taiga_mountains", listOf("积雪针叶林山地 - 冰系和草系精灵"))
        yaml.setComments("biomes.minecraft:swamp_hills", listOf("沼泽丘陵 - 水系和毒系精灵"))

        // 缺少的Terralith生物群系注释
        yaml.setComments("biomes.terralith:beach", listOf("海滩 - 水系和地面系精灵"))
        yaml.setComments("biomes.terralith:river", listOf("河流 - 水系精灵"))

        yaml.setComments("biomes.terralith:arid_highlands", listOf("干旱高地 - 地面系和火系精灵"))
        yaml.setComments("biomes.terralith:alpine_grove", listOf("高山林地 - 冰系和草系精灵"))
        yaml.setComments("biomes.terralith:andesite_caves", listOf("安山岩洞穴 - 岩石系精灵"))
        yaml.setComments("biomes.terralith:thermal_caves", listOf("热力洞穴 - 火系和岩石系精灵"))
        yaml.setComments("biomes.terralith:frostfire_caves", listOf("霜火洞穴 - 冰系和火系精灵"))
        yaml.setComments("biomes.terralith:cave/mantle_caves", listOf("地幔洞穴（斜杠格式） - 火系和岩石系精灵"))
    }
}
