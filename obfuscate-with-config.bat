@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔐 AceDex 高级混淆工具 (配置文件) 🔐          ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🚀 使用 skidfuscator-config.json 配置文件进行混淆          ║
echo ║  🛡️  高强度混淆保护，保留重要接口                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查输入文件是否存在
if not exist "build\libs\AceDex-1.0.0.jar" (
    echo ❌ 错误: 找不到输入文件 build\libs\AceDx-1.0.0.jar
    echo 请先运行 gradlew build 构建项目
    pause
    exit /b 1
)

REM 检查 skidfuscator.jar 是否存在
if not exist "skidfuscator.jar" (
    echo ❌ 错误: 找不到 skidfuscator.jar
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "skidfuscator-config.json" (
    echo ❌ 错误: 找不到 skidfuscator-config.json 配置文件
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "obfuscated" mkdir "obfuscated"

echo 🔄 开始混淆处理...
echo 📋 使用配置文件: skidfuscator-config.json
echo 📁 输入文件: build\libs\AceDx-1.0.0.jar
echo 📁 输出目录: obfuscated\
echo.

REM 使用配置文件运行 Skidfuscator
java -Xmx8G -Xms4G -XX:+UseG1GC -jar skidfuscator.jar obfuscate ^
    "build\libs\AceDx-1.0.0.jar" ^
    --config "skidfuscator-config.json" ^
    --output "obfuscated\AceDx-1.0.0-obfuscated.jar" ^
    --phantom ^
    --debug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ✅ 高级混淆完成 ✅                        ║
    echo ╠══════════════════════════════════════════════════════════════╣
    echo ║  🎉 代码混淆成功完成！                                       ║
    echo ║  📁 混淆后的文件: obfuscated\AceDx-1.0.0-obfuscated.jar    ║
    echo ║  🔒 使用了高强度混淆算法保护                                 ║
    echo ║  🛡️  重要接口和方法已受保护                                 ║
    echo ║  ⚡ 性能优化和反调试保护已启用                               ║
    echo ╚══════════════════════════════════════════════════════════════╝
    
    echo.
    echo 📊 文件信息:
    echo    原始文件: build\libs\AceDx-1.0.0.jar
    for %%A in ("build\libs\AceDx-1.0.0.jar") do echo    大小: %%~zA 字节
    echo.
    echo    混淆文件: obfuscated\AceDx-1.0.0-obfuscated.jar
    for %%A in ("obfuscated\AceDx-1.0.0-obfuscated.jar") do echo    大小: %%~zA 字节
    
    echo.
    echo 🔧 混淆特性:
    echo    ✓ 控制流混淆 (强度: 3)
    echo    ✓ 字符串加密 (强度: 3)
    echo    ✓ 数字混淆 (强度: 3)
    echo    ✓ 引用混淆 (强度: 3)
    echo    ✓ 方法轮廓化 (强度: 3)
    echo    ✓ 类/方法/字段重命名
    echo    ✓ 条件混淆
    echo    ✓ 虚假跳转
    echo    ✓ 异常处理混淆
    
) else (
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ❌ 混淆失败 ❌                            ║
    echo ╠══════════════════════════════════════════════════════════════╣
    echo ║  💥 代码混淆过程中发生错误                                   ║
    echo ║  🔧 请检查上方的错误信息                                     ║
    echo ║  📋 确认配置文件格式正确                                     ║
    echo ║  📁 确认所有依赖库文件存在                                   ║
    echo ╚══════════════════════════════════════════════════════════════╝
)

echo.
pause
