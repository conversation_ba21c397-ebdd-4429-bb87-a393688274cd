/**
 * AceDex - 精灵图鉴插件
 * Cobblemon 物品辅助工具类
 *
 * 使用纯 Cobblemon API，避免混淆类名依赖问题
 */

package cn.acebrand.acedex.util

import com.cobblemon.mod.common.api.pokemon.PokemonSpecies
import com.cobblemon.mod.common.item.PokemonItem
import com.cobblemon.mod.common.pokemon.Pokemon
import com.cobblemon.mod.common.pokemon.Species
import org.bukkit.inventory.ItemStack

/**
 * Cobblemon 物品辅助工具类
 * 模仿 LGPokemonMenu 的 getPokemonItem 方法
 */
object CobblemonItemHelper {
    
    /**
     * 从 Pokemon 对象创建物品
     * 使用反射调用 PokemonItem.from() 避免直接依赖混淆类
     */
    fun getPokemonItem(pokemon: Pokemon): ItemStack? {
        return try {
            // 使用 Cobblemon 的 PokemonItem.from() 创建原生物品
            val nmsItem = PokemonItem.from(pokemon)

            // 转换为 Bukkit ItemStack
            convertNMSItemToBukkit(nmsItem)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 转换 NMS ItemStack 为 Bukkit ItemStack
     * 使用多种方法尝试转换，确保兼容性
     */
    fun convertNMSItemToBukkit(nmsItemStack: Any): ItemStack? {
        return try {
            // 方法1：尝试使用服务器版本的 CraftItemStack
            val serverVersion = getServerVersion()
            if (serverVersion != null) {
                val result = convertWithVersion(nmsItemStack, serverVersion)
                if (result != null) return result
            }

            // 方法2：尝试常见版本
            val commonVersions = listOf("v1_21_R1", "v1_20_R3", "v1_20_R2", "v1_20_R1")
            for (version in commonVersions) {
                val result = convertWithVersion(nmsItemStack, version)
                if (result != null) return result
            }

            // 方法3：尝试通过 Bukkit 的方法
            convertThroughBukkit(nmsItemStack)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取服务器版本
     */
    private fun getServerVersion(): String? {
        return try {
            val bukkitClass = Class.forName("org.bukkit.Bukkit")
            val serverClass = bukkitClass.getMethod("getServer").invoke(null)
            val versionMethod = serverClass.javaClass.getMethod("getClass")
            val serverClassName = versionMethod.invoke(serverClass).toString()

            // 从类名中提取版本，如 "class org.bukkit.craftbukkit.v1_21_R1.CraftServer"
            val versionPattern = Regex("v\\d+_\\d+_R\\d+")
            versionPattern.find(serverClassName)?.value
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 使用指定版本转换
     */
    private fun convertWithVersion(nmsItemStack: Any, version: String): ItemStack? {
        return try {
            val craftItemStackClass = Class.forName("org.bukkit.craftbukkit.$version.inventory.CraftItemStack")
            val asBukkitCopyMethod = craftItemStackClass.getMethod("asBukkitCopy",
                Class.forName("net.minecraft.world.item.ItemStack"))
            asBukkitCopyMethod.invoke(null, nmsItemStack) as ItemStack
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 通过 Bukkit 方法转换
     */
    private fun convertThroughBukkit(nmsItemStack: Any): ItemStack? {
        return try {
            // 尝试使用 Bukkit 的通用方法
            val itemStackClass = Class.forName("org.bukkit.inventory.ItemStack")
            val constructors = itemStackClass.constructors

            // 这里可以尝试其他转换方法
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据精灵名称获取 Species
     */
    fun getSpeciesByName(pokemonName: String): Species? {
        return try {
            val resourceIdentifier = com.cobblemon.mod.common.util.cobblemonResource(pokemonName.lowercase())
            PokemonSpecies.getByIdentifier(resourceIdentifier)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据图鉴编号获取 Species
     */
    fun getSpeciesByDexNumber(dexNumber: Int): Species? {
        return try {
            PokemonSpecies.getByPokedexNumber(dexNumber)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 创建精灵对象
     */
    fun createPokemon(species: Species, isShiny: Boolean = false, level: Int = 1): Pokemon {
        val pokemon = Pokemon()
        pokemon.species = species
        pokemon.shiny = isShiny
        pokemon.level = level
        pokemon.initialize()
        return pokemon
    }
    
    /**
     * 根据精灵名称直接创建精灵物品
     */
    fun createPokemonItemByName(pokemonName: String, level: Int = 1): ItemStack? {
        return try {
            // 步骤1：获取精灵种类
            val species = getSpeciesByName(pokemonName) ?: return null

            // 步骤2：创建精灵对象
            val pokemon = createPokemon(species, false, level)

            // 步骤3：创建物品
            getPokemonItem(pokemon)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据图鉴编号创建精灵物品
     */
    fun createPokemonItemByDexNumber(dexNumber: Int, level: Int = 1): ItemStack? {
        return try {
            val species = getSpeciesByDexNumber(dexNumber) ?: return null
            val pokemon = createPokemon(species, false, level)
            getPokemonItem(pokemon)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 检查精灵是否存在
     */
    fun isPokemonExists(pokemonName: String): Boolean {
        return getSpeciesByName(pokemonName) != null
    }
    
    /**
     * 获取精灵的显示名称
     */
    fun getPokemonDisplayName(species: Species): String {
        return try {
            species.translatedName.string
        } catch (e: Exception) {
            species.name
        }
    }
    
    /**
     * 获取精灵的类型信息
     */
    fun getPokemonTypes(species: Species): Pair<String, String?> {
        return try {
            val primaryType = species.primaryType.displayName.string
            val secondaryType = species.secondaryType?.displayName?.string
            Pair(primaryType, secondaryType)
        } catch (e: Exception) {
            Pair("未知", null)
        }
    }

    /**
     * 将 Cobblemon ItemStack 转换为 Bukkit ItemStack
     */
    fun convertToItemStack(cobblemonStack: net.minecraft.world.item.ItemStack): ItemStack? {
        return try {
            // 这里需要实现 Cobblemon ItemStack 到 Bukkit ItemStack 的转换
            // 由于这是一个复杂的转换过程，我们先返回 null
            // 实际实现需要根据具体的 Cobblemon 版本和 API 来完成
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 测试功能
     */
    fun testCobblemonIntegration(): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()
        val testPokemon = listOf("pikachu", "charizard", "blastoise", "venusaur")
        
        
        testPokemon.forEach { pokemonName ->
            try {
                val item = createPokemonItemByName(pokemonName, 1)
                val success = item != null
                results[pokemonName] = success
            } catch (e: Exception) {
                results[pokemonName] = false
            }
        }
        
        val successCount = results.values.count { it }
        
        return results
    }

    /**
     * 创建精灵球物品
     * 直接使用 Cobblemon API 创建精灵球物品
     */
    fun createPokeBallItem(pokeBall: com.cobblemon.mod.common.pokeball.PokeBall): ItemStack? {
        return try {
            // 使用 Cobblemon 的 PokeBall.stack() 创建原生物品
            val nmsItem = pokeBall.stack(1)

            // 转换为 Bukkit ItemStack
            val bukkitItem = convertNMSItemToBukkit(nmsItem)
            if (bukkitItem != null) {
                return bukkitItem
            } else {
                return null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}
