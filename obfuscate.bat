@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔐 AceDex Skidfuscator 混淆脚本 🔐            ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🚀 自动构建并混淆 AceDex 精灵图鉴插件                       ║
echo ║  📦 使用 Gradle + Skidfuscator 进行完整构建流程              ║
echo ╚══════════════════════════════════════════════════════════════╝

echo.
echo 🔍 检查构建环境...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境，请确保已安装Java 17并添加到PATH
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Gradle环境
gradlew.bat --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Gradle环境，请确保gradlew.bat可执行
    pause
    exit /b 1
)
echo ✅ Gradle环境检查通过

:: 检查Skidfuscator是否存在
if not exist "skidfuscator.jar" (
    echo ❌ 错误: 未找到skidfuscator.jar文件
    echo 📥 请从 https://github.com/skidfuscatordev/skidfuscator-java-obfuscator/releases 下载最新版本
    echo 📁 并将其重命名为 skidfuscator.jar 放在项目根目录
    pause
    exit /b 1
)
echo ✅ Skidfuscator检查通过

echo.
echo 📦 步骤 1: 清理并编译项目...
call gradlew.bat clean build --no-daemon
if %errorlevel% neq 0 (
    echo ❌ 错误: Gradle编译失败
    pause
    exit /b 1
)
echo ✅ 项目编译完成

echo.
echo 🔍 步骤 2: 检查编译产物...
if not exist "build\libs\AceDex-1.0.0.jar" (
    echo ❌ 错误: 未找到编译后的JAR文件 build\libs\AceDex-1.0.0.jar
    pause
    exit /b 1
)
echo ✅ 编译产物检查通过

echo.
echo 🔐 步骤 3: 开始Skidfuscator混淆...
echo 📋 使用配置文件: skidfuscator-config.json
echo 📁 输入文件: build\libs\AceDex-1.0.0.jar
echo 📁 输出目录: obfuscated\

:: 创建输出目录
if not exist "obfuscated" mkdir "obfuscated"

java -Xmx8G -Xms4G -XX:+UseG1GC -jar skidfuscator.jar obfuscate --config skidfuscator-config.json

if %errorlevel% neq 0 (
    echo ❌ 错误: Skidfuscator混淆失败
    pause
    exit /b 1
)
echo ✅ 混淆处理完成

echo.
echo 🔍 步骤 4: 检查混淆结果...
if exist "obfuscated\AceDex-1.0.0-obfuscated.jar" (
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ✅ 混淆完成 ✅                            ║
    echo ╠══════════════════════════════════════════════════════════════╣
    echo ║  🎉 AceDex 代码混淆成功完成！                                ║
    echo ║  📁 原始文件: build\libs\AceDex-1.0.0.jar                   ║
    echo ║  📁 混淆文件: obfuscated\AceDx-1.0.0-obfuscated.jar        ║
    echo ╚══════════════════════════════════════════════════════════════╝

    echo.
    echo 📊 文件大小对比:
    for %%A in ("build\libs\AceDx-1.0.0.jar") do set "original_size=%%~zA"
    for %%A in ("obfuscated\AceDx-1.0.0-obfuscated.jar") do set "obfuscated_size=%%~zA"

    echo    原始文件大小: %original_size% 字节
    echo    混淆文件大小: %obfuscated_size% 字节

    echo.
    echo 🔒 混淆保护特性:
    echo    ✓ 高强度控制流混淆
    echo    ✓ 字符串加密保护
    echo    ✓ 数字混淆算法
    echo    ✓ 引用虚拟化
    echo    ✓ 方法轮廓化
    echo    ✓ 类/方法/字段重命名
    echo    ✓ 重要接口保护

    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║  🚀 混淆过程完成！您的 AceDx 插件现在已受到保护              ║
    echo ╚══════════════════════════════════════════════════════════════╝
) else (
    echo ❌ 错误: 混淆文件未生成，请检查上方错误信息
    exit /b 1
)

echo.
pause
