package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.generation.Generation
import org.bukkit.Material
import org.bukkit.inventory.ItemStack
import org.bukkit.inventory.meta.ItemMeta
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.io.File
import org.bukkit.configuration.file.YamlConfiguration
import org.bukkit.configuration.ConfigurationSection

/**
 * 精灵模型预加载管理器
 * 在插件启动时将所有世代的精灵模型预加载到内存合集中，避免每次打开菜单时重复创建
 */
class PokemonModelPreloader(private val plugin: AceDex) {
    
    // 预加载的精灵物品合集 - 使用ConcurrentHashMap保证线程安全
    private val preloadedPokemonItems = ConcurrentHashMap<String, ItemStack>()
    
    // 精灵按钮ID映射 - 用于快速查找和标识符判断
    private val pokemonButtonIds = ConcurrentHashMap<String, String>()
    
    // 世代精灵列表缓存
    private val generationPokemonCache = ConcurrentHashMap<String, List<String>>()
    
    // 预加载状态
    @Volatile
    private var isPreloaded = false
    
    // 异步执行器
    private val executor = Executors.newFixedThreadPool(4)
    
    // 本地缓存文件 - 改为YAML格式
    private val cacheDir = File(plugin.dataFolder, "cache")
    private val preloadStatusFile = File(cacheDir, "preload_status.yml")
    private val buttonIdCacheFile = File(cacheDir, "button_ids.yml")
    
    init {
        // 确保缓存目录存在
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }
    
    /**
     * 启动预加载过程
     */
    fun startPreloading(): CompletableFuture<Void> {
        plugin.logger.info("开始预加载精灵模型...")
        
        return CompletableFuture.runAsync({
            try {
                // 先尝试从本地缓存加载
                if (loadFromCache()) {
                    plugin.logger.info("从本地缓存成功加载精灵模型")
                    isPreloaded = true
                    return@runAsync
                }
                
                // 缓存不存在或损坏，重新生成
                generatePokemonModels()
                
                // 保存到本地缓存
                saveToCache()
                
                isPreloaded = true
                plugin.logger.info("精灵模型预加载完成，共加载 ${preloadedPokemonItems.size} 个精灵模型")
                
            } catch (e: Exception) {
                plugin.logger.severe("精灵模型预加载失败: ${e.message}")
                e.printStackTrace()
            }
        }, executor)
    }
    
    /**
     * 生成所有精灵模型
     */
    private fun generatePokemonModels() {
        val allGenerations = plugin.generationManager.getAllGenerations()
        
        for (generation in allGenerations) {
            plugin.logger.info("正在预加载第${generation.name}精灵模型...")
            preloadGenerationPokemon(generation)
        }
    }
    
    /**
     * 预加载指定世代的精灵
     */
    private fun preloadGenerationPokemon(generation: Generation) {
        val pokemonList = generation.getAllPokemon()
        val generationPokemonNames = mutableListOf<String>()
        
        for ((pokemonName, nationalDex) in pokemonList) {
            try {
                // 创建未收集状态的精灵物品（基础模型）
                val uncaughtItem = createBasePokemonItem(pokemonName, nationalDex, false)
                val uncaughtKey = generateItemKey(pokemonName, nationalDex, false)
                preloadedPokemonItems[uncaughtKey] = uncaughtItem
                
                // 创建已收集状态的精灵物品
                val caughtItem = createBasePokemonItem(pokemonName, nationalDex, true)
                val caughtKey = generateItemKey(pokemonName, nationalDex, true)
                preloadedPokemonItems[caughtKey] = caughtItem
                
                // 生成按钮ID
                val buttonId = "pokemon:${pokemonName.lowercase()}"
                pokemonButtonIds[buttonId] = pokemonName
                
                generationPokemonNames.add(pokemonName)
                
            } catch (e: Exception) {
                plugin.logger.warning("预加载精灵 $pokemonName 失败: ${e.message}")
            }
        }
        
        // 缓存世代精灵列表
        generationPokemonCache[generation.id] = generationPokemonNames
    }
    
    /**
     * 创建基础精灵物品
     */
    private fun createBasePokemonItem(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 使用插件的精灵物品创建器创建物品
        return plugin.pokemonItemCreator.createPokemonItem(pokemonName, nationalDex, hasCaught)
    }
    
    /**
     * 生成物品缓存键
     */
    private fun generateItemKey(pokemonName: String, nationalDex: Int, hasCaught: Boolean): String {
        return "${pokemonName.lowercase()}_${nationalDex}_${if (hasCaught) "caught" else "uncaught"}"
    }
    
    /**
     * 获取预加载的精灵物品
     */
    fun getPreloadedPokemonItem(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack? {
        if (!isPreloaded) {
            return null
        }
        
        val key = generateItemKey(pokemonName, nationalDex, hasCaught)
        return preloadedPokemonItems[key]?.clone()
    }
    
    /**
     * 根据按钮ID获取精灵名称
     */
    fun getPokemonNameByButtonId(buttonId: String): String? {
        return pokemonButtonIds[buttonId]
    }
    
    /**
     * 检查按钮ID是否为精灵按钮
     */
    fun isPokemonButton(buttonId: String): Boolean {
        return buttonId.startsWith("pokemon:") && pokemonButtonIds.containsKey(buttonId)
    }
    
    /**
     * 获取世代精灵列表
     */
    fun getGenerationPokemonList(generationId: String): List<String> {
        return generationPokemonCache[generationId] ?: emptyList()
    }
    
    /**
     * 检查是否已预加载完成
     */
    fun isPreloadComplete(): Boolean = isPreloaded
    
    /**
     * 从本地缓存加载
     */
    private fun loadFromCache(): Boolean {
        return try {
            if (!preloadStatusFile.exists() || !buttonIdCacheFile.exists()) {
                return false
            }

            // 检查预加载状态文件
            val statusConfig = YamlConfiguration.loadConfiguration(preloadStatusFile)
            val lastPreloadTime = statusConfig.getLong("last_preload_time", 0)
            val preloadedCount = statusConfig.getInt("preloaded_count", 0)

            // 如果预加载时间太久远（超过7天），重新预加载
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastPreloadTime > 7 * 24 * 60 * 60 * 1000) {
                plugin.logger.info("预加载缓存已过期，将重新生成")
                return false
            }

            // 加载按钮ID缓存
            val buttonConfig = YamlConfiguration.loadConfiguration(buttonIdCacheFile)
            val buttonSection = buttonConfig.getConfigurationSection("button_ids")
            if (buttonSection != null) {
                for (key in buttonSection.getKeys(false)) {
                    val pokemonName = buttonSection.getString(key)
                    if (pokemonName != null) {
                        pokemonButtonIds[key] = pokemonName
                    }
                }
            }

            // 重新生成精灵物品（因为ItemStack无法序列化，每次启动都需要重新生成）
            generatePokemonModels()

            // 重新生成世代精灵列表缓存
            regenerateGenerationCache()

            plugin.logger.info("从缓存加载预加载信息，共 $preloadedCount 个精灵模型")
            true
        } catch (e: Exception) {
            plugin.logger.warning("加载本地缓存失败: ${e.message}")
            false
        }
    }
    
    /**
     * 保存到本地缓存
     */
    private fun saveToCache() {
        try {
            // 保存预加载状态
            val statusConfig = YamlConfiguration()
            statusConfig.set("last_preload_time", System.currentTimeMillis())
            statusConfig.set("preloaded_count", preloadedPokemonItems.size)
            statusConfig.set("generation_count", generationPokemonCache.size)
            statusConfig.save(preloadStatusFile)

            // 保存按钮ID缓存
            val buttonConfig = YamlConfiguration()
            val buttonSection = buttonConfig.createSection("button_ids")
            for ((buttonId, pokemonName) in pokemonButtonIds) {
                buttonSection.set(buttonId, pokemonName)
            }
            buttonConfig.save(buttonIdCacheFile)

            plugin.logger.info("精灵模型预加载状态已保存到本地文件")
        } catch (e: Exception) {
            plugin.logger.warning("保存本地缓存失败: ${e.message}")
        }
    }
    
    /**
     * 重新生成世代精灵列表缓存
     */
    private fun regenerateGenerationCache() {
        val allGenerations = plugin.generationManager.getAllGenerations()
        
        for (generation in allGenerations) {
            val pokemonList = generation.getAllPokemon()
            val generationPokemonNames = pokemonList.keys.toList()
            generationPokemonCache[generation.id] = generationPokemonNames
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        preloadedPokemonItems.clear()
        pokemonButtonIds.clear()
        generationPokemonCache.clear()
        isPreloaded = false
        
        // 删除本地缓存文件
        if (preloadStatusFile.exists()) {
            preloadStatusFile.delete()
        }
        if (buttonIdCacheFile.exists()) {
            buttonIdCacheFile.delete()
        }
        
        plugin.logger.info("精灵模型缓存已清理")
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): String {
        return buildString {
            appendLine("预加载状态: ${if (isPreloaded) "已完成" else "未完成"}")
            appendLine("精灵物品数量: ${preloadedPokemonItems.size}")
            appendLine("按钮ID数量: ${pokemonButtonIds.size}")
            appendLine("世代缓存数量: ${generationPokemonCache.size}")
            appendLine("本地缓存文件: ${if (preloadStatusFile.exists()) "存在" else "不存在"}")
        }
    }
    
    /**
     * 关闭预加载器
     */
    fun shutdown() {
        executor.shutdown()
    }
}
