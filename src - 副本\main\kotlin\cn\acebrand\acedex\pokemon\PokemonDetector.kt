/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.pokemon

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.data.*
import cn.acebrand.acedex.util.PokemonInfo
import com.cobblemon.mod.common.api.storage.party.PlayerPartyStore
import com.cobblemon.mod.common.api.storage.pc.PCStore
import com.cobblemon.mod.common.pokemon.Pokemon
import com.cobblemon.mod.common.util.party
import com.cobblemon.mod.common.util.pc
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import net.minecraft.server.level.ServerPlayer
import org.bukkit.entity.Player
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.util.*
import java.util.concurrent.CompletableFuture
import kotlin.math.min

/**
 * 精灵检测器
 * 负责检测玩家PC和背包中的精灵
 * 使用直接API调用并保存到本地文件
 */
class PokemonDetector(private val plugin: AceDex) {

    // 数据存储目录
    private val dataFolder = File(plugin.dataFolder, "playerdata")

    // JSON序列化工具
    private val gson: Gson = GsonBuilder().setPrettyPrinting().create()

    init {
        // 确保数据目录存在
        if (!dataFolder.exists()) {
            dataFolder.mkdirs()
        }
    }

    companion object {
        /**
         * 获取精灵的中文名称
         */
        fun getPokemonChineseName(englishName: String): String {
            val normalizedName = englishName.lowercase().replace(Regex("[^a-z0-9-]+"), "")
            return PokemonNameMapping.getPokemonChineseName(normalizedName)
        }

        /**
         * 检查是否有中文名称映射
         */
        fun hasChineseName(englishName: String): Boolean {
            val normalizedName = englishName.lowercase().replace(Regex("[^a-z0-9-]+"), "")
            return PokemonNameMapping.hasChineseName(normalizedName)
        }
    }

    /**
     * 获取ServerPlayer对象
     */
    private fun getServerPlayer(player: Player): ServerPlayer? {
        return try {
            val craftPlayerClass = player.javaClass
            val getHandleMethod = craftPlayerClass.getMethod("getHandle")
            getHandleMethod.invoke(player) as? ServerPlayer
        } catch (e: Exception) {
            plugin.logger.warning("无法获取ServerPlayer: ${e.message}")
            null
        }
    }

    /**
     * 获取玩家拥有的所有精灵（从本地文件读取）
     */
    fun getPlayerPokemon(player: Player): PlayerPokemonData {
        return loadPlayerDataFromFile(player) ?: run {
            // 如果本地文件不存在，重新检测并保存
            val data = detectAndSavePlayerPokemon(player)
            data ?: getTestPokemonData(player)
        }
    }

    /**
     * 检测玩家精灵并保存到本地文件
     */
    fun detectAndSavePlayerPokemon(player: Player): PlayerPokemonData? {
        return try {
            val serverPlayer = getServerPlayer(player) ?: return null
            val data = getPlayerPokemonFromCobblemon(serverPlayer)
            savePlayerDataToFile(player, data)
            data
        } catch (e: Exception) {
            plugin.logger.warning("检测玩家精灵失败: ${e.message}")
            null
        }
    }

    /**
     * 使用新的CheckPokemon方法检测玩家精灵并保存到本地文件
     */
    fun detectAndSavePlayerPokemonWithCheckMethod(player: Player): Boolean {
        return try {
            val serverPlayer = getServerPlayer(player) ?: return false
            val data = getPlayerPokemonFromCobblemonWithCheckMethod(serverPlayer)
            savePlayerDataToFile(player, data)
            true
        } catch (e: Exception) {
            plugin.logger.warning("使用CheckPokemon方法检测玩家精灵失败: ${e.message}")
            false
        }
    }

    /**
     * 使用直接API调用获取玩家精灵数据
     */
    private fun getPlayerPokemonFromCobblemon(serverPlayer: ServerPlayer): PlayerPokemonData {
        val partyPokemon = mutableListOf<PokemonInfo>()
        val pcPokemon = mutableListOf<PokemonInfo>()

        try {
            // 获取队伍精灵
            val party: PlayerPartyStore = serverPlayer.party()
            for (i in 0 until 6) {
                val pokemon = party.get(i)
                if (pokemon != null) {
                    val pokemonInfo = createPokemonInfo(pokemon)
                    partyPokemon.add(pokemonInfo)
                }
            }

            // 获取PC精灵
            val pcStore: PCStore = serverPlayer.pc()
            for (box in pcStore.boxes) {
                val nonEmptySlots = box.getNonEmptySlots()
                for (entry in nonEmptySlots.entries) {
                    val pokemon = entry.value
                    val pokemonInfo = createPokemonInfo(pokemon)
                    pcPokemon.add(pokemonInfo)
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("获取精灵数据时发生错误: ${e.message}")
            throw e
        }

        return PlayerPokemonData(
            partyPokemon = partyPokemon,
            pcPokemon = pcPokemon,
            totalCaught = partyPokemon.size + pcPokemon.size
        )
    }

    /**
     * 使用新的CheckPokemon方法获取玩家精灵数据
     * 基于你提供的CheckPokemon逻辑进行优化
     */
    private fun getPlayerPokemonFromCobblemonWithCheckMethod(serverPlayer: ServerPlayer): PlayerPokemonData {
        val partyPokemon = mutableListOf<PokemonInfo>()
        val pcPokemon = mutableListOf<PokemonInfo>()

        try {
            // 获取队伍精灵 - 使用你的CheckPokemon逻辑
            val party: PlayerPartyStore = serverPlayer.party()
            for (i in 0 until 6) {
                val pokemon = party.get(i)
                if (pokemon != null) {
                    val pokemonInfo = createPokemonInfo(pokemon)
                    partyPokemon.add(pokemonInfo)
                }
            }

            // 获取PC精灵 - 使用你的CheckPokemon逻辑
            val pcStore: PCStore = serverPlayer.pc()
            for (box in pcStore.boxes) {
                val nonEmptySlots = box.getNonEmptySlots()
                if (nonEmptySlots.isNotEmpty()) {
                    for (entry in nonEmptySlots.entries) {
                        val pokemon = entry.value
                        val pokemonInfo = createPokemonInfo(pokemon)
                        pcPokemon.add(pokemonInfo)
                    }
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("使用CheckPokemon方法获取精灵数据时发生错误: ${e.message}")
            throw e
        }

        return PlayerPokemonData(
            partyPokemon = partyPokemon,
            pcPokemon = pcPokemon,
            totalCaught = partyPokemon.size + pcPokemon.size
        )
    }

    /**
     * 检查玩家是否拥有指定精灵 - 基于你的CheckPokemon方法
     */
    fun checkPokemon(serverPlayer: ServerPlayer, pokemonName: String): Boolean {
        try {
            val normalizedName = pokemonName.lowercase()

            // 检查队伍精灵
            val party: PlayerPartyStore = serverPlayer.party()
            for (i in 0 until 6) {
                val pokemon = party.get(i)
                if (pokemon != null) {
                    val speciesName = pokemon.species.name.lowercase()
                    if (speciesName == normalizedName) {
                        return true
                    }
                }
            }

            // 检查PC精灵
            val pcStore: PCStore = serverPlayer.pc()
            for (box in pcStore.boxes) {
                val nonEmptySlots = box.getNonEmptySlots()
                if (nonEmptySlots.isNotEmpty()) {
                    for (entry in nonEmptySlots.entries) {
                        val pokemon = entry.value
                        val speciesName = pokemon.species.name.lowercase()
                        if (speciesName == normalizedName) {
                            return true
                        }
                    }
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("检查精灵 $pokemonName 时发生错误: ${e.message}")
        }

        return false
    }

    /**
     * 创建PokemonInfo对象
     */
    private fun createPokemonInfo(pokemon: Pokemon): PokemonInfo {
        val species = pokemon.species
        val nationalDex = species.nationalPokedexNumber
        val generation = determineGeneration(nationalDex)

        return PokemonInfo(
            name = species.name,
            nationalDex = nationalDex,
            generation = generation
        )
    }

    /**
     * 根据全国图鉴编号确定世代
     */
    private fun determineGeneration(nationalDex: Int): String {
        return when (nationalDex) {
            in 1..151 -> "第一世代"
            in 152..251 -> "第二世代"
            in 252..386 -> "第三世代"
            in 387..493 -> "第四世代"
            in 494..649 -> "第五世代"
            in 650..721 -> "第六世代"
            in 722..809 -> "第七世代"
            in 810..905 -> "第八世代"
            in 906..1025 -> "第九世代"
            else -> "未知世代"
        }
    }

    /**
     * 异步获取玩家拥有的所有精灵
     */
    fun getPlayerPokemonAsync(player: Player): CompletableFuture<PlayerPokemonData> {
        return CompletableFuture.supplyAsync {
            getPlayerPokemon(player)
        }
    }

    /**
     * 保存玩家数据到本地文件
     */
    private fun savePlayerDataToFile(player: Player, data: PlayerPokemonData) {
        try {
            val file = File(dataFolder, "${player.uniqueId}.json")
            val storageData = PlayerDataStorage.fromPlayerPokemonData(
                player.uniqueId,
                player.name,
                data
            )

            FileWriter(file).use { writer ->
                gson.toJson(storageData, writer)
            }

            if (plugin.config.enableDebug) {
                plugin.logger.info("已保存玩家 ${player.name} 的精灵数据到本地文件")
            }
        } catch (e: Exception) {
            plugin.logger.warning("保存玩家数据失败: ${e.message}")
        }
    }

    /**
     * 从本地文件加载玩家数据
     */
    private fun loadPlayerDataFromFile(player: Player): PlayerPokemonData? {
        return try {
            val file = File(dataFolder, "${player.uniqueId}.json")
            if (!file.exists()) {
                return null
            }

            FileReader(file).use { reader ->
                val storageData = gson.fromJson(reader, PlayerDataStorage::class.java)

                // 验证数据完整性
                if (storageData == null) {
                    plugin.logger.warning("玩家 ${player.name} 的数据文件为空或格式错误")
                    return null
                }

                // 检查必要字段是否为null
                if (storageData.partyPokemon == null || storageData.pcPokemon == null) {
                    plugin.logger.warning("玩家 ${player.name} 的数据文件缺少必要字段，将重新生成")
                    return null
                }

                storageData.toPlayerPokemonData()
            }
        } catch (e: com.google.gson.JsonSyntaxException) {
            plugin.logger.warning("玩家 ${player.name} 的JSON文件格式错误: ${e.message}")
            // JSON格式错误，删除损坏的文件
            try {
                val file = File(dataFolder, "${player.uniqueId}.json")
                if (file.exists()) {
                    file.delete()
                    plugin.logger.info("已删除损坏的数据文件: ${player.uniqueId}.json")
                }
            } catch (deleteException: Exception) {
                plugin.logger.warning("删除损坏文件失败: ${deleteException.message}")
            }
            null
        } catch (e: java.io.EOFException) {
            plugin.logger.warning("玩家 ${player.name} 的数据文件不完整: ${e.message}")
            // 文件不完整，删除并重新生成
            try {
                val file = File(dataFolder, "${player.uniqueId}.json")
                if (file.exists()) {
                    file.delete()
                    plugin.logger.info("已删除不完整的数据文件: ${player.uniqueId}.json")
                }
            } catch (deleteException: Exception) {
                plugin.logger.warning("删除不完整文件失败: ${deleteException.message}")
            }
            null
        } catch (e: Exception) {
            plugin.logger.warning("加载玩家 ${player.name} 数据失败: ${e.message}")
            null
        }
    }

    /**
     * 获取玩家数据文件的最后修改时间
     */
    fun getPlayerDataFileLastModified(player: Player): Long {
        return try {
            val file = File(dataFolder, "${player.uniqueId}.json")
            if (file.exists()) file.lastModified() else 0L
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 检查玩家是否拥有指定精灵（类似你提供的方法）
     */
    fun checkPokemon(player: Player, pokemonName: String): Boolean {
        return try {
            val serverPlayer = getServerPlayer(player) ?: return false
            val leg = pokemonName.lowercase()

            // 检查队伍
            val party: PlayerPartyStore = serverPlayer.party()
            for (i in 0 until 6) {
                val pokemon = party.get(i)
                if (pokemon != null) {
                    val beibao = pokemon.species.name.lowercase()
                    if (beibao == leg) {
                        return true
                    }
                }
            }

            // 检查PC
            val pcStore: PCStore = serverPlayer.pc()
            for (box in pcStore.boxes) {
                val nonEmptySlots = box.getNonEmptySlots()
                for (entry in nonEmptySlots.entries) {
                    val pokemon = entry.value
                    if (pokemon.species.name.lowercase() == leg) {
                        return true
                    }
                }
            }

            false
        } catch (e: Exception) {
            plugin.logger.warning("检查精灵时发生错误: ${e.message}")
            false
        }
    }

    /**
     * 删除玩家数据文件
     */
    fun deletePlayerDataFile(player: Player) {
        try {
            val file = File(dataFolder, "${player.uniqueId}.json")
            if (file.exists()) {
                file.delete()
                plugin.logger.info("已删除玩家 ${player.name} 的数据文件")
            }
        } catch (e: Exception) {
            plugin.logger.warning("删除玩家数据文件失败: ${e.message}")
        }
    }

    /**
     * 强制刷新玩家数据（重新检测并保存）
     */
    fun forceRefreshPlayerData(player: Player): PlayerPokemonData? {
        return detectAndSavePlayerPokemon(player)
    }

    /**
     * 获取所有玩家数据文件列表
     */
    fun getAllPlayerDataFiles(): List<File> {
        return try {
            dataFolder.listFiles { file -> file.extension == "json" }?.toList() ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 清理所有玩家数据文件（谨慎使用）
     */
    fun clearAllPlayerData() {
        try {
            val files = getAllPlayerDataFiles()
            files.forEach { it.delete() }
            plugin.logger.info("已清理 ${files.size} 个玩家数据文件")
        } catch (e: Exception) {
            plugin.logger.warning("清理玩家数据文件失败: ${e.message}")
        }
    }







    /**
     * 获取测试用的精灵数据
     */
    private fun getTestPokemonData(player: Player): PlayerPokemonData {
        // 根据玩家名称生成不同的测试数据，模拟真实情况
        val playerName = player.name.lowercase()
        val baseData = listOf(
            PokemonInfo("pikachu", 25, "第一世代"),
            PokemonInfo("charizard", 6, "第一世代"),
            PokemonInfo("blastoise", 9, "第一世代"),
            PokemonInfo("venusaur", 3, "第一世代"),
            PokemonInfo("mewtwo", 150, "第一世代"),
            PokemonInfo("lugia", 249, "第二世代"),
            PokemonInfo("ho-oh", 250, "第二世代"),
            PokemonInfo("celebi", 251, "第二世代"),
            PokemonInfo("rayquaza", 384, "第三世代"),
            PokemonInfo("dialga", 483, "第四世代")
        )

        // 根据玩家名称选择不同的精灵组合
        val playerHash = playerName.hashCode()
        val pokemonCount = 3 + (playerHash % 5) // 3-7只精灵
        val selectedPokemon = baseData.shuffled().take(pokemonCount)

        val partyPokemon = selectedPokemon.take(6) // 最多6只在队伍
        val pcPokemon = selectedPokemon.drop(6) // 其余在PC

        return PlayerPokemonData(
            partyPokemon = partyPokemon,
            pcPokemon = pcPokemon,
            totalCaught = selectedPokemon.size
        )
    }
    
    /**
     * 检查玩家是否拥有指定精灵
     */
    fun hasPlayerCaughtPokemon(player: Player, pokemonName: String): Boolean {
        val playerData = getPlayerPokemon(player)
        val normalizedSearchName = normalizeShowdownId(pokemonName)

        return playerData.partyPokemon.any { normalizeShowdownId(it.name) == normalizedSearchName } ||
               playerData.pcPokemon.any { normalizeShowdownId(it.name) == normalizedSearchName }
    }



    /**
     * 标准化精灵名称为 Showdown ID 格式
     * 移除所有非字母数字字符并转换为小写
     */
    private fun normalizeShowdownId(name: String): String {
        return name.lowercase().replace(Regex("[^a-z0-9]+"), "")
    }
    
    /**
     * 获取玩家在指定世代的收集进度
     */
    fun getGenerationProgress(player: Player, generationId: String): GenerationProgress {
        val generation = plugin.generationManager.getGeneration(generationId)
        if (generation == null) {
            return GenerationProgress(0, 0, 0)
        }

        val playerData = getPlayerPokemon(player)
        val allPlayerPokemon = playerData.partyPokemon + playerData.pcPokemon

        val totalInGeneration = generation.getTotalPokemon()

        if (plugin.config.enableDebug) {
            plugin.logger.info("计算世代 ${generation.id} 进度 - 总精灵数: $totalInGeneration")
        }

        // 使用distinctBy去除重复精灵，只统计不同种类的精灵
        val uniquePokemonInGeneration = allPlayerPokemon
            .filter { pokemon ->
                // 使用标准化名称匹配
                val normalizedName = normalizeShowdownId(pokemon.name)
                val contains = generation.containsPokemonByShowdownId(normalizedName)
                if (plugin.config.enableDebug && contains) {
                    plugin.logger.info("玩家 ${player.name} 拥有世代 ${generation.id} 精灵: ${pokemon.name} (标准化: $normalizedName)")
                }
                contains
            }
            .distinctBy { normalizeShowdownId(it.name) } // 按标准化名称去重

        val caughtInGeneration = uniquePokemonInGeneration.size

        val percentage = if (totalInGeneration > 0) {
            (caughtInGeneration * 100) / totalInGeneration
        } else 0

        if (plugin.config.enableDebug) {
            plugin.logger.info("世代 ${generation.id} 进度计算完成 - 已捕获: $caughtInGeneration/$totalInGeneration (${percentage}%)")
        }

        return GenerationProgress(totalInGeneration, caughtInGeneration, percentage)
    }

    /**
     * 获取玩家在所有世代的总体收集进度
     */
    fun getAllGenerationsProgress(player: Player): AllGenerationsProgress {
        return calculateAllGenerationsProgress(player)
    }

    /**
     * 异步获取玩家在所有世代的总体收集进度
     */
    fun getAllGenerationsProgressAsync(player: Player): CompletableFuture<AllGenerationsProgress> {
        return CompletableFuture.supplyAsync {
            getAllGenerationsProgress(player)
        }
    }

    /**
     * 计算玩家在所有世代的总体收集进度（实际计算逻辑）
     */
    private fun calculateAllGenerationsProgress(player: Player): AllGenerationsProgress {
        val allGenerations = plugin.generationManager.getAllGenerations()
        val playerData = getPlayerPokemon(player)
        val allPlayerPokemon = playerData.partyPokemon + playerData.pcPokemon

        var totalPokemon = 0
        var caughtPokemon = 0
        var completedGenerations = 0
        val generationProgresses = mutableMapOf<String, GenerationProgress>()

        for (generation in allGenerations) {
            val totalInGeneration = generation.getTotalPokemon()
            // 使用distinctBy去除重复精灵，只统计不同种类的精灵
            val uniquePokemonInGeneration = allPlayerPokemon
                .filter { pokemon ->
                    generation.containsPokemonByShowdownId(normalizeShowdownId(pokemon.name))
                }
                .distinctBy { normalizeShowdownId(it.name) } // 按标准化名称去重

            val caughtInGeneration = uniquePokemonInGeneration.size

            val generationPercentage = if (totalInGeneration > 0) {
                (caughtInGeneration * 100) / totalInGeneration
            } else 0

            val generationProgress = GenerationProgress(totalInGeneration, caughtInGeneration, generationPercentage)
            generationProgresses[generation.id] = generationProgress

            totalPokemon += totalInGeneration
            caughtPokemon += caughtInGeneration

            if (generationPercentage >= 100) {
                completedGenerations++
            }
        }

        val overallPercentage = if (totalPokemon > 0) {
            (caughtPokemon * 100) / totalPokemon
        } else 0

        return AllGenerationsProgress(
            totalGenerations = allGenerations.size,
            completedGenerations = completedGenerations,
            totalPokemon = totalPokemon,
            caughtPokemon = caughtPokemon,
            overallPercentage = overallPercentage,
            generationProgresses = generationProgresses
        )
    }



    /**
     * 检测玩家数据是否发生变化并自动更新本地文件
     */
    fun checkAndUpdatePlayerData(player: Player) {
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            try {
                // 获取当前本地文件的数据
                val cachedData = loadPlayerDataFromFile(player)

                // 强制获取最新数据
                val serverPlayer = getServerPlayer(player)
                if (serverPlayer != null) {
                    val currentData = getPlayerPokemonFromCobblemon(serverPlayer)

                    // 比较数据是否发生变化
                    val hasChanged = cachedData == null || hasDataChanged(cachedData, currentData)

                    if (hasChanged) {
                        // 数据发生变化，保存到本地文件
                        savePlayerDataToFile(player, currentData)

                        // 回到主线程通知界面更新
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            plugin.mainGui.onPlayerDataUpdated(player)
                        })
                    }
                }
            } catch (e: Exception) {
                // 检测失败，静默处理
                plugin.logger.warning("检测玩家数据变化时发生错误: ${e.message}")
            }
        })
    }

    /**
     * 比较两个玩家数据是否发生变化
     */
    private fun hasDataChanged(oldData: PlayerPokemonData, newData: PlayerPokemonData): Boolean {
        // 比较总数量
        if (oldData.totalCaught != newData.totalCaught) return true

        // 比较队伍精灵数量
        if (oldData.partyPokemon.size != newData.partyPokemon.size) return true
        if (oldData.pcPokemon.size != newData.pcPokemon.size) return true

        // 比较精灵种类（简化比较，只比较名称集合）
        val oldPokemonNames = (oldData.partyPokemon + oldData.pcPokemon).map { it.name }.toSet()
        val newPokemonNames = (newData.partyPokemon + newData.pcPokemon).map { it.name }.toSet()

        return oldPokemonNames != newPokemonNames
    }
}






