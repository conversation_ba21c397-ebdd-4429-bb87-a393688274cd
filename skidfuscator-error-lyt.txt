handler=Block #WC, types=[Ljava/io/IOException;], range=[Block #CQ, Block #CP]
handler=Block #WG, types=[Ljava/io/IOException;], range=[Block #CT, Block #CS]
handler=Block #WK, types=[Ljava/io/IOException;], range=[Block #CW, Block #CV]
handler=Block #WO, types=[Ljava/io/IOException;], range=[Block #CZ, Block #CY]
handler=Block #WS, types=[Ljava/lang/IllegalAccessException;], range=[Block #DC, Block #DB]
handler=Block #WW, types=[Ljava/io/IOException;], range=[Block #DF, Block #DE]
handler=Block #XA, types=[Ljava/lang/IllegalAccessException;], range=[Block #DI, Block #DH]
handler=Block #XE, types=[Ljava/io/IOException;], range=[Block #DL, Block #DK]
handler=Block #XI, types=[Ljava/lang/RuntimeException;], range=[Block #DO, Block #DN]
handler=Block #XM, types=[Ljava/lang/IllegalAccessException;], range=[Block #DR, Block #DQ]
handler=Block #XQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #DU, Block #DT]
handler=Block #XU, types=[Ljava/lang/RuntimeException;], range=[Block #DX, Block #DW]
handler=Block #XY, types=[Ljava/lang/IllegalAccessException;], range=[Block #EA, Block #DZ]
handler=Block #YC, types=[Ljava/lang/RuntimeException;], range=[Block #ED, Block #EC]
handler=Block #YG, types=[Ljava/lang/IllegalAccessException;], range=[Block #EG, Block #EF]
handler=Block #YK, types=[Ljava/lang/IllegalAccessException;], range=[Block #EJ, Block #EI]
handler=Block #YO, types=[Ljava/io/IOException;], range=[Block #EM, Block #EL]
handler=Block #YS, types=[Ljava/lang/IllegalAccessException;], range=[Block #EP, Block #EO]
handler=Block #YW, types=[Ljava/lang/IllegalAccessException;], range=[Block #ES, Block #ER]
handler=Block #ZA, types=[Ljava/io/IOException;], range=[Block #EV, Block #EU]
handler=Block #ZE, types=[Ljava/io/IOException;], range=[Block #EY, Block #EX]
handler=Block #ZI, types=[Ljava/io/IOException;], range=[Block #FB, Block #FA]
handler=Block #ZM, types=[Ljava/lang/IllegalAccessException;], range=[Block #FE, Block #FD]
handler=Block #ZQ, types=[Ljava/io/IOException;], range=[Block #FH, Block #FG]
handler=Block #ZU, types=[Ljava/io/IOException;], range=[Block #FK, Block #FJ]
handler=Block #ZY, types=[Ljava/io/IOException;], range=[Block #FN, Block #FM]
handler=Block #AAC, types=[Ljava/lang/RuntimeException;], range=[Block #FQ, Block #FP]
handler=Block #AAG, types=[Ljava/lang/IllegalAccessException;], range=[Block #FT, Block #FS]
handler=Block #AAK, types=[Ljava/lang/RuntimeException;], range=[Block #FW, Block #FV]
handler=Block #AAO, types=[Ljava/io/IOException;], range=[Block #FZ, Block #FY]
handler=Block #AAS, types=[Ljava/lang/IllegalAccessException;], range=[Block #GC, Block #GB]
handler=Block #AAW, types=[Ljava/lang/RuntimeException;], range=[Block #GF, Block #GE]
handler=Block #ABA, types=[Ljava/lang/RuntimeException;], range=[Block #GI, Block #GH]
handler=Block #ABE, types=[Ljava/lang/RuntimeException;], range=[Block #GL, Block #GK]
handler=Block #ABI, types=[Ljava/lang/IllegalAccessException;], range=[Block #GO, Block #GN]
handler=Block #ABM, types=[Ljava/lang/RuntimeException;], range=[Block #GR, Block #GQ]
handler=Block #ABQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #GU, Block #GT]
handler=Block #ABU, types=[Ljava/lang/IllegalAccessException;], range=[Block #GX, Block #GW]
handler=Block #ABY, types=[Ljava/io/IOException;], range=[Block #HA, Block #GZ]
handler=Block #ACC, types=[Ljava/io/IOException;], range=[Block #HD, Block #HC]
handler=Block #ACG, types=[Ljava/lang/IllegalAccessException;], range=[Block #HG, Block #HF]
handler=Block #ACK, types=[Ljava/lang/IllegalAccessException;], range=[Block #HJ, Block #HI]
handler=Block #ACO, types=[Ljava/io/IOException;], range=[Block #HM, Block #HL]
handler=Block #ACS, types=[Ljava/lang/IllegalAccessException;], range=[Block #HP, Block #HO]
handler=Block #ACW, types=[Ljava/lang/RuntimeException;], range=[Block #HS, Block #HR]
handler=Block #ADA, types=[Ljava/lang/IllegalAccessException;], range=[Block #HV, Block #HU]
handler=Block #ADE, types=[Ljava/lang/IllegalAccessException;], range=[Block #HY, Block #HX]
handler=Block #ADI, types=[Ljava/io/IOException;], range=[Block #IB, Block #IA]
handler=Block #ADM, types=[Ljava/lang/IllegalAccessException;], range=[Block #IE, Block #ID]
handler=Block #ADQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #IH, Block #IG]
handler=Block #ADU, types=[Ljava/lang/IllegalAccessException;], range=[Block #IK, Block #IJ]
handler=Block #ADY, types=[Ljava/lang/RuntimeException;], range=[Block #IN, Block #IM]
handler=Block #AEC, types=[Ljava/io/IOException;], range=[Block #IQ, Block #IP]
handler=Block #AEG, types=[Ljava/lang/RuntimeException;], range=[Block #IT, Block #IS]
handler=Block #AEK, types=[Ljava/lang/IllegalAccessException;], range=[Block #IW, Block #IV]
handler=Block #AEO, types=[Ljava/lang/RuntimeException;], range=[Block #IZ, Block #IY]
===#Block A(size=6, flags=1)===
   0. lvar105 = {1247543109 ^ {477803852 ^ 642876916}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. synth(lvar2 = lvar2);
   4. synth(lvar3 = lvar3);
   5. lvar105 = {1376679335 ^ lvar105};
      -> Immediate #A -> #B
===#Block B(size=1, flags=0)===
   0. lvar105 = {58657917 ^ lvar105};
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=3, flags=0)===
   0. lvar5 = lvar3;
   1. if (lvar5 == {553760295 ^ lvar105})
      goto TR
   2. lvar105 = {524956355 ^ lvar105};
      -> Immediate #C -> #BK
      -> ConditionalJump[IF_ICMPEQ] #C -> #TR
      <- Immediate #B -> #C
===#Block TR(size=2, flags=10100)===
   0. lvar105 = {1567768678 ^ lvar105};
   1. goto JM
      -> UnconditionalJump[GOTO] #TR -> #JM
      <- ConditionalJump[IF_ICMPEQ] #C -> #TR
===#Block JM(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -476073461)
      goto SK
   1. goto OD
      -> ConditionalJump[IF_ICMPEQ] #JM -> #SK
      -> UnconditionalJump[GOTO] #JM -> #OD
      <- UnconditionalJump[GOTO] #TR -> #JM
===#Block OD(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 18927982:
      	 goto	#OE
      case 548605852:
      	 goto	#JC
      case 1554903685:
      	 goto	#OD
      case 1660548731:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[548605852] #OD -> #JC
      -> Immediate #OD -> #OE
      -> Switch[18927982] #OD -> #OE
      -> Switch[1554903685] #OD -> #OD
      -> DefaultSwitch #OD -> #JC
      <- UnconditionalJump[GOTO] #JM -> #OD
      <- Switch[1554903685] #OD -> #OD
===#Block OE(size=2, flags=100)===
   0. lvar105 = {1812426792 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OE -> #JC
      <- Immediate #OD -> #OE
      <- Switch[18927982] #OD -> #OE
===#Block SK(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 16214891:
      	 goto	#D
      case 18927982:
      	 goto	#SL
      case 419966015:
      	 goto	#JC
      case 1198420536:
      	 goto	#SK
      default:
      	 goto	#JC
   }
      -> Switch[1198420536] #SK -> #SK
      -> Switch[18927982] #SK -> #SL
      -> Switch[16214891] #SK -> #D
      -> Switch[419966015] #SK -> #JC
      -> Immediate #SK -> #SL
      -> DefaultSwitch #SK -> #JC
      <- ConditionalJump[IF_ICMPEQ] #JM -> #SK
      <- Switch[1198420536] #SK -> #SK
===#Block SL(size=2, flags=100)===
   0. lvar105 = {1992260581 ^ lvar105};
   1. goto D
      -> UnconditionalJump[GOTO] #SL -> #D
      <- Switch[18927982] #SK -> #SL
      <- Immediate #SK -> #SL
===#Block D(size=4, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar10 = lvar2;
   2. if (lvar10 == {181177764 ^ lvar105})
      goto TX
   3. lvar105 = {263269639 ^ lvar105};
      -> ConditionalJump[IF_ICMPEQ] #D -> #TX
      -> Immediate #D -> #E
      <- UnconditionalJump[GOTO] #SL -> #D
      <- Switch[16214891] #SK -> #D
===#Block E(size=6, flags=0)===
   0. lvar11 = lvar1;
   1. lvar9 = lvar11;
   2. lvar12 = lvar9;
   3. lvar13 = lvar12.hashCode();
   4. svar107 = {lvar13 ^ lvar105};
   5. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(svar107)) {
      case 83435472:
      	 goto	#VP
      case 83435473:
      	 goto	#VR
      case 83435474:
      	 goto	#VS
      case 83435496:
      	 goto	#VT
      case 83435499:
      	 goto	#VV
      case 83435500:
      	 goto	#VW
      case 83435501:
      	 goto	#VX
      case 83435503:
      	 goto	#VY
      default:
      	 goto	#WA
   }
      -> Switch[83435503] #E -> #VY
      -> Switch[83435501] #E -> #VX
      -> Switch[83435473] #E -> #VR
      -> Switch[83435474] #E -> #VS
      -> Switch[83435496] #E -> #VT
      -> DefaultSwitch #E -> #WA
      -> Switch[83435500] #E -> #VW
      -> Switch[83435499] #E -> #VV
      -> Switch[83435472] #E -> #VP
      <- Immediate #D -> #E
===#Block VP(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 86361061:
      	 goto	#VQ
      case 330419384:
      	 goto	#F
      case 1336948944:
      	 goto	#VP
      case 1646852032:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #VP -> #JC
      -> Immediate #VP -> #VQ
      -> Switch[1646852032] #VP -> #JC
      -> Switch[330419384] #VP -> #F
      -> Switch[86361061] #VP -> #VQ
      -> Switch[1336948944] #VP -> #VP
      <- Switch[1336948944] #VP -> #VP
      <- Switch[83435472] #E -> #VP
===#Block VQ(size=2, flags=100)===
   0. lvar105 = {2100297206 ^ lvar105};
   1. goto F
      -> UnconditionalJump[GOTO] #VQ -> #F
      <- Immediate #VP -> #VQ
      <- Switch[86361061] #VP -> #VQ
===#Block F(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar14 = lvar9;
   2. lvar6 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.zeqrxqweskmmcwd(), lvar105);
   3. lvar15 = lvar14.equals(lvar6);
   4. if (lvar15 != {2018663765 ^ lvar105})
      goto RX
   5. lvar105 = {1650629792 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #F -> #RX
      -> Immediate #F -> #H
      <- UnconditionalJump[GOTO] #VQ -> #F
      <- Switch[330419384] #VP -> #F
===#Block H(size=1, flags=0)===
   0. goto LP
      -> UnconditionalJump[GOTO] #H -> #LP
      <- Immediate #F -> #H
===#Block LP(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 46034426:
      	 goto	#LQ
      case 444782430:
      	 goto	#HD
      case 739690612:
      	 goto	#JC
      case 2014652092:
      	 goto	#LP
      default:
      	 goto	#JC
   }
      -> Switch[46034426] #LP -> #LQ
      -> Immediate #LP -> #LQ
      -> Switch[444782430] #LP -> #HD
      -> Switch[2014652092] #LP -> #LP
      -> DefaultSwitch #LP -> #JC
      -> Switch[739690612] #LP -> #JC
      <- Switch[2014652092] #LP -> #LP
      <- UnconditionalJump[GOTO] #H -> #LP
===#Block LQ(size=2, flags=100)===
   0. lvar105 = {1265533973 ^ lvar105};
   1. goto HD
      -> UnconditionalJump[GOTO] #LQ -> #HD
      <- Switch[46034426] #LP -> #LQ
      <- Immediate #LP -> #LQ
===#Block HD(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 229045820)
      goto HC
   1. throw nullconst;
      -> TryCatch range: [HD...HC] -> ACC ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HD -> #HC
      <- Switch[444782430] #LP -> #HD
      <- UnconditionalJump[GOTO] #LQ -> #HD
===#Block HC(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HD...HC] -> ACC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HD -> #HC
===#Block ACC(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1963839742:
      	 goto	#ACD
      case 526973562:
      	 goto	#ACE
      default:
      	 goto	#ACF
   }
      -> Switch[526973562] #ACC -> #ACE
      -> DefaultSwitch #ACC -> #ACF
      -> Switch[-1963839742] #ACC -> #ACD
      <- TryCatch range: [HD...HC] -> ACC ([Ljava/io/IOException;])
      <- TryCatch range: [HD...HC] -> ACC ([Ljava/io/IOException;])
===#Block ACD(size=2, flags=10100)===
   0. lvar105 = {686836203 ^ lvar105};
   1. goto HE
      -> UnconditionalJump[GOTO] #ACD -> #HE
      <- Switch[-1963839742] #ACC -> #ACD
===#Block ACF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACC -> #ACF
===#Block ACE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 977474372);
   1. goto HE
      -> UnconditionalJump[GOTO] #ACE -> #HE
      <- Switch[526973562] #ACC -> #ACE
===#Block HE(size=2, flags=0)===
   0. _consume(catch());
   1. goto LZ
      -> UnconditionalJump[GOTO] #HE -> #LZ
      <- UnconditionalJump[GOTO] #ACE -> #HE
      <- UnconditionalJump[GOTO] #ACD -> #HE
===#Block LZ(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 197218043:
      	 goto	#MA
      case 943885519:
      	 goto	#LZ
      case 1346340451:
      	 goto	#AG
      case 1779146031:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #LZ -> #JC
      -> Switch[1779146031] #LZ -> #JC
      -> Switch[1346340451] #LZ -> #AG
      -> Switch[197218043] #LZ -> #MA
      -> Switch[943885519] #LZ -> #LZ
      -> Immediate #LZ -> #MA
      <- Switch[943885519] #LZ -> #LZ
      <- UnconditionalJump[GOTO] #HE -> #LZ
===#Block MA(size=2, flags=100)===
   0. lvar105 = {1150041516 ^ lvar105};
   1. goto AG
      -> UnconditionalJump[GOTO] #MA -> #AG
      <- Switch[197218043] #LZ -> #MA
      <- Immediate #LZ -> #MA
===#Block RX(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 147701489:
      	 goto	#JC
      case 153087110:
      	 goto	#RY
      case 334133191:
      	 goto	#RX
      case 1023915263:
      	 goto	#JG
      default:
      	 goto	#JC
   }
      -> Switch[1023915263] #RX -> #JG
      -> Immediate #RX -> #RY
      -> Switch[147701489] #RX -> #JC
      -> Switch[334133191] #RX -> #RX
      -> Switch[153087110] #RX -> #RY
      -> DefaultSwitch #RX -> #JC
      <- ConditionalJump[IF_ICMPNE] #F -> #RX
      <- Switch[334133191] #RX -> #RX
===#Block RY(size=2, flags=100)===
   0. lvar105 = {1227301292 ^ lvar105};
   1. goto JG
      -> UnconditionalJump[GOTO] #RY -> #JG
      <- Immediate #RX -> #RY
      <- Switch[153087110] #RX -> #RY
===#Block JG(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1951668279)
      goto TM
   1. goto LY
      -> UnconditionalJump[GOTO] #JG -> #LY
      -> ConditionalJump[IF_ICMPEQ] #JG -> #TM
      <- UnconditionalJump[GOTO] #RY -> #JG
      <- Switch[1023915263] #RX -> #JG
===#Block TM(size=2, flags=10100)===
   0. lvar105 = {1730281357 ^ lvar105};
   1. goto G
      -> UnconditionalJump[GOTO] #TM -> #G
      <- ConditionalJump[IF_ICMPEQ] #JG -> #TM
===#Block G(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_GLAZED_TERRACOTTA;
   2. goto NX
      -> UnconditionalJump[GOTO] #G -> #NX
      <- UnconditionalJump[GOTO] #TM -> #G
===#Block NX(size=2, flags=10100)===
   0. lvar105 = {190304566 ^ lvar105};
   1. goto IZ
      -> UnconditionalJump[GOTO] #NX -> #IZ
      <- UnconditionalJump[GOTO] #G -> #NX
===#Block IZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 70660700)
      goto IY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IZ -> #IY
      -> TryCatch range: [IZ...IY] -> AEO ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #NX -> #IZ
===#Block IY(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IZ...IY] -> AEO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IZ -> #IY
===#Block AEO(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1745172704:
      	 goto	#AEQ
      case -400911854:
      	 goto	#AEP
      default:
      	 goto	#AER
   }
      -> DefaultSwitch #AEO -> #AER
      -> Switch[-1745172704] #AEO -> #AEQ
      -> Switch[-400911854] #AEO -> #AEP
      <- TryCatch range: [IZ...IY] -> AEO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IZ...IY] -> AEO ([Ljava/lang/RuntimeException;])
===#Block AEP(size=2, flags=10100)===
   0. lvar105 = {2112120909 ^ lvar105};
   1. goto JA
      -> UnconditionalJump[GOTO] #AEP -> #JA
      <- Switch[-400911854] #AEO -> #AEP
===#Block AEQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 840458987);
   1. goto JA
      -> UnconditionalJump[GOTO] #AEQ -> #JA
      <- Switch[-1745172704] #AEO -> #AEQ
===#Block JA(size=2, flags=0)===
   0. _consume(catch());
   1. goto MK
      -> UnconditionalJump[GOTO] #JA -> #MK
      <- UnconditionalJump[GOTO] #AEQ -> #JA
      <- UnconditionalJump[GOTO] #AEP -> #JA
===#Block MK(size=2, flags=10100)===
   0. lvar105 = {156880258 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #MK -> #CN
      <- UnconditionalJump[GOTO] #JA -> #MK
===#Block AER(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AEO -> #AER
===#Block LY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 553777808);
   1. goto JC
      -> UnconditionalJump[GOTO] #LY -> #JC
      <- UnconditionalJump[GOTO] #JG -> #LY
===#Block VV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1979771562);
   1. goto AD
      -> UnconditionalJump[GOTO] #VV -> #AD
      <- Switch[83435499] #E -> #VV
===#Block AD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar31 = lvar9;
   2. lvar83 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.dgikvsphsbmhdtx(), lvar105);
   3. lvar32 = lvar31.equals(lvar83);
   4. if (lvar32 != {1937590793 ^ lvar105})
      goto SX
   5. lvar105 = {570766356 ^ lvar105};
      -> Immediate #AD -> #AF
      -> ConditionalJump[IF_ICMPNE] #AD -> #SX
      <- UnconditionalJump[GOTO] #VV -> #AD
===#Block SX(size=2, flags=10100)===
   0. lvar105 = {2025599759 ^ lvar105};
   1. goto JZ
      -> UnconditionalJump[GOTO] #SX -> #JZ
      <- ConditionalJump[IF_ICMPNE] #AD -> #SX
===#Block JZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1577830448)
      goto TL
   1. goto LE
      -> ConditionalJump[IF_ICMPEQ] #JZ -> #TL
      -> UnconditionalJump[GOTO] #JZ -> #LE
      <- UnconditionalJump[GOTO] #SX -> #JZ
===#Block LE(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 113693114:
      	 goto	#LF
      case 648572197:
      	 goto	#JC
      case 1332300539:
      	 goto	#JC
      case 1699042163:
      	 goto	#LE
      default:
      	 goto	#JC
   }
      -> Switch[648572197] #LE -> #JC
      -> DefaultSwitch #LE -> #JC
      -> Switch[1699042163] #LE -> #LE
      -> Immediate #LE -> #LF
      -> Switch[113693114] #LE -> #LF
      <- Switch[1699042163] #LE -> #LE
      <- UnconditionalJump[GOTO] #JZ -> #LE
===#Block LF(size=2, flags=100)===
   0. lvar105 = {464912239 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #LF -> #JC
      <- Immediate #LE -> #LF
      <- Switch[113693114] #LE -> #LF
===#Block TL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1674820250);
   1. goto AE
      -> UnconditionalJump[GOTO] #TL -> #AE
      <- ConditionalJump[IF_ICMPEQ] #JZ -> #TL
===#Block AE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.MAGENTA_GLAZED_TERRACOTTA;
   2. goto NM
      -> UnconditionalJump[GOTO] #AE -> #NM
      <- UnconditionalJump[GOTO] #TL -> #AE
===#Block NM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1130438453);
   1. goto HJ
      -> UnconditionalJump[GOTO] #NM -> #HJ
      <- UnconditionalJump[GOTO] #AE -> #NM
===#Block HJ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 70275316)
      goto HI
   1. throw nullconst;
      -> TryCatch range: [HJ...HI] -> ACK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HJ -> #HI
      <- UnconditionalJump[GOTO] #NM -> #HJ
===#Block HI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HJ...HI] -> ACK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HJ -> #HI
===#Block ACK(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 1473421184:
      	 goto	#ACM
      case 1537140041:
      	 goto	#ACL
      default:
      	 goto	#ACN
   }
      -> DefaultSwitch #ACK -> #ACN
      -> Switch[1537140041] #ACK -> #ACL
      -> Switch[1473421184] #ACK -> #ACM
      <- TryCatch range: [HJ...HI] -> ACK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HJ...HI] -> ACK ([Ljava/lang/IllegalAccessException;])
===#Block ACM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1936699417);
   1. goto HK
      -> UnconditionalJump[GOTO] #ACM -> #HK
      <- Switch[1473421184] #ACK -> #ACM
===#Block ACL(size=2, flags=10100)===
   0. lvar105 = {1390821184 ^ lvar105};
   1. goto HK
      -> UnconditionalJump[GOTO] #ACL -> #HK
      <- Switch[1537140041] #ACK -> #ACL
===#Block HK(size=2, flags=0)===
   0. _consume(catch());
   1. goto NN
      -> UnconditionalJump[GOTO] #HK -> #NN
      <- UnconditionalJump[GOTO] #ACM -> #HK
      <- UnconditionalJump[GOTO] #ACL -> #HK
===#Block NN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1345000548);
   1. goto CN
      -> UnconditionalJump[GOTO] #NN -> #CN
      <- UnconditionalJump[GOTO] #HK -> #NN
===#Block ACN(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ACK -> #ACN
===#Block AF(size=1, flags=0)===
   0. goto KG
      -> UnconditionalJump[GOTO] #AF -> #KG
      <- Immediate #AD -> #AF
===#Block KG(size=2, flags=10100)===
   0. lvar105 = {********* ^ lvar105};
   1. goto DC
      -> UnconditionalJump[GOTO] #KG -> #DC
      <- UnconditionalJump[GOTO] #AF -> #KG
===#Block DC(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == *********)
      goto DB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DC -> #DB
      -> TryCatch range: [DC...DB] -> WS ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #KG -> #DC
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DC...DB] -> WS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DC -> #DB
===#Block WS(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 1106714907:
      	 goto	#WT
      case 1912077170:
      	 goto	#WU
      default:
      	 goto	#WV
   }
      -> Switch[1912077170] #WS -> #WU
      -> DefaultSwitch #WS -> #WV
      -> Switch[1106714907] #WS -> #WT
      <- TryCatch range: [DC...DB] -> WS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DC...DB] -> WS ([Ljava/lang/IllegalAccessException;])
===#Block WT(size=2, flags=10100)===
   0. lvar105 = {2011549798 ^ lvar105};
   1. goto DD
      -> UnconditionalJump[GOTO] #WT -> #DD
      <- Switch[1106714907] #WS -> #WT
===#Block WV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WS -> #WV
===#Block WU(size=2, flags=10100)===
   0. lvar105 = {1374014251 ^ lvar105};
   1. goto DD
      -> UnconditionalJump[GOTO] #WU -> #DD
      <- Switch[1912077170] #WS -> #WU
===#Block DD(size=2, flags=0)===
   0. _consume(catch());
   1. goto MQ
      -> UnconditionalJump[GOTO] #DD -> #MQ
      <- UnconditionalJump[GOTO] #WT -> #DD
      <- UnconditionalJump[GOTO] #WU -> #DD
===#Block MQ(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 162188319:
      	 goto	#MQ
      case 230206110:
      	 goto	#MR
      case 587696948:
      	 goto	#JC
      case 1180511050:
      	 goto	#AG
      default:
      	 goto	#JC
   }
      -> Immediate #MQ -> #MR
      -> Switch[1180511050] #MQ -> #AG
      -> DefaultSwitch #MQ -> #JC
      -> Switch[230206110] #MQ -> #MR
      -> Switch[162188319] #MQ -> #MQ
      -> Switch[587696948] #MQ -> #JC
      <- UnconditionalJump[GOTO] #DD -> #MQ
      <- Switch[162188319] #MQ -> #MQ
===#Block MR(size=2, flags=100)===
   0. lvar105 = {586745314 ^ lvar105};
   1. goto AG
      -> UnconditionalJump[GOTO] #MR -> #AG
      <- Immediate #MQ -> #MR
      <- Switch[230206110] #MQ -> #MR
===#Block VW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1568621074);
   1. goto L
      -> UnconditionalJump[GOTO] #VW -> #L
      <- Switch[83435500] #E -> #VW
===#Block L(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar9;
   2. lvar77 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.xisougacrxermzt(), lvar105);
   3. lvar20 = lvar19.equals(lvar77);
   4. if (lvar20 != {1476584113 ^ lvar105})
      goto UO
   5. lvar105 = {961770050 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #L -> #UO
      -> Immediate #L -> #N
      <- UnconditionalJump[GOTO] #VW -> #L
===#Block N(size=1, flags=0)===
   0. goto NR
      -> UnconditionalJump[GOTO] #N -> #NR
      <- Immediate #L -> #N
===#Block NR(size=2, flags=10100)===
   0. lvar105 = {1997688036 ^ lvar105};
   1. goto HY
      -> UnconditionalJump[GOTO] #NR -> #HY
      <- UnconditionalJump[GOTO] #N -> #NR
===#Block HY(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 186870831)
      goto HX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HY -> #HX
      -> TryCatch range: [HY...HX] -> ADE ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #NR -> #HY
===#Block HX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HY...HX] -> ADE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HY -> #HX
===#Block ADE(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1306599240:
      	 goto	#ADF
      case -744478661:
      	 goto	#ADG
      default:
      	 goto	#ADH
   }
      -> DefaultSwitch #ADE -> #ADH
      -> Switch[-1306599240] #ADE -> #ADF
      -> Switch[-744478661] #ADE -> #ADG
      <- TryCatch range: [HY...HX] -> ADE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HY...HX] -> ADE ([Ljava/lang/IllegalAccessException;])
===#Block ADG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 2135356490);
   1. goto HZ
      -> UnconditionalJump[GOTO] #ADG -> #HZ
      <- Switch[-744478661] #ADE -> #ADG
===#Block ADF(size=2, flags=10100)===
   0. lvar105 = {326184282 ^ lvar105};
   1. goto HZ
      -> UnconditionalJump[GOTO] #ADF -> #HZ
      <- Switch[-1306599240] #ADE -> #ADF
===#Block HZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto NO
      -> UnconditionalJump[GOTO] #HZ -> #NO
      <- UnconditionalJump[GOTO] #ADF -> #HZ
      <- UnconditionalJump[GOTO] #ADG -> #HZ
===#Block NO(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 1386580:
      	 goto	#AG
      case 81774346:
      	 goto	#NP
      case 123407018:
      	 goto	#JC
      case 1107346028:
      	 goto	#NO
      default:
      	 goto	#JC
   }
      -> Immediate #NO -> #NP
      -> Switch[1107346028] #NO -> #NO
      -> Switch[81774346] #NO -> #NP
      -> Switch[1386580] #NO -> #AG
      -> DefaultSwitch #NO -> #JC
      -> Switch[123407018] #NO -> #JC
      <- Switch[1107346028] #NO -> #NO
      <- UnconditionalJump[GOTO] #HZ -> #NO
===#Block NP(size=2, flags=100)===
   0. lvar105 = {940622058 ^ lvar105};
   1. goto AG
      -> UnconditionalJump[GOTO] #NP -> #AG
      <- Immediate #NO -> #NP
      <- Switch[81774346] #NO -> #NP
===#Block ADH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADE -> #ADH
===#Block UO(size=2, flags=10100)===
   0. lvar105 = {1590596092 ^ lvar105};
   1. goto KE
      -> UnconditionalJump[GOTO] #UO -> #KE
      <- ConditionalJump[IF_ICMPNE] #L -> #UO
===#Block KE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 912497256)
      goto UH
   1. goto PQ
      -> UnconditionalJump[GOTO] #KE -> #PQ
      -> ConditionalJump[IF_ICMPEQ] #KE -> #UH
      <- UnconditionalJump[GOTO] #UO -> #KE
===#Block UH(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 106932948:
      	 goto	#UI
      case 324393210:
      	 goto	#UH
      case 706596519:
      	 goto	#JC
      case 1559433678:
      	 goto	#M
      default:
      	 goto	#JC
   }
      -> Switch[106932948] #UH -> #UI
      -> Immediate #UH -> #UI
      -> Switch[324393210] #UH -> #UH
      -> Switch[1559433678] #UH -> #M
      -> DefaultSwitch #UH -> #JC
      -> Switch[706596519] #UH -> #JC
      <- Switch[324393210] #UH -> #UH
      <- ConditionalJump[IF_ICMPEQ] #KE -> #UH
===#Block UI(size=2, flags=100)===
   0. lvar105 = {781414721 ^ lvar105};
   1. goto M
      -> UnconditionalJump[GOTO] #UI -> #M
      <- Switch[106932948] #UH -> #UI
      <- Immediate #UH -> #UI
===#Block M(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_GLAZED_TERRACOTTA;
   2. goto MH
      -> UnconditionalJump[GOTO] #M -> #MH
      <- UnconditionalJump[GOTO] #UI -> #M
      <- Switch[1559433678] #UH -> #M
===#Block MH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 483576999);
   1. goto CQ
      -> UnconditionalJump[GOTO] #MH -> #CQ
      <- UnconditionalJump[GOTO] #M -> #MH
===#Block CQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 97711334)
      goto CP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CQ -> #CP
      -> TryCatch range: [CQ...CP] -> WC ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #MH -> #CQ
===#Block CP(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CQ...CP] -> WC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CQ -> #CP
===#Block WC(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1969096325:
      	 goto	#WE
      case -1536264871:
      	 goto	#WD
      default:
      	 goto	#WF
   }
      -> Switch[-1969096325] #WC -> #WE
      -> Switch[-1536264871] #WC -> #WD
      -> DefaultSwitch #WC -> #WF
      <- TryCatch range: [CQ...CP] -> WC ([Ljava/io/IOException;])
      <- TryCatch range: [CQ...CP] -> WC ([Ljava/io/IOException;])
===#Block WF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WC -> #WF
===#Block WD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 754347005);
   1. goto CR
      -> UnconditionalJump[GOTO] #WD -> #CR
      <- Switch[-1536264871] #WC -> #WD
===#Block WE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1764727417);
   1. goto CR
      -> UnconditionalJump[GOTO] #WE -> #CR
      <- Switch[-1969096325] #WC -> #WE
===#Block CR(size=2, flags=0)===
   0. _consume(catch());
   1. goto OS
      -> UnconditionalJump[GOTO] #CR -> #OS
      <- UnconditionalJump[GOTO] #WE -> #CR
      <- UnconditionalJump[GOTO] #WD -> #CR
===#Block OS(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 135279667:
      	 goto	#CN
      case 258967597:
      	 goto	#OT
      case 1649867366:
      	 goto	#OS
      case 1982373174:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[258967597] #OS -> #OT
      -> Switch[135279667] #OS -> #CN
      -> DefaultSwitch #OS -> #JC
      -> Switch[1649867366] #OS -> #OS
      -> Immediate #OS -> #OT
      -> Switch[1982373174] #OS -> #JC
      <- Switch[1649867366] #OS -> #OS
      <- UnconditionalJump[GOTO] #CR -> #OS
===#Block OT(size=2, flags=100)===
   0. lvar105 = {835015387 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OT -> #CN
      <- Switch[258967597] #OS -> #OT
      <- Immediate #OS -> #OT
===#Block PQ(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 106932948:
      	 goto	#PR
      case 604759149:
      	 goto	#PQ
      case 1408752236:
      	 goto	#JC
      case 1746109716:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[604759149] #PQ -> #PQ
      -> Switch[106932948] #PQ -> #PR
      -> Immediate #PQ -> #PR
      -> DefaultSwitch #PQ -> #JC
      -> Switch[1746109716] #PQ -> #JC
      <- Switch[604759149] #PQ -> #PQ
      <- UnconditionalJump[GOTO] #KE -> #PQ
===#Block PR(size=2, flags=100)===
   0. lvar105 = {381220132 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PR -> #JC
      <- Switch[106932948] #PQ -> #PR
      <- Immediate #PQ -> #PR
===#Block WA(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 86361061:
      	 goto	#WB
      case 647704295:
      	 goto	#AG
      case 1203487958:
      	 goto	#JC
      case 2011805952:
      	 goto	#WA
      default:
      	 goto	#JC
   }
      -> Switch[2011805952] #WA -> #WA
      -> Immediate #WA -> #WB
      -> Switch[647704295] #WA -> #AG
      -> DefaultSwitch #WA -> #JC
      -> Switch[1203487958] #WA -> #JC
      -> Switch[86361061] #WA -> #WB
      <- Switch[2011805952] #WA -> #WA
      <- DefaultSwitch #E -> #WA
===#Block WB(size=2, flags=100)===
   0. lvar105 = {945787140 ^ lvar105};
   1. goto AG
      -> UnconditionalJump[GOTO] #WB -> #AG
      <- Immediate #WA -> #WB
      <- Switch[86361061] #WA -> #WB
===#Block VT(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 86361061:
      	 goto	#VU
      case 1085537559:
      	 goto	#VT
      case 1377943361:
      	 goto	#JC
      case 1992110565:
      	 goto	#AA
      default:
      	 goto	#JC
   }
      -> Switch[1992110565] #VT -> #AA
      -> DefaultSwitch #VT -> #JC
      -> Switch[86361061] #VT -> #VU
      -> Switch[1085537559] #VT -> #VT
      -> Switch[1377943361] #VT -> #JC
      -> Immediate #VT -> #VU
      <- Switch[83435496] #E -> #VT
      <- Switch[1085537559] #VT -> #VT
===#Block VU(size=2, flags=100)===
   0. lvar105 = {2046905101 ^ lvar105};
   1. goto AA
      -> UnconditionalJump[GOTO] #VU -> #AA
      <- Switch[86361061] #VT -> #VU
      <- Immediate #VT -> #VU
===#Block AA(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar9;
   2. lvar82 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.bqvombrsmtgxach(), lvar105);
   3. lvar30 = lvar29.equals(lvar82);
   4. if (lvar30 != {2138893230 ^ lvar105})
      goto SI
   5. lvar105 = {248296482 ^ lvar105};
      -> Immediate #AA -> #AB
      -> ConditionalJump[IF_ICMPNE] #AA -> #SI
      <- Switch[1992110565] #VT -> #AA
      <- UnconditionalJump[GOTO] #VU -> #AA
===#Block SI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 494987838);
   1. goto JO
      -> UnconditionalJump[GOTO] #SI -> #JO
      <- ConditionalJump[IF_ICMPNE] #AA -> #SI
===#Block JO(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 400559235)
      goto UB
   1. goto PF
      -> ConditionalJump[IF_ICMPEQ] #JO -> #UB
      -> UnconditionalJump[GOTO] #JO -> #PF
      <- UnconditionalJump[GOTO] #SI -> #JO
===#Block PF(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 79634127:
      	 goto	#JC
      case 264774139:
      	 goto	#PG
      case 456562622:
      	 goto	#PF
      case 1363361327:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #PF -> #PG
      -> Switch[456562622] #PF -> #PF
      -> Switch[264774139] #PF -> #PG
      -> Switch[79634127] #PF -> #JC
      -> DefaultSwitch #PF -> #JC
      <- UnconditionalJump[GOTO] #JO -> #PF
      <- Switch[456562622] #PF -> #PF
===#Block PG(size=2, flags=100)===
   0. lvar105 = {1921550329 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PG -> #JC
      <- Immediate #PF -> #PG
      <- Switch[264774139] #PF -> #PG
===#Block UB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1412998220);
   1. goto AC
      -> UnconditionalJump[GOTO] #UB -> #AC
      <- ConditionalJump[IF_ICMPEQ] #JO -> #UB
===#Block AC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_GLAZED_TERRACOTTA;
   2. goto PW
      -> UnconditionalJump[GOTO] #AC -> #PW
      <- UnconditionalJump[GOTO] #UB -> #AC
===#Block PW(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 60069382:
      	 goto	#JC
      case 169686942:
      	 goto	#PX
      case 1358382510:
      	 goto	#CW
      case 1847983806:
      	 goto	#PW
      default:
      	 goto	#JC
   }
      -> Switch[60069382] #PW -> #JC
      -> DefaultSwitch #PW -> #JC
      -> Switch[1847983806] #PW -> #PW
      -> Immediate #PW -> #PX
      -> Switch[169686942] #PW -> #PX
      -> Switch[1358382510] #PW -> #CW
      <- Switch[1847983806] #PW -> #PW
      <- UnconditionalJump[GOTO] #AC -> #PW
===#Block PX(size=2, flags=100)===
   0. lvar105 = {1536816587 ^ lvar105};
   1. goto CW
      -> UnconditionalJump[GOTO] #PX -> #CW
      <- Immediate #PW -> #PX
      <- Switch[169686942] #PW -> #PX
===#Block CW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 65316465)
      goto CV
   1. throw nullconst;
      -> TryCatch range: [CW...CV] -> WK ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CW -> #CV
      <- UnconditionalJump[GOTO] #PX -> #CW
      <- Switch[1358382510] #PW -> #CW
===#Block CV(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CW...CV] -> WK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CW -> #CV
===#Block WK(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1336153864:
      	 goto	#WM
      case 1793753275:
      	 goto	#WL
      default:
      	 goto	#WN
   }
      -> Switch[-1336153864] #WK -> #WM
      -> DefaultSwitch #WK -> #WN
      -> Switch[1793753275] #WK -> #WL
      <- TryCatch range: [CW...CV] -> WK ([Ljava/io/IOException;])
      <- TryCatch range: [CW...CV] -> WK ([Ljava/io/IOException;])
===#Block WL(size=2, flags=10100)===
   0. lvar105 = {1006517021 ^ lvar105};
   1. goto CX
      -> UnconditionalJump[GOTO] #WL -> #CX
      <- Switch[1793753275] #WK -> #WL
===#Block WN(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WK -> #WN
===#Block WM(size=2, flags=10100)===
   0. lvar105 = {1084757525 ^ lvar105};
   1. goto CX
      -> UnconditionalJump[GOTO] #WM -> #CX
      <- Switch[-1336153864] #WK -> #WM
===#Block CX(size=2, flags=0)===
   0. _consume(catch());
   1. goto QR
      -> UnconditionalJump[GOTO] #CX -> #QR
      <- UnconditionalJump[GOTO] #WL -> #CX
      <- UnconditionalJump[GOTO] #WM -> #CX
===#Block QR(size=2, flags=10100)===
   0. lvar105 = {2132647047 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QR -> #CN
      <- UnconditionalJump[GOTO] #CX -> #QR
===#Block AB(size=1, flags=0)===
   0. goto OU
      -> UnconditionalJump[GOTO] #AB -> #OU
      <- Immediate #AA -> #AB
===#Block OU(size=2, flags=10100)===
   0. lvar105 = {221020792 ^ lvar105};
   1. goto FH
      -> UnconditionalJump[GOTO] #OU -> #FH
      <- UnconditionalJump[GOTO] #AB -> #OU
===#Block FH(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 24089236)
      goto FG
   1. throw nullconst;
      -> TryCatch range: [FH...FG] -> ZQ ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #FH -> #FG
      <- UnconditionalJump[GOTO] #OU -> #FH
===#Block FG(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FH...FG] -> ZQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FH -> #FG
===#Block ZQ(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -454627421:
      	 goto	#ZR
      case 1904329706:
      	 goto	#ZS
      default:
      	 goto	#ZT
   }
      -> Switch[-454627421] #ZQ -> #ZR
      -> Switch[1904329706] #ZQ -> #ZS
      -> DefaultSwitch #ZQ -> #ZT
      <- TryCatch range: [FH...FG] -> ZQ ([Ljava/io/IOException;])
      <- TryCatch range: [FH...FG] -> ZQ ([Ljava/io/IOException;])
===#Block ZT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZQ -> #ZT
===#Block ZS(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1057082600);
   1. goto FI
      -> UnconditionalJump[GOTO] #ZS -> #FI
      <- Switch[1904329706] #ZQ -> #ZS
===#Block ZR(size=2, flags=10100)===
   0. lvar105 = {229452129 ^ lvar105};
   1. goto FI
      -> UnconditionalJump[GOTO] #ZR -> #FI
      <- Switch[-454627421] #ZQ -> #ZR
===#Block FI(size=2, flags=0)===
   0. _consume(catch());
   1. goto OZ
      -> UnconditionalJump[GOTO] #FI -> #OZ
      <- UnconditionalJump[GOTO] #ZS -> #FI
      <- UnconditionalJump[GOTO] #ZR -> #FI
===#Block OZ(size=2, flags=10100)===
   0. lvar105 = {1276364082 ^ lvar105};
   1. goto AG
      -> UnconditionalJump[GOTO] #OZ -> #AG
      <- UnconditionalJump[GOTO] #FI -> #OZ
===#Block VS(size=2, flags=10100)===
   0. lvar105 = {506146698 ^ lvar105};
   1. goto I
      -> UnconditionalJump[GOTO] #VS -> #I
      <- Switch[83435474] #E -> #VS
===#Block I(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar9;
   2. lvar76 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.dtgshdrykivjlph(), lvar105);
   3. lvar18 = lvar17.equals(lvar76);
   4. if (lvar18 != {458657577 ^ lvar105})
      goto SH
   5. lvar105 = {765341743 ^ lvar105};
      -> Immediate #I -> #K
      -> ConditionalJump[IF_ICMPNE] #I -> #SH
      <- UnconditionalJump[GOTO] #VS -> #I
===#Block SH(size=2, flags=10100)===
   0. lvar105 = {1821188997 ^ lvar105};
   1. goto JN
      -> UnconditionalJump[GOTO] #SH -> #JN
      <- ConditionalJump[IF_ICMPNE] #I -> #SH
===#Block JN(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1092786845)
      goto SY
   1. goto PU
      -> UnconditionalJump[GOTO] #JN -> #PU
      -> ConditionalJump[IF_ICMPEQ] #JN -> #SY
      <- UnconditionalJump[GOTO] #SH -> #JN
===#Block SY(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 138013014:
      	 goto	#SZ
      case 257948040:
      	 goto	#JC
      case 887713794:
      	 goto	#J
      case 1976516021:
      	 goto	#SY
      default:
      	 goto	#JC
   }
      -> Immediate #SY -> #SZ
      -> Switch[1976516021] #SY -> #SY
      -> Switch[257948040] #SY -> #JC
      -> DefaultSwitch #SY -> #JC
      -> Switch[887713794] #SY -> #J
      -> Switch[138013014] #SY -> #SZ
      <- ConditionalJump[IF_ICMPEQ] #JN -> #SY
      <- Switch[1976516021] #SY -> #SY
===#Block SZ(size=2, flags=100)===
   0. lvar105 = {1213655112 ^ lvar105};
   1. goto J
      -> UnconditionalJump[GOTO] #SZ -> #J
      <- Immediate #SY -> #SZ
      <- Switch[138013014] #SY -> #SZ
===#Block J(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_GLAZED_TERRACOTTA;
   2. goto QA
      -> UnconditionalJump[GOTO] #J -> #QA
      <- UnconditionalJump[GOTO] #SZ -> #J
      <- Switch[887713794] #SY -> #J
===#Block QA(size=2, flags=10100)===
   0. lvar105 = {1808059177 ^ lvar105};
   1. goto DF
      -> UnconditionalJump[GOTO] #QA -> #DF
      <- UnconditionalJump[GOTO] #J -> #QA
===#Block DF(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 55497428)
      goto DE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DF -> #DE
      -> TryCatch range: [DF...DE] -> WW ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #QA -> #DF
===#Block DE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DF...DE] -> WW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DF -> #DE
===#Block WW(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1572045206:
      	 goto	#WX
      case -87217333:
      	 goto	#WY
      default:
      	 goto	#WZ
   }
      -> DefaultSwitch #WW -> #WZ
      -> Switch[-87217333] #WW -> #WY
      -> Switch[-1572045206] #WW -> #WX
      <- TryCatch range: [DF...DE] -> WW ([Ljava/io/IOException;])
      <- TryCatch range: [DF...DE] -> WW ([Ljava/io/IOException;])
===#Block WX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1931841399);
   1. goto DG
      -> UnconditionalJump[GOTO] #WX -> #DG
      <- Switch[-1572045206] #WW -> #WX
===#Block WY(size=2, flags=10100)===
   0. lvar105 = {1479910867 ^ lvar105};
   1. goto DG
      -> UnconditionalJump[GOTO] #WY -> #DG
      <- Switch[-87217333] #WW -> #WY
===#Block DG(size=2, flags=0)===
   0. _consume(catch());
   1. goto RM
      -> UnconditionalJump[GOTO] #DG -> #RM
      <- UnconditionalJump[GOTO] #WY -> #DG
      <- UnconditionalJump[GOTO] #WX -> #DG
===#Block RM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 248706871);
   1. goto CN
      -> UnconditionalJump[GOTO] #RM -> #CN
      <- UnconditionalJump[GOTO] #DG -> #RM
===#Block WZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #WW -> #WZ
===#Block PU(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 8388374:
      	 goto	#PU
      case 138013014:
      	 goto	#PV
      case 1075031567:
      	 goto	#JC
      case 1531335126:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #PU -> #PV
      -> DefaultSwitch #PU -> #JC
      -> Switch[1075031567] #PU -> #JC
      -> Switch[8388374] #PU -> #PU
      -> Switch[138013014] #PU -> #PV
      <- UnconditionalJump[GOTO] #JN -> #PU
      <- Switch[8388374] #PU -> #PU
===#Block PV(size=2, flags=100)===
   0. lvar105 = {1739532997 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PV -> #JC
      <- Immediate #PU -> #PV
      <- Switch[138013014] #PU -> #PV
===#Block K(size=1, flags=0)===
   0. goto PN
      -> UnconditionalJump[GOTO] #K -> #PN
      <- Immediate #I -> #K
===#Block PN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 277208672);
   1. goto IH
      -> UnconditionalJump[GOTO] #PN -> #IH
      <- UnconditionalJump[GOTO] #K -> #PN
===#Block IH(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 171297528)
      goto IG
   1. throw nullconst;
      -> TryCatch range: [IH...IG] -> ADQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IH -> #IG
      <- UnconditionalJump[GOTO] #PN -> #IH
===#Block IG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IH...IG] -> ADQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IH -> #IG
===#Block ADQ(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 564370635:
      	 goto	#ADS
      case 845876017:
      	 goto	#ADR
      default:
      	 goto	#ADT
   }
      -> Switch[845876017] #ADQ -> #ADR
      -> Switch[564370635] #ADQ -> #ADS
      -> DefaultSwitch #ADQ -> #ADT
      <- TryCatch range: [IH...IG] -> ADQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IH...IG] -> ADQ ([Ljava/lang/IllegalAccessException;])
===#Block ADT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ADQ -> #ADT
===#Block ADS(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 403796291);
   1. goto II
      -> UnconditionalJump[GOTO] #ADS -> #II
      <- Switch[564370635] #ADQ -> #ADS
===#Block ADR(size=2, flags=10100)===
   0. lvar105 = {1516790716 ^ lvar105};
   1. goto II
      -> UnconditionalJump[GOTO] #ADR -> #II
      <- Switch[845876017] #ADQ -> #ADR
===#Block II(size=2, flags=0)===
   0. _consume(catch());
   1. goto RB
      -> UnconditionalJump[GOTO] #II -> #RB
      <- UnconditionalJump[GOTO] #ADR -> #II
      <- UnconditionalJump[GOTO] #ADS -> #II
===#Block RB(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 8898979:
      	 goto	#RC
      case 1180676239:
      	 goto	#RB
      case 1910505643:
      	 goto	#JC
      case 1971509612:
      	 goto	#AG
      default:
      	 goto	#JC
   }
      -> Switch[1971509612] #RB -> #AG
      -> DefaultSwitch #RB -> #JC
      -> Switch[1180676239] #RB -> #RB
      -> Switch[1910505643] #RB -> #JC
      -> Switch[8898979] #RB -> #RC
      -> Immediate #RB -> #RC
      <- Switch[1180676239] #RB -> #RB
      <- UnconditionalJump[GOTO] #II -> #RB
===#Block RC(size=2, flags=100)===
   0. lvar105 = {1090990973 ^ lvar105};
   1. goto AG
      -> UnconditionalJump[GOTO] #RC -> #AG
      <- Switch[8898979] #RB -> #RC
      <- Immediate #RB -> #RC
===#Block VR(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1208590877);
   1. goto R
      -> UnconditionalJump[GOTO] #VR -> #R
      <- Switch[83435473] #E -> #VR
===#Block R(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = lvar9;
   2. lvar79 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.hrvishchoqoyptr(), lvar105);
   3. lvar24 = lvar23.equals(lvar79);
   4. if (lvar24 != {1299448510 ^ lvar105})
      goto SG
   5. lvar105 = {264169681 ^ lvar105};
      -> Immediate #R -> #S
      -> ConditionalJump[IF_ICMPNE] #R -> #SG
      <- UnconditionalJump[GOTO] #VR -> #R
===#Block SG(size=2, flags=10100)===
   0. lvar105 = {2006280238 ^ lvar105};
   1. goto JL
      -> UnconditionalJump[GOTO] #SG -> #JL
      <- ConditionalJump[IF_ICMPNE] #R -> #SG
===#Block JL(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -687123327)
      goto UK
   1. goto QX
      -> ConditionalJump[IF_ICMPEQ] #JL -> #UK
      -> UnconditionalJump[GOTO] #JL -> #QX
      <- UnconditionalJump[GOTO] #SG -> #JL
===#Block QX(size=2, flags=10100)===
   0. lvar105 = {714468601 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #QX -> #JC
      <- UnconditionalJump[GOTO] #JL -> #QX
===#Block UK(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 34899094:
      	 goto	#UL
      case 404287545:
      	 goto	#UK
      case 951255304:
      	 goto	#JC
      case 1329602513:
      	 goto	#T
      default:
      	 goto	#JC
   }
      -> Immediate #UK -> #UL
      -> DefaultSwitch #UK -> #JC
      -> Switch[404287545] #UK -> #UK
      -> Switch[34899094] #UK -> #UL
      -> Switch[1329602513] #UK -> #T
      -> Switch[951255304] #UK -> #JC
      <- Switch[404287545] #UK -> #UK
      <- ConditionalJump[IF_ICMPEQ] #JL -> #UK
===#Block UL(size=2, flags=100)===
   0. lvar105 = {563625076 ^ lvar105};
   1. goto T
      -> UnconditionalJump[GOTO] #UL -> #T
      <- Immediate #UK -> #UL
      <- Switch[34899094] #UK -> #UL
===#Block T(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_GLAZED_TERRACOTTA;
   2. goto LW
      -> UnconditionalJump[GOTO] #T -> #LW
      <- UnconditionalJump[GOTO] #UL -> #T
      <- Switch[1329602513] #UK -> #T
===#Block LW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 197457116);
   1. goto HS
      -> UnconditionalJump[GOTO] #LW -> #HS
      <- UnconditionalJump[GOTO] #T -> #LW
===#Block HS(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 7305969)
      goto HR
   1. throw nullconst;
      -> TryCatch range: [HS...HR] -> ACW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #HS -> #HR
      <- UnconditionalJump[GOTO] #LW -> #HS
===#Block HR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HS...HR] -> ACW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HS -> #HR
===#Block ACW(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -2048060992:
      	 goto	#ACX
      case 1242193072:
      	 goto	#ACY
      default:
      	 goto	#ACZ
   }
      -> DefaultSwitch #ACW -> #ACZ
      -> Switch[1242193072] #ACW -> #ACY
      -> Switch[-2048060992] #ACW -> #ACX
      <- TryCatch range: [HS...HR] -> ACW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HS...HR] -> ACW ([Ljava/lang/RuntimeException;])
===#Block ACX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 175172652);
   1. goto HT
      -> UnconditionalJump[GOTO] #ACX -> #HT
      <- Switch[-2048060992] #ACW -> #ACX
===#Block ACY(size=2, flags=10100)===
   0. lvar105 = {327943554 ^ lvar105};
   1. goto HT
      -> UnconditionalJump[GOTO] #ACY -> #HT
      <- Switch[1242193072] #ACW -> #ACY
===#Block HT(size=2, flags=0)===
   0. _consume(catch());
   1. goto NY
      -> UnconditionalJump[GOTO] #HT -> #NY
      <- UnconditionalJump[GOTO] #ACX -> #HT
      <- UnconditionalJump[GOTO] #ACY -> #HT
===#Block NY(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 65920395:
      	 goto	#NZ
      case 562652702:
      	 goto	#NY
      case 754693075:
      	 goto	#CN
      case 1687584494:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1687584494] #NY -> #JC
      -> DefaultSwitch #NY -> #JC
      -> Switch[754693075] #NY -> #CN
      -> Immediate #NY -> #NZ
      -> Switch[65920395] #NY -> #NZ
      -> Switch[562652702] #NY -> #NY
      <- UnconditionalJump[GOTO] #HT -> #NY
      <- Switch[562652702] #NY -> #NY
===#Block NZ(size=2, flags=100)===
   0. lvar105 = {863222169 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NZ -> #CN
      <- Immediate #NY -> #NZ
      <- Switch[65920395] #NY -> #NZ
===#Block ACZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACW -> #ACZ
===#Block S(size=1, flags=0)===
   0. goto MS
      -> UnconditionalJump[GOTO] #S -> #MS
      <- Immediate #R -> #S
===#Block MS(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 23684349:
      	 goto	#MT
      case 610540435:
      	 goto	#MS
      case 619299078:
      	 goto	#ES
      case 892555768:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[610540435] #MS -> #MS
      -> DefaultSwitch #MS -> #JC
      -> Switch[892555768] #MS -> #JC
      -> Immediate #MS -> #MT
      -> Switch[619299078] #MS -> #ES
      -> Switch[23684349] #MS -> #MT
      <- Switch[610540435] #MS -> #MS
      <- UnconditionalJump[GOTO] #S -> #MS
===#Block MT(size=2, flags=100)===
   0. lvar105 = {2136373658 ^ lvar105};
   1. goto ES
      -> UnconditionalJump[GOTO] #MT -> #ES
      <- Immediate #MS -> #MT
      <- Switch[23684349] #MS -> #MT
===#Block ES(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 123691830)
      goto ER
   1. throw nullconst;
      -> TryCatch range: [ES...ER] -> YW ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #ES -> #ER
      <- Switch[619299078] #MS -> #ES
      <- UnconditionalJump[GOTO] #MT -> #ES
===#Block ER(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [ES...ER] -> YW ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #ES -> #ER
===#Block YW(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1011662440:
      	 goto	#YY
      case -320561239:
      	 goto	#YX
      default:
      	 goto	#YZ
   }
      -> Switch[-320561239] #YW -> #YX
      -> DefaultSwitch #YW -> #YZ
      -> Switch[-1011662440] #YW -> #YY
      <- TryCatch range: [ES...ER] -> YW ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [ES...ER] -> YW ([Ljava/lang/IllegalAccessException;])
===#Block YY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1879943071);
   1. goto ET
      -> UnconditionalJump[GOTO] #YY -> #ET
      <- Switch[-1011662440] #YW -> #YY
===#Block YZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YW -> #YZ
===#Block YX(size=2, flags=10100)===
   0. lvar105 = {1441225817 ^ lvar105};
   1. goto ET
      -> UnconditionalJump[GOTO] #YX -> #ET
      <- Switch[-320561239] #YW -> #YX
===#Block ET(size=2, flags=0)===
   0. _consume(catch());
   1. goto MJ
      -> UnconditionalJump[GOTO] #ET -> #MJ
      <- UnconditionalJump[GOTO] #YX -> #ET
      <- UnconditionalJump[GOTO] #YY -> #ET
===#Block MJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1431961099);
   1. goto AG
      -> UnconditionalJump[GOTO] #MJ -> #AG
      <- UnconditionalJump[GOTO] #ET -> #MJ
===#Block VX(size=2, flags=10100)===
   0. lvar105 = {1529204770 ^ lvar105};
   1. goto X
      -> UnconditionalJump[GOTO] #VX -> #X
      <- Switch[83435501] #E -> #VX
===#Block X(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = lvar9;
   2. lvar81 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.nzrvbcchmgmjipx(), lvar105);
   3. lvar28 = lvar27.equals(lvar81);
   4. if (lvar28 != {1582854273 ^ lvar105})
      goto TF
   5. lvar105 = {813524360 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #X -> #TF
      -> Immediate #X -> #Y
      <- UnconditionalJump[GOTO] #VX -> #X
===#Block Y(size=1, flags=0)===
   0. goto PY
      -> UnconditionalJump[GOTO] #Y -> #PY
      <- Immediate #X -> #Y
===#Block PY(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 90683108:
      	 goto	#PZ
      case 649165595:
      	 goto	#JC
      case 1171164561:
      	 goto	#CZ
      case 1382271099:
      	 goto	#PY
      default:
      	 goto	#JC
   }
      -> Switch[90683108] #PY -> #PZ
      -> Switch[1171164561] #PY -> #CZ
      -> Switch[649165595] #PY -> #JC
      -> Immediate #PY -> #PZ
      -> DefaultSwitch #PY -> #JC
      -> Switch[1382271099] #PY -> #PY
      <- UnconditionalJump[GOTO] #Y -> #PY
      <- Switch[1382271099] #PY -> #PY
===#Block PZ(size=2, flags=100)===
   0. lvar105 = {1729126057 ^ lvar105};
   1. goto CZ
      -> UnconditionalJump[GOTO] #PZ -> #CZ
      <- Switch[90683108] #PY -> #PZ
      <- Immediate #PY -> #PZ
===#Block CZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 30886835)
      goto CY
   1. throw nullconst;
      -> TryCatch range: [CZ...CY] -> WO ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CZ -> #CY
      <- UnconditionalJump[GOTO] #PZ -> #CZ
      <- Switch[1171164561] #PY -> #CZ
===#Block CY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CZ...CY] -> WO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CZ -> #CY
===#Block WO(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 559720282:
      	 goto	#WQ
      case 1235901696:
      	 goto	#WP
      default:
      	 goto	#WR
   }
      -> DefaultSwitch #WO -> #WR
      -> Switch[559720282] #WO -> #WQ
      -> Switch[1235901696] #WO -> #WP
      <- TryCatch range: [CZ...CY] -> WO ([Ljava/io/IOException;])
      <- TryCatch range: [CZ...CY] -> WO ([Ljava/io/IOException;])
===#Block WP(size=2, flags=10100)===
   0. lvar105 = {411886708 ^ lvar105};
   1. goto DA
      -> UnconditionalJump[GOTO] #WP -> #DA
      <- Switch[1235901696] #WO -> #WP
===#Block WQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1435646783);
   1. goto DA
      -> UnconditionalJump[GOTO] #WQ -> #DA
      <- Switch[559720282] #WO -> #WQ
===#Block DA(size=2, flags=0)===
   0. _consume(catch());
   1. goto QH
      -> UnconditionalJump[GOTO] #DA -> #QH
      <- UnconditionalJump[GOTO] #WP -> #DA
      <- UnconditionalJump[GOTO] #WQ -> #DA
===#Block QH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 748396147);
   1. goto AG
      -> UnconditionalJump[GOTO] #QH -> #AG
      <- UnconditionalJump[GOTO] #DA -> #QH
===#Block WR(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WO -> #WR
===#Block TF(size=2, flags=10100)===
   0. lvar105 = {1138922947 ^ lvar105};
   1. goto KC
      -> UnconditionalJump[GOTO] #TF -> #KC
      <- ConditionalJump[IF_ICMPNE] #X -> #TF
===#Block KC(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -304666096)
      goto TH
   1. goto KL
      -> ConditionalJump[IF_ICMPEQ] #KC -> #TH
      -> UnconditionalJump[GOTO] #KC -> #KL
      <- UnconditionalJump[GOTO] #TF -> #KC
===#Block KL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 231629611);
   1. goto JC
      -> UnconditionalJump[GOTO] #KL -> #JC
      <- UnconditionalJump[GOTO] #KC -> #KL
===#Block TH(size=2, flags=10100)===
   0. lvar105 = {599131086 ^ lvar105};
   1. goto Z
      -> UnconditionalJump[GOTO] #TH -> #Z
      <- ConditionalJump[IF_ICMPEQ] #KC -> #TH
===#Block Z(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_GLAZED_TERRACOTTA;
   2. goto OK
      -> UnconditionalJump[GOTO] #Z -> #OK
      <- UnconditionalJump[GOTO] #TH -> #Z
===#Block OK(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 54475480:
      	 goto	#EJ
      case 137937059:
      	 goto	#OL
      case 719904399:
      	 goto	#OK
      case 1059141958:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[54475480] #OK -> #EJ
      -> Switch[137937059] #OK -> #OL
      -> DefaultSwitch #OK -> #JC
      -> Immediate #OK -> #OL
      -> Switch[719904399] #OK -> #OK
      -> Switch[1059141958] #OK -> #JC
      <- UnconditionalJump[GOTO] #Z -> #OK
      <- Switch[719904399] #OK -> #OK
===#Block OL(size=2, flags=100)===
   0. lvar105 = {1050960129 ^ lvar105};
   1. goto EJ
      -> UnconditionalJump[GOTO] #OL -> #EJ
      <- Switch[137937059] #OK -> #OL
      <- Immediate #OK -> #OL
===#Block EJ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 10354620)
      goto EI
   1. throw nullconst;
      -> TryCatch range: [EJ...EI] -> YK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EJ -> #EI
      <- Switch[54475480] #OK -> #EJ
      <- UnconditionalJump[GOTO] #OL -> #EJ
===#Block EI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EJ...EI] -> YK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EJ -> #EI
===#Block YK(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 88357992:
      	 goto	#YL
      case 2125255001:
      	 goto	#YM
      default:
      	 goto	#YN
   }
      -> DefaultSwitch #YK -> #YN
      -> Switch[2125255001] #YK -> #YM
      -> Switch[88357992] #YK -> #YL
      <- TryCatch range: [EJ...EI] -> YK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EJ...EI] -> YK ([Ljava/lang/IllegalAccessException;])
===#Block YL(size=2, flags=10100)===
   0. lvar105 = {1485976275 ^ lvar105};
   1. goto EK
      -> UnconditionalJump[GOTO] #YL -> #EK
      <- Switch[88357992] #YK -> #YL
===#Block YM(size=2, flags=10100)===
   0. lvar105 = {2012163957 ^ lvar105};
   1. goto EK
      -> UnconditionalJump[GOTO] #YM -> #EK
      <- Switch[2125255001] #YK -> #YM
===#Block EK(size=2, flags=0)===
   0. _consume(catch());
   1. goto RG
      -> UnconditionalJump[GOTO] #EK -> #RG
      <- UnconditionalJump[GOTO] #YM -> #EK
      <- UnconditionalJump[GOTO] #YL -> #EK
===#Block RG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1904493267);
   1. goto CN
      -> UnconditionalJump[GOTO] #RG -> #CN
      <- UnconditionalJump[GOTO] #EK -> #RG
===#Block YN(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #YK -> #YN
===#Block VY(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 86361061:
      	 goto	#VZ
      case 894537350:
      	 goto	#U
      case 1644889360:
      	 goto	#VY
      case 1785802360:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[86361061] #VY -> #VZ
      -> DefaultSwitch #VY -> #JC
      -> Immediate #VY -> #VZ
      -> Switch[894537350] #VY -> #U
      -> Switch[1785802360] #VY -> #JC
      -> Switch[1644889360] #VY -> #VY
      <- Switch[83435503] #E -> #VY
      <- Switch[1644889360] #VY -> #VY
===#Block VZ(size=2, flags=100)===
   0. lvar105 = {1641733113 ^ lvar105};
   1. goto U
      -> UnconditionalJump[GOTO] #VZ -> #U
      <- Switch[86361061] #VY -> #VZ
      <- Immediate #VY -> #VZ
===#Block U(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar9;
   2. lvar80 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ztkdqnegeuafzau(), lvar105);
   3. lvar26 = lvar25.equals(lvar80);
   4. if (lvar26 != {1688697690 ^ lvar105})
      goto UF
   5. lvar105 = {947711495 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #U -> #UF
      -> Immediate #U -> #W
      <- UnconditionalJump[GOTO] #VZ -> #U
      <- Switch[894537350] #VY -> #U
===#Block W(size=1, flags=0)===
   0. goto RK
      -> UnconditionalJump[GOTO] #W -> #RK
      <- Immediate #U -> #W
===#Block RK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1232617947);
   1. goto IB
      -> UnconditionalJump[GOTO] #RK -> #IB
      <- UnconditionalJump[GOTO] #W -> #RK
===#Block IB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 166559328)
      goto IA
   1. throw nullconst;
      -> TryCatch range: [IB...IA] -> ADI ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #IB -> #IA
      <- UnconditionalJump[GOTO] #RK -> #IB
===#Block IA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IB...IA] -> ADI ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IB -> #IA
===#Block ADI(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1390500816:
      	 goto	#ADJ
      case 358351313:
      	 goto	#ADK
      default:
      	 goto	#ADL
   }
      -> DefaultSwitch #ADI -> #ADL
      -> Switch[358351313] #ADI -> #ADK
      -> Switch[-1390500816] #ADI -> #ADJ
      <- TryCatch range: [IB...IA] -> ADI ([Ljava/io/IOException;])
      <- TryCatch range: [IB...IA] -> ADI ([Ljava/io/IOException;])
===#Block ADJ(size=2, flags=10100)===
   0. lvar105 = {1921704250 ^ lvar105};
   1. goto IC
      -> UnconditionalJump[GOTO] #ADJ -> #IC
      <- Switch[-1390500816] #ADI -> #ADJ
===#Block ADK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1166194054);
   1. goto IC
      -> UnconditionalJump[GOTO] #ADK -> #IC
      <- Switch[358351313] #ADI -> #ADK
===#Block IC(size=2, flags=0)===
   0. _consume(catch());
   1. goto OJ
      -> UnconditionalJump[GOTO] #IC -> #OJ
      <- UnconditionalJump[GOTO] #ADJ -> #IC
      <- UnconditionalJump[GOTO] #ADK -> #IC
===#Block OJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1510671387);
   1. goto AG
      -> UnconditionalJump[GOTO] #OJ -> #AG
      <- UnconditionalJump[GOTO] #IC -> #OJ
===#Block AG(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_GLAZED_TERRACOTTA;
   2. goto QC
      -> UnconditionalJump[GOTO] #AG -> #QC
      <- UnconditionalJump[GOTO] #RC -> #AG
      <- UnconditionalJump[GOTO] #MA -> #AG
      <- UnconditionalJump[GOTO] #MJ -> #AG
      <- UnconditionalJump[GOTO] #OJ -> #AG
      <- UnconditionalJump[GOTO] #MR -> #AG
      <- UnconditionalJump[GOTO] #OZ -> #AG
      <- UnconditionalJump[GOTO] #WB -> #AG
      <- Switch[1971509612] #RB -> #AG
      <- Switch[647704295] #WA -> #AG
      <- Switch[1346340451] #LZ -> #AG
      <- Switch[1180511050] #MQ -> #AG
      <- UnconditionalJump[GOTO] #OY -> #AG
      <- Switch[1386580] #NO -> #AG
      <- UnconditionalJump[GOTO] #NP -> #AG
      <- UnconditionalJump[GOTO] #QH -> #AG
===#Block QC(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 108166417:
      	 goto	#QD
      case 322070643:
      	 goto	#QC
      case 856158122:
      	 goto	#DL
      case 1050023647:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[322070643] #QC -> #QC
      -> Switch[108166417] #QC -> #QD
      -> Switch[1050023647] #QC -> #JC
      -> DefaultSwitch #QC -> #JC
      -> Switch[856158122] #QC -> #DL
      -> Immediate #QC -> #QD
      <- Switch[322070643] #QC -> #QC
      <- UnconditionalJump[GOTO] #AG -> #QC
===#Block QD(size=2, flags=100)===
   0. lvar105 = {1164452957 ^ lvar105};
   1. goto DL
      -> UnconditionalJump[GOTO] #QD -> #DL
      <- Switch[108166417] #QC -> #QD
      <- Immediate #QC -> #QD
===#Block DL(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 152040158)
      goto DK
   1. throw nullconst;
      -> TryCatch range: [DL...DK] -> XE ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #DL -> #DK
      <- UnconditionalJump[GOTO] #QD -> #DL
      <- Switch[856158122] #QC -> #DL
===#Block DK(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DL...DK] -> XE ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DL -> #DK
===#Block XE(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1034899501:
      	 goto	#XF
      case 1836835834:
      	 goto	#XG
      default:
      	 goto	#XH
   }
      -> DefaultSwitch #XE -> #XH
      -> Switch[-1034899501] #XE -> #XF
      -> Switch[1836835834] #XE -> #XG
      <- TryCatch range: [DL...DK] -> XE ([Ljava/io/IOException;])
      <- TryCatch range: [DL...DK] -> XE ([Ljava/io/IOException;])
===#Block XG(size=2, flags=10100)===
   0. lvar105 = {318424039 ^ lvar105};
   1. goto DM
      -> UnconditionalJump[GOTO] #XG -> #DM
      <- Switch[1836835834] #XE -> #XG
===#Block XF(size=2, flags=10100)===
   0. lvar105 = {656386658 ^ lvar105};
   1. goto DM
      -> UnconditionalJump[GOTO] #XF -> #DM
      <- Switch[-1034899501] #XE -> #XF
===#Block DM(size=2, flags=0)===
   0. _consume(catch());
   1. goto QY
      -> UnconditionalJump[GOTO] #DM -> #QY
      <- UnconditionalJump[GOTO] #XF -> #DM
      <- UnconditionalJump[GOTO] #XG -> #DM
===#Block QY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1995130901);
   1. goto CN
      -> UnconditionalJump[GOTO] #QY -> #CN
      <- UnconditionalJump[GOTO] #DM -> #QY
===#Block XH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #XE -> #XH
===#Block ADL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ADI -> #ADL
===#Block UF(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 50600537:
      	 goto	#UG
      case 419790954:
      	 goto	#UF
      case 900619731:
      	 goto	#JC
      case 1558866218:
      	 goto	#JY
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #UF -> #JC
      -> Switch[900619731] #UF -> #JC
      -> Switch[50600537] #UF -> #UG
      -> Switch[1558866218] #UF -> #JY
      -> Immediate #UF -> #UG
      -> Switch[419790954] #UF -> #UF
      <- ConditionalJump[IF_ICMPNE] #U -> #UF
      <- Switch[419790954] #UF -> #UF
===#Block UG(size=2, flags=100)===
   0. lvar105 = {1548458153 ^ lvar105};
   1. goto JY
      -> UnconditionalJump[GOTO] #UG -> #JY
      <- Switch[50600537] #UF -> #UG
      <- Immediate #UF -> #UG
===#Block JY(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -949543015)
      goto SB
   1. goto LL
      -> UnconditionalJump[GOTO] #JY -> #LL
      -> ConditionalJump[IF_ICMPEQ] #JY -> #SB
      <- Switch[1558866218] #UF -> #JY
      <- UnconditionalJump[GOTO] #UG -> #JY
===#Block SB(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 239780170:
      	 goto	#SC
      case 1081521714:
      	 goto	#V
      case 1712032753:
      	 goto	#JC
      case 1794205652:
      	 goto	#SB
      default:
      	 goto	#JC
   }
      -> Switch[1081521714] #SB -> #V
      -> Switch[1794205652] #SB -> #SB
      -> Switch[1712032753] #SB -> #JC
      -> Immediate #SB -> #SC
      -> DefaultSwitch #SB -> #JC
      -> Switch[239780170] #SB -> #SC
      <- Switch[1794205652] #SB -> #SB
      <- ConditionalJump[IF_ICMPEQ] #JY -> #SB
===#Block SC(size=2, flags=100)===
   0. lvar105 = {184741870 ^ lvar105};
   1. goto V
      -> UnconditionalJump[GOTO] #SC -> #V
      <- Immediate #SB -> #SC
      <- Switch[239780170] #SB -> #SC
===#Block V(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_GLAZED_TERRACOTTA;
   2. goto LC
      -> UnconditionalJump[GOTO] #V -> #LC
      <- Switch[1081521714] #SB -> #V
      <- UnconditionalJump[GOTO] #SC -> #V
===#Block LC(size=2, flags=10100)===
   0. lvar105 = {288807450 ^ lvar105};
   1. goto FZ
      -> UnconditionalJump[GOTO] #LC -> #FZ
      <- UnconditionalJump[GOTO] #V -> #LC
===#Block FZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 58997877)
      goto FY
   1. throw nullconst;
      -> TryCatch range: [FZ...FY] -> AAO ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #FZ -> #FY
      <- UnconditionalJump[GOTO] #LC -> #FZ
===#Block FY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FZ...FY] -> AAO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FZ -> #FY
===#Block AAO(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -2722174:
      	 goto	#AAQ
      case 382119993:
      	 goto	#AAP
      default:
      	 goto	#AAR
   }
      -> DefaultSwitch #AAO -> #AAR
      -> Switch[382119993] #AAO -> #AAP
      -> Switch[-2722174] #AAO -> #AAQ
      <- TryCatch range: [FZ...FY] -> AAO ([Ljava/io/IOException;])
      <- TryCatch range: [FZ...FY] -> AAO ([Ljava/io/IOException;])
===#Block AAQ(size=2, flags=10100)===
   0. lvar105 = {964962335 ^ lvar105};
   1. goto GA
      -> UnconditionalJump[GOTO] #AAQ -> #GA
      <- Switch[-2722174] #AAO -> #AAQ
===#Block AAP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1151742152);
   1. goto GA
      -> UnconditionalJump[GOTO] #AAP -> #GA
      <- Switch[382119993] #AAO -> #AAP
===#Block GA(size=2, flags=0)===
   0. _consume(catch());
   1. goto LI
      -> UnconditionalJump[GOTO] #GA -> #LI
      <- UnconditionalJump[GOTO] #AAQ -> #GA
      <- UnconditionalJump[GOTO] #AAP -> #GA
===#Block LI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1337987394);
   1. goto CN
      -> UnconditionalJump[GOTO] #LI -> #CN
      <- UnconditionalJump[GOTO] #GA -> #LI
===#Block AAR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AAO -> #AAR
===#Block LL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 681076122);
   1. goto JC
      -> UnconditionalJump[GOTO] #LL -> #JC
      <- UnconditionalJump[GOTO] #JY -> #LL
===#Block TX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1959042590);
   1. goto JR
      -> UnconditionalJump[GOTO] #TX -> #JR
      <- ConditionalJump[IF_ICMPEQ] #D -> #TX
===#Block JR(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -264200749)
      goto SM
   1. goto OM
      -> UnconditionalJump[GOTO] #JR -> #OM
      -> ConditionalJump[IF_ICMPEQ] #JR -> #SM
      <- UnconditionalJump[GOTO] #TX -> #JR
===#Block SM(size=2, flags=10100)===
   0. lvar105 = {308015289 ^ lvar105};
   1. goto AH
      -> UnconditionalJump[GOTO] #SM -> #AH
      <- ConditionalJump[IF_ICMPEQ] #JR -> #SM
===#Block AH(size=7, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar33 = lvar1;
   2. lvar102 = lvar33;
   3. lvar34 = lvar102;
   4. lvar35 = lvar34.hashCode();
   5. svar107 = {lvar35 ^ lvar105};
   6. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(svar107)) {
      case 33641536:
      	 goto	#VD
      case 33641537:
      	 goto	#VF
      case 33641542:
      	 goto	#VH
      case 33641544:
      	 goto	#VI
      case 33641546:
      	 goto	#VJ
      case 33641548:
      	 goto	#VK
      case 33641550:
      	 goto	#VL
      case 33641556:
      	 goto	#VM
      case 33641558:
      	 goto	#VN
      default:
      	 goto	#VO
   }
      -> Switch[33641537] #AH -> #VF
      -> Switch[33641556] #AH -> #VM
      -> Switch[33641558] #AH -> #VN
      -> Switch[33641542] #AH -> #VH
      -> Switch[33641546] #AH -> #VJ
      -> DefaultSwitch #AH -> #VO
      -> Switch[33641544] #AH -> #VI
      -> Switch[33641550] #AH -> #VL
      -> Switch[33641536] #AH -> #VD
      -> Switch[33641548] #AH -> #VK
      <- UnconditionalJump[GOTO] #SM -> #AH
===#Block VK(size=2, flags=10100)===
   0. lvar105 = {1714705803 ^ lvar105};
   1. goto AL
      -> UnconditionalJump[GOTO] #VK -> #AL
      <- Switch[33641548] #AH -> #VK
===#Block AL(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar38 = lvar102;
   2. lvar85 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.jvdnwlmwnvtlqtl(), lvar105);
   3. lvar39 = lvar38.equals(lvar85);
   4. if (lvar39 != {174569096 ^ lvar105})
      goto UE
   5. lvar105 = {1752448959 ^ lvar105};
      -> Immediate #AL -> #AN
      -> ConditionalJump[IF_ICMPNE] #AL -> #UE
      <- UnconditionalJump[GOTO] #VK -> #AL
===#Block UE(size=2, flags=10100)===
   0. lvar105 = {1632146952 ^ lvar105};
   1. goto JX
      -> UnconditionalJump[GOTO] #UE -> #JX
      <- ConditionalJump[IF_ICMPNE] #AL -> #UE
===#Block JX(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1501103107)
      goto SU
   1. goto PL
      -> ConditionalJump[IF_ICMPEQ] #JX -> #SU
      -> UnconditionalJump[GOTO] #JX -> #PL
      <- UnconditionalJump[GOTO] #UE -> #JX
===#Block PL(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 262874583:
      	 goto	#PM
      case 282456654:
      	 goto	#JC
      case 1030333018:
      	 goto	#PL
      case 1234005085:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1030333018] #PL -> #PL
      -> Immediate #PL -> #PM
      -> Switch[1234005085] #PL -> #JC
      -> DefaultSwitch #PL -> #JC
      -> Switch[262874583] #PL -> #PM
      <- Switch[1030333018] #PL -> #PL
      <- UnconditionalJump[GOTO] #JX -> #PL
===#Block PM(size=2, flags=100)===
   0. lvar105 = {2069604073 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PM -> #JC
      <- Immediate #PL -> #PM
      <- Switch[262874583] #PL -> #PM
===#Block SU(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 262874583:
      	 goto	#SV
      case 656733973:
      	 goto	#AM
      case 1124081060:
      	 goto	#SU
      case 1418618534:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1418618534] #SU -> #JC
      -> Immediate #SU -> #SV
      -> DefaultSwitch #SU -> #JC
      -> Switch[262874583] #SU -> #SV
      -> Switch[656733973] #SU -> #AM
      -> Switch[1124081060] #SU -> #SU
      <- ConditionalJump[IF_ICMPEQ] #JX -> #SU
      <- Switch[1124081060] #SU -> #SU
===#Block SV(size=2, flags=100)===
   0. lvar105 = {710148465 ^ lvar105};
   1. goto AM
      -> UnconditionalJump[GOTO] #SV -> #AM
      <- Immediate #SU -> #SV
      <- Switch[262874583] #SU -> #SV
===#Block AM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_CONCRETE_POWDER;
   2. goto KT
      -> UnconditionalJump[GOTO] #AM -> #KT
      <- Switch[656733973] #SU -> #AM
      <- UnconditionalJump[GOTO] #SV -> #AM
===#Block KT(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 249483449:
      	 goto	#KU
      case 1250261915:
      	 goto	#EV
      case 1540573306:
      	 goto	#JC
      case 1955323031:
      	 goto	#KT
      default:
      	 goto	#JC
   }
      -> Switch[1955323031] #KT -> #KT
      -> Immediate #KT -> #KU
      -> Switch[1250261915] #KT -> #EV
      -> DefaultSwitch #KT -> #JC
      -> Switch[249483449] #KT -> #KU
      -> Switch[1540573306] #KT -> #JC
      <- Switch[1955323031] #KT -> #KT
      <- UnconditionalJump[GOTO] #AM -> #KT
===#Block KU(size=2, flags=100)===
   0. lvar105 = {1437327462 ^ lvar105};
   1. goto EV
      -> UnconditionalJump[GOTO] #KU -> #EV
      <- Immediate #KT -> #KU
      <- Switch[249483449] #KT -> #KU
===#Block EV(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 139748292)
      goto EU
   1. throw nullconst;
      -> TryCatch range: [EV...EU] -> ZA ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #EV -> #EU
      <- UnconditionalJump[GOTO] #KU -> #EV
      <- Switch[1250261915] #KT -> #EV
===#Block EU(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EV...EU] -> ZA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EV -> #EU
===#Block ZA(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1501164360:
      	 goto	#ZB
      case 114674905:
      	 goto	#ZC
      default:
      	 goto	#ZD
   }
      -> Switch[114674905] #ZA -> #ZC
      -> Switch[-1501164360] #ZA -> #ZB
      -> DefaultSwitch #ZA -> #ZD
      <- TryCatch range: [EV...EU] -> ZA ([Ljava/io/IOException;])
      <- TryCatch range: [EV...EU] -> ZA ([Ljava/io/IOException;])
===#Block ZD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZA -> #ZD
===#Block ZB(size=2, flags=10100)===
   0. lvar105 = {939751618 ^ lvar105};
   1. goto EW
      -> UnconditionalJump[GOTO] #ZB -> #EW
      <- Switch[-1501164360] #ZA -> #ZB
===#Block ZC(size=2, flags=10100)===
   0. lvar105 = {201916622 ^ lvar105};
   1. goto EW
      -> UnconditionalJump[GOTO] #ZC -> #EW
      <- Switch[114674905] #ZA -> #ZC
===#Block EW(size=2, flags=0)===
   0. _consume(catch());
   1. goto RN
      -> UnconditionalJump[GOTO] #EW -> #RN
      <- UnconditionalJump[GOTO] #ZB -> #EW
      <- UnconditionalJump[GOTO] #ZC -> #EW
===#Block RN(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 114957255:
      	 goto	#RO
      case 999089629:
      	 goto	#JC
      case 1112029742:
      	 goto	#CN
      case 2036360441:
      	 goto	#RN
      default:
      	 goto	#JC
   }
      -> Switch[1112029742] #RN -> #CN
      -> Immediate #RN -> #RO
      -> Switch[2036360441] #RN -> #RN
      -> Switch[999089629] #RN -> #JC
      -> DefaultSwitch #RN -> #JC
      -> Switch[114957255] #RN -> #RO
      <- Switch[2036360441] #RN -> #RN
      <- UnconditionalJump[GOTO] #EW -> #RN
===#Block RO(size=2, flags=100)===
   0. lvar105 = {91049688 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #RO -> #CN
      <- Immediate #RN -> #RO
      <- Switch[114957255] #RN -> #RO
===#Block AN(size=1, flags=0)===
   0. goto KK
      -> UnconditionalJump[GOTO] #AN -> #KK
      <- Immediate #AL -> #AN
===#Block KK(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1898998964);
   1. goto EA
      -> UnconditionalJump[GOTO] #KK -> #EA
      <- UnconditionalJump[GOTO] #AN -> #KK
===#Block EA(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 85303182)
      goto DZ
   1. throw nullconst;
      -> TryCatch range: [EA...DZ] -> XY ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EA -> #DZ
      <- UnconditionalJump[GOTO] #KK -> #EA
===#Block DZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EA...DZ] -> XY ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EA -> #DZ
===#Block XY(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1726010344:
      	 goto	#XZ
      case -1097231542:
      	 goto	#YA
      default:
      	 goto	#YB
   }
      -> DefaultSwitch #XY -> #YB
      -> Switch[-1726010344] #XY -> #XZ
      -> Switch[-1097231542] #XY -> #YA
      <- TryCatch range: [EA...DZ] -> XY ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EA...DZ] -> XY ([Ljava/lang/IllegalAccessException;])
===#Block YA(size=2, flags=10100)===
   0. lvar105 = {702818859 ^ lvar105};
   1. goto EB
      -> UnconditionalJump[GOTO] #YA -> #EB
      <- Switch[-1097231542] #XY -> #YA
===#Block XZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1830092225);
   1. goto EB
      -> UnconditionalJump[GOTO] #XZ -> #EB
      <- Switch[-1726010344] #XY -> #XZ
===#Block EB(size=2, flags=0)===
   0. _consume(catch());
   1. goto QO
      -> UnconditionalJump[GOTO] #EB -> #QO
      <- UnconditionalJump[GOTO] #YA -> #EB
      <- UnconditionalJump[GOTO] #XZ -> #EB
===#Block QO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 921642389);
   1. goto BJ
      -> UnconditionalJump[GOTO] #QO -> #BJ
      <- UnconditionalJump[GOTO] #EB -> #QO
===#Block YB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XY -> #YB
===#Block VD(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 31648646:
      	 goto	#VE
      case 569658524:
      	 goto	#BA
      case 1037479017:
      	 goto	#JC
      case 1951026227:
      	 goto	#VD
      default:
      	 goto	#JC
   }
      -> Switch[569658524] #VD -> #BA
      -> Switch[1037479017] #VD -> #JC
      -> Immediate #VD -> #VE
      -> DefaultSwitch #VD -> #JC
      -> Switch[1951026227] #VD -> #VD
      -> Switch[31648646] #VD -> #VE
      <- Switch[33641536] #AH -> #VD
      <- Switch[1951026227] #VD -> #VD
===#Block VE(size=2, flags=100)===
   0. lvar105 = {2066495231 ^ lvar105};
   1. goto BA
      -> UnconditionalJump[GOTO] #VE -> #BA
      <- Immediate #VD -> #VE
      <- Switch[31648646] #VD -> #VE
===#Block BA(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar48 = lvar102;
   2. lvar90 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.vnooadmixdmqxzh(), lvar105);
   3. lvar49 = lvar48.equals(lvar90);
   4. if (lvar49 != {394253820 ^ lvar105})
      goto UC
   5. lvar105 = {1573035912 ^ lvar105};
      -> Immediate #BA -> #BC
      -> ConditionalJump[IF_ICMPNE] #BA -> #UC
      <- UnconditionalJump[GOTO] #VE -> #BA
      <- Switch[569658524] #VD -> #BA
===#Block UC(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 145475336:
      	 goto	#JC
      case 226998759:
      	 goto	#UD
      case 987011652:
      	 goto	#JW
      case 1186147146:
      	 goto	#UC
      default:
      	 goto	#JC
   }
      -> Immediate #UC -> #UD
      -> Switch[145475336] #UC -> #JC
      -> DefaultSwitch #UC -> #JC
      -> Switch[1186147146] #UC -> #UC
      -> Switch[987011652] #UC -> #JW
      -> Switch[226998759] #UC -> #UD
      <- ConditionalJump[IF_ICMPNE] #BA -> #UC
      <- Switch[1186147146] #UC -> #UC
===#Block UD(size=2, flags=100)===
   0. lvar105 = {692708573 ^ lvar105};
   1. goto JW
      -> UnconditionalJump[GOTO] #UD -> #JW
      <- Immediate #UC -> #UD
      <- Switch[226998759] #UC -> #UD
===#Block JW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -240023287)
      goto SR
   1. goto PI
      -> UnconditionalJump[GOTO] #JW -> #PI
      -> ConditionalJump[IF_ICMPEQ] #JW -> #SR
      <- UnconditionalJump[GOTO] #UD -> #JW
      <- Switch[987011652] #UC -> #JW
===#Block SR(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 143180057:
      	 goto	#SS
      case 515067286:
      	 goto	#JC
      case 1343946410:
      	 goto	#SR
      case 1617212294:
      	 goto	#BB
      default:
      	 goto	#JC
   }
      -> Switch[1617212294] #SR -> #BB
      -> Switch[1343946410] #SR -> #SR
      -> Switch[143180057] #SR -> #SS
      -> Switch[515067286] #SR -> #JC
      -> DefaultSwitch #SR -> #JC
      -> Immediate #SR -> #SS
      <- Switch[1343946410] #SR -> #SR
      <- ConditionalJump[IF_ICMPEQ] #JW -> #SR
===#Block SS(size=2, flags=100)===
   0. lvar105 = {958386396 ^ lvar105};
   1. goto BB
      -> UnconditionalJump[GOTO] #SS -> #BB
      <- Switch[143180057] #SR -> #SS
      <- Immediate #SR -> #SS
===#Block BB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLUE_CONCRETE_POWDER;
   2. goto LJ
      -> UnconditionalJump[GOTO] #BB -> #LJ
      <- Switch[1617212294] #SR -> #BB
      <- UnconditionalJump[GOTO] #SS -> #BB
===#Block LJ(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 112674036:
      	 goto	#LK
      case 165550250:
      	 goto	#JC
      case 551580319:
      	 goto	#LJ
      case 1279589731:
      	 goto	#GC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #LJ -> #JC
      -> Switch[165550250] #LJ -> #JC
      -> Switch[112674036] #LJ -> #LK
      -> Immediate #LJ -> #LK
      -> Switch[1279589731] #LJ -> #GC
      -> Switch[551580319] #LJ -> #LJ
      <- UnconditionalJump[GOTO] #BB -> #LJ
      <- Switch[551580319] #LJ -> #LJ
===#Block LK(size=2, flags=100)===
   0. lvar105 = {1272437240 ^ lvar105};
   1. goto GC
      -> UnconditionalJump[GOTO] #LK -> #GC
      <- Switch[112674036] #LJ -> #LK
      <- Immediate #LJ -> #LK
===#Block GC(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 86818935)
      goto GB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GC -> #GB
      -> TryCatch range: [GC...GB] -> AAS ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #LK -> #GC
      <- Switch[1279589731] #LJ -> #GC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GC...GB] -> AAS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GC -> #GB
===#Block AAS(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -930481773:
      	 goto	#AAU
      case 1743896618:
      	 goto	#AAT
      default:
      	 goto	#AAV
   }
      -> DefaultSwitch #AAS -> #AAV
      -> Switch[1743896618] #AAS -> #AAT
      -> Switch[-930481773] #AAS -> #AAU
      <- TryCatch range: [GC...GB] -> AAS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GC...GB] -> AAS ([Ljava/lang/IllegalAccessException;])
===#Block AAU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 120835528);
   1. goto GD
      -> UnconditionalJump[GOTO] #AAU -> #GD
      <- Switch[-930481773] #AAS -> #AAU
===#Block AAT(size=2, flags=10100)===
   0. lvar105 = {853330687 ^ lvar105};
   1. goto GD
      -> UnconditionalJump[GOTO] #AAT -> #GD
      <- Switch[1743896618] #AAS -> #AAT
===#Block GD(size=2, flags=0)===
   0. _consume(catch());
   1. goto QL
      -> UnconditionalJump[GOTO] #GD -> #QL
      <- UnconditionalJump[GOTO] #AAT -> #GD
      <- UnconditionalJump[GOTO] #AAU -> #GD
===#Block QL(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 73648358:
      	 goto	#QM
      case 1454689451:
      	 goto	#QL
      case 1970482566:
      	 goto	#CN
      case 2086976937:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #QL -> #QM
      -> Switch[1454689451] #QL -> #QL
      -> Switch[73648358] #QL -> #QM
      -> Switch[1970482566] #QL -> #CN
      -> Switch[2086976937] #QL -> #JC
      -> DefaultSwitch #QL -> #JC
      <- Switch[1454689451] #QL -> #QL
      <- UnconditionalJump[GOTO] #GD -> #QL
===#Block QM(size=2, flags=100)===
   0. lvar105 = {1469842807 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QM -> #CN
      <- Immediate #QL -> #QM
      <- Switch[73648358] #QL -> #QM
===#Block AAV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAS -> #AAV
===#Block PI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 776124232);
   1. goto JC
      -> UnconditionalJump[GOTO] #PI -> #JC
      <- UnconditionalJump[GOTO] #JW -> #PI
===#Block BC(size=1, flags=0)===
   0. goto MB
      -> UnconditionalJump[GOTO] #BC -> #MB
      <- Immediate #BA -> #BC
===#Block MB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1391000738);
   1. goto IE
      -> UnconditionalJump[GOTO] #MB -> #IE
      <- UnconditionalJump[GOTO] #BC -> #MB
===#Block IE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 254151275)
      goto ID
   1. throw nullconst;
      -> TryCatch range: [IE...ID] -> ADM ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IE -> #ID
      <- UnconditionalJump[GOTO] #MB -> #IE
===#Block ID(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IE...ID] -> ADM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IE -> #ID
===#Block ADM(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1028802896:
      	 goto	#ADN
      case -953835453:
      	 goto	#ADO
      default:
      	 goto	#ADP
   }
      -> DefaultSwitch #ADM -> #ADP
      -> Switch[-1028802896] #ADM -> #ADN
      -> Switch[-953835453] #ADM -> #ADO
      <- TryCatch range: [IE...ID] -> ADM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IE...ID] -> ADM ([Ljava/lang/IllegalAccessException;])
===#Block ADO(size=2, flags=10100)===
   0. lvar105 = {407804122 ^ lvar105};
   1. goto IF
      -> UnconditionalJump[GOTO] #ADO -> #IF
      <- Switch[-953835453] #ADM -> #ADO
===#Block ADN(size=2, flags=10100)===
   0. lvar105 = {2030017028 ^ lvar105};
   1. goto IF
      -> UnconditionalJump[GOTO] #ADN -> #IF
      <- Switch[-1028802896] #ADM -> #ADN
===#Block IF(size=2, flags=0)===
   0. _consume(catch());
   1. goto PT
      -> UnconditionalJump[GOTO] #IF -> #PT
      <- UnconditionalJump[GOTO] #ADN -> #IF
      <- UnconditionalJump[GOTO] #ADO -> #IF
===#Block PT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 678564101);
   1. goto BJ
      -> UnconditionalJump[GOTO] #PT -> #BJ
      <- UnconditionalJump[GOTO] #IF -> #PT
===#Block ADP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ADM -> #ADP
===#Block VL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 222177539);
   1. goto BG
      -> UnconditionalJump[GOTO] #VL -> #BG
      <- Switch[33641550] #AH -> #VL
===#Block BG(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar52 = lvar102;
   2. lvar92 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.asyufbtclwkyocu(), lvar105);
   3. lvar53 = lvar52.equals(lvar92);
   4. if (lvar53 != {1634585088 ^ lvar105})
      goto RU
   5. lvar105 = {509143741 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BG -> #RU
      -> Immediate #BG -> #BI
      <- UnconditionalJump[GOTO] #VL -> #BG
===#Block BI(size=1, flags=0)===
   0. goto LT
      -> UnconditionalJump[GOTO] #BI -> #LT
      <- Immediate #BG -> #BI
===#Block LT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1177153508);
   1. goto HM
      -> UnconditionalJump[GOTO] #LT -> #HM
      <- UnconditionalJump[GOTO] #BI -> #LT
===#Block HM(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 245885088)
      goto HL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HM -> #HL
      -> TryCatch range: [HM...HL] -> ACO ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #LT -> #HM
===#Block HL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HM...HL] -> ACO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HM -> #HL
===#Block ACO(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -924419383:
      	 goto	#ACP
      case 2025115018:
      	 goto	#ACQ
      default:
      	 goto	#ACR
   }
      -> DefaultSwitch #ACO -> #ACR
      -> Switch[-924419383] #ACO -> #ACP
      -> Switch[2025115018] #ACO -> #ACQ
      <- TryCatch range: [HM...HL] -> ACO ([Ljava/io/IOException;])
      <- TryCatch range: [HM...HL] -> ACO ([Ljava/io/IOException;])
===#Block ACQ(size=2, flags=10100)===
   0. lvar105 = {1802016143 ^ lvar105};
   1. goto HN
      -> UnconditionalJump[GOTO] #ACQ -> #HN
      <- Switch[2025115018] #ACO -> #ACQ
===#Block ACP(size=2, flags=10100)===
   0. lvar105 = {493011815 ^ lvar105};
   1. goto HN
      -> UnconditionalJump[GOTO] #ACP -> #HN
      <- Switch[-924419383] #ACO -> #ACP
===#Block HN(size=2, flags=0)===
   0. _consume(catch());
   1. goto NE
      -> UnconditionalJump[GOTO] #HN -> #NE
      <- UnconditionalJump[GOTO] #ACP -> #HN
      <- UnconditionalJump[GOTO] #ACQ -> #HN
===#Block NE(size=2, flags=10100)===
   0. lvar105 = {1822831081 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #NE -> #BJ
      <- UnconditionalJump[GOTO] #HN -> #NE
===#Block ACR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACO -> #ACR
===#Block RU(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1434111938);
   1. goto JD
      -> UnconditionalJump[GOTO] #RU -> #JD
      <- ConditionalJump[IF_ICMPNE] #BG -> #RU
===#Block JD(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1598542319)
      goto TB
   1. goto QB
      -> UnconditionalJump[GOTO] #JD -> #QB
      -> ConditionalJump[IF_ICMPEQ] #JD -> #TB
      <- UnconditionalJump[GOTO] #RU -> #JD
===#Block TB(size=2, flags=10100)===
   0. lvar105 = {377111843 ^ lvar105};
   1. goto BH
      -> UnconditionalJump[GOTO] #TB -> #BH
      <- ConditionalJump[IF_ICMPEQ] #JD -> #TB
===#Block BH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_CONCRETE_POWDER;
   2. goto NI
      -> UnconditionalJump[GOTO] #BH -> #NI
      <- UnconditionalJump[GOTO] #TB -> #BH
===#Block NI(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 45322206:
      	 goto	#NJ
      case 295028727:
      	 goto	#GU
      case 1399178255:
      	 goto	#NI
      case 1772082589:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[45322206] #NI -> #NJ
      -> Immediate #NI -> #NJ
      -> Switch[295028727] #NI -> #GU
      -> Switch[1399178255] #NI -> #NI
      -> Switch[1772082589] #NI -> #JC
      -> DefaultSwitch #NI -> #JC
      <- UnconditionalJump[GOTO] #BH -> #NI
      <- Switch[1399178255] #NI -> #NI
===#Block NJ(size=2, flags=100)===
   0. lvar105 = {2108735308 ^ lvar105};
   1. goto GU
      -> UnconditionalJump[GOTO] #NJ -> #GU
      <- Switch[45322206] #NI -> #NJ
      <- Immediate #NI -> #NJ
===#Block GU(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 163463298)
      goto GT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GU -> #GT
      -> TryCatch range: [GU...GT] -> ABQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #NJ -> #GU
      <- Switch[295028727] #NI -> #GU
===#Block GT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GU...GT] -> ABQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GU -> #GT
===#Block ABQ(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -17826454:
      	 goto	#ABR
      case 1182577073:
      	 goto	#ABS
      default:
      	 goto	#ABT
   }
      -> DefaultSwitch #ABQ -> #ABT
      -> Switch[-17826454] #ABQ -> #ABR
      -> Switch[1182577073] #ABQ -> #ABS
      <- TryCatch range: [GU...GT] -> ABQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GU...GT] -> ABQ ([Ljava/lang/IllegalAccessException;])
===#Block ABS(size=2, flags=10100)===
   0. lvar105 = {1675228780 ^ lvar105};
   1. goto GV
      -> UnconditionalJump[GOTO] #ABS -> #GV
      <- Switch[1182577073] #ABQ -> #ABS
===#Block ABR(size=2, flags=10100)===
   0. lvar105 = {348889079 ^ lvar105};
   1. goto GV
      -> UnconditionalJump[GOTO] #ABR -> #GV
      <- Switch[-17826454] #ABQ -> #ABR
===#Block GV(size=2, flags=0)===
   0. _consume(catch());
   1. goto NS
      -> UnconditionalJump[GOTO] #GV -> #NS
      <- UnconditionalJump[GOTO] #ABS -> #GV
      <- UnconditionalJump[GOTO] #ABR -> #GV
===#Block NS(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 24842680:
      	 goto	#NT
      case 1146135205:
      	 goto	#NS
      case 1389413467:
      	 goto	#CN
      case 1421321762:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[24842680] #NS -> #NT
      -> Switch[1146135205] #NS -> #NS
      -> DefaultSwitch #NS -> #JC
      -> Switch[1421321762] #NS -> #JC
      -> Immediate #NS -> #NT
      -> Switch[1389413467] #NS -> #CN
      <- Switch[1146135205] #NS -> #NS
      <- UnconditionalJump[GOTO] #GV -> #NS
===#Block NT(size=2, flags=100)===
   0. lvar105 = {1655216087 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NT -> #CN
      <- Switch[24842680] #NS -> #NT
      <- Immediate #NS -> #NT
===#Block ABT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABQ -> #ABT
===#Block QB(size=2, flags=10100)===
   0. lvar105 = {610501547 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #QB -> #JC
      <- UnconditionalJump[GOTO] #JD -> #QB
===#Block VI(size=2, flags=10100)===
   0. lvar105 = {1592554629 ^ lvar105};
   1. goto AR
      -> UnconditionalJump[GOTO] #VI -> #AR
      <- Switch[33641544] #AH -> #VI
===#Block AR(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar42 = lvar102;
   2. lvar87 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.jhoayhdbevbwmmg(), lvar105);
   3. lvar43 = lvar42.equals(lvar87);
   4. if (lvar43 != {851416966 ^ lvar105})
      goto SJ
   5. lvar105 = {978946793 ^ lvar105};
      -> Immediate #AR -> #AT
      -> ConditionalJump[IF_ICMPNE] #AR -> #SJ
      <- UnconditionalJump[GOTO] #VI -> #AR
===#Block SJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1965891854);
   1. goto JQ
      -> UnconditionalJump[GOTO] #SJ -> #JQ
      <- ConditionalJump[IF_ICMPNE] #AR -> #SJ
===#Block JQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1016443970)
      goto RW
   1. goto KR
      -> UnconditionalJump[GOTO] #JQ -> #KR
      -> ConditionalJump[IF_ICMPEQ] #JQ -> #RW
      <- UnconditionalJump[GOTO] #SJ -> #JQ
===#Block RW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 652557163);
   1. goto AS
      -> UnconditionalJump[GOTO] #RW -> #AS
      <- ConditionalJump[IF_ICMPEQ] #JQ -> #RW
===#Block AS(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_CONCRETE_POWDER;
   2. goto QN
      -> UnconditionalJump[GOTO] #AS -> #QN
      <- UnconditionalJump[GOTO] #RW -> #AS
===#Block QN(size=2, flags=10100)===
   0. lvar105 = {40910948 ^ lvar105};
   1. goto EY
      -> UnconditionalJump[GOTO] #QN -> #EY
      <- UnconditionalJump[GOTO] #AS -> #QN
===#Block EY(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 266304818)
      goto EX
   1. throw nullconst;
      -> TryCatch range: [EY...EX] -> ZE ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #EY -> #EX
      <- UnconditionalJump[GOTO] #QN -> #EY
===#Block EX(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EY...EX] -> ZE ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EY -> #EX
===#Block ZE(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 406744123:
      	 goto	#ZF
      case 1147495499:
      	 goto	#ZG
      default:
      	 goto	#ZH
   }
      -> DefaultSwitch #ZE -> #ZH
      -> Switch[406744123] #ZE -> #ZF
      -> Switch[1147495499] #ZE -> #ZG
      <- TryCatch range: [EY...EX] -> ZE ([Ljava/io/IOException;])
      <- TryCatch range: [EY...EX] -> ZE ([Ljava/io/IOException;])
===#Block ZG(size=2, flags=10100)===
   0. lvar105 = {1537790616 ^ lvar105};
   1. goto EZ
      -> UnconditionalJump[GOTO] #ZG -> #EZ
      <- Switch[1147495499] #ZE -> #ZG
===#Block ZF(size=2, flags=10100)===
   0. lvar105 = {1344517014 ^ lvar105};
   1. goto EZ
      -> UnconditionalJump[GOTO] #ZF -> #EZ
      <- Switch[406744123] #ZE -> #ZF
===#Block EZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto LD
      -> UnconditionalJump[GOTO] #EZ -> #LD
      <- UnconditionalJump[GOTO] #ZG -> #EZ
      <- UnconditionalJump[GOTO] #ZF -> #EZ
===#Block LD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 446335388);
   1. goto CN
      -> UnconditionalJump[GOTO] #LD -> #CN
      <- UnconditionalJump[GOTO] #EZ -> #LD
===#Block ZH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ZE -> #ZH
===#Block KR(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 179045941:
      	 goto	#KS
      case 217149347:
      	 goto	#JC
      case 1548372534:
      	 goto	#KR
      case 2050200866:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[179045941] #KR -> #KS
      -> Switch[1548372534] #KR -> #KR
      -> Immediate #KR -> #KS
      -> Switch[2050200866] #KR -> #JC
      -> DefaultSwitch #KR -> #JC
      <- UnconditionalJump[GOTO] #JQ -> #KR
      <- Switch[1548372534] #KR -> #KR
===#Block KS(size=2, flags=100)===
   0. lvar105 = {1474703585 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #KS -> #JC
      <- Switch[179045941] #KR -> #KS
      <- Immediate #KR -> #KS
===#Block AT(size=1, flags=0)===
   0. goto MD
      -> UnconditionalJump[GOTO] #AT -> #MD
      <- Immediate #AR -> #AT
===#Block MD(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 20828641:
      	 goto	#ME
      case 252215002:
      	 goto	#IN
      case 585585669:
      	 goto	#JC
      case 1684580759:
      	 goto	#MD
      default:
      	 goto	#JC
   }
      -> Switch[20828641] #MD -> #ME
      -> Switch[1684580759] #MD -> #MD
      -> DefaultSwitch #MD -> #JC
      -> Switch[252215002] #MD -> #IN
      -> Switch[585585669] #MD -> #JC
      -> Immediate #MD -> #ME
      <- Switch[1684580759] #MD -> #MD
      <- UnconditionalJump[GOTO] #AT -> #MD
===#Block ME(size=2, flags=100)===
   0. lvar105 = {268103941 ^ lvar105};
   1. goto IN
      -> UnconditionalJump[GOTO] #ME -> #IN
      <- Switch[20828641] #MD -> #ME
      <- Immediate #MD -> #ME
===#Block IN(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 111876799)
      goto IM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IN -> #IM
      -> TryCatch range: [IN...IM] -> ADY ([Ljava/lang/RuntimeException;])
      <- Switch[252215002] #MD -> #IN
      <- UnconditionalJump[GOTO] #ME -> #IN
===#Block IM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IN...IM] -> ADY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IN -> #IM
===#Block ADY(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 954688336:
      	 goto	#ADZ
      case 1133338209:
      	 goto	#AEA
      default:
      	 goto	#AEB
   }
      -> DefaultSwitch #ADY -> #AEB
      -> Switch[1133338209] #ADY -> #AEA
      -> Switch[954688336] #ADY -> #ADZ
      <- TryCatch range: [IN...IM] -> ADY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IN...IM] -> ADY ([Ljava/lang/RuntimeException;])
===#Block ADZ(size=2, flags=10100)===
   0. lvar105 = {954103150 ^ lvar105};
   1. goto IO
      -> UnconditionalJump[GOTO] #ADZ -> #IO
      <- Switch[954688336] #ADY -> #ADZ
===#Block AEA(size=2, flags=10100)===
   0. lvar105 = {397619912 ^ lvar105};
   1. goto IO
      -> UnconditionalJump[GOTO] #AEA -> #IO
      <- Switch[1133338209] #ADY -> #AEA
===#Block IO(size=2, flags=0)===
   0. _consume(catch());
   1. goto PJ
      -> UnconditionalJump[GOTO] #IO -> #PJ
      <- UnconditionalJump[GOTO] #AEA -> #IO
      <- UnconditionalJump[GOTO] #ADZ -> #IO
===#Block PJ(size=2, flags=10100)===
   0. lvar105 = {1998234835 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #PJ -> #BJ
      <- UnconditionalJump[GOTO] #IO -> #PJ
===#Block AEB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADY -> #AEB
===#Block VO(size=2, flags=10100)===
   0. lvar105 = {613146324 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #VO -> #BJ
      <- DefaultSwitch #AH -> #VO
===#Block VJ(size=2, flags=10100)===
   0. lvar105 = {1385112123 ^ lvar105};
   1. goto AX
      -> UnconditionalJump[GOTO] #VJ -> #AX
      <- Switch[33641546] #AH -> #VJ
===#Block AX(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar46 = lvar102;
   2. lvar89 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.uluftgmjjdrqdib(), lvar105);
   3. lvar47 = lvar46.equals(lvar89);
   4. if (lvar47 != {1054654776 ^ lvar105})
      goto TI
   5. lvar105 = {302171231 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AX -> #TI
      -> Immediate #AX -> #AY
      <- UnconditionalJump[GOTO] #VJ -> #AX
===#Block AY(size=1, flags=0)===
   0. goto OO
      -> UnconditionalJump[GOTO] #AY -> #OO
      <- Immediate #AX -> #AY
===#Block OO(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 6274788:
      	 goto	#EM
      case 116262569:
      	 goto	#OP
      case 203913136:
      	 goto	#JC
      case 963371925:
      	 goto	#OO
      default:
      	 goto	#JC
   }
      -> Switch[203913136] #OO -> #JC
      -> Switch[116262569] #OO -> #OP
      -> Switch[963371925] #OO -> #OO
      -> Immediate #OO -> #OP
      -> Switch[6274788] #OO -> #EM
      -> DefaultSwitch #OO -> #JC
      <- UnconditionalJump[GOTO] #AY -> #OO
      <- Switch[963371925] #OO -> #OO
===#Block OP(size=2, flags=100)===
   0. lvar105 = {1150556514 ^ lvar105};
   1. goto EM
      -> UnconditionalJump[GOTO] #OP -> #EM
      <- Switch[116262569] #OO -> #OP
      <- Immediate #OO -> #OP
===#Block EM(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 168820227)
      goto EL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EM -> #EL
      -> TryCatch range: [EM...EL] -> YO ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #OP -> #EM
      <- Switch[6274788] #OO -> #EM
===#Block EL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EM...EL] -> YO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EM -> #EL
===#Block YO(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -2057660149:
      	 goto	#YQ
      case 1112596523:
      	 goto	#YP
      default:
      	 goto	#YR
   }
      -> Switch[1112596523] #YO -> #YP
      -> DefaultSwitch #YO -> #YR
      -> Switch[-2057660149] #YO -> #YQ
      <- TryCatch range: [EM...EL] -> YO ([Ljava/io/IOException;])
      <- TryCatch range: [EM...EL] -> YO ([Ljava/io/IOException;])
===#Block YQ(size=2, flags=10100)===
   0. lvar105 = {1389605343 ^ lvar105};
   1. goto EN
      -> UnconditionalJump[GOTO] #YQ -> #EN
      <- Switch[-2057660149] #YO -> #YQ
===#Block YR(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #YO -> #YR
===#Block YP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1244856059);
   1. goto EN
      -> UnconditionalJump[GOTO] #YP -> #EN
      <- Switch[1112596523] #YO -> #YP
===#Block EN(size=2, flags=0)===
   0. _consume(catch());
   1. goto LG
      -> UnconditionalJump[GOTO] #EN -> #LG
      <- UnconditionalJump[GOTO] #YP -> #EN
      <- UnconditionalJump[GOTO] #YQ -> #EN
===#Block LG(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 46798260:
      	 goto	#LH
      case 780561794:
      	 goto	#LG
      case 1436541947:
      	 goto	#JC
      case 1485481227:
      	 goto	#BJ
      default:
      	 goto	#JC
   }
      -> Immediate #LG -> #LH
      -> Switch[1485481227] #LG -> #BJ
      -> DefaultSwitch #LG -> #JC
      -> Switch[46798260] #LG -> #LH
      -> Switch[1436541947] #LG -> #JC
      -> Switch[780561794] #LG -> #LG
      <- UnconditionalJump[GOTO] #EN -> #LG
      <- Switch[780561794] #LG -> #LG
===#Block LH(size=2, flags=100)===
   0. lvar105 = {1788927785 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #LH -> #BJ
      <- Immediate #LG -> #LH
      <- Switch[46798260] #LG -> #LH
===#Block TI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 2087788344);
   1. goto JE
      -> UnconditionalJump[GOTO] #TI -> #JE
      <- ConditionalJump[IF_ICMPNE] #AX -> #TI
===#Block JE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 359591938)
      goto TC
   1. goto QW
      -> UnconditionalJump[GOTO] #JE -> #QW
      -> ConditionalJump[IF_ICMPEQ] #JE -> #TC
      <- UnconditionalJump[GOTO] #TI -> #JE
===#Block TC(size=2, flags=10100)===
   0. lvar105 = {929602941 ^ lvar105};
   1. goto AZ
      -> UnconditionalJump[GOTO] #TC -> #AZ
      <- ConditionalJump[IF_ICMPEQ] #JE -> #TC
===#Block AZ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_CONCRETE_POWDER;
   2. goto ML
      -> UnconditionalJump[GOTO] #AZ -> #ML
      <- UnconditionalJump[GOTO] #TC -> #AZ
===#Block ML(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 70141703:
      	 goto	#MM
      case 414482294:
      	 goto	#ML
      case 896582108:
      	 goto	#DO
      case 1901487497:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[414482294] #ML -> #ML
      -> Switch[70141703] #ML -> #MM
      -> Immediate #ML -> #MM
      -> DefaultSwitch #ML -> #JC
      -> Switch[1901487497] #ML -> #JC
      -> Switch[896582108] #ML -> #DO
      <- Switch[414482294] #ML -> #ML
      <- UnconditionalJump[GOTO] #AZ -> #ML
===#Block MM(size=2, flags=100)===
   0. lvar105 = {493588700 ^ lvar105};
   1. goto DO
      -> UnconditionalJump[GOTO] #MM -> #DO
      <- Switch[70141703] #ML -> #MM
      <- Immediate #ML -> #MM
===#Block DO(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 181588649)
      goto DN
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DO -> #DN
      -> TryCatch range: [DO...DN] -> XI ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #MM -> #DO
      <- Switch[896582108] #ML -> #DO
===#Block DN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DO...DN] -> XI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DO -> #DN
===#Block XI(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1554539150:
      	 goto	#XK
      case 1165401355:
      	 goto	#XJ
      default:
      	 goto	#XL
   }
      -> Switch[1165401355] #XI -> #XJ
      -> DefaultSwitch #XI -> #XL
      -> Switch[-1554539150] #XI -> #XK
      <- TryCatch range: [DO...DN] -> XI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DO...DN] -> XI ([Ljava/lang/RuntimeException;])
===#Block XK(size=2, flags=10100)===
   0. lvar105 = {1448192165 ^ lvar105};
   1. goto DP
      -> UnconditionalJump[GOTO] #XK -> #DP
      <- Switch[-1554539150] #XI -> #XK
===#Block XL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #XI -> #XL
===#Block XJ(size=2, flags=10100)===
   0. lvar105 = {1788184746 ^ lvar105};
   1. goto DP
      -> UnconditionalJump[GOTO] #XJ -> #DP
      <- Switch[1165401355] #XI -> #XJ
===#Block DP(size=2, flags=0)===
   0. _consume(catch());
   1. goto NK
      -> UnconditionalJump[GOTO] #DP -> #NK
      <- UnconditionalJump[GOTO] #XJ -> #DP
      <- UnconditionalJump[GOTO] #XK -> #DP
===#Block NK(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 35104609:
      	 goto	#NL
      case 485439043:
      	 goto	#CN
      case 769082164:
      	 goto	#NK
      case 1654828388:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[485439043] #NK -> #CN
      -> Switch[769082164] #NK -> #NK
      -> Switch[35104609] #NK -> #NL
      -> Switch[1654828388] #NK -> #JC
      -> DefaultSwitch #NK -> #JC
      -> Immediate #NK -> #NL
      <- Switch[769082164] #NK -> #NK
      <- UnconditionalJump[GOTO] #DP -> #NK
===#Block NL(size=2, flags=100)===
   0. lvar105 = {730180742 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NL -> #CN
      <- Switch[35104609] #NK -> #NL
      <- Immediate #NK -> #NL
===#Block QW(size=2, flags=10100)===
   0. lvar105 = {1389975657 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #QW -> #JC
      <- UnconditionalJump[GOTO] #JE -> #QW
===#Block VH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1333361535);
   1. goto AI
      -> UnconditionalJump[GOTO] #VH -> #AI
      <- Switch[33641542] #AH -> #VH
===#Block AI(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar36 = lvar102;
   2. lvar84 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ryojhfxxmxgkmrf(), lvar105);
   3. lvar37 = lvar36.equals(lvar84);
   4. if (lvar37 != {589994108 ^ lvar105})
      goto SP
   5. lvar105 = {1307182429 ^ lvar105};
      -> Immediate #AI -> #AJ
      -> ConditionalJump[IF_ICMPNE] #AI -> #SP
      <- UnconditionalJump[GOTO] #VH -> #AI
===#Block SP(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 69379162:
      	 goto	#SQ
      case 430017599:
      	 goto	#JU
      case 1224287282:
      	 goto	#SP
      case 1876720141:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[430017599] #SP -> #JU
      -> Switch[1224287282] #SP -> #SP
      -> Switch[1876720141] #SP -> #JC
      -> Switch[69379162] #SP -> #SQ
      -> DefaultSwitch #SP -> #JC
      -> Immediate #SP -> #SQ
      <- Switch[1224287282] #SP -> #SP
      <- ConditionalJump[IF_ICMPNE] #AI -> #SP
===#Block SQ(size=2, flags=100)===
   0. lvar105 = {1603237861 ^ lvar105};
   1. goto JU
      -> UnconditionalJump[GOTO] #SQ -> #JU
      <- Switch[69379162] #SP -> #SQ
      <- Immediate #SP -> #SQ
===#Block JU(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -449872693)
      goto TN
   1. goto MO
      -> ConditionalJump[IF_ICMPEQ] #JU -> #TN
      -> UnconditionalJump[GOTO] #JU -> #MO
      <- Switch[430017599] #SP -> #JU
      <- UnconditionalJump[GOTO] #SQ -> #JU
===#Block MO(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 25296029:
      	 goto	#MP
      case 383298315:
      	 goto	#JC
      case 775375486:
      	 goto	#MO
      case 1074987053:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #MO -> #JC
      -> Switch[25296029] #MO -> #MP
      -> Switch[1074987053] #MO -> #JC
      -> Immediate #MO -> #MP
      -> Switch[775375486] #MO -> #MO
      <- UnconditionalJump[GOTO] #JU -> #MO
      <- Switch[775375486] #MO -> #MO
===#Block MP(size=2, flags=100)===
   0. lvar105 = {1825663472 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MP -> #JC
      <- Switch[25296029] #MO -> #MP
      <- Immediate #MO -> #MP
===#Block TN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 730026976);
   1. goto AK
      -> UnconditionalJump[GOTO] #TN -> #AK
      <- ConditionalJump[IF_ICMPEQ] #JU -> #TN
===#Block AK(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_CONCRETE_POWDER;
   2. goto NG
      -> UnconditionalJump[GOTO] #AK -> #NG
      <- UnconditionalJump[GOTO] #TN -> #AK
===#Block NG(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 148593164:
      	 goto	#NH
      case 581026089:
      	 goto	#GR
      case 741139399:
      	 goto	#JC
      case 1210542784:
      	 goto	#NG
      default:
      	 goto	#JC
   }
      -> Switch[148593164] #NG -> #NH
      -> Switch[1210542784] #NG -> #NG
      -> DefaultSwitch #NG -> #JC
      -> Switch[581026089] #NG -> #GR
      -> Switch[741139399] #NG -> #JC
      -> Immediate #NG -> #NH
      <- Switch[1210542784] #NG -> #NG
      <- UnconditionalJump[GOTO] #AK -> #NG
===#Block NH(size=2, flags=100)===
   0. lvar105 = {850043735 ^ lvar105};
   1. goto GR
      -> UnconditionalJump[GOTO] #NH -> #GR
      <- Switch[148593164] #NG -> #NH
      <- Immediate #NG -> #NH
===#Block GR(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 79632869)
      goto GQ
   1. throw nullconst;
      -> TryCatch range: [GR...GQ] -> ABM ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GR -> #GQ
      <- UnconditionalJump[GOTO] #NH -> #GR
      <- Switch[581026089] #NG -> #GR
===#Block GQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GR...GQ] -> ABM ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GR -> #GQ
===#Block ABM(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1434649551:
      	 goto	#ABO
      case 744552819:
      	 goto	#ABN
      default:
      	 goto	#ABP
   }
      -> Switch[-1434649551] #ABM -> #ABO
      -> Switch[744552819] #ABM -> #ABN
      -> DefaultSwitch #ABM -> #ABP
      <- TryCatch range: [GR...GQ] -> ABM ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GR...GQ] -> ABM ([Ljava/lang/RuntimeException;])
===#Block ABP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABM -> #ABP
===#Block ABN(size=2, flags=10100)===
   0. lvar105 = {446757199 ^ lvar105};
   1. goto GS
      -> UnconditionalJump[GOTO] #ABN -> #GS
      <- Switch[744552819] #ABM -> #ABN
===#Block ABO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1248033255);
   1. goto GS
      -> UnconditionalJump[GOTO] #ABO -> #GS
      <- Switch[-1434649551] #ABM -> #ABO
===#Block GS(size=2, flags=0)===
   0. _consume(catch());
   1. goto QP
      -> UnconditionalJump[GOTO] #GS -> #QP
      <- UnconditionalJump[GOTO] #ABO -> #GS
      <- UnconditionalJump[GOTO] #ABN -> #GS
===#Block QP(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 107381111:
      	 goto	#QQ
      case 393060633:
      	 goto	#JC
      case 1238062078:
      	 goto	#CN
      case 1905097677:
      	 goto	#QP
      default:
      	 goto	#JC
   }
      -> Immediate #QP -> #QQ
      -> Switch[1238062078] #QP -> #CN
      -> Switch[393060633] #QP -> #JC
      -> DefaultSwitch #QP -> #JC
      -> Switch[1905097677] #QP -> #QP
      -> Switch[107381111] #QP -> #QQ
      <- UnconditionalJump[GOTO] #GS -> #QP
      <- Switch[1905097677] #QP -> #QP
===#Block QQ(size=2, flags=100)===
   0. lvar105 = {1452413420 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QQ -> #CN
      <- Immediate #QP -> #QQ
      <- Switch[107381111] #QP -> #QQ
===#Block AJ(size=1, flags=0)===
   0. goto QU
      -> UnconditionalJump[GOTO] #AJ -> #QU
      <- Immediate #AI -> #AJ
===#Block QU(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 110452399:
      	 goto	#QV
      case 409272081:
      	 goto	#JC
      case 1497538543:
      	 goto	#FN
      case 1609818405:
      	 goto	#QU
      default:
      	 goto	#JC
   }
      -> Immediate #QU -> #QV
      -> Switch[110452399] #QU -> #QV
      -> DefaultSwitch #QU -> #JC
      -> Switch[1497538543] #QU -> #FN
      -> Switch[1609818405] #QU -> #QU
      -> Switch[409272081] #QU -> #JC
      <- UnconditionalJump[GOTO] #AJ -> #QU
      <- Switch[1609818405] #QU -> #QU
===#Block QV(size=2, flags=100)===
   0. lvar105 = {1168616407 ^ lvar105};
   1. goto FN
      -> UnconditionalJump[GOTO] #QV -> #FN
      <- Immediate #QU -> #QV
      <- Switch[110452399] #QU -> #QV
===#Block FN(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 68669723)
      goto FM
   1. throw nullconst;
      -> TryCatch range: [FN...FM] -> ZY ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #FN -> #FM
      <- Switch[1497538543] #QU -> #FN
      <- UnconditionalJump[GOTO] #QV -> #FN
===#Block FM(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FN...FM] -> ZY ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FN -> #FM
===#Block ZY(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1582694717:
      	 goto	#AAA
      case 1530509233:
      	 goto	#ZZ
      default:
      	 goto	#AAB
   }
      -> Switch[-1582694717] #ZY -> #AAA
      -> DefaultSwitch #ZY -> #AAB
      -> Switch[1530509233] #ZY -> #ZZ
      <- TryCatch range: [FN...FM] -> ZY ([Ljava/io/IOException;])
      <- TryCatch range: [FN...FM] -> ZY ([Ljava/io/IOException;])
===#Block ZZ(size=2, flags=10100)===
   0. lvar105 = {1942929687 ^ lvar105};
   1. goto FO
      -> UnconditionalJump[GOTO] #ZZ -> #FO
      <- Switch[1530509233] #ZY -> #ZZ
===#Block AAB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZY -> #AAB
===#Block AAA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 748472377);
   1. goto FO
      -> UnconditionalJump[GOTO] #AAA -> #FO
      <- Switch[-1582694717] #ZY -> #AAA
===#Block FO(size=2, flags=0)===
   0. _consume(catch());
   1. goto PC
      -> UnconditionalJump[GOTO] #FO -> #PC
      <- UnconditionalJump[GOTO] #AAA -> #FO
      <- UnconditionalJump[GOTO] #ZZ -> #FO
===#Block PC(size=2, flags=10100)===
   0. lvar105 = {275892790 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #PC -> #BJ
      <- UnconditionalJump[GOTO] #FO -> #PC
===#Block VN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1761674368);
   1. goto AU
      -> UnconditionalJump[GOTO] #VN -> #AU
      <- Switch[33641558] #AH -> #VN
===#Block AU(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar44 = lvar102;
   2. lvar88 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.pnczhzkzhwzcxye(), lvar105);
   3. lvar45 = lvar44.equals(lvar88);
   4. if (lvar45 != {89319299 ^ lvar105})
      goto SD
   5. lvar105 = {279129357 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #AU -> #SD
      -> Immediate #AU -> #AV
      <- UnconditionalJump[GOTO] #VN -> #AU
===#Block AV(size=1, flags=0)===
   0. goto LM
      -> UnconditionalJump[GOTO] #AV -> #LM
      <- Immediate #AU -> #AV
===#Block LM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 843822056);
   1. goto GL
      -> UnconditionalJump[GOTO] #LM -> #GL
      <- UnconditionalJump[GOTO] #AV -> #LM
===#Block GL(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 217672847)
      goto GK
   1. throw nullconst;
      -> TryCatch range: [GL...GK] -> ABE ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GL -> #GK
      <- UnconditionalJump[GOTO] #LM -> #GL
===#Block GK(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GL...GK] -> ABE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GL -> #GK
===#Block ABE(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -105063335:
      	 goto	#ABG
      case 1037290289:
      	 goto	#ABF
      default:
      	 goto	#ABH
   }
      -> Switch[-105063335] #ABE -> #ABG
      -> Switch[1037290289] #ABE -> #ABF
      -> DefaultSwitch #ABE -> #ABH
      <- TryCatch range: [GL...GK] -> ABE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GL...GK] -> ABE ([Ljava/lang/RuntimeException;])
===#Block ABH(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABE -> #ABH
===#Block ABF(size=2, flags=10100)===
   0. lvar105 = {2101759761 ^ lvar105};
   1. goto GM
      -> UnconditionalJump[GOTO] #ABF -> #GM
      <- Switch[1037290289] #ABE -> #ABF
===#Block ABG(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1707847164);
   1. goto GM
      -> UnconditionalJump[GOTO] #ABG -> #GM
      <- Switch[-105063335] #ABE -> #ABG
===#Block GM(size=2, flags=0)===
   0. _consume(catch());
   1. goto RE
      -> UnconditionalJump[GOTO] #GM -> #RE
      <- UnconditionalJump[GOTO] #ABG -> #GM
      <- UnconditionalJump[GOTO] #ABF -> #GM
===#Block RE(size=2, flags=10100)===
   0. lvar105 = {304352160 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #RE -> #BJ
      <- UnconditionalJump[GOTO] #GM -> #RE
===#Block SD(size=2, flags=10100)===
   0. lvar105 = {1956933076 ^ lvar105};
   1. goto JJ
      -> UnconditionalJump[GOTO] #SD -> #JJ
      <- ConditionalJump[IF_ICMPNE] #AU -> #SD
===#Block JJ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1884007749)
      goto UP
   1. goto RP
      -> ConditionalJump[IF_ICMPEQ] #JJ -> #UP
      -> UnconditionalJump[GOTO] #JJ -> #RP
      <- UnconditionalJump[GOTO] #SD -> #JJ
===#Block RP(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 19770517:
      	 goto	#JC
      case 87800924:
      	 goto	#RP
      case 214818302:
      	 goto	#RQ
      case 1280335975:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #RP -> #RQ
      -> Switch[87800924] #RP -> #RP
      -> Switch[214818302] #RP -> #RQ
      -> DefaultSwitch #RP -> #JC
      -> Switch[1280335975] #RP -> #JC
      <- Switch[87800924] #RP -> #RP
      <- UnconditionalJump[GOTO] #JJ -> #RP
===#Block RQ(size=2, flags=100)===
   0. lvar105 = {1635912766 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #RQ -> #JC
      <- Immediate #RP -> #RQ
      <- Switch[214818302] #RP -> #RQ
===#Block UP(size=2, flags=10100)===
   0. lvar105 = {330545872 ^ lvar105};
   1. goto AW
      -> UnconditionalJump[GOTO] #UP -> #AW
      <- ConditionalJump[IF_ICMPEQ] #JJ -> #UP
===#Block AW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_CONCRETE_POWDER;
   2. goto NF
      -> UnconditionalJump[GOTO] #AW -> #NF
      <- UnconditionalJump[GOTO] #UP -> #AW
===#Block NF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 799943811);
   1. goto GO
      -> UnconditionalJump[GOTO] #NF -> #GO
      <- UnconditionalJump[GOTO] #AW -> #NF
===#Block GO(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 116902828)
      goto GN
   1. throw nullconst;
      -> TryCatch range: [GO...GN] -> ABI ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #GO -> #GN
      <- UnconditionalJump[GOTO] #NF -> #GO
===#Block GN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GO...GN] -> ABI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GO -> #GN
===#Block ABI(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 740902160:
      	 goto	#ABK
      case 1868095522:
      	 goto	#ABJ
      default:
      	 goto	#ABL
   }
      -> Switch[1868095522] #ABI -> #ABJ
      -> Switch[740902160] #ABI -> #ABK
      -> DefaultSwitch #ABI -> #ABL
      <- TryCatch range: [GO...GN] -> ABI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GO...GN] -> ABI ([Ljava/lang/IllegalAccessException;])
===#Block ABL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ABI -> #ABL
===#Block ABK(size=2, flags=10100)===
   0. lvar105 = {947499333 ^ lvar105};
   1. goto GP
      -> UnconditionalJump[GOTO] #ABK -> #GP
      <- Switch[740902160] #ABI -> #ABK
===#Block ABJ(size=2, flags=10100)===
   0. lvar105 = {1880589795 ^ lvar105};
   1. goto GP
      -> UnconditionalJump[GOTO] #ABJ -> #GP
      <- Switch[1868095522] #ABI -> #ABJ
===#Block GP(size=2, flags=0)===
   0. _consume(catch());
   1. goto LU
      -> UnconditionalJump[GOTO] #GP -> #LU
      <- UnconditionalJump[GOTO] #ABJ -> #GP
      <- UnconditionalJump[GOTO] #ABK -> #GP
===#Block LU(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 135878611:
      	 goto	#LV
      case 306589607:
      	 goto	#JC
      case 359444912:
      	 goto	#CN
      case 1168905008:
      	 goto	#LU
      default:
      	 goto	#JC
   }
      -> Immediate #LU -> #LV
      -> Switch[135878611] #LU -> #LV
      -> Switch[1168905008] #LU -> #LU
      -> Switch[359444912] #LU -> #CN
      -> Switch[306589607] #LU -> #JC
      -> DefaultSwitch #LU -> #JC
      <- UnconditionalJump[GOTO] #GP -> #LU
      <- Switch[1168905008] #LU -> #LU
===#Block LV(size=2, flags=100)===
   0. lvar105 = {339897962 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #LV -> #CN
      <- Immediate #LU -> #LV
      <- Switch[135878611] #LU -> #LV
===#Block VM(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 153385381);
   1. goto BD
      -> UnconditionalJump[GOTO] #VM -> #BD
      <- Switch[33641556] #AH -> #VM
===#Block BD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar50 = lvar102;
   2. lvar91 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.bmmkvsxmrdvgbdj(), lvar105);
   3. lvar51 = lvar50.equals(lvar91);
   4. if (lvar51 != {1702337190 ^ lvar105})
      goto TY
   5. lvar105 = {673201722 ^ lvar105};
      -> Immediate #BD -> #BE
      -> ConditionalJump[IF_ICMPNE] #BD -> #TY
      <- UnconditionalJump[GOTO] #VM -> #BD
===#Block TY(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 569133104);
   1. goto JS
      -> UnconditionalJump[GOTO] #TY -> #JS
      <- ConditionalJump[IF_ICMPNE] #BD -> #TY
===#Block JS(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 618566834)
      goto SW
   1. goto PP
      -> ConditionalJump[IF_ICMPEQ] #JS -> #SW
      -> UnconditionalJump[GOTO] #JS -> #PP
      <- UnconditionalJump[GOTO] #TY -> #JS
===#Block PP(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1424971007);
   1. goto JC
      -> UnconditionalJump[GOTO] #PP -> #JC
      <- UnconditionalJump[GOTO] #JS -> #PP
===#Block SW(size=2, flags=10100)===
   0. lvar105 = {964023626 ^ lvar105};
   1. goto BF
      -> UnconditionalJump[GOTO] #SW -> #BF
      <- ConditionalJump[IF_ICMPEQ] #JS -> #SW
===#Block BF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.MAGENTA_CONCRETE_POWDER;
   2. goto LO
      -> UnconditionalJump[GOTO] #BF -> #LO
      <- UnconditionalJump[GOTO] #SW -> #BF
===#Block LO(size=2, flags=10100)===
   0. lvar105 = {892791794 ^ lvar105};
   1. goto GX
      -> UnconditionalJump[GOTO] #LO -> #GX
      <- UnconditionalJump[GOTO] #BF -> #LO
===#Block GX(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 220463425)
      goto GW
   1. throw nullconst;
      -> TryCatch range: [GX...GW] -> ABU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #GX -> #GW
      <- UnconditionalJump[GOTO] #LO -> #GX
===#Block GW(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GX...GW] -> ABU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GX -> #GW
===#Block ABU(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 968783771:
      	 goto	#ABW
      case 1187471730:
      	 goto	#ABV
      default:
      	 goto	#ABX
   }
      -> DefaultSwitch #ABU -> #ABX
      -> Switch[1187471730] #ABU -> #ABV
      -> Switch[968783771] #ABU -> #ABW
      <- TryCatch range: [GX...GW] -> ABU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GX...GW] -> ABU ([Ljava/lang/IllegalAccessException;])
===#Block ABW(size=2, flags=10100)===
   0. lvar105 = {639110099 ^ lvar105};
   1. goto GY
      -> UnconditionalJump[GOTO] #ABW -> #GY
      <- Switch[968783771] #ABU -> #ABW
===#Block ABV(size=2, flags=10100)===
   0. lvar105 = {167192846 ^ lvar105};
   1. goto GY
      -> UnconditionalJump[GOTO] #ABV -> #GY
      <- Switch[1187471730] #ABU -> #ABV
===#Block GY(size=2, flags=0)===
   0. _consume(catch());
   1. goto PE
      -> UnconditionalJump[GOTO] #GY -> #PE
      <- UnconditionalJump[GOTO] #ABV -> #GY
      <- UnconditionalJump[GOTO] #ABW -> #GY
===#Block PE(size=2, flags=10100)===
   0. lvar105 = {1754346157 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PE -> #CN
      <- UnconditionalJump[GOTO] #GY -> #PE
===#Block ABX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ABU -> #ABX
===#Block BE(size=1, flags=0)===
   0. goto OG
      -> UnconditionalJump[GOTO] #BE -> #OG
      <- Immediate #BD -> #BE
===#Block OG(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 98141113:
      	 goto	#OH
      case 1910408491:
      	 goto	#OG
      case 1975726827:
      	 goto	#DX
      case 1976509115:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #OG -> #JC
      -> Switch[1975726827] #OG -> #DX
      -> Immediate #OG -> #OH
      -> Switch[1976509115] #OG -> #JC
      -> Switch[1910408491] #OG -> #OG
      -> Switch[98141113] #OG -> #OH
      <- Switch[1910408491] #OG -> #OG
      <- UnconditionalJump[GOTO] #BE -> #OG
===#Block OH(size=2, flags=100)===
   0. lvar105 = {398877032 ^ lvar105};
   1. goto DX
      -> UnconditionalJump[GOTO] #OH -> #DX
      <- Immediate #OG -> #OH
      <- Switch[98141113] #OG -> #OH
===#Block DX(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 259667225)
      goto DW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DX -> #DW
      -> TryCatch range: [DX...DW] -> XU ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #OH -> #DX
      <- Switch[1975726827] #OG -> #DX
===#Block DW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DX...DW] -> XU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DX -> #DW
===#Block XU(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -728879198:
      	 goto	#XV
      case -619566573:
      	 goto	#XW
      default:
      	 goto	#XX
   }
      -> Switch[-728879198] #XU -> #XV
      -> Switch[-619566573] #XU -> #XW
      -> DefaultSwitch #XU -> #XX
      <- TryCatch range: [DX...DW] -> XU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DX...DW] -> XU ([Ljava/lang/RuntimeException;])
===#Block XX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #XU -> #XX
===#Block XW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1989134469);
   1. goto DY
      -> UnconditionalJump[GOTO] #XW -> #DY
      <- Switch[-619566573] #XU -> #XW
===#Block XV(size=2, flags=10100)===
   0. lvar105 = {1467762739 ^ lvar105};
   1. goto DY
      -> UnconditionalJump[GOTO] #XV -> #DY
      <- Switch[-728879198] #XU -> #XV
===#Block DY(size=2, flags=0)===
   0. _consume(catch());
   1. goto QG
      -> UnconditionalJump[GOTO] #DY -> #QG
      <- UnconditionalJump[GOTO] #XV -> #DY
      <- UnconditionalJump[GOTO] #XW -> #DY
===#Block QG(size=2, flags=10100)===
   0. lvar105 = {1161146384 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #QG -> #BJ
      <- UnconditionalJump[GOTO] #DY -> #QG
===#Block VF(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 31648646:
      	 goto	#VG
      case 171231716:
      	 goto	#AO
      case 1055598698:
      	 goto	#VF
      case 1721204549:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #VF -> #VG
      -> Switch[1055598698] #VF -> #VF
      -> Switch[171231716] #VF -> #AO
      -> Switch[1721204549] #VF -> #JC
      -> DefaultSwitch #VF -> #JC
      -> Switch[31648646] #VF -> #VG
      <- Switch[33641537] #AH -> #VF
      <- Switch[1055598698] #VF -> #VF
===#Block VG(size=2, flags=100)===
   0. lvar105 = {1897594970 ^ lvar105};
   1. goto AO
      -> UnconditionalJump[GOTO] #VG -> #AO
      <- Immediate #VF -> #VG
      <- Switch[31648646] #VF -> #VG
===#Block AO(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar40 = lvar102;
   2. lvar86 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ifcochvaojtmvrn(), lvar105);
   3. lvar41 = lvar40.equals(lvar86);
   4. if (lvar41 != {491316057 ^ lvar105})
      goto TG
   5. lvar105 = {859320467 ^ lvar105};
      -> Immediate #AO -> #AP
      -> ConditionalJump[IF_ICMPNE] #AO -> #TG
      <- Switch[171231716] #VF -> #AO
      <- UnconditionalJump[GOTO] #VG -> #AO
===#Block TG(size=2, flags=10100)===
   0. lvar105 = {925537712 ^ lvar105};
   1. goto JB
      -> UnconditionalJump[GOTO] #TG -> #JB
      <- ConditionalJump[IF_ICMPNE] #AO -> #TG
===#Block JB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1393801033)
      goto RT
   1. goto KJ
      -> ConditionalJump[IF_ICMPEQ] #JB -> #RT
      -> UnconditionalJump[GOTO] #JB -> #KJ
      <- UnconditionalJump[GOTO] #TG -> #JB
===#Block KJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 974581888);
   1. goto JC
      -> UnconditionalJump[GOTO] #KJ -> #JC
      <- UnconditionalJump[GOTO] #JB -> #KJ
===#Block RT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 773219762);
   1. goto AQ
      -> UnconditionalJump[GOTO] #RT -> #AQ
      <- ConditionalJump[IF_ICMPEQ] #JB -> #RT
===#Block AQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_CONCRETE_POWDER;
   2. goto KZ
      -> UnconditionalJump[GOTO] #AQ -> #KZ
      <- UnconditionalJump[GOTO] #RT -> #AQ
===#Block KZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 268814977);
   1. goto FT
      -> UnconditionalJump[GOTO] #KZ -> #FT
      <- UnconditionalJump[GOTO] #AQ -> #KZ
===#Block FT(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 127697511)
      goto FS
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FT -> #FS
      -> TryCatch range: [FT...FS] -> AAG ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #KZ -> #FT
===#Block FS(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FT...FS] -> AAG ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FT -> #FS
===#Block AAG(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1550922032:
      	 goto	#AAH
      case -1147657743:
      	 goto	#AAI
      default:
      	 goto	#AAJ
   }
      -> Switch[-1550922032] #AAG -> #AAH
      -> Switch[-1147657743] #AAG -> #AAI
      -> DefaultSwitch #AAG -> #AAJ
      <- TryCatch range: [FT...FS] -> AAG ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FT...FS] -> AAG ([Ljava/lang/IllegalAccessException;])
===#Block AAJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAG -> #AAJ
===#Block AAI(size=2, flags=10100)===
   0. lvar105 = {538628431 ^ lvar105};
   1. goto FU
      -> UnconditionalJump[GOTO] #AAI -> #FU
      <- Switch[-1147657743] #AAG -> #AAI
===#Block AAH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 51907755);
   1. goto FU
      -> UnconditionalJump[GOTO] #AAH -> #FU
      <- Switch[-1550922032] #AAG -> #AAH
===#Block FU(size=2, flags=0)===
   0. _consume(catch());
   1. goto RH
      -> UnconditionalJump[GOTO] #FU -> #RH
      <- UnconditionalJump[GOTO] #AAH -> #FU
      <- UnconditionalJump[GOTO] #AAI -> #FU
===#Block RH(size=2, flags=10100)===
   0. lvar105 = {1054286588 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #RH -> #CN
      <- UnconditionalJump[GOTO] #FU -> #RH
===#Block AP(size=1, flags=0)===
   0. goto OF
      -> UnconditionalJump[GOTO] #AP -> #OF
      <- Immediate #AO -> #AP
===#Block OF(size=2, flags=10100)===
   0. lvar105 = {622196904 ^ lvar105};
   1. goto DU
      -> UnconditionalJump[GOTO] #OF -> #DU
      <- UnconditionalJump[GOTO] #AP -> #OF
===#Block DU(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 101977960)
      goto DT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DU -> #DT
      -> TryCatch range: [DU...DT] -> XQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #OF -> #DU
===#Block DT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DU...DT] -> XQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DU -> #DT
===#Block XQ(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 457266314:
      	 goto	#XS
      case 1529436944:
      	 goto	#XR
      default:
      	 goto	#XT
   }
      -> Switch[1529436944] #XQ -> #XR
      -> Switch[457266314] #XQ -> #XS
      -> DefaultSwitch #XQ -> #XT
      <- TryCatch range: [DU...DT] -> XQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DU...DT] -> XQ ([Ljava/lang/IllegalAccessException;])
===#Block XT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #XQ -> #XT
===#Block XS(size=2, flags=10100)===
   0. lvar105 = {542347936 ^ lvar105};
   1. goto DV
      -> UnconditionalJump[GOTO] #XS -> #DV
      <- Switch[457266314] #XQ -> #XS
===#Block XR(size=2, flags=10100)===
   0. lvar105 = {1751028563 ^ lvar105};
   1. goto DV
      -> UnconditionalJump[GOTO] #XR -> #DV
      <- Switch[1529436944] #XQ -> #XR
===#Block DV(size=2, flags=0)===
   0. _consume(catch());
   1. goto QI
      -> UnconditionalJump[GOTO] #DV -> #QI
      <- UnconditionalJump[GOTO] #XR -> #DV
      <- UnconditionalJump[GOTO] #XS -> #DV
===#Block QI(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 4446181:
      	 goto	#QJ
      case 624671626:
      	 goto	#BJ
      case 843491530:
      	 goto	#QI
      case 1982944598:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #QI -> #QJ
      -> Switch[843491530] #QI -> #QI
      -> DefaultSwitch #QI -> #JC
      -> Switch[4446181] #QI -> #QJ
      -> Switch[1982944598] #QI -> #JC
      -> Switch[624671626] #QI -> #BJ
      <- Switch[843491530] #QI -> #QI
      <- UnconditionalJump[GOTO] #DV -> #QI
===#Block QJ(size=2, flags=100)===
   0. lvar105 = {736331238 ^ lvar105};
   1. goto BJ
      -> UnconditionalJump[GOTO] #QJ -> #BJ
      <- Immediate #QI -> #QJ
      <- Switch[4446181] #QI -> #QJ
===#Block BJ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_CONCRETE_POWDER;
   2. lvar105 = {1634126426 ^ lvar105};
      -> Immediate #BJ -> #CN
      <- UnconditionalJump[GOTO] #PJ -> #BJ
      <- UnconditionalJump[GOTO] #NE -> #BJ
      <- UnconditionalJump[GOTO] #PC -> #BJ
      <- UnconditionalJump[GOTO] #QJ -> #BJ
      <- UnconditionalJump[GOTO] #LH -> #BJ
      <- UnconditionalJump[GOTO] #VO -> #BJ
      <- Switch[624671626] #QI -> #BJ
      <- UnconditionalJump[GOTO] #PT -> #BJ
      <- UnconditionalJump[GOTO] #QO -> #BJ
      <- Switch[1485481227] #LG -> #BJ
      <- UnconditionalJump[GOTO] #QG -> #BJ
      <- UnconditionalJump[GOTO] #RE -> #BJ
===#Block OM(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 70244400:
      	 goto	#ON
      case 523652971:
      	 goto	#OM
      case 1113173369:
      	 goto	#JC
      case 1557388651:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #OM -> #JC
      -> Immediate #OM -> #ON
      -> Switch[70244400] #OM -> #ON
      -> Switch[1557388651] #OM -> #JC
      -> Switch[523652971] #OM -> #OM
      <- Switch[523652971] #OM -> #OM
      <- UnconditionalJump[GOTO] #JR -> #OM
===#Block ON(size=2, flags=100)===
   0. lvar105 = {1853658579 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #ON -> #JC
      <- Immediate #OM -> #ON
      <- Switch[70244400] #OM -> #ON
===#Block BK(size=6, flags=0)===
   0. lvar54 = lvar1;
   1. lvar103 = lvar54;
   2. lvar55 = lvar103;
   3. lvar56 = lvar55.hashCode();
   4. svar107 = {lvar56 ^ lvar105};
   5. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(svar107)) {
      case 152027202:
      	 goto	#UQ
      case 152027212:
      	 goto	#UR
      case 152027216:
      	 goto	#UT
      case 152027218:
      	 goto	#UV
      case 152027222:
      	 goto	#UW
      case 152027224:
      	 goto	#UX
      case 152027226:
      	 goto	#UY
      case 152027227:
      	 goto	#UZ
      case 152027230:
      	 goto	#VA
      default:
      	 goto	#VB
   }
      -> Switch[152027227] #BK -> #UZ
      -> Switch[152027224] #BK -> #UX
      -> Switch[152027202] #BK -> #UQ
      -> Switch[152027216] #BK -> #UT
      -> Switch[152027230] #BK -> #VA
      -> DefaultSwitch #BK -> #VB
      -> Switch[152027222] #BK -> #UW
      -> Switch[152027212] #BK -> #UR
      -> Switch[152027226] #BK -> #UY
      -> Switch[152027218] #BK -> #UV
      <- Immediate #C -> #BK
===#Block UV(size=2, flags=10100)===
   0. lvar105 = {737678259 ^ lvar105};
   1. goto CA
      -> UnconditionalJump[GOTO] #UV -> #CA
      <- Switch[152027218] #BK -> #UV
===#Block CA(size=6, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar67 = lvar103;
   2. lvar98 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.rlhhtqlhxjzlrpv(), lvar105);
   3. lvar68 = lvar67.equals(lvar98);
   4. if (lvar68 != {364091223 ^ lvar105})
      goto TJ
   5. lvar105 = {1319924169 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CA -> #TJ
      -> Immediate #CA -> #CC
      <- UnconditionalJump[GOTO] #UV -> #CA
===#Block CC(size=1, flags=0)===
   0. goto QE
      -> UnconditionalJump[GOTO] #CC -> #QE
      <- Immediate #CA -> #CC
===#Block QE(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 9278925:
      	 goto	#QF
      case 722330401:
      	 goto	#QE
      case 1522480817:
      	 goto	#JC
      case 1796157774:
      	 goto	#DR
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #QE -> #JC
      -> Switch[722330401] #QE -> #QE
      -> Switch[1522480817] #QE -> #JC
      -> Switch[9278925] #QE -> #QF
      -> Switch[1796157774] #QE -> #DR
      -> Immediate #QE -> #QF
      <- Switch[722330401] #QE -> #QE
      <- UnconditionalJump[GOTO] #CC -> #QE
===#Block QF(size=2, flags=100)===
   0. lvar105 = {1384886039 ^ lvar105};
   1. goto DR
      -> UnconditionalJump[GOTO] #QF -> #DR
      <- Switch[9278925] #QE -> #QF
      <- Immediate #QE -> #QF
===#Block DR(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 42944493)
      goto DQ
   1. throw nullconst;
      -> TryCatch range: [DR...DQ] -> XM ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DR -> #DQ
      <- Switch[1796157774] #QE -> #DR
      <- UnconditionalJump[GOTO] #QF -> #DR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DR...DQ] -> XM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DR -> #DQ
===#Block XM(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 1151526528:
      	 goto	#XO
      case 1285704776:
      	 goto	#XN
      default:
      	 goto	#XP
   }
      -> DefaultSwitch #XM -> #XP
      -> Switch[1151526528] #XM -> #XO
      -> Switch[1285704776] #XM -> #XN
      <- TryCatch range: [DR...DQ] -> XM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DR...DQ] -> XM ([Ljava/lang/IllegalAccessException;])
===#Block XN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 609956867);
   1. goto DS
      -> UnconditionalJump[GOTO] #XN -> #DS
      <- Switch[1285704776] #XM -> #XN
===#Block XO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 626729306);
   1. goto DS
      -> UnconditionalJump[GOTO] #XO -> #DS
      <- Switch[1151526528] #XM -> #XO
===#Block DS(size=2, flags=0)===
   0. _consume(catch());
   1. goto MU
      -> UnconditionalJump[GOTO] #DS -> #MU
      <- UnconditionalJump[GOTO] #XO -> #DS
      <- UnconditionalJump[GOTO] #XN -> #DS
===#Block MU(size=2, flags=10100)===
   0. lvar105 = {1174090359 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #MU -> #CM
      <- UnconditionalJump[GOTO] #DS -> #MU
===#Block XP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #XM -> #XP
===#Block TJ(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 168550539:
      	 goto	#TK
      case 842024203:
      	 goto	#JC
      case 1317730270:
      	 goto	#JF
      case 2131611562:
      	 goto	#TJ
      default:
      	 goto	#JC
   }
      -> Switch[1317730270] #TJ -> #JF
      -> Switch[842024203] #TJ -> #JC
      -> DefaultSwitch #TJ -> #JC
      -> Switch[168550539] #TJ -> #TK
      -> Switch[2131611562] #TJ -> #TJ
      -> Immediate #TJ -> #TK
      <- ConditionalJump[IF_ICMPNE] #CA -> #TJ
      <- Switch[2131611562] #TJ -> #TJ
===#Block TK(size=2, flags=100)===
   0. lvar105 = {2030126685 ^ lvar105};
   1. goto JF
      -> UnconditionalJump[GOTO] #TK -> #JF
      <- Switch[168550539] #TJ -> #TK
      <- Immediate #TJ -> #TK
===#Block JF(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1704364115)
      goto ST
   1. goto PK
      -> ConditionalJump[IF_ICMPEQ] #JF -> #ST
      -> UnconditionalJump[GOTO] #JF -> #PK
      <- Switch[1317730270] #TJ -> #JF
      <- UnconditionalJump[GOTO] #TK -> #JF
===#Block PK(size=2, flags=10100)===
   0. lvar105 = {2093373283 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #PK -> #JC
      <- UnconditionalJump[GOTO] #JF -> #PK
===#Block ST(size=2, flags=10100)===
   0. lvar105 = {1675396907 ^ lvar105};
   1. goto CB
      -> UnconditionalJump[GOTO] #ST -> #CB
      <- ConditionalJump[IF_ICMPEQ] #JF -> #ST
===#Block CB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.REDSTONE_BLOCK;
   2. goto OQ
      -> UnconditionalJump[GOTO] #CB -> #OQ
      <- UnconditionalJump[GOTO] #ST -> #CB
===#Block OQ(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 33151190:
      	 goto	#EP
      case 116653994:
      	 goto	#OQ
      case 233163185:
      	 goto	#OR
      case 471587702:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[233163185] #OQ -> #OR
      -> Immediate #OQ -> #OR
      -> Switch[33151190] #OQ -> #EP
      -> Switch[471587702] #OQ -> #JC
      -> DefaultSwitch #OQ -> #JC
      -> Switch[116653994] #OQ -> #OQ
      <- UnconditionalJump[GOTO] #CB -> #OQ
      <- Switch[116653994] #OQ -> #OQ
===#Block OR(size=2, flags=100)===
   0. lvar105 = {2071384558 ^ lvar105};
   1. goto EP
      -> UnconditionalJump[GOTO] #OR -> #EP
      <- Switch[233163185] #OQ -> #OR
      <- Immediate #OQ -> #OR
===#Block EP(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 15725369)
      goto EO
   1. throw nullconst;
      -> TryCatch range: [EP...EO] -> YS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EP -> #EO
      <- UnconditionalJump[GOTO] #OR -> #EP
      <- Switch[33151190] #OQ -> #EP
===#Block EO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EP...EO] -> YS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EP -> #EO
===#Block YS(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1597751685:
      	 goto	#YT
      case -1259207294:
      	 goto	#YU
      default:
      	 goto	#YV
   }
      -> Switch[-1259207294] #YS -> #YU
      -> Switch[-1597751685] #YS -> #YT
      -> DefaultSwitch #YS -> #YV
      <- TryCatch range: [EP...EO] -> YS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EP...EO] -> YS ([Ljava/lang/IllegalAccessException;])
===#Block YV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #YS -> #YV
===#Block YT(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 940426864);
   1. goto EQ
      -> UnconditionalJump[GOTO] #YT -> #EQ
      <- Switch[-1597751685] #YS -> #YT
===#Block YU(size=2, flags=10100)===
   0. lvar105 = {445317391 ^ lvar105};
   1. goto EQ
      -> UnconditionalJump[GOTO] #YU -> #EQ
      <- Switch[-1259207294] #YS -> #YU
===#Block EQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto PS
      -> UnconditionalJump[GOTO] #EQ -> #PS
      <- UnconditionalJump[GOTO] #YU -> #EQ
      <- UnconditionalJump[GOTO] #YT -> #EQ
===#Block PS(size=2, flags=10100)===
   0. lvar105 = {1705752114 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PS -> #CN
      <- UnconditionalJump[GOTO] #EQ -> #PS
===#Block UY(size=2, flags=10100)===
   0. lvar105 = {698294687 ^ lvar105};
   1. goto BU
      -> UnconditionalJump[GOTO] #UY -> #BU
      <- Switch[152027226] #BK -> #UY
===#Block BU(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar63 = lvar103;
   2. lvar96 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.fjglrpvmxfgfwqm(), lvar105);
   3. lvar64 = lvar63.equals(lvar96);
   4. if (lvar64 != {399811963 ^ lvar105})
      goto SN
   5. lvar105 = {1041834679 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BU -> #SN
      -> Immediate #BU -> #BV
      <- UnconditionalJump[GOTO] #UY -> #BU
===#Block BV(size=1, flags=0)===
   0. goto QS
      -> UnconditionalJump[GOTO] #BV -> #QS
      <- Immediate #BU -> #BV
===#Block QS(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 6217747:
      	 goto	#QS
      case 16654038:
      	 goto	#QT
      case 476654884:
      	 goto	#JC
      case 1107252067:
      	 goto	#FK
      default:
      	 goto	#JC
   }
      -> Switch[6217747] #QS -> #QS
      -> Switch[1107252067] #QS -> #FK
      -> Immediate #QS -> #QT
      -> Switch[16654038] #QS -> #QT
      -> Switch[476654884] #QS -> #JC
      -> DefaultSwitch #QS -> #JC
      <- UnconditionalJump[GOTO] #BV -> #QS
      <- Switch[6217747] #QS -> #QS
===#Block QT(size=2, flags=100)===
   0. lvar105 = {1765720326 ^ lvar105};
   1. goto FK
      -> UnconditionalJump[GOTO] #QT -> #FK
      <- Immediate #QS -> #QT
      <- Switch[16654038] #QS -> #QT
===#Block FK(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 232200660)
      goto FJ
   1. throw nullconst;
      -> TryCatch range: [FK...FJ] -> ZU ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #FK -> #FJ
      <- UnconditionalJump[GOTO] #QT -> #FK
      <- Switch[1107252067] #QS -> #FK
===#Block FJ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FK...FJ] -> ZU ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FK -> #FJ
===#Block ZU(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -692436375:
      	 goto	#ZW
      case 127546962:
      	 goto	#ZV
      default:
      	 goto	#ZX
   }
      -> DefaultSwitch #ZU -> #ZX
      -> Switch[127546962] #ZU -> #ZV
      -> Switch[-692436375] #ZU -> #ZW
      <- TryCatch range: [FK...FJ] -> ZU ([Ljava/io/IOException;])
      <- TryCatch range: [FK...FJ] -> ZU ([Ljava/io/IOException;])
===#Block ZW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1662613374);
   1. goto FL
      -> UnconditionalJump[GOTO] #ZW -> #FL
      <- Switch[-692436375] #ZU -> #ZW
===#Block ZV(size=2, flags=10100)===
   0. lvar105 = {423459321 ^ lvar105};
   1. goto FL
      -> UnconditionalJump[GOTO] #ZV -> #FL
      <- Switch[127546962] #ZU -> #ZV
===#Block FL(size=2, flags=0)===
   0. _consume(catch());
   1. goto MN
      -> UnconditionalJump[GOTO] #FL -> #MN
      <- UnconditionalJump[GOTO] #ZV -> #FL
      <- UnconditionalJump[GOTO] #ZW -> #FL
===#Block MN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 838496462);
   1. goto CM
      -> UnconditionalJump[GOTO] #MN -> #CM
      <- UnconditionalJump[GOTO] #FL -> #MN
===#Block ZX(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ZU -> #ZX
===#Block SN(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1799537622);
   1. goto JT
      -> UnconditionalJump[GOTO] #SN -> #JT
      <- ConditionalJump[IF_ICMPNE] #BU -> #SN
===#Block JT(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -458017429)
      goto TD
   1. goto RD
      -> UnconditionalJump[GOTO] #JT -> #RD
      -> ConditionalJump[IF_ICMPEQ] #JT -> #TD
      <- UnconditionalJump[GOTO] #SN -> #JT
===#Block TD(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 23272057:
      	 goto	#TE
      case 497391473:
      	 goto	#TD
      case 780243458:
      	 goto	#BW
      case 1523069994:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[23272057] #TD -> #TE
      -> Switch[780243458] #TD -> #BW
      -> Immediate #TD -> #TE
      -> DefaultSwitch #TD -> #JC
      -> Switch[497391473] #TD -> #TD
      -> Switch[1523069994] #TD -> #JC
      <- Switch[497391473] #TD -> #TD
      <- ConditionalJump[IF_ICMPEQ] #JT -> #TD
===#Block TE(size=2, flags=100)===
   0. lvar105 = {2127521460 ^ lvar105};
   1. goto BW
      -> UnconditionalJump[GOTO] #TE -> #BW
      <- Switch[23272057] #TD -> #TE
      <- Immediate #TD -> #TE
===#Block BW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.OBSIDIAN;
   2. goto MZ
      -> UnconditionalJump[GOTO] #BW -> #MZ
      <- Switch[780243458] #TD -> #BW
      <- UnconditionalJump[GOTO] #TE -> #BW
===#Block MZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 241868297);
   1. goto GF
      -> UnconditionalJump[GOTO] #MZ -> #GF
      <- UnconditionalJump[GOTO] #BW -> #MZ
===#Block GF(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 128192012)
      goto GE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GF -> #GE
      -> TryCatch range: [GF...GE] -> AAW ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #MZ -> #GF
===#Block GE(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GF...GE] -> AAW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GF -> #GE
===#Block AAW(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 610818435:
      	 goto	#AAY
      case 1637675136:
      	 goto	#AAX
      default:
      	 goto	#AAZ
   }
      -> Switch[610818435] #AAW -> #AAY
      -> DefaultSwitch #AAW -> #AAZ
      -> Switch[1637675136] #AAW -> #AAX
      <- TryCatch range: [GF...GE] -> AAW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GF...GE] -> AAW ([Ljava/lang/RuntimeException;])
===#Block AAX(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 849914524);
   1. goto GG
      -> UnconditionalJump[GOTO] #AAX -> #GG
      <- Switch[1637675136] #AAW -> #AAX
===#Block AAZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #AAW -> #AAZ
===#Block AAY(size=2, flags=10100)===
   0. lvar105 = {1511407548 ^ lvar105};
   1. goto GG
      -> UnconditionalJump[GOTO] #AAY -> #GG
      <- Switch[610818435] #AAW -> #AAY
===#Block GG(size=2, flags=0)===
   0. _consume(catch());
   1. goto MW
      -> UnconditionalJump[GOTO] #GG -> #MW
      <- UnconditionalJump[GOTO] #AAX -> #GG
      <- UnconditionalJump[GOTO] #AAY -> #GG
===#Block MW(size=2, flags=10100)===
   0. lvar105 = {388350721 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #MW -> #CN
      <- UnconditionalJump[GOTO] #GG -> #MW
===#Block RD(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1826808004);
   1. goto JC
      -> UnconditionalJump[GOTO] #RD -> #JC
      <- UnconditionalJump[GOTO] #JT -> #RD
===#Block UR(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 145911602:
      	 goto	#US
      case 990436312:
      	 goto	#JC
      case 1728070313:
      	 goto	#CD
      case 1740281493:
      	 goto	#UR
      default:
      	 goto	#JC
   }
      -> Switch[1740281493] #UR -> #UR
      -> Switch[145911602] #UR -> #US
      -> Switch[990436312] #UR -> #JC
      -> DefaultSwitch #UR -> #JC
      -> Switch[1728070313] #UR -> #CD
      -> Immediate #UR -> #US
      <- Switch[1740281493] #UR -> #UR
      <- Switch[152027212] #BK -> #UR
===#Block US(size=2, flags=100)===
   0. lvar105 = {1749839847 ^ lvar105};
   1. goto CD
      -> UnconditionalJump[GOTO] #US -> #CD
      <- Switch[145911602] #UR -> #US
      <- Immediate #UR -> #US
===#Block CD(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar69 = lvar103;
   2. lvar99 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ndlkpolztlpreph(), lvar105);
   3. lvar70 = lvar69.equals(lvar99);
   4. if (lvar70 != {1443359491 ^ lvar105})
      goto UM
   5. lvar105 = {672625743 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CD -> #UM
      -> Immediate #CD -> #CE
      <- UnconditionalJump[GOTO] #US -> #CD
      <- Switch[1728070313] #UR -> #CD
===#Block CE(size=1, flags=0)===
   0. goto LR
      -> UnconditionalJump[GOTO] #CE -> #LR
      <- Immediate #CD -> #CE
===#Block LR(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 71322291:
      	 goto	#LS
      case 421721652:
      	 goto	#JC
      case 1744463087:
      	 goto	#LR
      case 1776601087:
      	 goto	#HG
      default:
      	 goto	#JC
   }
      -> Switch[1776601087] #LR -> #HG
      -> Switch[71322291] #LR -> #LS
      -> Immediate #LR -> #LS
      -> DefaultSwitch #LR -> #JC
      -> Switch[1744463087] #LR -> #LR
      -> Switch[421721652] #LR -> #JC
      <- UnconditionalJump[GOTO] #CE -> #LR
      <- Switch[1744463087] #LR -> #LR
===#Block LS(size=2, flags=100)===
   0. lvar105 = {439442082 ^ lvar105};
   1. goto HG
      -> UnconditionalJump[GOTO] #LS -> #HG
      <- Switch[71322291] #LR -> #LS
      <- Immediate #LR -> #LS
===#Block HG(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 33622876)
      goto HF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HG -> #HF
      -> TryCatch range: [HG...HF] -> ACG ([Ljava/lang/IllegalAccessException;])
      <- Switch[1776601087] #LR -> #HG
      <- UnconditionalJump[GOTO] #LS -> #HG
===#Block HF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HG...HF] -> ACG ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HG -> #HF
===#Block ACG(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -813127717:
      	 goto	#ACI
      case 554577779:
      	 goto	#ACH
      default:
      	 goto	#ACJ
   }
      -> Switch[-813127717] #ACG -> #ACI
      -> DefaultSwitch #ACG -> #ACJ
      -> Switch[554577779] #ACG -> #ACH
      <- TryCatch range: [HG...HF] -> ACG ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HG...HF] -> ACG ([Ljava/lang/IllegalAccessException;])
===#Block ACH(size=2, flags=10100)===
   0. lvar105 = {1630003026 ^ lvar105};
   1. goto HH
      -> UnconditionalJump[GOTO] #ACH -> #HH
      <- Switch[554577779] #ACG -> #ACH
===#Block ACJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ACG -> #ACJ
===#Block ACI(size=2, flags=10100)===
   0. lvar105 = {2096565831 ^ lvar105};
   1. goto HH
      -> UnconditionalJump[GOTO] #ACI -> #HH
      <- Switch[-813127717] #ACG -> #ACI
===#Block HH(size=2, flags=0)===
   0. _consume(catch());
   1. goto OX
      -> UnconditionalJump[GOTO] #HH -> #OX
      <- UnconditionalJump[GOTO] #ACI -> #HH
      <- UnconditionalJump[GOTO] #ACH -> #HH
===#Block OX(size=2, flags=10100)===
   0. lvar105 = {1832015169 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #OX -> #CM
      <- UnconditionalJump[GOTO] #HH -> #OX
===#Block UM(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 112132434:
      	 goto	#UN
      case 1119774888:
      	 goto	#KD
      case 1349328947:
      	 goto	#JC
      case 2068294954:
      	 goto	#UM
      default:
      	 goto	#JC
   }
      -> Switch[1119774888] #UM -> #KD
      -> DefaultSwitch #UM -> #JC
      -> Switch[1349328947] #UM -> #JC
      -> Switch[2068294954] #UM -> #UM
      -> Immediate #UM -> #UN
      -> Switch[112132434] #UM -> #UN
      <- Switch[2068294954] #UM -> #UM
      <- ConditionalJump[IF_ICMPNE] #CD -> #UM
===#Block UN(size=2, flags=100)===
   0. lvar105 = {107599197 ^ lvar105};
   1. goto KD
      -> UnconditionalJump[GOTO] #UN -> #KD
      <- Immediate #UM -> #UN
      <- Switch[112132434] #UM -> #UN
===#Block KD(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -2089684238)
      goto TT
   1. goto NU
      -> UnconditionalJump[GOTO] #KD -> #NU
      -> ConditionalJump[IF_ICMPEQ] #KD -> #TT
      <- Switch[1119774888] #UM -> #KD
      <- UnconditionalJump[GOTO] #UN -> #KD
===#Block TT(size=2, flags=10100)===
   0. lvar105 = {667865623 ^ lvar105};
   1. goto CF
      -> UnconditionalJump[GOTO] #TT -> #CF
      <- ConditionalJump[IF_ICMPEQ] #KD -> #TT
===#Block CF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPUR_BLOCK;
   2. goto OI
      -> UnconditionalJump[GOTO] #CF -> #OI
      <- UnconditionalJump[GOTO] #TT -> #CF
===#Block OI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1126614902);
   1. goto ED
      -> UnconditionalJump[GOTO] #OI -> #ED
      <- UnconditionalJump[GOTO] #CF -> #OI
===#Block ED(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 96716620)
      goto EC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #ED -> #EC
      -> TryCatch range: [ED...EC] -> YC ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #OI -> #ED
===#Block EC(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [ED...EC] -> YC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #ED -> #EC
===#Block YC(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1540269575:
      	 goto	#YD
      case -320959032:
      	 goto	#YE
      default:
      	 goto	#YF
   }
      -> Switch[-320959032] #YC -> #YE
      -> Switch[-1540269575] #YC -> #YD
      -> DefaultSwitch #YC -> #YF
      <- TryCatch range: [ED...EC] -> YC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [ED...EC] -> YC ([Ljava/lang/RuntimeException;])
===#Block YF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #YC -> #YF
===#Block YD(size=2, flags=10100)===
   0. lvar105 = {562105521 ^ lvar105};
   1. goto EE
      -> UnconditionalJump[GOTO] #YD -> #EE
      <- Switch[-1540269575] #YC -> #YD
===#Block YE(size=2, flags=10100)===
   0. lvar105 = {144504375 ^ lvar105};
   1. goto EE
      -> UnconditionalJump[GOTO] #YE -> #EE
      <- Switch[-320959032] #YC -> #YE
===#Block EE(size=2, flags=0)===
   0. _consume(catch());
   1. goto PH
      -> UnconditionalJump[GOTO] #EE -> #PH
      <- UnconditionalJump[GOTO] #YD -> #EE
      <- UnconditionalJump[GOTO] #YE -> #EE
===#Block PH(size=2, flags=10100)===
   0. lvar105 = {1018810371 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PH -> #CN
      <- UnconditionalJump[GOTO] #EE -> #PH
===#Block NU(size=2, flags=10100)===
   0. lvar105 = {1075492919 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #NU -> #JC
      <- UnconditionalJump[GOTO] #KD -> #NU
===#Block UW(size=2, flags=10100)===
   0. lvar105 = {1986420270 ^ lvar105};
   1. goto BL
      -> UnconditionalJump[GOTO] #UW -> #BL
      <- Switch[152027222] #BK -> #UW
===#Block BL(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar57 = lvar103;
   2. lvar93 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.xgphahrxtskkhmq(), lvar105);
   3. lvar58 = lvar57.equals(lvar93);
   4. if (lvar58 != {1210964682 ^ lvar105})
      goto TA
   5. lvar105 = {881807435 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #BL -> #TA
      -> Immediate #BL -> #BM
      <- UnconditionalJump[GOTO] #UW -> #BL
===#Block BM(size=1, flags=0)===
   0. goto PO
      -> UnconditionalJump[GOTO] #BM -> #PO
      <- Immediate #BL -> #BM
===#Block PO(size=2, flags=10100)===
   0. lvar105 = {1120619816 ^ lvar105};
   1. goto IK
      -> UnconditionalJump[GOTO] #PO -> #IK
      <- UnconditionalJump[GOTO] #BM -> #PO
===#Block IK(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 149736638)
      goto IJ
   1. throw nullconst;
      -> TryCatch range: [IK...IJ] -> ADU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IK -> #IJ
      <- UnconditionalJump[GOTO] #PO -> #IK
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IK...IJ] -> ADU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IK -> #IJ
===#Block ADU(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -212976311:
      	 goto	#ADV
      case 276861539:
      	 goto	#ADW
      default:
      	 goto	#ADX
   }
      -> Switch[-212976311] #ADU -> #ADV
      -> Switch[276861539] #ADU -> #ADW
      -> DefaultSwitch #ADU -> #ADX
      <- TryCatch range: [IK...IJ] -> ADU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IK...IJ] -> ADU ([Ljava/lang/IllegalAccessException;])
===#Block ADX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ADU -> #ADX
===#Block ADW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1193524846);
   1. goto IL
      -> UnconditionalJump[GOTO] #ADW -> #IL
      <- Switch[276861539] #ADU -> #ADW
===#Block ADV(size=2, flags=10100)===
   0. lvar105 = {458910603 ^ lvar105};
   1. goto IL
      -> UnconditionalJump[GOTO] #ADV -> #IL
      <- Switch[-212976311] #ADU -> #ADV
===#Block IL(size=2, flags=0)===
   0. _consume(catch());
   1. goto KF
      -> UnconditionalJump[GOTO] #IL -> #KF
      <- UnconditionalJump[GOTO] #ADW -> #IL
      <- UnconditionalJump[GOTO] #ADV -> #IL
===#Block KF(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1292362719);
   1. goto CM
      -> UnconditionalJump[GOTO] #KF -> #CM
      <- UnconditionalJump[GOTO] #IL -> #KF
===#Block TA(size=2, flags=10100)===
   0. lvar105 = {748800287 ^ lvar105};
   1. goto KA
      -> UnconditionalJump[GOTO] #TA -> #KA
      <- ConditionalJump[IF_ICMPNE] #BL -> #TA
===#Block KA(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 610311851)
      goto TU
   1. goto OA
      -> UnconditionalJump[GOTO] #KA -> #OA
      -> ConditionalJump[IF_ICMPEQ] #KA -> #TU
      <- UnconditionalJump[GOTO] #TA -> #KA
===#Block TU(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 47121120:
      	 goto	#TV
      case 425118639:
      	 goto	#BN
      case 1354740887:
      	 goto	#JC
      case 1893819604:
      	 goto	#TU
      default:
      	 goto	#JC
   }
      -> Switch[47121120] #TU -> #TV
      -> Switch[1893819604] #TU -> #TU
      -> Immediate #TU -> #TV
      -> Switch[425118639] #TU -> #BN
      -> Switch[1354740887] #TU -> #JC
      -> DefaultSwitch #TU -> #JC
      <- ConditionalJump[IF_ICMPEQ] #KA -> #TU
      <- Switch[1893819604] #TU -> #TU
===#Block TV(size=2, flags=100)===
   0. lvar105 = {1652603493 ^ lvar105};
   1. goto BN
      -> UnconditionalJump[GOTO] #TV -> #BN
      <- Switch[47121120] #TU -> #TV
      <- Immediate #TU -> #TV
===#Block BN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GOLD_BLOCK;
   2. goto RL
      -> UnconditionalJump[GOTO] #BN -> #RL
      <- UnconditionalJump[GOTO] #TV -> #BN
      <- Switch[425118639] #TU -> #BN
===#Block RL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 319473033);
   1. goto IQ
      -> UnconditionalJump[GOTO] #RL -> #IQ
      <- UnconditionalJump[GOTO] #BN -> #RL
===#Block IQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 146564264)
      goto IP
   1. throw nullconst;
      -> TryCatch range: [IQ...IP] -> AEC ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #IQ -> #IP
      <- UnconditionalJump[GOTO] #RL -> #IQ
===#Block IP(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IQ...IP] -> AEC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IQ -> #IP
===#Block AEC(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1473027640:
      	 goto	#AED
      case 353311280:
      	 goto	#AEE
      default:
      	 goto	#AEF
   }
      -> DefaultSwitch #AEC -> #AEF
      -> Switch[-1473027640] #AEC -> #AED
      -> Switch[353311280] #AEC -> #AEE
      <- TryCatch range: [IQ...IP] -> AEC ([Ljava/io/IOException;])
      <- TryCatch range: [IQ...IP] -> AEC ([Ljava/io/IOException;])
===#Block AEE(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1630481538);
   1. goto IR
      -> UnconditionalJump[GOTO] #AEE -> #IR
      <- Switch[353311280] #AEC -> #AEE
===#Block AED(size=2, flags=10100)===
   0. lvar105 = {1988666365 ^ lvar105};
   1. goto IR
      -> UnconditionalJump[GOTO] #AED -> #IR
      <- Switch[-1473027640] #AEC -> #AED
===#Block IR(size=2, flags=0)===
   0. _consume(catch());
   1. goto PD
      -> UnconditionalJump[GOTO] #IR -> #PD
      <- UnconditionalJump[GOTO] #AEE -> #IR
      <- UnconditionalJump[GOTO] #AED -> #IR
===#Block PD(size=2, flags=10100)===
   0. lvar105 = {1244674121 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #PD -> #CN
      <- UnconditionalJump[GOTO] #IR -> #PD
===#Block AEF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AEC -> #AEF
===#Block OA(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 8846401:
      	 goto	#OA
      case 47121120:
      	 goto	#OB
      case 617055052:
      	 goto	#JC
      case 1678825304:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #OA -> #JC
      -> Switch[617055052] #OA -> #JC
      -> Switch[47121120] #OA -> #OB
      -> Switch[8846401] #OA -> #OA
      -> Immediate #OA -> #OB
      <- UnconditionalJump[GOTO] #KA -> #OA
      <- Switch[8846401] #OA -> #OA
===#Block OB(size=2, flags=100)===
   0. lvar105 = {1962448316 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #OB -> #JC
      <- Switch[47121120] #OA -> #OB
      <- Immediate #OA -> #OB
===#Block VB(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 145911602:
      	 goto	#VC
      case 1401220681:
      	 goto	#JC
      case 1464493680:
      	 goto	#VB
      case 2072095836:
      	 goto	#CM
      default:
      	 goto	#JC
   }
      -> Switch[2072095836] #VB -> #CM
      -> DefaultSwitch #VB -> #JC
      -> Switch[1401220681] #VB -> #JC
      -> Switch[145911602] #VB -> #VC
      -> Switch[1464493680] #VB -> #VB
      -> Immediate #VB -> #VC
      <- DefaultSwitch #BK -> #VB
      <- Switch[1464493680] #VB -> #VB
===#Block VC(size=2, flags=100)===
   0. lvar105 = {1451215641 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #VC -> #CM
      <- Switch[145911602] #VB -> #VC
      <- Immediate #VB -> #VC
===#Block VA(size=2, flags=10100)===
   0. lvar105 = {106661885 ^ lvar105};
   1. goto CG
      -> UnconditionalJump[GOTO] #VA -> #CG
      <- Switch[152027230] #BK -> #VA
===#Block CG(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar71 = lvar103;
   2. lvar100 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.tlbymtvwtamllfs(), lvar105);
   3. lvar72 = lvar71.equals(lvar100);
   4. if (lvar72 != {940573465 ^ lvar105})
      goto SE
   5. lvar105 = {201662755 ^ lvar105};
      -> Immediate #CG -> #CI
      -> ConditionalJump[IF_ICMPNE] #CG -> #SE
      <- UnconditionalJump[GOTO] #VA -> #CG
===#Block SE(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 211762704:
      	 goto	#SF
      case 805889677:
      	 goto	#JK
      case 1229923767:
      	 goto	#SE
      case 1960893117:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #SE -> #SF
      -> Switch[211762704] #SE -> #SF
      -> Switch[1960893117] #SE -> #JC
      -> DefaultSwitch #SE -> #JC
      -> Switch[805889677] #SE -> #JK
      -> Switch[1229923767] #SE -> #SE
      <- Switch[1229923767] #SE -> #SE
      <- ConditionalJump[IF_ICMPNE] #CG -> #SE
===#Block SF(size=2, flags=100)===
   0. lvar105 = {384695841 ^ lvar105};
   1. goto JK
      -> UnconditionalJump[GOTO] #SF -> #JK
      <- Immediate #SE -> #SF
      <- Switch[211762704] #SE -> #SF
===#Block JK(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 2012211649)
      goto TO
   1. goto MV
      -> ConditionalJump[IF_ICMPEQ] #JK -> #TO
      -> UnconditionalJump[GOTO] #JK -> #MV
      <- Switch[805889677] #SE -> #JK
      <- UnconditionalJump[GOTO] #SF -> #JK
===#Block MV(size=2, flags=10100)===
   0. lvar105 = {1049197393 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MV -> #JC
      <- UnconditionalJump[GOTO] #JK -> #MV
===#Block TO(size=2, flags=10100)===
   0. lvar105 = {1217744363 ^ lvar105};
   1. goto CH
      -> UnconditionalJump[GOTO] #TO -> #CH
      <- ConditionalJump[IF_ICMPEQ] #JK -> #TO
===#Block CH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.QUARTZ_BLOCK;
   2. goto RI
      -> UnconditionalJump[GOTO] #CH -> #RI
      <- UnconditionalJump[GOTO] #TO -> #CH
===#Block RI(size=2, flags=10100)===
   0. lvar105 = {2123948101 ^ lvar105};
   1. goto HV
      -> UnconditionalJump[GOTO] #RI -> #HV
      <- UnconditionalJump[GOTO] #CH -> #RI
===#Block HV(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 5342418)
      goto HU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HV -> #HU
      -> TryCatch range: [HV...HU] -> ADA ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #RI -> #HV
===#Block HU(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HV...HU] -> ADA ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HV -> #HU
===#Block ADA(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -947747664:
      	 goto	#ADB
      case 284000000:
      	 goto	#ADC
      default:
      	 goto	#ADD
   }
      -> Switch[-947747664] #ADA -> #ADB
      -> Switch[284000000] #ADA -> #ADC
      -> DefaultSwitch #ADA -> #ADD
      <- TryCatch range: [HV...HU] -> ADA ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HV...HU] -> ADA ([Ljava/lang/IllegalAccessException;])
===#Block ADD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ADA -> #ADD
===#Block ADC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1995784822);
   1. goto HW
      -> UnconditionalJump[GOTO] #ADC -> #HW
      <- Switch[284000000] #ADA -> #ADC
===#Block ADB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1813560576);
   1. goto HW
      -> UnconditionalJump[GOTO] #ADB -> #HW
      <- Switch[-947747664] #ADA -> #ADB
===#Block HW(size=2, flags=0)===
   0. _consume(catch());
   1. goto NC
      -> UnconditionalJump[GOTO] #HW -> #NC
      <- UnconditionalJump[GOTO] #ADB -> #HW
      <- UnconditionalJump[GOTO] #ADC -> #HW
===#Block NC(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 42154170:
      	 goto	#ND
      case 149601554:
      	 goto	#NC
      case 582592966:
      	 goto	#JC
      case 2060736650:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[42154170] #NC -> #ND
      -> Switch[2060736650] #NC -> #CN
      -> Switch[149601554] #NC -> #NC
      -> Switch[582592966] #NC -> #JC
      -> DefaultSwitch #NC -> #JC
      -> Immediate #NC -> #ND
      <- UnconditionalJump[GOTO] #HW -> #NC
      <- Switch[149601554] #NC -> #NC
===#Block ND(size=2, flags=100)===
   0. lvar105 = {1565923867 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #ND -> #CN
      <- Switch[42154170] #NC -> #ND
      <- Immediate #NC -> #ND
===#Block CI(size=1, flags=0)===
   0. goto KY
      -> UnconditionalJump[GOTO] #CI -> #KY
      <- Immediate #CG -> #CI
===#Block KY(size=2, flags=10100)===
   0. lvar105 = {407302582 ^ lvar105};
   1. goto FQ
      -> UnconditionalJump[GOTO] #KY -> #FQ
      <- UnconditionalJump[GOTO] #CI -> #KY
===#Block FQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 98715020)
      goto FP
   1. throw nullconst;
      -> TryCatch range: [FQ...FP] -> AAC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FQ -> #FP
      <- UnconditionalJump[GOTO] #KY -> #FQ
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FQ...FP] -> AAC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FQ -> #FP
===#Block AAC(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case 1654561889:
      	 goto	#AAD
      case 1975025481:
      	 goto	#AAE
      default:
      	 goto	#AAF
   }
      -> Switch[1975025481] #AAC -> #AAE
      -> Switch[1654561889] #AAC -> #AAD
      -> DefaultSwitch #AAC -> #AAF
      <- TryCatch range: [FQ...FP] -> AAC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FQ...FP] -> AAC ([Ljava/lang/RuntimeException;])
===#Block AAF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAC -> #AAF
===#Block AAD(size=2, flags=10100)===
   0. lvar105 = {490884540 ^ lvar105};
   1. goto FR
      -> UnconditionalJump[GOTO] #AAD -> #FR
      <- Switch[1654561889] #AAC -> #AAD
===#Block AAE(size=2, flags=10100)===
   0. lvar105 = {531010009 ^ lvar105};
   1. goto FR
      -> UnconditionalJump[GOTO] #AAE -> #FR
      <- Switch[1975025481] #AAC -> #AAE
===#Block FR(size=2, flags=0)===
   0. _consume(catch());
   1. goto MC
      -> UnconditionalJump[GOTO] #FR -> #MC
      <- UnconditionalJump[GOTO] #AAE -> #FR
      <- UnconditionalJump[GOTO] #AAD -> #FR
===#Block MC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1495650765);
   1. goto CM
      -> UnconditionalJump[GOTO] #MC -> #CM
      <- UnconditionalJump[GOTO] #FR -> #MC
===#Block UT(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 145911602:
      	 goto	#UU
      case 873235756:
      	 goto	#CJ
      case 1154653068:
      	 goto	#UT
      case 1727262788:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[145911602] #UT -> #UU
      -> Switch[873235756] #UT -> #CJ
      -> Switch[1727262788] #UT -> #JC
      -> Immediate #UT -> #UU
      -> DefaultSwitch #UT -> #JC
      -> Switch[1154653068] #UT -> #UT
      <- Switch[1154653068] #UT -> #UT
      <- Switch[152027216] #BK -> #UT
===#Block UU(size=2, flags=100)===
   0. lvar105 = {2029935266 ^ lvar105};
   1. goto CJ
      -> UnconditionalJump[GOTO] #UU -> #CJ
      <- Switch[145911602] #UT -> #UU
      <- Immediate #UT -> #UU
===#Block CJ(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar73 = lvar103;
   2. lvar101 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.vfnfrbygnvmxidu(), lvar105);
   3. lvar74 = lvar73.equals(lvar101);
   4. if (lvar74 != {1186324038 ^ lvar105})
      goto TS
   5. lvar105 = {813244857 ^ lvar105};
      -> ConditionalJump[IF_ICMPNE] #CJ -> #TS
      -> Immediate #CJ -> #CK
      <- Switch[873235756] #UT -> #CJ
      <- UnconditionalJump[GOTO] #UU -> #CJ
===#Block CK(size=1, flags=0)===
   0. goto RF
      -> UnconditionalJump[GOTO] #CK -> #RF
      <- Immediate #CJ -> #CK
===#Block RF(size=2, flags=10100)===
   0. lvar105 = {1220685349 ^ lvar105};
   1. goto HA
      -> UnconditionalJump[GOTO] #RF -> #HA
      <- UnconditionalJump[GOTO] #CK -> #RF
===#Block HA(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 138184856)
      goto GZ
   1. throw nullconst;
      -> TryCatch range: [HA...GZ] -> ABY ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HA -> #GZ
      <- UnconditionalJump[GOTO] #RF -> #HA
===#Block GZ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HA...GZ] -> ABY ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HA -> #GZ
===#Block ABY(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -260665647:
      	 goto	#ABZ
      case 832455601:
      	 goto	#ACA
      default:
      	 goto	#ACB
   }
      -> Switch[-260665647] #ABY -> #ABZ
      -> DefaultSwitch #ABY -> #ACB
      -> Switch[832455601] #ABY -> #ACA
      <- TryCatch range: [HA...GZ] -> ABY ([Ljava/io/IOException;])
      <- TryCatch range: [HA...GZ] -> ABY ([Ljava/io/IOException;])
===#Block ACA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1264514337);
   1. goto HB
      -> UnconditionalJump[GOTO] #ACA -> #HB
      <- Switch[832455601] #ABY -> #ACA
===#Block ACB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ABY -> #ACB
===#Block ABZ(size=2, flags=10100)===
   0. lvar105 = {1399057549 ^ lvar105};
   1. goto HB
      -> UnconditionalJump[GOTO] #ABZ -> #HB
      <- Switch[-260665647] #ABY -> #ABZ
===#Block HB(size=2, flags=0)===
   0. _consume(catch());
   1. goto LN
      -> UnconditionalJump[GOTO] #HB -> #LN
      <- UnconditionalJump[GOTO] #ABZ -> #HB
      <- UnconditionalJump[GOTO] #ACA -> #HB
===#Block LN(size=2, flags=10100)===
   0. lvar105 = {89748138 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #LN -> #CM
      <- UnconditionalJump[GOTO] #HB -> #LN
===#Block TS(size=2, flags=10100)===
   0. lvar105 = {1662926879 ^ lvar105};
   1. goto JP
      -> UnconditionalJump[GOTO] #TS -> #JP
      <- ConditionalJump[IF_ICMPNE] #CJ -> #TS
===#Block JP(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 761066185)
      goto TP
   1. goto MX
      -> UnconditionalJump[GOTO] #JP -> #MX
      -> ConditionalJump[IF_ICMPEQ] #JP -> #TP
      <- UnconditionalJump[GOTO] #TS -> #JP
===#Block TP(size=2, flags=10100)===
   0. lvar105 = {2124424061 ^ lvar105};
   1. goto CL
      -> UnconditionalJump[GOTO] #TP -> #CL
      <- ConditionalJump[IF_ICMPEQ] #JP -> #TP
===#Block CL(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.LAPIS_BLOCK;
   2. goto KW
      -> UnconditionalJump[GOTO] #CL -> #KW
      <- UnconditionalJump[GOTO] #TP -> #CL
===#Block KW(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 6743422:
      	 goto	#KX
      case 152605613:
      	 goto	#FE
      case 1950191397:
      	 goto	#KW
      case 2009647151:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[6743422] #KW -> #KX
      -> Switch[152605613] #KW -> #FE
      -> Switch[2009647151] #KW -> #JC
      -> DefaultSwitch #KW -> #JC
      -> Switch[1950191397] #KW -> #KW
      -> Immediate #KW -> #KX
      <- Switch[1950191397] #KW -> #KW
      <- UnconditionalJump[GOTO] #CL -> #KW
===#Block KX(size=2, flags=100)===
   0. lvar105 = {1312466095 ^ lvar105};
   1. goto FE
      -> UnconditionalJump[GOTO] #KX -> #FE
      <- Switch[6743422] #KW -> #KX
      <- Immediate #KW -> #KX
===#Block FE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 151991372)
      goto FD
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FE -> #FD
      -> TryCatch range: [FE...FD] -> ZM ([Ljava/lang/IllegalAccessException;])
      <- Switch[152605613] #KW -> #FE
      <- UnconditionalJump[GOTO] #KX -> #FE
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FE...FD] -> ZM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FE -> #FD
===#Block ZM(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1450652584:
      	 goto	#ZN
      case 1435381944:
      	 goto	#ZO
      default:
      	 goto	#ZP
   }
      -> Switch[1435381944] #ZM -> #ZO
      -> DefaultSwitch #ZM -> #ZP
      -> Switch[-1450652584] #ZM -> #ZN
      <- TryCatch range: [FE...FD] -> ZM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FE...FD] -> ZM ([Ljava/lang/IllegalAccessException;])
===#Block ZN(size=2, flags=10100)===
   0. lvar105 = {1018042191 ^ lvar105};
   1. goto FF
      -> UnconditionalJump[GOTO] #ZN -> #FF
      <- Switch[-1450652584] #ZM -> #ZN
===#Block ZP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ZM -> #ZP
===#Block ZO(size=2, flags=10100)===
   0. lvar105 = {590268627 ^ lvar105};
   1. goto FF
      -> UnconditionalJump[GOTO] #ZO -> #FF
      <- Switch[1435381944] #ZM -> #ZO
===#Block FF(size=2, flags=0)===
   0. _consume(catch());
   1. goto LA
      -> UnconditionalJump[GOTO] #FF -> #LA
      <- UnconditionalJump[GOTO] #ZN -> #FF
      <- UnconditionalJump[GOTO] #ZO -> #FF
===#Block LA(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 10749188:
      	 goto	#LB
      case 442470793:
      	 goto	#CN
      case 1485537549:
      	 goto	#LA
      case 2011766412:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Immediate #LA -> #LB
      -> Switch[442470793] #LA -> #CN
      -> Switch[2011766412] #LA -> #JC
      -> DefaultSwitch #LA -> #JC
      -> Switch[10749188] #LA -> #LB
      -> Switch[1485537549] #LA -> #LA
      <- UnconditionalJump[GOTO] #FF -> #LA
      <- Switch[1485537549] #LA -> #LA
===#Block LB(size=2, flags=100)===
   0. lvar105 = {2226505 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #LB -> #CN
      <- Immediate #LA -> #LB
      <- Switch[10749188] #LA -> #LB
===#Block MX(size=2, flags=10100)===
   0. lvar105 = {903813168 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MX -> #JC
      <- UnconditionalJump[GOTO] #JP -> #MX
===#Block UQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 151381137);
   1. goto BO
      -> UnconditionalJump[GOTO] #UQ -> #BO
      <- Switch[152027202] #BK -> #UQ
===#Block BO(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar59 = lvar103;
   2. lvar94 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ndroujsrmuwuslo(), lvar105);
   3. lvar60 = lvar59.equals(lvar94);
   4. if (lvar60 != {927883381 ^ lvar105})
      goto UJ
   5. lvar105 = {1600500340 ^ lvar105};
      -> Immediate #BO -> #BQ
      -> ConditionalJump[IF_ICMPNE] #BO -> #UJ
      <- UnconditionalJump[GOTO] #UQ -> #BO
===#Block UJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1079314643);
   1. goto KB
      -> UnconditionalJump[GOTO] #UJ -> #KB
      <- ConditionalJump[IF_ICMPNE] #BO -> #UJ
===#Block KB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1193589453)
      goto TQ
   1. goto MY
      -> ConditionalJump[IF_ICMPEQ] #KB -> #TQ
      -> UnconditionalJump[GOTO] #KB -> #MY
      <- UnconditionalJump[GOTO] #UJ -> #KB
===#Block MY(size=2, flags=10100)===
   0. lvar105 = {1735388879 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #MY -> #JC
      <- UnconditionalJump[GOTO] #KB -> #MY
===#Block TQ(size=2, flags=10100)===
   0. lvar105 = {1636511487 ^ lvar105};
   1. goto BP
      -> UnconditionalJump[GOTO] #TQ -> #BP
      <- ConditionalJump[IF_ICMPEQ] #KB -> #TQ
===#Block BP(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PRISMARINE;
   2. goto QK
      -> UnconditionalJump[GOTO] #BP -> #QK
      <- UnconditionalJump[GOTO] #TQ -> #BP
===#Block QK(size=2, flags=10100)===
   0. lvar105 = {2054869798 ^ lvar105};
   1. goto EG
      -> UnconditionalJump[GOTO] #QK -> #EG
      <- UnconditionalJump[GOTO] #BP -> #QK
===#Block EG(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 50785612)
      goto EF
   1. throw nullconst;
      -> TryCatch range: [EG...EF] -> YG ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EG -> #EF
      <- UnconditionalJump[GOTO] #QK -> #EG
===#Block EF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EG...EF] -> YG ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EG -> #EF
===#Block YG(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -606103607:
      	 goto	#YI
      case 1733602299:
      	 goto	#YH
      default:
      	 goto	#YJ
   }
      -> Switch[1733602299] #YG -> #YH
      -> Switch[-606103607] #YG -> #YI
      -> DefaultSwitch #YG -> #YJ
      <- TryCatch range: [EG...EF] -> YG ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EG...EF] -> YG ([Ljava/lang/IllegalAccessException;])
===#Block YJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #YG -> #YJ
===#Block YI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1928978966);
   1. goto EH
      -> UnconditionalJump[GOTO] #YI -> #EH
      <- Switch[-606103607] #YG -> #YI
===#Block YH(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 627606928);
   1. goto EH
      -> UnconditionalJump[GOTO] #YH -> #EH
      <- Switch[1733602299] #YG -> #YH
===#Block EH(size=2, flags=0)===
   0. _consume(catch());
   1. goto NA
      -> UnconditionalJump[GOTO] #EH -> #NA
      <- UnconditionalJump[GOTO] #YI -> #EH
      <- UnconditionalJump[GOTO] #YH -> #EH
===#Block NA(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 241977677:
      	 goto	#NB
      case 377909121:
      	 goto	#NA
      case 1046342259:
      	 goto	#JC
      case 1207037329:
      	 goto	#CN
      default:
      	 goto	#JC
   }
      -> Switch[377909121] #NA -> #NA
      -> Switch[1207037329] #NA -> #CN
      -> Switch[241977677] #NA -> #NB
      -> Switch[1046342259] #NA -> #JC
      -> Immediate #NA -> #NB
      -> DefaultSwitch #NA -> #JC
      <- Switch[377909121] #NA -> #NA
      <- UnconditionalJump[GOTO] #EH -> #NA
===#Block NB(size=2, flags=100)===
   0. lvar105 = {1614604130 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #NB -> #CN
      <- Switch[241977677] #NA -> #NB
      <- Immediate #NA -> #NB
===#Block BQ(size=1, flags=0)===
   0. goto NQ
      -> UnconditionalJump[GOTO] #BQ -> #NQ
      <- Immediate #BO -> #BQ
===#Block NQ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 54414626);
   1. goto HP
      -> UnconditionalJump[GOTO] #NQ -> #HP
      <- UnconditionalJump[GOTO] #BQ -> #NQ
===#Block HP(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 259624774)
      goto HO
   1. throw nullconst;
      -> TryCatch range: [HP...HO] -> ACS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HP -> #HO
      <- UnconditionalJump[GOTO] #NQ -> #HP
===#Block HO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HP...HO] -> ACS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HP -> #HO
===#Block ACS(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -627581365:
      	 goto	#ACU
      case 1487730971:
      	 goto	#ACT
      default:
      	 goto	#ACV
   }
      -> DefaultSwitch #ACS -> #ACV
      -> Switch[-627581365] #ACS -> #ACU
      -> Switch[1487730971] #ACS -> #ACT
      <- TryCatch range: [HP...HO] -> ACS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HP...HO] -> ACS ([Ljava/lang/IllegalAccessException;])
===#Block ACT(size=2, flags=10100)===
   0. lvar105 = {2048027805 ^ lvar105};
   1. goto HQ
      -> UnconditionalJump[GOTO] #ACT -> #HQ
      <- Switch[1487730971] #ACS -> #ACT
===#Block ACU(size=2, flags=10100)===
   0. lvar105 = {1783957623 ^ lvar105};
   1. goto HQ
      -> UnconditionalJump[GOTO] #ACU -> #HQ
      <- Switch[-627581365] #ACS -> #ACU
===#Block HQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto KO
      -> UnconditionalJump[GOTO] #HQ -> #KO
      <- UnconditionalJump[GOTO] #ACT -> #HQ
      <- UnconditionalJump[GOTO] #ACU -> #HQ
===#Block KO(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 2033433667);
   1. goto CM
      -> UnconditionalJump[GOTO] #KO -> #CM
      <- UnconditionalJump[GOTO] #HQ -> #KO
===#Block ACV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ACS -> #ACV
===#Block UX(size=2, flags=10100)===
   0. lvar105 = {1187724544 ^ lvar105};
   1. goto BX
      -> UnconditionalJump[GOTO] #UX -> #BX
      <- Switch[152027224] #BK -> #UX
===#Block BX(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar65 = lvar103;
   2. lvar97 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.cnyvlzwhgbdegzw(), lvar105);
   3. lvar66 = lvar65.equals(lvar97);
   4. if (lvar66 != {2021702116 ^ lvar105})
      goto SA
   5. lvar105 = {605738358 ^ lvar105};
      -> Immediate #BX -> #BZ
      -> ConditionalJump[IF_ICMPNE] #BX -> #SA
      <- UnconditionalJump[GOTO] #UX -> #BX
===#Block SA(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 301391933);
   1. goto JI
      -> UnconditionalJump[GOTO] #SA -> #JI
      <- ConditionalJump[IF_ICMPNE] #BX -> #SA
===#Block JI(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1270009547)
      goto TW
   1. goto OC
      -> UnconditionalJump[GOTO] #JI -> #OC
      -> ConditionalJump[IF_ICMPEQ] #JI -> #TW
      <- UnconditionalJump[GOTO] #SA -> #JI
===#Block TW(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 471264481);
   1. goto BY
      -> UnconditionalJump[GOTO] #TW -> #BY
      <- ConditionalJump[IF_ICMPEQ] #JI -> #TW
===#Block BY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.EMERALD_BLOCK;
   2. goto KV
      -> UnconditionalJump[GOTO] #BY -> #KV
      <- UnconditionalJump[GOTO] #TW -> #BY
===#Block KV(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 811220274);
   1. goto FB
      -> UnconditionalJump[GOTO] #KV -> #FB
      <- UnconditionalJump[GOTO] #BY -> #KV
===#Block FB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 102834857)
      goto FA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FB -> #FA
      -> TryCatch range: [FB...FA] -> ZI ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #KV -> #FB
===#Block FA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FB...FA] -> ZI ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FB -> #FA
===#Block ZI(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -364717656:
      	 goto	#ZK
      case 701775954:
      	 goto	#ZJ
      default:
      	 goto	#ZL
   }
      -> Switch[-364717656] #ZI -> #ZK
      -> Switch[701775954] #ZI -> #ZJ
      -> DefaultSwitch #ZI -> #ZL
      <- TryCatch range: [FB...FA] -> ZI ([Ljava/io/IOException;])
      <- TryCatch range: [FB...FA] -> ZI ([Ljava/io/IOException;])
===#Block ZL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ZI -> #ZL
===#Block ZJ(size=2, flags=10100)===
   0. lvar105 = {291124718 ^ lvar105};
   1. goto FC
      -> UnconditionalJump[GOTO] #ZJ -> #FC
      <- Switch[701775954] #ZI -> #ZJ
===#Block ZK(size=2, flags=10100)===
   0. lvar105 = {1227417297 ^ lvar105};
   1. goto FC
      -> UnconditionalJump[GOTO] #ZK -> #FC
      <- Switch[-364717656] #ZI -> #ZK
===#Block FC(size=2, flags=0)===
   0. _consume(catch());
   1. goto OV
      -> UnconditionalJump[GOTO] #FC -> #OV
      <- UnconditionalJump[GOTO] #ZJ -> #FC
      <- UnconditionalJump[GOTO] #ZK -> #FC
===#Block OV(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 58407817:
      	 goto	#OW
      case 335362827:
      	 goto	#CN
      case 563639429:
      	 goto	#JC
      case 1762245525:
      	 goto	#OV
      default:
      	 goto	#JC
   }
      -> Switch[58407817] #OV -> #OW
      -> Immediate #OV -> #OW
      -> Switch[335362827] #OV -> #CN
      -> Switch[563639429] #OV -> #JC
      -> DefaultSwitch #OV -> #JC
      -> Switch[1762245525] #OV -> #OV
      <- UnconditionalJump[GOTO] #FC -> #OV
      <- Switch[1762245525] #OV -> #OV
===#Block OW(size=2, flags=100)===
   0. lvar105 = {2111720041 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #OW -> #CN
      <- Switch[58407817] #OV -> #OW
      <- Immediate #OV -> #OW
===#Block OC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 2030231472);
   1. goto JC
      -> UnconditionalJump[GOTO] #OC -> #JC
      <- UnconditionalJump[GOTO] #JI -> #OC
===#Block BZ(size=1, flags=0)===
   0. goto RA
      -> UnconditionalJump[GOTO] #BZ -> #RA
      <- Immediate #BX -> #BZ
===#Block RA(size=2, flags=10100)===
   0. lvar105 = {532149653 ^ lvar105};
   1. goto GI
      -> UnconditionalJump[GOTO] #RA -> #GI
      <- UnconditionalJump[GOTO] #BZ -> #RA
===#Block GI(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 36182864)
      goto GH
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GI -> #GH
      -> TryCatch range: [GI...GH] -> ABA ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #RA -> #GI
===#Block GH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GI...GH] -> ABA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GI -> #GH
===#Block ABA(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1359053054:
      	 goto	#ABC
      case 426559546:
      	 goto	#ABB
      default:
      	 goto	#ABD
   }
      -> Switch[426559546] #ABA -> #ABB
      -> DefaultSwitch #ABA -> #ABD
      -> Switch[-1359053054] #ABA -> #ABC
      <- TryCatch range: [GI...GH] -> ABA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GI...GH] -> ABA ([Ljava/lang/RuntimeException;])
===#Block ABC(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 2050501498);
   1. goto GJ
      -> UnconditionalJump[GOTO] #ABC -> #GJ
      <- Switch[-1359053054] #ABA -> #ABC
===#Block ABD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #ABA -> #ABD
===#Block ABB(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1825209757);
   1. goto GJ
      -> UnconditionalJump[GOTO] #ABB -> #GJ
      <- Switch[426559546] #ABA -> #ABB
===#Block GJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto RJ
      -> UnconditionalJump[GOTO] #GJ -> #RJ
      <- UnconditionalJump[GOTO] #ABC -> #GJ
      <- UnconditionalJump[GOTO] #ABB -> #GJ
===#Block RJ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1205054311);
   1. goto CM
      -> UnconditionalJump[GOTO] #RJ -> #CM
      <- UnconditionalJump[GOTO] #GJ -> #RJ
===#Block UZ(size=2, flags=10100)===
   0. lvar105 = {1320864291 ^ lvar105};
   1. goto BR
      -> UnconditionalJump[GOTO] #UZ -> #BR
      <- Switch[152027227] #BK -> #UZ
===#Block BR(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar61 = lvar103;
   2. lvar95 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.mtdriadqvclixuv(), lvar105);
   3. lvar62 = lvar61.equals(lvar95);
   4. if (lvar62 != {1894862535 ^ lvar105})
      goto RZ
   5. lvar105 = {1646071387 ^ lvar105};
      -> Immediate #BR -> #BT
      -> ConditionalJump[IF_ICMPNE] #BR -> #RZ
      <- UnconditionalJump[GOTO] #UZ -> #BR
===#Block RZ(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 572633276);
   1. goto JH
      -> UnconditionalJump[GOTO] #RZ -> #JH
      <- ConditionalJump[IF_ICMPNE] #BR -> #RZ
===#Block JH(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1769475110)
      goto RV
   1. goto KM
      -> ConditionalJump[IF_ICMPEQ] #JH -> #RV
      -> UnconditionalJump[GOTO] #JH -> #KM
      <- UnconditionalJump[GOTO] #RZ -> #JH
===#Block KM(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 7651294:
      	 goto	#KN
      case 695982185:
      	 goto	#JC
      case 1275936631:
      	 goto	#KM
      case 1371371309:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> Switch[1275936631] #KM -> #KM
      -> Immediate #KM -> #KN
      -> DefaultSwitch #KM -> #JC
      -> Switch[1371371309] #KM -> #JC
      -> Switch[7651294] #KM -> #KN
      <- Switch[1275936631] #KM -> #KM
      <- UnconditionalJump[GOTO] #JH -> #KM
===#Block KN(size=2, flags=100)===
   0. lvar105 = {1118074898 ^ lvar105};
   1. goto JC
      -> UnconditionalJump[GOTO] #KN -> #JC
      <- Immediate #KM -> #KN
      <- Switch[7651294] #KM -> #KN
===#Block RV(size=2, flags=10100)===
   0. lvar105 = {1523695859 ^ lvar105};
   1. goto BS
      -> UnconditionalJump[GOTO] #RV -> #BS
      <- ConditionalJump[IF_ICMPEQ] #JH -> #RV
===#Block BS(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.DIAMOND_BLOCK;
   2. goto PB
      -> UnconditionalJump[GOTO] #BS -> #PB
      <- UnconditionalJump[GOTO] #RV -> #BS
===#Block PB(size=2, flags=10100)===
   0. lvar105 = {344148923 ^ lvar105};
   1. goto FW
      -> UnconditionalJump[GOTO] #PB -> #FW
      <- UnconditionalJump[GOTO] #BS -> #PB
===#Block FW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 121429904)
      goto FV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FW -> #FV
      -> TryCatch range: [FW...FV] -> AAK ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #PB -> #FW
===#Block FV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FW...FV] -> AAK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FW -> #FV
===#Block AAK(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1986791520:
      	 goto	#AAM
      case -468473448:
      	 goto	#AAL
      default:
      	 goto	#AAN
   }
      -> Switch[-1986791520] #AAK -> #AAM
      -> Switch[-468473448] #AAK -> #AAL
      -> DefaultSwitch #AAK -> #AAN
      <- TryCatch range: [FW...FV] -> AAK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FW...FV] -> AAK ([Ljava/lang/RuntimeException;])
===#Block AAN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #AAK -> #AAN
===#Block AAL(size=2, flags=10100)===
   0. lvar105 = {582817836 ^ lvar105};
   1. goto FX
      -> UnconditionalJump[GOTO] #AAL -> #FX
      <- Switch[-468473448] #AAK -> #AAL
===#Block AAM(size=2, flags=10100)===
   0. lvar105 = {789386219 ^ lvar105};
   1. goto FX
      -> UnconditionalJump[GOTO] #AAM -> #FX
      <- Switch[-1986791520] #AAK -> #AAM
===#Block FX(size=2, flags=0)===
   0. _consume(catch());
   1. goto RR
      -> UnconditionalJump[GOTO] #FX -> #RR
      <- UnconditionalJump[GOTO] #AAM -> #FX
      <- UnconditionalJump[GOTO] #AAL -> #FX
===#Block RR(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 44347773:
      	 goto	#RR
      case 144366931:
      	 goto	#RS
      case 1255130392:
      	 goto	#CN
      case 1815152317:
      	 goto	#JC
      default:
      	 goto	#JC
   }
      -> DefaultSwitch #RR -> #JC
      -> Immediate #RR -> #RS
      -> Switch[1815152317] #RR -> #JC
      -> Switch[1255130392] #RR -> #CN
      -> Switch[44347773] #RR -> #RR
      -> Switch[144366931] #RR -> #RS
      <- Switch[44347773] #RR -> #RR
      <- UnconditionalJump[GOTO] #FX -> #RR
===#Block RS(size=2, flags=100)===
   0. lvar105 = {394369682 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #RS -> #CN
      <- Immediate #RR -> #RS
      <- Switch[144366931] #RR -> #RS
===#Block BT(size=1, flags=0)===
   0. goto MI
      -> UnconditionalJump[GOTO] #BT -> #MI
      <- Immediate #BR -> #BT
===#Block MI(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 42269760);
   1. goto CT
      -> UnconditionalJump[GOTO] #MI -> #CT
      <- UnconditionalJump[GOTO] #BT -> #MI
===#Block CT(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 264961410)
      goto CS
   1. throw nullconst;
      -> TryCatch range: [CT...CS] -> WG ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CT -> #CS
      <- UnconditionalJump[GOTO] #MI -> #CT
===#Block CS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CT...CS] -> WG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CT -> #CS
===#Block WG(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -2092570912:
      	 goto	#WH
      case -1767819190:
      	 goto	#WI
      default:
      	 goto	#WJ
   }
      -> Switch[-2092570912] #WG -> #WH
      -> Switch[-1767819190] #WG -> #WI
      -> DefaultSwitch #WG -> #WJ
      <- TryCatch range: [CT...CS] -> WG ([Ljava/io/IOException;])
      <- TryCatch range: [CT...CS] -> WG ([Ljava/io/IOException;])
===#Block WJ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #WG -> #WJ
===#Block WI(size=2, flags=10100)===
   0. lvar105 = {592287099 ^ lvar105};
   1. goto CU
      -> UnconditionalJump[GOTO] #WI -> #CU
      <- Switch[-1767819190] #WG -> #WI
===#Block WH(size=2, flags=10100)===
   0. lvar105 = {1643187886 ^ lvar105};
   1. goto CU
      -> UnconditionalJump[GOTO] #WH -> #CU
      <- Switch[-2092570912] #WG -> #WH
===#Block CU(size=2, flags=0)===
   0. _consume(catch());
   1. goto LX
      -> UnconditionalJump[GOTO] #CU -> #LX
      <- UnconditionalJump[GOTO] #WH -> #CU
      <- UnconditionalJump[GOTO] #WI -> #CU
===#Block LX(size=2, flags=10100)===
   0. lvar105 = {430833039 ^ lvar105};
   1. goto CM
      -> UnconditionalJump[GOTO] #LX -> #CM
      <- UnconditionalJump[GOTO] #CU -> #LX
===#Block CM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.IRON_BLOCK;
   2. goto MF
      -> UnconditionalJump[GOTO] #CM -> #MF
      <- Switch[2072095836] #VB -> #CM
      <- UnconditionalJump[GOTO] #LN -> #CM
      <- UnconditionalJump[GOTO] #MN -> #CM
      <- UnconditionalJump[GOTO] #OX -> #CM
      <- UnconditionalJump[GOTO] #LX -> #CM
      <- UnconditionalJump[GOTO] #MC -> #CM
      <- UnconditionalJump[GOTO] #KF -> #CM
      <- UnconditionalJump[GOTO] #MU -> #CM
      <- UnconditionalJump[GOTO] #VC -> #CM
      <- UnconditionalJump[GOTO] #RJ -> #CM
      <- UnconditionalJump[GOTO] #KO -> #CM
===#Block MF(size=1, flags=10100)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105)) {
      case 166015694:
      	 goto	#MG
      case 434765075:
      	 goto	#JC
      case 1643607394:
      	 goto	#MF
      case 2068020369:
      	 goto	#IW
      default:
      	 goto	#JC
   }
      -> Switch[2068020369] #MF -> #IW
      -> Switch[1643607394] #MF -> #MF
      -> Immediate #MF -> #MG
      -> Switch[166015694] #MF -> #MG
      -> Switch[434765075] #MF -> #JC
      -> DefaultSwitch #MF -> #JC
      <- UnconditionalJump[GOTO] #CM -> #MF
      <- Switch[1643607394] #MF -> #MF
===#Block JC(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      <- Switch[1418618534] #SU -> #JC
      <- DefaultSwitch #SU -> #JC
      <- Switch[548605852] #OD -> #JC
      <- DefaultSwitch #QI -> #JC
      <- Switch[1982944598] #QI -> #JC
      <- DefaultSwitch #OK -> #JC
      <- DefaultSwitch #SK -> #JC
      <- DefaultSwitch #OD -> #JC
      <- UnconditionalJump[GOTO] #MP -> #JC
      <- DefaultSwitch #NO -> #JC
      <- Switch[123407018] #NO -> #JC
      <- Switch[1059141958] #OK -> #JC
      <- Switch[1687584494] #NY -> #JC
      <- DefaultSwitch #NY -> #JC
      <- DefaultSwitch #OG -> #JC
      <- Switch[1976509115] #OG -> #JC
      <- Switch[419966015] #SK -> #JC
      <- Switch[79634127] #PF -> #JC
      <- DefaultSwitch #PF -> #JC
      <- UnconditionalJump[GOTO] #MX -> #JC
      <- DefaultSwitch #OS -> #JC
      <- UnconditionalJump[GOTO] #KJ -> #JC
      <- Switch[476654884] #QS -> #JC
      <- DefaultSwitch #QS -> #JC
      <- Switch[1982373174] #OS -> #JC
      <- Switch[648572197] #LE -> #JC
      <- DefaultSwitch #LJ -> #JC
      <- DefaultSwitch #LE -> #JC
      <- UnconditionalJump[GOTO] #PA -> #JC
      <- Switch[165550250] #LJ -> #JC
      <- Switch[2009647151] #KW -> #JC
      <- DefaultSwitch #KW -> #JC
      <- Switch[1721204549] #VF -> #JC
      <- UnconditionalJump[GOTO] #PK -> #JC
      <- DefaultSwitch #VF -> #JC
      <- Switch[1960893117] #SE -> #JC
      <- Switch[563639429] #OV -> #JC
      <- DefaultSwitch #SE -> #JC
      <- UnconditionalJump[GOTO] #LL -> #JC
      <- DefaultSwitch #OV -> #JC
      <- DefaultSwitch #NG -> #JC
      <- Switch[741139399] #NG -> #JC
      <- Switch[1354740887] #TU -> #JC
      <- DefaultSwitch #TU -> #JC
      <- DefaultSwitch #RR -> #JC
      <- DefaultSwitch #OA -> #JC
      <- Switch[617055052] #OA -> #JC
      <- Switch[1815152317] #RR -> #JC
      <- DefaultSwitch #KM -> #JC
      <- Switch[842024203] #TJ -> #JC
      <- DefaultSwitch #MS -> #JC
      <- DefaultSwitch #TJ -> #JC
      <- Switch[892555768] #MS -> #JC
      <- Switch[1371371309] #KM -> #JC
      <- Switch[147701489] #RX -> #JC
      <- DefaultSwitch #WA -> #JC
      <- UnconditionalJump[GOTO] #RQ -> #JC
      <- Switch[1203487958] #WA -> #JC
      <- DefaultSwitch #RX -> #JC
      <- Switch[1772082589] #NI -> #JC
      <- DefaultSwitch #PQ -> #JC
      <- DefaultSwitch #NI -> #JC
      <- Switch[1746109716] #PQ -> #JC
      <- DefaultSwitch #RB -> #JC
      <- Switch[1727262788] #UT -> #JC
      <- Switch[1910505643] #RB -> #JC
      <- DefaultSwitch #ML -> #JC
      <- DefaultSwitch #UT -> #JC
      <- UnconditionalJump[GOTO] #PM -> #JC
      <- Switch[1901487497] #ML -> #JC
      <- UnconditionalJump[GOTO] #KS -> #JC
      <- DefaultSwitch #VB -> #JC
      <- DefaultSwitch #UM -> #JC
      <- Switch[1037479017] #VD -> #JC
      <- Switch[1401220681] #VB -> #JC
      <- Switch[1349328947] #UM -> #JC
      <- DefaultSwitch #VD -> #JC
      <- Switch[393060633] #QP -> #JC
      <- DefaultSwitch #QP -> #JC
      <- Switch[2011766412] #LA -> #JC
      <- DefaultSwitch #LA -> #JC
      <- DefaultSwitch #LP -> #JC
      <- DefaultSwitch #VT -> #JC
      <- UnconditionalJump[GOTO] #PP -> #JC
      <- Switch[1046342259] #NA -> #JC
      <- UnconditionalJump[GOTO] #KL -> #JC
      <- Switch[1377943361] #VT -> #JC
      <- Switch[739690612] #LP -> #JC
      <- DefaultSwitch #NA -> #JC
      <- DefaultSwitch #VP -> #JC
      <- Switch[1646852032] #VP -> #JC
      <- DefaultSwitch #RP -> #JC
      <- Switch[1280335975] #RP -> #JC
      <- DefaultSwitch #MO -> #JC
      <- DefaultSwitch #MD -> #JC
      <- Switch[1074987053] #MO -> #JC
      <- Switch[1234005085] #PL -> #JC
      <- DefaultSwitch #PL -> #JC
      <- Switch[585585669] #MD -> #JC
      <- UnconditionalJump[GOTO] #PV -> #JC
      <- UnconditionalJump[GOTO] #NU -> #JC
      <- UnconditionalJump[GOTO] #OE -> #JC
      <- UnconditionalJump[GOTO] #QB -> #JC
      <- UnconditionalJump[GOTO] #OC -> #JC
      <- Switch[471587702] #OQ -> #JC
      <- Switch[434765075] #MF -> #JC
      <- DefaultSwitch #OQ -> #JC
      <- DefaultSwitch #MF -> #JC
      <- UnconditionalJump[GOTO] #RD -> #JC
      <- UnconditionalJump[GOTO] #MY -> #JC
      <- UnconditionalJump[GOTO] #PG -> #JC
      <- DefaultSwitch #QE -> #JC
      <- Switch[1522480817] #QE -> #JC
      <- Switch[1654828388] #NK -> #JC
      <- Switch[2050200866] #KR -> #JC
      <- Switch[2086976937] #QL -> #JC
      <- DefaultSwitch #NK -> #JC
      <- DefaultSwitch #KR -> #JC
      <- DefaultSwitch #QL -> #JC
      <- DefaultSwitch #KT -> #JC
      <- Switch[1540573306] #KT -> #JC
      <- DefaultSwitch #OO -> #JC
      <- UnconditionalJump[GOTO] #ON -> #JC
      <- DefaultSwitch #NV -> #JC
      <- Switch[931813680] #NV -> #JC
      <- Switch[145475336] #UC -> #JC
      <- DefaultSwitch #UC -> #JC
      <- Switch[990436312] #UR -> #JC
      <- DefaultSwitch #KH -> #JC
      <- DefaultSwitch #UR -> #JC
      <- Switch[878642049] #KH -> #JC
      <- DefaultSwitch #MQ -> #JC
      <- Switch[649165595] #PY -> #JC
      <- DefaultSwitch #PY -> #JC
      <- DefaultSwitch #SB -> #JC
      <- DefaultSwitch #UH -> #JC
      <- Switch[759567454] #TZ -> #JC
      <- Switch[706596519] #UH -> #JC
      <- Switch[587696948] #MQ -> #JC
      <- DefaultSwitch #TZ -> #JC
      <- DefaultSwitch #LZ -> #JC
      <- Switch[1779146031] #LZ -> #JC
      <- Switch[1712032753] #SB -> #JC
      <- Switch[203913136] #OO -> #JC
      <- UnconditionalJump[GOTO] #QX -> #JC
      <- Switch[515067286] #SR -> #JC
      <- DefaultSwitch #SR -> #JC
      <- Switch[60069382] #PW -> #JC
      <- DefaultSwitch #PW -> #JC
      <- Switch[1050023647] #QC -> #JC
      <- DefaultSwitch #QC -> #JC
      <- Switch[1876720141] #SP -> #JC
      <- DefaultSwitch #NS -> #JC
      <- Switch[582592966] #NC -> #JC
      <- Switch[1421321762] #NS -> #JC
      <- DefaultSwitch #NC -> #JC
      <- DefaultSwitch #TD -> #JC
      <- Switch[1523069994] #TD -> #JC
      <- Switch[306589607] #LU -> #JC
      <- DefaultSwitch #LU -> #JC
      <- DefaultSwitch #PU -> #JC
      <- DefaultSwitch #KP -> #JC
      <- Switch[1075031567] #PU -> #JC
      <- Switch[274525430] #KP -> #JC
      <- UnconditionalJump[GOTO] #LY -> #JC
      <- UnconditionalJump[GOTO] #LF -> #JC
      <- DefaultSwitch #LG -> #JC
      <- Switch[1436541947] #LG -> #JC
      <- DefaultSwitch #RN -> #JC
      <- Switch[257948040] #SY -> #JC
      <- DefaultSwitch #SY -> #JC
      <- DefaultSwitch #SP -> #JC
      <- Switch[951255304] #UK -> #JC
      <- UnconditionalJump[GOTO] #QW -> #JC
      <- UnconditionalJump[GOTO] #PI -> #JC
      <- DefaultSwitch #UK -> #JC
      <- UnconditionalJump[GOTO] #MV -> #JC
      <- UnconditionalJump[GOTO] #KN -> #JC
      <- Switch[999089629] #RN -> #JC
      <- DefaultSwitch #QU -> #JC
      <- Switch[409272081] #QU -> #JC
      <- UnconditionalJump[GOTO] #OB -> #JC
      <- DefaultSwitch #OM -> #JC
      <- DefaultSwitch #UF -> #JC
      <- Switch[900619731] #UF -> #JC
      <- DefaultSwitch #VY -> #JC
      <- DefaultSwitch #LR -> #JC
      <- Switch[1557388651] #OM -> #JC
      <- Switch[421721652] #LR -> #JC
      <- Switch[1785802360] #VY -> #JC
      <- UnconditionalJump[GOTO] #PR -> #JC
===#Block MG(size=2, flags=100)===
   0. lvar105 = {528214037 ^ lvar105};
   1. goto IW
      -> UnconditionalJump[GOTO] #MG -> #IW
      <- Immediate #MF -> #MG
      <- Switch[166015694] #MF -> #MG
===#Block IW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 120201758)
      goto IV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IW -> #IV
      -> TryCatch range: [IW...IV] -> AEK ([Ljava/lang/IllegalAccessException;])
      <- Switch[2068020369] #MF -> #IW
      <- UnconditionalJump[GOTO] #MG -> #IW
===#Block IV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IW...IV] -> AEK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IW -> #IV
===#Block AEK(size=1, flags=0)===
   0. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.jzuyapiurryayszk(lvar105)) {
      case -1166196925:
      	 goto	#AEL
      case 796091544:
      	 goto	#AEM
      default:
      	 goto	#AEN
   }
      -> Switch[-1166196925] #AEK -> #AEL
      -> Switch[796091544] #AEK -> #AEM
      -> DefaultSwitch #AEK -> #AEN
      <- TryCatch range: [IW...IV] -> AEK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IW...IV] -> AEK ([Ljava/lang/IllegalAccessException;])
===#Block AEN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #AEK -> #AEN
===#Block AEM(size=2, flags=10100)===
   0. lvar105 = {1049051341 ^ lvar105};
   1. goto IX
      -> UnconditionalJump[GOTO] #AEM -> #IX
      <- Switch[796091544] #AEK -> #AEM
===#Block AEL(size=2, flags=10100)===
   0. lvar105 = cn.acebrand.acedex.gui.PokeBallItemCreator.koycbntfapwxqqyk(lvar105, 1277594550);
   1. goto IX
      -> UnconditionalJump[GOTO] #AEL -> #IX
      <- Switch[-1166196925] #AEK -> #AEL
===#Block IX(size=2, flags=0)===
   0. _consume(catch());
   1. goto QZ
      -> UnconditionalJump[GOTO] #IX -> #QZ
      <- UnconditionalJump[GOTO] #AEM -> #IX
      <- UnconditionalJump[GOTO] #AEL -> #IX
===#Block QZ(size=2, flags=10100)===
   0. lvar105 = {316136403 ^ lvar105};
   1. goto CN
      -> UnconditionalJump[GOTO] #QZ -> #CN
      <- UnconditionalJump[GOTO] #IX -> #QZ
===#Block CN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[1] [org/bukkit/Material]
   1. lvar8 = lvar75;
   2. lvar105 = {980712790 ^ lvar105};
      -> Immediate #CN -> #CO
      <- Switch[135279667] #OS -> #CN
      <- Switch[1238062078] #QP -> #CN
      <- UnconditionalJump[GOTO] #QR -> #CN
      <- Switch[442470793] #LA -> #CN
      <- UnconditionalJump[GOTO] #RO -> #CN
      <- Switch[1255130392] #RR -> #CN
      <- UnconditionalJump[GOTO] #LI -> #CN
      <- UnconditionalJump[GOTO] #QQ -> #CN
      <- Immediate #BJ -> #CN
      <- UnconditionalJump[GOTO] #ND -> #CN
      <- UnconditionalJump[GOTO] #NL -> #CN
      <- UnconditionalJump[GOTO] #NT -> #CN
      <- Switch[1112029742] #RN -> #CN
      <- Switch[1207037329] #NA -> #CN
      <- UnconditionalJump[GOTO] #NB -> #CN
      <- Switch[1059391237] #KP -> #CN
      <- UnconditionalJump[GOTO] #RG -> #CN
      <- UnconditionalJump[GOTO] #RM -> #CN
      <- UnconditionalJump[GOTO] #NZ -> #CN
      <- UnconditionalJump[GOTO] #PS -> #CN
      <- Switch[485439043] #NK -> #CN
      <- UnconditionalJump[GOTO] #LD -> #CN
      <- UnconditionalJump[GOTO] #OW -> #CN
      <- UnconditionalJump[GOTO] #RS -> #CN
      <- Switch[754693075] #NY -> #CN
      <- Switch[1970482566] #QL -> #CN
      <- UnconditionalJump[GOTO] #NN -> #CN
      <- UnconditionalJump[GOTO] #QY -> #CN
      <- Switch[359444912] #LU -> #CN
      <- Switch[1389413467] #NS -> #CN
      <- UnconditionalJump[GOTO] #RH -> #CN
      <- UnconditionalJump[GOTO] #QM -> #CN
      <- UnconditionalJump[GOTO] #MK -> #CN
      <- UnconditionalJump[GOTO] #LV -> #CN
      <- Switch[2060736650] #NC -> #CN
      <- UnconditionalJump[GOTO] #LB -> #CN
      <- UnconditionalJump[GOTO] #MW -> #CN
      <- UnconditionalJump[GOTO] #PH -> #CN
      <- Switch[335362827] #OV -> #CN
      <- UnconditionalJump[GOTO] #PD -> #CN
      <- UnconditionalJump[GOTO] #PE -> #CN
      <- UnconditionalJump[GOTO] #KQ -> #CN
      <- UnconditionalJump[GOTO] #OT -> #CN
      <- UnconditionalJump[GOTO] #QZ -> #CN
===#Block CO(size=4, flags=0)===
   0. lvar16 = new org.bukkit.inventory.ItemStack;
   1. lvar7 = lvar8;
   2. _consume(lvar16.<init>(lvar7));
   3. return lvar16;
      <- Immediate #CN -> #CO
