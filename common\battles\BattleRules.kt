/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.battles

object BattleRules {
    const val OBTAINABLE = "Obtainable"
    const val PAST = "+Past"
    const val UNOBTAINABLE = "+Unobtainable"
    const val TEAM_PREVIEW = "Team Preview"
    const val ENDLESS_BATTLE_CLAUSE = "Endless Battle Clause"
    const val CANCEL_MOD = "Cancel Mod"
    const val SLEEP_CLAUSE = "Sleep Clause"
    const val HP_PERCENTAGE_MOD = "HP Percentage Mod"
}