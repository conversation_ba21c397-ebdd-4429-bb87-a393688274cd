/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.data.AllGenerationsProgress
import cn.acebrand.acedex.data.GenerationProgress
import cn.acebrand.acedex.data.PlayerPokemonData
import cn.acebrand.acedex.gui.PokemonItemCreator
import cn.acebrand.acedex.gui.PokeBallItemCreator
import cn.acebrand.acedex.util.PokemonInfo
import org.bukkit.Bukkit
import org.bukkit.Material
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.ClickType
import org.bukkit.event.inventory.InventoryClickEvent
import org.bukkit.event.inventory.InventoryCloseEvent
import org.bukkit.event.player.PlayerQuitEvent
import org.bukkit.inventory.Inventory
import org.bukkit.inventory.ItemStack
import org.bukkit.inventory.meta.ItemMeta
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 图鉴主界面GUI
 */
class DexMainGui(private val plugin: AceDex) : Listener {

    val openGuis = mutableMapOf<Player, String>()
    private val playerPages = mutableMapOf<Player, Int>() // 玩家当前页码

    // 缓存玩家的主菜单Inventory，避免重复创建 - 改为线程安全
    private val playerMainInventories = ConcurrentHashMap<Player, Inventory>()

    // 缓存玩家的世代菜单Inventory，避免重复创建 - 改为线程安全
    private val playerGenerationInventories = ConcurrentHashMap<String, Inventory>() // key: "playerUUID:generationId:page"
    private val pokemonItemCreator = PokemonItemCreator(plugin) // 精灵物品创建器
    private val pokeBallItemCreator = PokeBallItemCreator(plugin) // 精灵球物品创建器

    // 存储玩家点击冷却时间
    private val playerCooldowns = ConcurrentHashMap<Player, MutableMap<String, Long>>()
    
    init {
        plugin.server.pluginManager.registerEvents(this, plugin)
    }

    /**
     * 强制清理玩家的菜单状态（用于强制关闭指令）
     */
    fun forceCleanupPlayerState(player: Player) {
        openGuis.remove(player)
        playerPages.remove(player)
        playerCooldowns.remove(player)
        playerMainInventories.remove(player)

        // 清理该玩家的所有世代菜单缓存
        val playerUUID = player.uniqueId.toString()
        val keysToRemove = playerGenerationInventories.keys.filter { it.startsWith("$playerUUID:") }
        keysToRemove.forEach { playerGenerationInventories.remove(it) }
    }

    /**
     * 检查玩家是否在冷却时间内
     */
    private fun isPlayerOnCooldown(player: Player, action: String): Boolean {
        val playerCooldownMap = playerCooldowns.getOrPut(player) { mutableMapOf() }
        val lastClickTime = playerCooldownMap[action] ?: 0
        val currentTime = System.currentTimeMillis()

        val cooldownTime = when (action) {
            "stats" -> plugin.config.statsButtonCooldown
            "progress" -> plugin.config.progressButtonCooldown
            "generation-reward" -> plugin.config.generationRewardCooldown
            else -> 0
        }

        return (currentTime - lastClickTime) < cooldownTime
    }

    /**
     * 设置玩家冷却时间
     */
    private fun setPlayerCooldown(player: Player, action: String) {
        val playerCooldownMap = playerCooldowns.getOrPut(player) { mutableMapOf() }
        playerCooldownMap[action] = System.currentTimeMillis()
    }

    /**
     * 获取剩余冷却时间（秒）
     */
    private fun getRemainingCooldown(player: Player, action: String): Int {
        val playerCooldownMap = playerCooldowns.getOrPut(player) { mutableMapOf() }
        val lastClickTime = playerCooldownMap[action] ?: 0
        val currentTime = System.currentTimeMillis()

        val cooldownTime = when (action) {
            "stats" -> plugin.config.statsButtonCooldown
            "progress" -> plugin.config.progressButtonCooldown
            "generation-reward" -> plugin.config.generationRewardCooldown
            else -> 0
        }

        val remaining = cooldownTime - (currentTime - lastClickTime)
        return if (remaining > 0) (remaining / 1000).toInt() + 1 else 0
    }

    /**
     * 获取或创建玩家的主菜单Inventory
     */
    private fun getOrCreateMainInventory(player: Player): Inventory {
        // 检查是否已有缓存的主菜单
        val existingInventory = playerMainInventories[player]
        if (existingInventory != null) {
            return existingInventory
        }

        // 创建新的主菜单
        val title = plugin.config.getProcessedGuiTitle(player)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景和装饰（这些不会变化）
        fillBackground(inventory)
        addDecorations(inventory)
        addCloseButton(inventory)

        // 缓存菜单
        playerMainInventories[player] = inventory
        return inventory
    }

    /**
     * 获取或创建玩家的世代菜单Inventory
     */
    private fun getOrCreateGenerationInventory(player: Player, generationId: String, page: Int): Inventory {
        val generation = plugin.generationManager.getGeneration(generationId) ?: return Bukkit.createInventory(null, 54, "§c错误")
        val cacheKey = "${player.uniqueId}:$generationId:$page"

        // 检查是否已有缓存的世代菜单
        val existingInventory = playerGenerationInventories[cacheKey]
        if (existingInventory != null) {
            return existingInventory
        }

        // 创建新的世代菜单 - 使用配置的标题
        val title = plugin.config.getProcessedGenerationTitle(player, generation)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景（这些不会变化）
        fillBackground(inventory)

        // 缓存菜单
        playerGenerationInventories[cacheKey] = inventory
        return inventory
    }

    /**
     * 只更新主菜单数据，不重新创建菜单
     */
    fun updateMainGuiData(player: Player) {
        val inventory = playerMainInventories[player] ?: return

        // 检查玩家是否还在主界面
        if (openGuis[player] != "main" || player.openInventory.topInventory != inventory) {
            return
        }

        // 直接从本地文件读取数据，快速响应
        try {
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            // 直接更新界面内容（世代按钮现在使用实时计算）
            val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
            addGenerationButtonsWithData(inventory, player, emptyProgress)
            addStatsButtonWithData(inventory, player, playerData)
            addOverallProgressDisplayWithData(inventory, player, allProgress)
        } catch (e: Exception) {
            // 如果读取失败，显示错误信息并尝试重新检测
            player.sendMessage("§c加载数据时发生错误，正在重新检测...")
            plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                try {
                    // 使用新的CheckPokemon方法重新检测并保存数据
                    plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

                    // 回到主线程更新界面
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        if (openGuis[player] == "main" && player.openInventory.topInventory == inventory) {
                            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

                            val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
                            addGenerationButtonsWithData(inventory, player, emptyProgress)
                            addStatsButtonWithData(inventory, player, playerData)
                            addOverallProgressDisplayWithData(inventory, player, allProgress)

                            player.sendMessage("§a数据已更新！")
                        }
                    })
                } catch (e2: Exception) {
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        if (openGuis[player] == "main") {
                            player.sendMessage("§c无法获取精灵数据，请稍后重试")
                        }
                    })
                }
            })
        }
    }

    /**
     * 清除玩家的主菜单缓存（当需要重新创建菜单时使用）
     */
    fun clearMainInventoryCache(player: Player) {
        playerMainInventories.remove(player)
    }

    /**
     * 清除所有玩家的主菜单缓存
     */
    fun clearAllMainInventoryCache() {
        playerMainInventories.clear()
    }

    /**
     * 清除玩家的世代菜单缓存
     */
    fun clearGenerationInventoryCache(player: Player, generationId: String? = null) {
        val playerUUID = player.uniqueId.toString()
        if (generationId != null) {
            // 清除特定世代的所有页面缓存
            val keysToRemove = playerGenerationInventories.keys.filter {
                it.startsWith("$playerUUID:$generationId:")
            }
            keysToRemove.forEach { playerGenerationInventories.remove(it) }
        } else {
            // 清除该玩家的所有世代菜单缓存
            val keysToRemove = playerGenerationInventories.keys.filter {
                it.startsWith("$playerUUID:")
            }
            keysToRemove.forEach { playerGenerationInventories.remove(it) }
        }
    }

    /**
     * 优化版本：获取或创建主菜单，集成预加载管理器
     */
    private fun getOrCreateMainInventoryOptimized(player: Player): Inventory {
        // 检查是否已有缓存的主菜单
        val existingInventory = playerMainInventories[player]
        if (existingInventory != null) {
            return existingInventory
        }

        // 创建新的主菜单
        val title = plugin.config.getProcessedGuiTitle(player)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景（这些不会变化）
        fillBackground(inventory)

        // 缓存菜单
        playerMainInventories[player] = inventory
        return inventory
    }



    /**
     * 优化版本：创建世代按钮 - 使用配置文件和精灵球物品创建器，显示详细信息
     */
    private fun createGenerationButtonOptimized(generation: cn.acebrand.acedex.generation.Generation, progress: GenerationProgress?, player: Player): ItemStack {
        // 使用精灵球物品创建器，它会自动使用 gui.yml 配置的材质
        val isCompleted = (progress?.percentage ?: 0) >= 100
        val hasClaimedReward = if (isCompleted) plugin.rewardManager.hasClaimedGenerationReward(player, generation.id) else false
        val hasCaught = (progress?.caught ?: 0) > 0

        val item = pokeBallItemCreator.createGenerationPokeBallItem(
            generationId = generation.id,
            hasCaught = hasCaught,
            isCompleted = isCompleted && hasClaimedReward
        )

        val meta = item.itemMeta
        meta?.let {
            // 设置显示名称（与世代菜单内部按钮一致）
            val displayName = when {
                isCompleted && hasClaimedReward -> "${generation.color}§l${generation.name} §a✓"
                isCompleted -> "${generation.color}§l${generation.name} §6★"
                else -> "${generation.color}§l${generation.name}"
            }
            it.setDisplayName(displayName)

            val lore = mutableListOf<String>()

            // 添加世代基本信息（与世代菜单内部按钮一致）
            lore.add("§7地区: §f${generation.region}")
            lore.add("§7图鉴范围: §f${generation.pokemonRange.first}-${generation.pokemonRange.last}")

            // 使用精灵球物品创建器获取主题信息
            val ballThemeName = pokeBallItemCreator.getPokeBallThemeName(generation.id)
            val ballThemeColor = pokeBallItemCreator.getPokeBallThemeColor(generation.id)
            lore.add("§7主题: $ballThemeColor$ballThemeName")
            lore.add("")

            // 添加进度信息
            lore.add("§e你的进度:")
            if (progress != null) {
                lore.add("§7已收集: §a${progress.caught}§7/§f${progress.total}")

                // 使用与世代菜单内部完全相同的进度条显示方式
                lore.add("§7完成度: ${progress.getProgressBar()}")
            } else {
                lore.add("§7已收集: §c0§7/§c0")
                lore.add("§7完成度: §c未检测")
            }
            lore.add("")

            // 添加奖励状态信息（与世代菜单内部按钮一致）
            when {
                progress != null && isCompleted && hasClaimedReward -> {
                    lore.add("§a§l已完成并领取奖励！")
                    lore.add("§7✦ 大师级收集家 ✦")
                    lore.add("")
                    lore.add("§e左键: §7查看精灵详情")
                }
                progress != null && isCompleted -> {
                    lore.add("§6§l恭喜完成！可以领取奖励")
                    lore.add("§7◇ 准备成为大师 ◇")
                    lore.add("")
                    lore.add("§e完成奖励:")
                    // 从配置文件获取奖励描述
                    val rewardDescriptions = plugin.config.getGenerationRewardDescriptions(generation.id)
                    rewardDescriptions.forEach { description ->
                        lore.add(description)
                    }
                    lore.add("")
                    lore.add("§e左键: §7查看精灵详情")
                    lore.add("§a右键: §7领取完成奖励")
                }
                progress != null -> {
                    val remaining = progress.total - progress.caught
                    lore.add("§7还需收集 §c$remaining §7只精灵")
                    lore.add("§7完成后可在主菜单领取奖励")
                    lore.add("")

                    // 显示进度奖励信息
                    addProgressRewardInfoWithPlayer(lore, generation.id, progress.percentage, player)

                    lore.add("§e点击查看详情")
                }
                else -> {
                    lore.add("§7进度: §c未检测")
                    lore.add("")
                    lore.add("§e点击查看详情")
                }
            }

            it.lore = lore

            // 添加附魔效果（完成但未领取）
            if (isCompleted && !hasClaimedReward) {
                it.addEnchant(org.bukkit.enchantments.Enchantment.LURE, 1, true)
                it.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS)
            }
        }
        item.itemMeta = meta

        return item
    }

    /**
     * 为异步GUI管理器创建主菜单
     */
    fun createMainInventoryForPlayer(player: Player): Inventory {
        val title = plugin.config.getProcessedGuiTitle(player)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景
        fillBackground(inventory)

        // 添加基础内容
        try {
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
            addGenerationButtonsWithData(inventory, player, emptyProgress)
            addStatsButtonWithData(inventory, player, playerData)
            addOverallProgressDisplayWithData(inventory, player, allProgress)
        } catch (e: Exception) {
            plugin.logger.warning("创建玩家 ${player.name} 主菜单时出错: ${e.message}")
        }

        return inventory
    }

    /**
     * 为异步GUI管理器创建世代菜单
     */
    fun createGenerationInventoryForPlayer(player: Player, generationId: String): Inventory? {
        val generation = plugin.generationManager.getGeneration(generationId) ?: return null

        val title = plugin.config.getProcessedGenerationTitle(player, generation)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景
        fillBackground(inventory)

        // 添加世代精灵（使用预加载的模型）
        addGenerationPokemonOptimized(inventory, player, generation)

        // 添加进度信息
        addProgressInfo(inventory, player, generationId)

        // 添加分页按钮
        addPaginationButtons(inventory, player, generationId, 0)

        return inventory
    }

    /**
     * 优化版本：添加世代精灵，使用预加载的模型
     */
    private fun addGenerationPokemonOptimized(inventory: Inventory, player: Player, generation: cn.acebrand.acedex.generation.Generation) {
        val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
        val currentPage = playerPages[player] ?: 0
        val pokemonPerPage = 28

        // 使用预加载管理器获取世代精灵列表
        val pokemonList = plugin.pokemonModelPreloader.getGenerationPokemonList(generation.id)

        // 分页处理
        val startIndex = currentPage * pokemonPerPage
        val endIndex = minOf(startIndex + pokemonPerPage, pokemonList.size)

        // 检查索引有效性，避免越界错误
        if (startIndex >= pokemonList.size) {
            plugin.logger.warning("创建玩家 ${player.name} 的世代菜单 ${generation.id} 失败: 页码超出范围 (startIndex: $startIndex, pokemonList.size: ${pokemonList.size})")
            return
        }

        val pagePokemons = if (startIndex < endIndex) {
            pokemonList.subList(startIndex, endIndex)
        } else {
            emptyList()
        }

        // 精灵显示位置（跳过边框和功能按钮）
        val pokemonSlots = mutableListOf<Int>()
        for (row in 1..3) {
            for (col in 1..7) {
                pokemonSlots.add(row * 9 + col)
            }
        }

        pagePokemons.forEachIndexed { index, pokemonName ->
            if (index < pokemonSlots.size) {
                val slot = pokemonSlots[index]
                val pokemonData = plugin.generationManager.getPokemonData(pokemonName)

                if (pokemonData != null) {
                    val hasCaught = (playerData.partyPokemon + playerData.pcPokemon).any {
                        it.name.lowercase() == pokemonName.lowercase()
                    }

                    // 使用预加载的精灵物品
                    val pokemonItem = plugin.pokemonModelPreloader.getPreloadedPokemonItem(
                        pokemonName, pokemonData.dex, hasCaught
                    ) ?: plugin.pokemonItemCreator.createPokemonItem(pokemonName, pokemonData.dex, hasCaught)

                    inventory.setItem(slot, pokemonItem)
                }
            }
        }
    }

    /**
     * 刷新当前打开的GUI
     */
    fun refreshCurrentGui(player: Player) {
        val currentGui = openGuis[player]
        if (plugin.config.enableDebug) {
            plugin.logger.info("刷新玩家 ${player.name} 的GUI，当前界面: $currentGui")
        }
        when {
            currentGui == "main" -> {
                // 清除主界面缓存并重新打开
                clearMainInventoryCache(player)
                if (plugin.config.enableDebug) {
                    plugin.logger.info("重新打开玩家 ${player.name} 的主界面")
                }
                openMainGuiAsync(player)
            }
            currentGui?.startsWith("generation:") == true -> {
                // 清除世代界面缓存并重新打开
                val generationId = currentGui.substring("generation:".length)
                clearGenerationInventoryCache(player, generationId)
                val generation = plugin.generationManager.getGeneration(generationId)
                if (generation != null) {
                    val currentPage = playerPages[player] ?: 0
                    if (plugin.config.enableDebug) {
                        plugin.logger.info("重新打开玩家 ${player.name} 的世代界面: $generationId, 页码: $currentPage")
                    }
                    openGenerationGuiAsync(player, generationId, currentPage)
                }
            }
            else -> {
                // 即使玩家没有打开图鉴界面，也要清除缓存，确保下次打开时显示最新数据
                clearMainInventoryCache(player)
                clearGenerationInventoryCache(player)

                if (plugin.config.enableDebug) {
                    plugin.logger.info("玩家 ${player.name} 当前没有打开图鉴界面，但已清除缓存确保下次显示最新数据")
                }
            }
        }
    }

    /**
     * 清除所有世代菜单缓存
     */
    fun clearAllGenerationInventoryCache() {
        playerGenerationInventories.clear()
    }

    /**
     * 检查玩家是否正在查看图鉴菜单
     */
    fun isPlayerInGui(player: Player): Boolean {
        val currentGui = openGuis[player]
        return currentGui == "main" || currentGui?.startsWith("generation:") == true
    }

    /**
     * 刷新玩家当前打开的菜单
     * 当检测到本地数据变化时自动调用
     */
    fun refreshPlayerMenu(player: Player) {
        try {
            val inventory = player.openInventory.topInventory
            val title = try {
                // 尝试获取标题，如果失败则使用默认值
                val view = player.openInventory
                view.title.toString()
            } catch (e: Exception) {
                "精灵图鉴"
            }

            when {
                title.contains("精灵图鉴") && !title.contains("世代") -> {
                    // 刷新主菜单
                    playerMainInventories.remove(player)

                    // 重新打开主菜单
                    openMainMenu(player)
                }

                title.contains("世代") -> {
                    // 刷新世代菜单
                    val generationId = extractGenerationIdFromTitle(title)
                    if (generationId != null) {
                        val playerId = player.uniqueId.toString()
                        val cacheKey = "${playerId}_${generationId}"
                        playerGenerationInventories.remove(cacheKey)

                        // 重新打开世代菜单
                        val generation = plugin.generationManager.getGeneration(generationId)
                        if (generation != null) {
                            openGenerationMenu(player, generation, 0)
                        }
                    }
                }

                title.contains("统计") -> {
                    // 刷新统计菜单（如果有的话）
                    // 这里可以添加统计菜单的刷新逻辑
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("刷新玩家菜单失败 (${player.name}): ${e.message}")
        }
    }

    /**
     * 从菜单标题中提取世代ID
     */
    private fun extractGenerationIdFromTitle(title: String): String? {
        return try {
            // 假设标题格式为 "第X世代 - 精灵图鉴"
            val regex = "第(\\d+)世代".toRegex()
            val matchResult = regex.find(title)
            matchResult?.groupValues?.get(1)?.let { "gen$it" }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 检测到玩家数据更新后，自动异步更新所有打开的界面
     */
    fun onPlayerDataUpdated(player: Player) {
        // 数据已更新到本地文件，直接刷新界面

        // 检查玩家当前打开的界面类型
        val currentGui = openGuis[player]

        when {
            currentGui == "main" -> {
                // 玩家在主界面，异步更新主界面数据
                updateMainGuiDataAsync(player)
            }
            currentGui?.startsWith("generation:") == true -> {
                // 玩家在世代界面，异步更新世代界面数据
                val generationId = currentGui.substring("generation:".length)
                val currentPage = playerPages[player] ?: 0
                updateGenerationGuiDataAsync(player, generationId, currentPage)
            }
        }
    }

    /**
     * 异步更新主界面数据（不重新打开界面）
     */
    private fun updateMainGuiDataAsync(player: Player) {
        val inventory = playerMainInventories[player] ?: return

        // 检查玩家是否还在主界面
        if (openGuis[player] != "main" || player.openInventory.topInventory != inventory) {
            return
        }

        // 直接从本地数据文件读取，快速响应
        try {
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            // 直接更新界面内容（世代按钮现在使用实时计算）
            val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
            addGenerationButtonsWithData(inventory, player, emptyProgress)
            addStatsButtonWithData(inventory, player, playerData)
            addOverallProgressDisplayWithData(inventory, player, allProgress)

            if (plugin.config.enableDebug) {
                player.sendMessage("§7[调试] 主菜单数据已从本地文件更新")
            }
        } catch (e: Exception) {
            // 如果本地文件读取失败，异步重新检测
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败: ${e.message}")

            plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                try {
                    // 使用新的CheckPokemon方法重新检测并保存数据
                    val success = plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

                    if (success) {
                        // 回到主线程更新界面
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (openGuis[player] == "main" && player.openInventory.topInventory == inventory) {
                                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

                                val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
                                addGenerationButtonsWithData(inventory, player, emptyProgress)
                                addStatsButtonWithData(inventory, player, playerData)
                                addOverallProgressDisplayWithData(inventory, player, allProgress)

                                player.sendMessage("§a主菜单数据已重新检测并更新！")
                            }
                        })
                    } else {
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (openGuis[player] == "main") {
                                player.sendMessage("§c无法检测精灵数据，请确保Cobblemon正常运行")
                            }
                        })
                    }
                } catch (e2: Exception) {
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        if (openGuis[player] == "main") {
                            player.sendMessage("§c检测精灵数据时发生错误: ${e2.message}")
                        }
                    })
                }
            })
        }
    }

    /**
     * 异步更新世代界面数据（不重新打开界面）
     */
    private fun updateGenerationGuiDataAsync(player: Player, generationId: String, page: Int) {
        val generation = plugin.generationManager.getGeneration(generationId) ?: return
        val cacheKey = "${player.uniqueId}:$generationId:$page"
        val inventory = playerGenerationInventories[cacheKey] ?: return

        // 检查玩家是否还在世代界面
        if (openGuis[player] != "generation:$generationId" || player.openInventory.topInventory != inventory) {
            return
        }

        // 直接从本地数据文件读取，快速响应
        try {
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)

            // 直接更新界面内容
            addGenerationPokemonWithData(inventory, player, generation, playerData, page)
            addProgressInfoWithData(inventory, player, generationId, playerData)
            addPaginationButtons(inventory, player, generationId, page)

            if (plugin.config.enableDebug) {
                player.sendMessage("§7[调试] ${generation.displayName} 数据已从本地文件更新")
            }
        } catch (e: Exception) {
            // 如果本地文件读取失败，异步重新检测
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败: ${e.message}")

            plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                try {
                    // 使用新的CheckPokemon方法重新检测并保存数据
                    val success = plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

                    if (success) {
                        // 回到主线程更新界面
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (openGuis[player] == "generation:$generationId" && player.openInventory.topInventory == inventory) {
                                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)

                                addGenerationPokemonWithData(inventory, player, generation, playerData, page)
                                addProgressInfoWithData(inventory, player, generationId, playerData)
                                addPaginationButtons(inventory, player, generationId, page)

                                player.sendMessage("§a${generation.displayName} 数据已重新检测并更新！")
                            }
                        })
                    } else {
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (openGuis[player] == "generation:$generationId") {
                                player.sendMessage("§c无法检测精灵数据，请确保Cobblemon正常运行")
                            }
                        })
                    }
                } catch (e2: Exception) {
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        if (openGuis[player] == "generation:$generationId") {
                            player.sendMessage("§c检测精灵数据时发生错误: ${e2.message}")
                        }
                    })
                }
            })
        }
    }

    /**
     * 手动触发数据更新检测（当玩家执行特定操作时调用）
     */
    fun triggerDataUpdateCheck(player: Player) {
        // 使用新的CheckPokemon方法异步检测并保存数据，然后通知界面更新
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

            // 回到主线程通知界面更新
            plugin.server.scheduler.runTask(plugin, Runnable {
                onPlayerDataUpdated(player)
            })
        })
    }



    /**
     * 打开主图鉴界面（已弃用，请使用openMainGuiAsync）
     */
    @Deprecated("使用openMainGuiAsync以避免主线程阻塞", ReplaceWith("openMainGuiAsync(player)"))
    fun openMainGui(player: Player) {
        // 直接调用异步版本，避免主线程阻塞
        openMainGuiAsync(player)
    }

    /**
     * 打开主菜单 - 兼容性方法
     */
    fun openMainMenu(player: Player) {
        openMainGuiAsync(player)
    }

    /**
     * 异步打开主图鉴界面 - 优化版本，集成预加载管理器和异步GUI管理器
     */
    fun openMainGuiAsync(player: Player) {
        // 不使用预创建的菜单，确保每次都显示最新数据
        // 注释掉预创建菜单的使用，强制每次重新创建以确保数据最新
        /*
        val preCreatedInventory = plugin.asyncGuiManager.getPlayerMainInventory(player)

        if (preCreatedInventory != null) {
            // 使用预创建的菜单，立即打开
            player.openInventory(preCreatedInventory)
            openGuis[player] = "main"

            if (plugin.config.enableDebug) {
                player.sendMessage("§7[调试] 使用预创建的主菜单")
            }
            return
        }
        */

        // 如果预创建菜单不可用，使用传统方式但优化性能
        val inventory = getOrCreateMainInventoryOptimized(player)

        player.openInventory(inventory)
        openGuis[player] = "main"

        // 每次打开菜单都强制重新检测最新数据，确保显示是最新的
        try {
            // 强制重新检测并保存最新数据到本地文件
            val latestPlayerData = plugin.pokemonDetector.detectAndSavePlayerPokemon(player)
                ?: plugin.pokemonDetector.getPlayerPokemon(player)

            // 重新计算所有世代进度
            val latestAllProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

            // 使用最新数据更新所有界面内容（不使用数据缓存，只缓存按钮样式）
            val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
            addGenerationButtonsWithData(inventory, player, emptyProgress)
            addStatsButtonWithData(inventory, player, latestPlayerData)
            addOverallProgressDisplayWithData(inventory, player, latestAllProgress)

            if (plugin.config.enableDebug) {
                player.sendMessage("§7[调试] 主菜单已强制更新最新数据")
                plugin.logger.info("主菜单强制更新 - 玩家 ${player.name} 总精灵数: ${latestPlayerData.partyPokemon.size + latestPlayerData.pcPokemon.size}")
            }
        } catch (e: Exception) {
            // 如果本地文件读取失败，异步重新检测
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败: ${e.message}")

            plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                try {
                    // 使用新的CheckPokemon方法重新检测并保存数据
                    val success = plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

                    if (success) {
                        // 回到主线程更新界面
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (openGuis[player] == "main" && player.openInventory.topInventory.size == 54) {
                                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                                val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)

                                val emptyProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
                                addGenerationButtonsWithData(inventory, player, emptyProgress)
                                addStatsButtonWithData(inventory, player, playerData)
                                addOverallProgressDisplayWithData(inventory, player, allProgress)

                                player.sendMessage("§a主菜单数据已重新检测并加载！")
                            }
                        })
                    } else {
                        plugin.server.scheduler.runTask(plugin, Runnable {
                            if (openGuis[player] == "main") {
                                player.closeInventory()
                                openGuis.remove(player)
                                player.sendMessage("§c图鉴数据加载失败，请稍后重试")
                            }
                        })
                    }
                } catch (e2: Exception) {
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        if (openGuis[player] == "main") {
                            player.closeInventory()
                            openGuis.remove(player)
                            player.sendMessage("§c图鉴数据加载失败，请稍后重试")
                            plugin.logger.warning("异步加载图鉴数据失败: ${e2.message}")
                        }
                    })
                }
            })
        }
    }

    /**
     * 打开世代菜单 - 兼容性方法
     */
    fun openGenerationMenu(player: Player, generation: cn.acebrand.acedex.generation.Generation, page: Int = 0) {
        openGenerationGui(player, generation.id, page)
    }

    /**
     * 打开世代详情界面
     */
    fun openGenerationGui(player: Player, generationId: String, page: Int = 0) {
        val generation = plugin.generationManager.getGeneration(generationId)
        if (generation == null) {
            player.sendMessage("§c未找到指定的世代！")
            return
        }

        // 验证页码有效性
        val pokemonList = generation.getAllPokemon().entries.toList()
        val itemsPerPage = 28
        val maxPage = if (pokemonList.isEmpty()) 0 else (pokemonList.size - 1) / itemsPerPage
        val validPage = page.coerceIn(0, maxPage)

        // 设置玩家当前页码
        playerPages[player] = validPage

        // 使用配置的标题
        val title = plugin.config.getProcessedGenerationTitle(player, generation)
        val inventory = Bukkit.createInventory(null, 54, title)

        // 设置背景
        fillBackground(inventory)

        // 添加世代精灵（分页）
        addGenerationPokemon(inventory, player, generation)

        // 添加进度信息
        addProgressInfo(inventory, player, generationId)

        // 添加下一页按钮
        addPaginationButtons(inventory, player, generationId, validPage)

        player.openInventory(inventory)
        openGuis[player] = "generation:$generationId"
    }

    /**
     * 异步打开世代详情界面 - 优化版本，减少延迟
     */
    fun openGenerationGuiAsync(player: Player, generationId: String, page: Int = 0) {
        val generation = plugin.generationManager.getGeneration(generationId)
        if (generation == null) {
            player.sendMessage("§c未找到指定的世代！")
            return
        }

        // 验证页码有效性
        val pokemonList = generation.getAllPokemon().entries.toList()
        val itemsPerPage = 28
        val maxPage = if (pokemonList.isEmpty()) 0 else (pokemonList.size - 1) / itemsPerPage
        val validPage = page.coerceIn(0, maxPage)

        // 设置玩家当前页码
        playerPages[player] = validPage

        // 获取或创建缓存的世代菜单
        val inventory = getOrCreateGenerationInventory(player, generationId, validPage)

        // 立即打开界面
        player.openInventory(inventory)
        openGuis[player] = "generation:$generationId"

        // 异步加载数据，避免阻塞界面打开
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            try {
                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)

                // 回到主线程更新界面
                plugin.server.scheduler.runTask(plugin, Runnable {
                    if (openGuis[player] == "generation:$generationId" && player.openInventory.topInventory == inventory) {
                        addGenerationPokemonWithData(inventory, player, generation, playerData, page)
                        addProgressInfoWithData(inventory, player, generationId, playerData)
                        addPaginationButtons(inventory, player, generationId, page)

                        if (plugin.config.enableDebug) {
                            player.sendMessage("§7[调试] ${generation.displayName} 已从本地文件加载数据")
                        }
                    }
                })
            } catch (e: Exception) {
                // 如果本地文件读取失败，重新检测
                plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败: ${e.message}")

                val success = plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)

                plugin.server.scheduler.runTask(plugin, Runnable {
                    if (openGuis[player] == "generation:$generationId" && player.openInventory.topInventory == inventory) {
                        if (success) {
                            try {
                                val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
                                addGenerationPokemonWithData(inventory, player, generation, playerData, page)
                                addProgressInfoWithData(inventory, player, generationId, playerData)
                                addPaginationButtons(inventory, player, generationId, page)
                                player.sendMessage("§a${generation.displayName} 数据已重新检测并加载！")
                            } catch (e2: Exception) {
                                player.sendMessage("§c重新检测后仍无法读取数据")
                            }
                        } else {
                            player.sendMessage("§c无法检测精灵数据，请确保Cobblemon正常运行")
                        }
                    }
                })
            }
        })
    }
    
    /**
     * 填充背景 - 使用缓存优化
     */
    private fun fillBackground(inventory: Inventory) {
        // 优先使用缓存的装饰物品
        val glass = plugin.guiButtonCacheManager.getCachedDecorationItem("decoration")
            ?: plugin.config.createDecorationItem()

        // 填充边框
        for (i in 0..8) inventory.setItem(i, glass.clone())
        for (i in 45..53) inventory.setItem(i, glass.clone())
        for (i in 9..44 step 9) inventory.setItem(i, glass.clone())
        for (i in 17..44 step 9) inventory.setItem(i, glass.clone())
    }
    
    /**
     * 添加世代选择按钮 - 使用实时计算数据确保与世代菜单同步
     */
    private fun addGenerationButtons(inventory: Inventory, player: Player) {
        try {
            // 使用空的AllGenerationsProgress，因为我们现在使用实时计算
            val allProgress = AllGenerationsProgress(0, 0, 0, 0, 0, emptyMap())
            addGenerationButtonsWithData(inventory, player, allProgress)
        } catch (e: Exception) {
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败，使用默认显示: ${e.message}")

            // 降级到基础显示 - 使用配置的位置
            val generations = plugin.generationManager.getAllGenerations().toList()

            generations.forEach { generation ->
                val slot = plugin.config.getGenerationSlot(generation.id)
                val item = createGenerationItem(generation, player)
                inventory.setItem(slot, item)
            }
        }
    }
    
    /**
     * 创建世代按钮物品 - 从本地文件读取数据
     */
    private fun createGenerationItem(generation: cn.acebrand.acedex.generation.Generation, player: Player): ItemStack {
        try {
            // 从本地文件获取玩家进度
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)
            val isCompleted = progress.percentage >= 100
            val hasClaimedReward = plugin.rewardManager.hasClaimedGenerationReward(player, generation.id)

            // 使用精灵球物品创建器创建物品
            val item = pokeBallItemCreator.createGenerationPokeBallItem(
                generationId = generation.id,
                hasCaught = progress.caught > 0,
                isCompleted = isCompleted && hasClaimedReward
            )

            return createGenerationItemWithProgress(generation, player, progress)
        } catch (e: Exception) {
            // 降级到基础显示
            return pokeBallItemCreator.createGenerationPokeBallItem(
                generationId = generation.id,
                hasCaught = false,
                isCompleted = false
            )
        }
    }

    /**
     * 使用预计算进度创建世代按钮物品 - 避免重复计算，使用缓存优化
     */
    private fun createGenerationItemWithProgress(generation: cn.acebrand.acedex.generation.Generation, player: Player, progress: GenerationProgress): ItemStack {
        val isCompleted = progress.percentage >= 100
        val hasClaimedReward = plugin.rewardManager.hasClaimedGenerationReward(player, generation.id)
        val hasCaught = progress.caught > 0
        val finalCompleted = isCompleted && hasClaimedReward

        // 优先使用缓存的世代按钮
        val item = plugin.guiButtonCacheManager.getCachedGenerationButton(
            generationId = generation.id,
            hasCaught = hasCaught,
            isCompleted = finalCompleted
        ) ?: pokeBallItemCreator.createGenerationPokeBallItem(
            generationId = generation.id,
            hasCaught = hasCaught,
            isCompleted = finalCompleted
        )

        val meta = item.itemMeta

        // 设置显示名称
        val displayName = when {
            isCompleted && hasClaimedReward -> "${generation.color}§l${generation.name} §a✓"
            isCompleted -> "${generation.color}§l${generation.name} §6★"
            else -> "${generation.color}§l${generation.name}"
        }
        meta?.setDisplayName(displayName)

        // 设置描述 - 添加精灵球主题信息
        val lore = mutableListOf<String>()
        lore.add("§7地区: §f${generation.region}")
        lore.add("§7图鉴范围: §f${generation.pokemonRange.first}-${generation.pokemonRange.last}")

        // 使用精灵球物品创建器获取主题信息
        val ballThemeName = pokeBallItemCreator.getPokeBallThemeName(generation.id)
        val ballThemeColor = pokeBallItemCreator.getPokeBallThemeColor(generation.id)
        lore.add("§7主题: $ballThemeColor$ballThemeName")
        lore.add("")
        lore.add("§e你的进度:")
        lore.add("§7已收集: §a${progress.caught}§7/§f${progress.total}")

        // 使用与世代菜单内部完全相同的进度条显示方式
        lore.add("§7完成度: ${progress.getProgressBar()}")
        lore.add("")

        when {
            isCompleted && hasClaimedReward -> {
                lore.add("§a§l已完成并领取奖励！")
                lore.add("§7✦ 大师级收集家 ✦")
                lore.add("")
                lore.add("§e左键: §7查看精灵详情")
            }
            isCompleted -> {
                lore.add("§6§l恭喜完成！可以领取奖励")
                lore.add("§7◇ 准备成为大师 ◇")
                lore.add("")

                // 显示进度奖励状态
                addProgressRewardInfoWithPlayer(lore, generation.id, progress.percentage, player)

                lore.add("§e100%完成奖励:")
                // 从配置文件获取奖励描述
                val rewardDescriptions = plugin.config.getGenerationRewardDescriptions(generation.id)
                rewardDescriptions.forEach { description ->
                    lore.add(description)
                }
                lore.add("")
                lore.add("§e左键: §7查看精灵详情")
                lore.add("§a右键: §7领取完成奖励")
            }
            else -> {
                val remaining = progress.total - progress.caught
                lore.add("§7还需收集 §c$remaining §7只精灵")
                lore.add("§7完成后可在主菜单领取奖励")
                lore.add("")

                // 显示进度奖励信息
                addProgressRewardInfoWithPlayer(lore, generation.id, progress.percentage, player)

                lore.add("§e左键: §7查看精灵详情")
            }
        }

        meta?.lore = lore

        // 添加附魔效果（完成但未领取）
        if (isCompleted && !hasClaimedReward) {
            meta?.addEnchant(org.bukkit.enchantments.Enchantment.UNBREAKING, 1, true)
            meta?.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS)
        }

        item.itemMeta = meta
        return item
    }

    /**
     * 创建详细的进度条
     */
    private fun createDetailedProgressBar(percentage: Double): String {
        val barLength = 20
        val filledLength = (percentage / 100.0 * barLength).toInt()
        val emptyLength = barLength - filledLength

        return "§a" + "█".repeat(filledLength) + "§7" + "█".repeat(emptyLength)
    }

    /**
     * 添加统计按钮 - 从本地文件读取数据
     */
    private fun addStatsButton(inventory: Inventory, player: Player) {
        try {
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)
            addStatsButtonWithData(inventory, player, playerData)
        } catch (e: Exception) {
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败，使用默认显示: ${e.message}")

            // 降级到基础显示
            val item = plugin.config.createStatsButtonItem()
            val meta = item.itemMeta

            if (meta != null) {
                meta.setDisplayName("§6§l✦ 个人统计 ✦")
                meta.lore = listOf(
                    "§7═══════════════════",
                    "§c数据加载失败",
                    "§7请稍后重试",
                    "§7═══════════════════",
                    "",
                    "§e点击重新加载数据"
                )
                item.itemMeta = meta
            }

            inventory.setItem(plugin.config.statsButtonSlot, item)
        }
    }

    /**
     * 添加总体进度显示 - 从本地文件读取数据
     */
    private fun addOverallProgressDisplay(inventory: Inventory, player: Player) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            addOverallProgressDisplayWithData(inventory, player, allProgress)
        } catch (e: Exception) {
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败，使用默认显示: ${e.message}")

            // 降级到基础显示 - 使用缓存优化
            val item = plugin.guiButtonCacheManager.getCachedDecorationItem("decoration")
                ?: plugin.config.createDecorationItem()
            val meta = item.itemMeta

            if (meta != null) {
                meta.setDisplayName("§c§l✦ 数据加载失败 ✦")
                meta.lore = listOf(
                    "§7无法读取本地数据文件",
                    "§7请稍后重试",
                    "",
                    "§e点击重新加载数据"
                )
                item.itemMeta = meta
            }

            inventory.setItem(plugin.config.progressButtonSlot, item)
        }
    }
    
    /**
     * 添加装饰元素
     */
    private fun addDecorations(inventory: Inventory) {
        // 注意：不要在slot 4放置装饰物品，因为那里是总体进度显示位置
        // 可以在其他位置添加装饰元素

        // 添加侧边装饰
        val decoration = ItemStack(Material.NETHER_STAR)
        val decorationMeta = decoration.itemMeta
        decorationMeta?.setDisplayName("§e§l✧")
        decoration.itemMeta = decorationMeta
        inventory.setItem(10, decoration) // 左侧装饰
        inventory.setItem(16, decoration) // 右侧装饰
        inventory.setItem(28, decoration) // 左侧装饰
        inventory.setItem(34, decoration) // 右侧装饰
    }

    /**
     * 添加关闭按钮 - 使用缓存优化
     */
    private fun addCloseButton(inventory: Inventory) {
        // 优先使用缓存的按钮
        val item = plugin.guiButtonCacheManager.getCachedButton("close_button")
            ?: plugin.config.createCloseButtonItem()

        val meta = item.itemMeta
        if (meta != null) {
            // 保留原有的CustomModelData，只更新显示名称
            meta.setDisplayName("§c关闭")
            item.itemMeta = meta
        }
        inventory.setItem(plugin.config.closeButtonSlot, item)
    }

    /**
     * 添加加载提示 - 已禁用，保持界面简洁
     */
    private fun addLoadingIndicator(inventory: Inventory) {
        // 不显示任何加载提示，保持界面简洁
    }

    /**
     * 清除加载提示 - 已禁用，因为不再显示加载提示
     */
    private fun clearLoadingIndicator(inventory: Inventory) {
        // 不需要清除加载提示
    }

    /**
     * 使用实时计算数据添加世代按钮 - 与世代菜单内部保持完全同步
     */
    private fun addGenerationButtonsWithData(inventory: Inventory, player: Player, allProgress: AllGenerationsProgress) {
        val generations = plugin.generationManager.getAllGenerations().toList()

        // 强制重新检测玩家数据，确保数据是最新的
        val playerData = try {
            plugin.pokemonDetector.detectAndSavePlayerPokemon(player) ?: plugin.pokemonDetector.getPlayerPokemon(player)
        } catch (e: Exception) {
            plugin.logger.warning("强制检测玩家数据失败，使用缓存数据: ${e.message}")
            plugin.pokemonDetector.getPlayerPokemon(player)
        }

        if (plugin.config.enableDebug) {
            plugin.logger.info("主菜单世代按钮 - 玩家 ${player.name} 总精灵数: ${playerData.partyPokemon.size + playerData.pcPokemon.size}")
        }

        generations.forEach { generation ->
            val slot = plugin.config.getGenerationSlot(generation.id)

            // 使用与世代菜单内部相同的实时计算方式
            val progress = calculateGenerationProgressRealtime(generation, playerData)

            if (plugin.config.enableDebug) {
                plugin.logger.info("主菜单 - 世代 ${generation.id}: ${progress.caught}/${progress.total}")
            }

            // 尝试使用优化的创建方法，如果失败则使用原有方法
            val item = try {
                val optimizedItem = createGenerationButtonOptimized(generation, progress, player)
                if (plugin.config.enableDebug) {
                    plugin.logger.info("主菜单使用优化方法创建 ${generation.id} 按钮: ${progress.caught}/${progress.total}")
                }
                optimizedItem
            } catch (e: Exception) {
                if (plugin.config.enableDebug) {
                    plugin.logger.info("主菜单优化方法失败，使用原有方法创建 ${generation.id} 按钮: ${progress.caught}/${progress.total}")
                }
                createGenerationItemWithProgress(generation, player, progress)
            }
            inventory.setItem(slot, item)
        }
    }

    /**
     * 实时计算世代进度 - 与世代菜单内部使用相同的计算逻辑
     */
    private fun calculateGenerationProgressRealtime(generation: cn.acebrand.acedex.generation.Generation, playerData: PlayerPokemonData): GenerationProgress {
        val allPlayerPokemon = playerData.partyPokemon + playerData.pcPokemon
        val totalInGeneration = generation.getTotalPokemon()

        // 使用distinctBy去除重复精灵，只统计不同种类的精灵（与世代菜单内部逻辑完全一致）
        val uniquePokemonInGeneration = allPlayerPokemon
            .filter { pokemon ->
                // 使用标准化名称匹配
                val normalizedName = pokemon.name.lowercase().replace(Regex("[^a-z0-9]+"), "")
                generation.containsPokemonByShowdownId(normalizedName)
            }
            .distinctBy { pokemon ->
                pokemon.name.lowercase().replace(Regex("[^a-z0-9]+"), "")
            }

        val caughtInGeneration = uniquePokemonInGeneration.size
        val percentage = if (totalInGeneration > 0) {
            (caughtInGeneration * 100) / totalInGeneration
        } else 0

        return GenerationProgress(totalInGeneration, caughtInGeneration, percentage)
    }

    /**
     * 使用预计算数据添加统计按钮 - 使用缓存优化
     */
    private fun addStatsButtonWithData(inventory: Inventory, player: Player, playerData: PlayerPokemonData) {
        // 优先使用缓存的按钮
        val item = plugin.guiButtonCacheManager.getCachedButton("stats_button")
            ?: plugin.config.createStatsButtonItem()

        val meta = item.itemMeta

        if (meta != null) {
            // 保留原有的CustomModelData，只更新显示名称和描述
            meta.setDisplayName("§6§l✦ 个人统计 ✦")
            meta.lore = listOf(
                "§7═══════════════════",
                "§7总收集数: §a${playerData.totalCaught}",
                "§7队伍精灵: §b${playerData.partyPokemon.size}",
                "§7PC精灵: §d${playerData.pcPokemon.size}",
                "§7═══════════════════",
                "",
                "§e点击查看详细统计"
            )
            item.itemMeta = meta
        }

        inventory.setItem(plugin.config.statsButtonSlot, item)
    }

    /**
     * 使用预计算数据添加总体进度显示 - 使用配置的位置和材质，优化缓存
     */
    private fun addOverallProgressDisplayWithData(inventory: Inventory, player: Player, allProgress: AllGenerationsProgress) {
        val canClaimReward = plugin.rewardManager.canClaimAllGenerationsReward(player)
        val hasClaimedReward = plugin.rewardManager.hasClaimedAllGenerationsReward(player)

        // 优先使用缓存的进度按钮
        val progressPercentage = allProgress.overallPercentage.toDouble()
        val item = plugin.guiButtonCacheManager.getCachedProgressButtonByPercentage(progressPercentage)
            ?: plugin.config.createProgressButtonItem(progressPercentage)

        val meta = item.itemMeta

        if (meta != null) {
            // 设置显示名称
            val displayName = if (hasClaimedReward) {
                "§6§l✦ 全世界收集进度 ✦ §a(已完成)"
            } else if (canClaimReward) {
                "§6§l✦ 全世界收集进度 ✦ §e(可领取奖励)"
            } else {
                "§6§l✦ 全世界收集进度 ✦"
            }
            meta.setDisplayName(displayName)

            // 设置描述
            val lore = mutableListOf<String>()
            lore.add("§7═══════════════════")
            lore.add("§7总进度: §a${String.format("%.1f", progressPercentage)}%")
            lore.add("§7总收集: §b${allProgress.caughtPokemon}/${allProgress.totalPokemon}")
            lore.add("§7═══════════════════")
            lore.add("")

            // 添加各世代进度
            allProgress.generationProgresses.forEach { (generationId, genProgress) ->
                val generation = plugin.generationManager.getGeneration(generationId)
                if (generation != null) {
                    val statusIcon = if (genProgress.percentage >= 100) "§a✓" else "§7○"
                    lore.add("$statusIcon §f${generation.displayName}: §e${genProgress.percentage}% §7(${genProgress.caught}/${genProgress.total})")
                }
            }

            lore.add("")
            if (hasClaimedReward) {
                lore.add("§a§l✓ 已领取全部奖励")
                lore.add("§7◆ 精灵大师 ◆")
            } else if (canClaimReward) {
                lore.add("§6§l可领取精灵大师奖励:")
                // 从配置文件获取全世代奖励描述
                val allGenRewardDescriptions = plugin.config.getAllGenerationsRewardDescriptions()
                allGenRewardDescriptions.forEach { description ->
                    lore.add(description)
                }
                lore.add("")
                lore.add("§e§l点击领取全世界完成奖励！")
            } else {
                lore.add("§7完成所有世代收集来解锁奖励:")
                // 显示奖励预览
                val allGenRewardDescriptions = plugin.config.getAllGenerationsRewardDescriptions()
                allGenRewardDescriptions.forEach { description ->
                    lore.add(description)
                }

                // 显示全世代进度奖励状态
                lore.add("")
                addOverallProgressRewardStatus(lore, player, allProgress.overallPercentage)

                lore.add("§e左键: §7领取全世代完成奖励")
                lore.add("§a右键: §7查看详细进度")
            }

            meta.lore = lore
            item.itemMeta = meta
        }

        inventory.setItem(plugin.config.progressButtonSlot, item)
    }








    
    /**
     * 添加返回按钮 - 使用缓存优化
     */
    private fun addBackButton(inventory: Inventory) {
        // 优先使用缓存的导航按钮
        val item = plugin.guiButtonCacheManager.getCachedNavigationButton("back_button")
            ?: plugin.config.createBackToMainMenuItem()

        val meta = item.itemMeta
        if (meta != null) {
            // 保留原有的CustomModelData，只更新显示名称
            meta.setDisplayName("§7← 返回主菜单")
            item.itemMeta = meta
        }
        inventory.setItem(45, item)
    }
    
    /**
     * 添加世代精灵（支持分页）
     */
    private fun addGenerationPokemon(inventory: Inventory, player: Player, generation: cn.acebrand.acedex.generation.Generation) {
        val pokemonList = generation.getAllPokemon().entries.toList()
        val currentPage = playerPages[player] ?: 0
        val itemsPerPage = 28

        // 计算当前页的精灵范围
        val startIndex = currentPage * itemsPerPage
        val endIndex = minOf(startIndex + itemsPerPage, pokemonList.size)

        // 检查索引有效性，避免越界错误
        if (startIndex >= pokemonList.size) {
            plugin.logger.warning("创建玩家 ${player.name} 的世代菜单 ${generation.id} 失败: 页码超出范围 (startIndex: $startIndex, pokemonList.size: ${pokemonList.size})")
            return
        }

        // 获取当前页的精灵
        val currentPagePokemon = if (startIndex < endIndex) {
            pokemonList.subList(startIndex, endIndex)
        } else {
            emptyList()
        }

        // 设置精灵物品的起始位置
        val startSlot = 10
        var currentSlot = startSlot

        currentPagePokemon.forEach { (name, dex) ->
            // 跳过边框位置
            if (currentSlot % 9 == 8) currentSlot += 2

            val hasCaught = plugin.pokemonDetector.hasPlayerCaughtPokemon(player, name)
            val item = pokemonItemCreator.createPokemonItem(name, dex, hasCaught)

            inventory.setItem(currentSlot, item)
            currentSlot++
        }
    }

    /**
     * 使用预计算数据添加世代精灵（支持分页）
     */
    private fun addGenerationPokemonWithData(inventory: Inventory, player: Player, generation: cn.acebrand.acedex.generation.Generation, playerData: PlayerPokemonData, page: Int) {
        val pokemonList = generation.getAllPokemon().entries.toList()
        val itemsPerPage = 28

        // 计算当前页的精灵范围
        val startIndex = page * itemsPerPage
        val endIndex = minOf(startIndex + itemsPerPage, pokemonList.size)

        // 检查索引有效性，避免越界错误
        if (startIndex >= pokemonList.size) {
            plugin.logger.warning("创建玩家 ${player.name} 的世代菜单 ${generation.id} 失败: 页码超出范围 (startIndex: $startIndex, pokemonList.size: ${pokemonList.size})")
            return
        }

        // 获取当前页的精灵
        val currentPagePokemon = if (startIndex < endIndex) {
            pokemonList.subList(startIndex, endIndex)
        } else {
            emptyList()
        }

        // 预处理玩家拥有的精灵数据，避免重复查询
        val allPlayerPokemon = playerData.partyPokemon + playerData.pcPokemon
        val caughtPokemonNames = allPlayerPokemon.map {
            it.name.lowercase().replace(Regex("[^a-z0-9]+"), "")
        }.toSet()

        // 设置精灵物品的起始位置
        val startSlot = 10
        var currentSlot = startSlot

        // 批量创建精灵物品，减少单个创建的开销
        val pokemonItems = mutableMapOf<Int, ItemStack>()

        currentPagePokemon.forEach { (name, dex) ->
            // 跳过边框位置
            if (currentSlot % 9 == 8) currentSlot += 2

            // 使用预处理的数据检查是否已捕获
            val normalizedName = name.lowercase().replace(Regex("[^a-z0-9]+"), "")
            val hasCaught = caughtPokemonNames.contains(normalizedName)

            try {
                val item = pokemonItemCreator.createPokemonItem(name, dex, hasCaught)
                pokemonItems[currentSlot] = item
            } catch (e: Exception) {
                // 如果创建失败，使用默认物品
                plugin.logger.warning("创建精灵物品失败 ($name): ${e.message}")
                val fallbackItem = ItemStack(org.bukkit.Material.PAPER)
                val meta = fallbackItem.itemMeta
                meta?.setDisplayName("§c$name (加载失败)")
                fallbackItem.itemMeta = meta
                pokemonItems[currentSlot] = fallbackItem
            }

            currentSlot++
        }

        // 批量设置物品到界面
        pokemonItems.forEach { (slot, item) ->
            inventory.setItem(slot, item)
        }
    }

    /**
     * 添加进度信息 - 强制重新检测数据确保与主菜单同步
     */
    private fun addProgressInfo(inventory: Inventory, player: Player, generationId: String) {
        try {
            // 强制重新检测玩家数据，确保数据是最新的
            val playerData = try {
                plugin.pokemonDetector.detectAndSavePlayerPokemon(player) ?: plugin.pokemonDetector.getPlayerPokemon(player)
            } catch (e: Exception) {
                plugin.logger.warning("强制检测玩家数据失败，使用缓存数据: ${e.message}")
                plugin.pokemonDetector.getPlayerPokemon(player)
            }

            addProgressInfoWithData(inventory, player, generationId, playerData)
        } catch (e: Exception) {
            plugin.logger.warning("读取玩家 ${player.name} 的本地数据失败: ${e.message}")

            // 降级到基础显示
            val generation = plugin.generationManager.getGeneration(generationId)
            if (generation != null) {
                val item = plugin.config.createDecorationItem()
                val meta = item.itemMeta

                if (meta != null) {
                    meta.setDisplayName("§c§l进度信息")
                    meta.lore = listOf(
                        "§7数据加载失败",
                        "§7请稍后重试"
                    )
                    item.itemMeta = meta
                }

                inventory.setItem(50, item)
            }
        }
    }

    /**
     * 使用预计算数据添加进度信息
     */
    private fun addProgressInfoWithData(inventory: Inventory, player: Player, generationId: String, playerData: PlayerPokemonData) {
        val generation = plugin.generationManager.getGeneration(generationId) ?: return

        // 直接从玩家数据计算进度，避免重复计算
        val allPlayerPokemon = playerData.partyPokemon + playerData.pcPokemon
        val totalInGeneration = generation.getTotalPokemon()

        // 使用distinctBy去除重复精灵，只统计不同种类的精灵
        val uniquePokemonInGeneration = allPlayerPokemon
            .filter { pokemon ->
                // 使用标准化名称匹配
                val normalizedName = pokemon.name.lowercase().replace(Regex("[^a-z0-9]+"), "")
                generation.containsPokemonByShowdownId(normalizedName)
            }
            .distinctBy { pokemon ->
                pokemon.name.lowercase().replace(Regex("[^a-z0-9]+"), "")
            }

        val caughtInGeneration = uniquePokemonInGeneration.size
        val percentage = if (totalInGeneration > 0) {
            (caughtInGeneration * 100) / totalInGeneration
        } else 0

        if (plugin.config.enableDebug) {
            plugin.logger.info("世代菜单 - 世代 ${generation.id}: ${caughtInGeneration}/${totalInGeneration}")
        }

        val progress = GenerationProgress(totalInGeneration, caughtInGeneration, percentage)
        addProgressInfoWithData(inventory, player, generationId, progress)
    }

    /**
     * 添加进度信息（使用已有数据）
     */
    private fun addProgressInfoWithData(inventory: Inventory, player: Player, generationId: String, progress: GenerationProgress) {
        val generation = plugin.generationManager.getGeneration(generationId) ?: return
        val isCompleted = progress.percentage >= 100
        val hasClaimedReward = plugin.rewardManager.hasClaimedGenerationReward(player, generationId)

        val item = plugin.config.createProgressInfoItem()
        val meta = item.itemMeta

        if (meta != null) {
            // 设置世代名称作为标题
            val displayName = when {
                isCompleted && hasClaimedReward -> "${generation.color}§l${generation.name} §a★"
                isCompleted -> "${generation.color}§l${generation.name} §6★"
                else -> "${generation.color}§l${generation.name}"
            }
            meta.setDisplayName(displayName)

            val lore = mutableListOf<String>()

            // 添加世代基本信息
            lore.add("§7地区: §f${generation.region}")
            lore.add("§7图鉴范围: §f${generation.pokemonRange.first}-${generation.pokemonRange.last}")

            // 使用精灵球物品创建器获取主题信息
            val ballThemeName = pokeBallItemCreator.getPokeBallThemeName(generation.id)
            val ballThemeColor = pokeBallItemCreator.getPokeBallThemeColor(generation.id)
            lore.add("§7主题: $ballThemeColor$ballThemeName")
            lore.add("")

            // 添加进度信息
            lore.add("§e你的进度:")
            lore.add("§7已收集: §a${progress.caught}§7/§f${progress.total}")
            lore.add("§7完成度: ${progress.getProgressBar()}")
            lore.add("")

            when {
                isCompleted && hasClaimedReward -> {
                    lore.add("§a§l已完成并领取奖励！")
                    lore.add("§7✦ 大师级收集家 ✦")
                }
                isCompleted -> {
                    lore.add("§6§l恭喜完成！可以领取奖励")
                    lore.add("§7✧ 准备成为大师 ✧")
                    lore.add("")
                    lore.add("§e完成奖励:")
                    // 从配置文件获取奖励描述
                    val rewardDescriptions = plugin.config.getGenerationRewardDescriptions(generation.id)
                    rewardDescriptions.forEach { description ->
                        lore.add(description)
                    }
                    lore.add("")
                    lore.add("§a右键: §7领取完成奖励")
                }
                else -> {
                    val remaining = progress.total - progress.caught
                    lore.add("§7还需收集 §c$remaining §7只精灵")
                    lore.add("§7完成后可在主菜单领取奖励")
                }
            }

            meta.lore = lore

            // 添加附魔效果（完成但未领取）
            if (isCompleted && !hasClaimedReward) {
                meta.addEnchant(org.bukkit.enchantments.Enchantment.LURE, 1, true)
                meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS)
            }

            item.itemMeta = meta
        }
        inventory.setItem(50, item)
    }

    /**
     * 添加分页按钮
     */
    private fun addPaginationButtons(inventory: Inventory, player: Player, generationId: String, currentPage: Int) {
        val generation = plugin.generationManager.getGeneration(generationId)
        if (generation != null) {
            val pokemonList = generation.getAllPokemon().entries.toList()
            val itemsPerPage = 28
            val totalPages = (pokemonList.size + itemsPerPage - 1) / itemsPerPage

            // 添加上一页按钮（slot45） - 使用缓存优化
            if (currentPage > 0) {
                val prevItem = plugin.guiButtonCacheManager.getCachedNavigationButton("previous_page")
                    ?: plugin.config.createPreviousPageItem()
                val prevMeta = prevItem.itemMeta
                if (prevMeta != null) {
                    // 保留原有的CustomModelData，只更新显示名称和描述
                    prevMeta.setDisplayName("§e← 上一页")
                    prevMeta.lore = listOf(
                        "§7当前页: §f${currentPage + 1}§7/§f$totalPages",
                        "§7点击查看上一页"
                    )
                    prevItem.itemMeta = prevMeta
                }
                inventory.setItem(45, prevItem)
            }

            // 添加下一页按钮（slot53） - 使用缓存优化
            if (currentPage < totalPages - 1) {
                val nextItem = plugin.guiButtonCacheManager.getCachedNavigationButton("next_page")
                    ?: plugin.config.createNextPageItem()
                val nextMeta = nextItem.itemMeta
                if (nextMeta != null) {
                    // 保留原有的CustomModelData，只更新显示名称和描述
                    nextMeta.setDisplayName("§e下一页 →")
                    nextMeta.lore = listOf(
                        "§7当前页: §f${currentPage + 1}§7/§f$totalPages",
                        "§7点击查看下一页"
                    )
                    nextItem.itemMeta = nextMeta
                }
                inventory.setItem(53, nextItem)
            }

            // 添加页码显示（slot48） - 使用缓存优化
            val pageItem = plugin.guiButtonCacheManager.getCachedNavigationButton("page_indicator")
                ?: plugin.config.createPageIndicatorItem()
            val pageMeta = pageItem.itemMeta
            if (pageMeta != null) {
                // 保留原有的CustomModelData，只更新显示名称和描述
                pageMeta.setDisplayName("§6第${currentPage + 1}页")
                pageMeta.lore = listOf(
                    "§7总页数: §f$totalPages",
                    "§7精灵总数: §f${pokemonList.size}"
                )
                pageItem.itemMeta = pageMeta
            }
            inventory.setItem(48, pageItem)

            // 添加返回按钮（slot49）
            val backItem = plugin.config.createBackToMainMenuItem()
            val backMeta = backItem.itemMeta
            if (backMeta != null) {
                // 保留原有的CustomModelData，只更新显示名称和描述
                backMeta.setDisplayName("§c← 返回主菜单")
                backMeta.lore = listOf("§7点击返回图鉴主界面")
                backItem.itemMeta = backMeta
            }
            inventory.setItem(49, backItem)
        }
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    fun onInventoryClick(event: InventoryClickEvent) {
        val player = event.whoClicked as? Player ?: return
        val guiType = openGuis[player]

        // 如果玩家没有打开普通菜单，直接返回，不处理任何点击
        if (guiType == null) {
            return
        }

        // 只处理普通菜单的点击事件
        if (!guiType.equals("main") && !guiType.startsWith("generation:")) {
            return
        }

        // 取消所有涉及GUI界面的操作，防止物品被拖拽到GUI中
        event.isCancelled = true

        // 只有点击GUI界面时才处理按钮逻辑
        if (event.clickedInventory == event.view.topInventory) {
            val clickedItem = event.currentItem ?: return
            val slot = event.slot

            when {
                guiType == "main" -> handleMainGuiClick(player, slot, clickedItem, event.click)
                guiType.startsWith("generation:") -> handleGenerationGuiClick(player, slot, clickedItem, guiType, event.click)
            }
        }
    }
    
    /**
     * 处理主界面点击
     */
    private fun handleMainGuiClick(player: Player, slot: Int, item: ItemStack, clickType: org.bukkit.event.inventory.ClickType) {
        when (slot) {
            4 -> {
                // 总体进度显示 - 奖励领取或显示详细进度
                // 统一的冷却检查，因为两种操作都可能显示进度信息
                if (isPlayerOnCooldown(player, "progress")) {
                    val remaining = getRemainingCooldown(player, "progress")
                    player.sendMessage("§c请等待 ${remaining} 秒后再操作！")
                    return
                }
                setPlayerCooldown(player, "progress")

                when (clickType) {
                    org.bukkit.event.inventory.ClickType.LEFT -> {
                        handleAllGenerationsRewardClaimWithoutCooldown(player)
                    }
                    else -> {
                        // 其他点击显示详细进度 - 使用异步加载避免延迟
                        // 播放查看进度音效
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.0f)
                        showAllGenerationsProgressAsync(player)
                    }
                }
            }
            plugin.config.statsButtonSlot -> {
                // 统计按钮 - 使用配置的位置
                if (isPlayerOnCooldown(player, "stats")) {
                    val remaining = getRemainingCooldown(player, "stats")
                    player.sendMessage("§c请等待 ${remaining} 秒后再查看统计信息！")
                    return
                }
                setPlayerCooldown(player, "stats")
                // 播放查看统计音效
                player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.0f)
                showPlayerStats(player)
            }
            plugin.config.closeButtonSlot -> {
                // 关闭按钮 - 使用配置的位置
                // 播放关闭音效
                player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.5f, 0.8f)
                player.closeInventory()
                openGuis.remove(player)
            }
            plugin.config.progressButtonSlot -> {
                // 全世界进度按钮 - 使用配置的位置
                if (isPlayerOnCooldown(player, "progress")) {
                    val remaining = getRemainingCooldown(player, "progress")
                    player.sendMessage("§c请等待 ${remaining} 秒后再查看进度信息！")
                    return
                }
                setPlayerCooldown(player, "progress")

                when (clickType) {
                    org.bukkit.event.inventory.ClickType.LEFT -> {
                        // 左键：查看详细进度
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.0f)
                        showAllGenerationsProgressAsync(player)
                    }
                    org.bukkit.event.inventory.ClickType.RIGHT -> {
                        // 右键：尝试领取全世界完成奖励
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.2f)
                        handleAllGenerationsRewardClaimWithoutCooldown(player)
                    }
                    else -> {
                        // 其他点击类型默认查看详细进度
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.0f)
                        showAllGenerationsProgressAsync(player)
                    }
                }
            }
            else -> {
                // 检查是否是世代按钮位置
                val generations = plugin.generationManager.getAllGenerations().toList()
                val generation = generations.find { plugin.config.getGenerationSlot(it.id) == slot }

                if (generation != null) {
                    when (clickType) {
                        org.bukkit.event.inventory.ClickType.LEFT -> {
                            // 左键：进入世代菜单
                            // 播放进入世代音效
                            player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.1f)
                            openGenerationGuiAsync(player, generation.id)
                        }
                        org.bukkit.event.inventory.ClickType.RIGHT -> {
                            // 右键：尝试领取奖励
                            handleGenerationRewardClaim(player, generation)
                        }
                        else -> {
                            // 其他点击类型默认进入世代菜单
                            // 播放进入世代音效
                            player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 1.1f)
                            openGenerationGuiAsync(player, generation.id)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 处理世代界面点击
     */
    private fun handleGenerationGuiClick(player: Player, slot: Int, item: ItemStack, guiType: String, clickType: org.bukkit.event.inventory.ClickType) {
        val generationId = guiType.substringAfter("generation:")
        val currentPage = playerPages[player] ?: 0

        when (slot) {
            45 -> {
                // 上一页按钮
                if (currentPage > 0) {
                    // 播放翻页音效
                    player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.5f, 1.2f)
                    openGenerationGuiAsync(player, generationId, currentPage - 1)
                } else {
                    // 已经是第一页，播放错误音效
                    player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.3f, 0.8f)
                }
            }
            49 -> {
                // 返回按钮 - 强制清除缓存并更新数据
                // 播放返回音效
                player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.6f, 0.9f)

                // 直接从本地文件读取最新数据

                // 使用异步加载显示最新数据
                openMainGuiAsync(player)
            }
            50 -> {
                // 进度信息 - 右键领取奖励
                if (clickType == org.bukkit.event.inventory.ClickType.RIGHT) {
                    val generation = plugin.generationManager.getGeneration(generationId)
                    if (generation != null) {
                        handleGenerationRewardClaim(player, generation)
                    }
                }
            }
            53 -> {
                // 下一页按钮
                val generation = plugin.generationManager.getGeneration(generationId)
                if (generation != null) {
                    val pokemonList = generation.getAllPokemon().entries.toList()
                    val itemsPerPage = 28
                    val totalPages = (pokemonList.size + itemsPerPage - 1) / itemsPerPage
                    if (currentPage < totalPages - 1) {
                        // 播放翻页音效
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.5f, 1.2f)
                        openGenerationGuiAsync(player, generationId, currentPage + 1)
                    } else {
                        // 已经是最后一页，播放错误音效
                        player.playSound(player.location, org.bukkit.Sound.UI_BUTTON_CLICK, 0.3f, 0.8f)
                    }
                }
            }
        }
    }

    /**
     * 处理世代奖励领取
     */
    private fun handleGenerationRewardClaim(player: Player, generation: cn.acebrand.acedex.generation.Generation) {
        // 检查冷却时间，防止刷屏
        if (isPlayerOnCooldown(player, "generation-reward")) {
            val remaining = getRemainingCooldown(player, "generation-reward")
            player.sendMessage("§c请等待 ${remaining} 秒后再查看${generation.name}信息！")
            return
        }
        setPlayerCooldown(player, "generation-reward")

        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val progress = allProgress.generationProgresses[generation.id] ?: GenerationProgress(0, 0, 0)
            val isCompleted = progress.percentage >= 100
            val hasClaimedReward = plugin.rewardManager.hasClaimedGenerationReward(player, generation.id)

        when {
            !isCompleted -> {
                // 尝试领取可用的进度奖励
                val claimedPercentages = plugin.rewardManager.claimAvailableProgressRewards(player, generation.id)

                if (claimedPercentages.isNotEmpty()) {
                    // 成功领取了进度奖励
                    player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.2f)
                    player.sendMessage("§a§l[进度奖励] §f成功领取${generation.name}进度奖励！")
                    player.sendMessage("§7领取的进度: §e${claimedPercentages.joinToString("%, ", "", "%")}")

                    // 刷新主菜单显示
                    openMainGuiAsync(player)
                } else {
                    // 没有可领取的进度奖励，显示详细信息
                    player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.6f, 1.0f)
                    val remaining = progress.total - progress.caught
                    player.sendMessage("§c你还需要收集 $remaining 只精灵才能完成${generation.name}！")
                    player.sendMessage("§7当前进度: §a${progress.caught}§7/§f${progress.total}")

                    // 显示按顺序的进度奖励信息
                    showProgressRewardsInOrder(player, generation.id, progress.percentage)
                }
            }
            hasClaimedReward -> {
                // 播放已领取提示音效
                player.playSound(player.location, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 0.5f, 0.8f)
                player.sendMessage("§e你已经领取过${generation.name}的完成奖励了！")
            }
            else -> {
                // 先尝试领取所有可用的进度奖励
                val claimedProgressPercentages = plugin.rewardManager.claimAvailableProgressRewards(player, generation.id)

                // 然后领取完成奖励
                val success = plugin.rewardManager.giveGenerationReward(player, generation.id)
                if (success) {
                    // 显示领取信息
                    if (claimedProgressPercentages.isNotEmpty()) {
                        player.sendMessage("§a§l[批量领取] §f同时领取了以下奖励：")
                        player.sendMessage("§6§l进度奖励: §e${claimedProgressPercentages.joinToString("%, ", "", "%")}进度奖励")
                        player.sendMessage("§a§l完成奖励: §f${generation.name}世代完成奖励")

                        // 显示具体的进度奖励内容
                        showClaimedProgressRewardsDetails(player, generation.id, claimedProgressPercentages)
                    }

                    // 刷新主菜单显示
                    openMainGuiAsync(player)

                    // 播放成功音效
                    player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f)
                } else {
                    // 播放失败音效
                    player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.8f, 0.7f)
                    player.sendMessage("§c奖励发放失败，请联系管理员！")
                }
            }
        }
        } catch (e: Exception) {
            player.sendMessage("§c读取世代进度数据失败: ${e.message}")
            plugin.logger.warning("读取玩家 ${player.name} 世代进度失败: ${e.message}")
        }
    }

    /**
     * 显示进度奖励信息到聊天栏 - 显示全部奖励信息
     */
    private fun showProgressRewardsInOrder(player: Player, generationId: String, currentPercentage: Int) {
        try {
            val config = plugin.config.getConfig()
            val progressSection = config.getConfigurationSection("rewards.generation.$generationId.progress")

            if (progressSection != null) {
                val progressRewards = mutableListOf<Triple<Int, List<String>, List<String>>>()

                // 收集所有进度奖励
                for (percentageKey in progressSection.getKeys(false)) {
                    try {
                        val percentage = percentageKey.toInt()
                        val commands = config.getStringList("rewards.generation.$generationId.progress.$percentageKey.commands")
                        val descriptions = config.getStringList("rewards.generation.$generationId.progress.$percentageKey.descriptions")
                        if (descriptions.isNotEmpty()) {
                            progressRewards.add(Triple(percentage, commands, descriptions))
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                    }
                }

                // 按百分比排序
                progressRewards.sortBy { it.first }

                if (progressRewards.isNotEmpty()) {
                    player.sendMessage("")
                    player.sendMessage("§6§l进度奖励详情:")

                    // 显示所有进度奖励信息，让玩家看到全部奖励内容
                    for ((percentage, commands, descriptions) in progressRewards) {
                        val rewardKey = "progress_${generationId}_${percentage}"
                        val hasClaimedProgress = plugin.rewardManager.hasClaimedProgressReward(player, rewardKey)

                        // 根据状态显示不同的图标和颜色
                        val statusIcon = when {
                            hasClaimedProgress -> "§a✓"  // 已领取
                            currentPercentage >= percentage -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedProgress -> "§a已领取"
                            currentPercentage >= percentage -> "§6可领取"
                            else -> "§c需要${percentage}%进度"
                        }

                        player.sendMessage("$statusIcon §e${percentage}%进度奖励 §7- $statusText")
                        descriptions.forEach { description ->
                            player.sendMessage("  $description")
                        }
                        player.sendMessage("")
                    }

                    // 添加100%完成奖励
                    val completionDescriptions = plugin.config.getGenerationRewardDescriptions(generationId)
                    if (completionDescriptions.isNotEmpty()) {
                        val hasClaimedCompletion = plugin.rewardManager.hasClaimedGenerationReward(player, generationId)

                        val statusIcon = when {
                            hasClaimedCompletion -> "§a✓"  // 已领取
                            currentPercentage >= 100 -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedCompletion -> "§a已领取"
                            currentPercentage >= 100 -> "§6可领取"
                            else -> "§c需要100%进度"
                        }

                        player.sendMessage("$statusIcon §e100%完成奖励 §7- $statusText")
                        completionDescriptions.forEach { description ->
                            player.sendMessage("  $description")
                        }
                        player.sendMessage("")
                    }
                } else {
                    player.sendMessage("§7该世代暂无进度奖励配置")
                }
            } else {
                player.sendMessage("§7该世代暂无进度奖励配置")
            }

        } catch (e: Exception) {
            plugin.logger.warning("显示进度奖励信息失败: ${e.message}")
            player.sendMessage("§c获取进度奖励信息失败")
        }
    }

    /**
     * 显示已领取的进度奖励详细信息
     */
    private fun showClaimedProgressRewardsDetails(player: Player, generationId: String, claimedPercentages: List<Int>) {
        try {
            val config = plugin.config.getConfig()

            player.sendMessage("")
            player.sendMessage("§6§l领取的进度奖励详情:")

            claimedPercentages.sorted().forEach { percentage ->
                val descriptions = config.getStringList("rewards.generation.$generationId.progress.$percentage.descriptions")

                player.sendMessage("§a✓ §e${percentage}%进度奖励:")
                descriptions.forEach { description ->
                    player.sendMessage("  $description")
                }
                player.sendMessage("")
            }

        } catch (e: Exception) {
            plugin.logger.warning("显示已领取进度奖励详情失败: ${e.message}")
        }
    }

    /**
     * 处理全世代完成奖励领取（不包含冷却检查，由调用方处理）
     */
    private fun handleAllGenerationsRewardClaimWithoutCooldown(player: Player) {
        val canClaim = plugin.rewardManager.canClaimAllGenerationsReward(player)
        val hasClaimed = plugin.rewardManager.hasClaimedAllGenerationsReward(player)

        when {
            hasClaimed -> {
                // 播放已领取提示音效
                player.playSound(player.location, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 0.5f, 0.8f)
                player.sendMessage("§e你已经领取过精灵大师奖励了！")
                showAllGenerationsProgressAsync(player)
            }
            !canClaim -> {
                // 尝试领取可用的全世代进度奖励
                val claimedOverallPercentages = plugin.rewardManager.claimAvailableOverallProgressRewards(player)

                if (claimedOverallPercentages.isNotEmpty()) {
                    // 成功领取了全世代进度奖励
                    player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.2f)
                    player.sendMessage("§d§l[全世代进度奖励] §f成功领取全世代进度奖励！")
                    player.sendMessage("§7领取的进度: §e${claimedOverallPercentages.joinToString("%, ", "", "%")}全世代进度奖励")

                    // 显示具体的全世代进度奖励内容
                    showClaimedOverallProgressRewardsDetails(player, claimedOverallPercentages)

                    // 刷新主菜单显示
                    openMainGuiAsync(player)
                } else {
                    // 没有可领取的全世代进度奖励，显示详细信息
                    try {
                        val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                        val remaining = allProgress.totalGenerations - allProgress.completedGenerations

                        // 播放未完成音效
                        player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.6f, 1.0f)
                        player.sendMessage("§c你还需要完成 $remaining 个世代才能领取精灵大师奖励！")
                        player.sendMessage("§7当前进度: §a${allProgress.completedGenerations}§7/§f${allProgress.totalGenerations}")
                        showAllGenerationsProgressWithData(player, allProgress)
                    } catch (e: Exception) {
                        player.sendMessage("§c读取进度数据失败: ${e.message}")
                    }
                }
            }
            else -> {
                // 可以领取全世代奖励
                val success = plugin.rewardManager.claimAllGenerationsReward(player)
                if (success) {
                    // 消息已在RewardManager中发送，这里不重复发送
                    // 刷新主菜单显示
                    openMainGuiAsync(player)

                    // 播放成功音效
                    player.playSound(player.location, org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.2f)
                } else {
                    // 播放失败音效
                    player.playSound(player.location, org.bukkit.Sound.ENTITY_VILLAGER_NO, 0.8f, 0.7f)
                    player.sendMessage("§c精灵大师奖励发放失败，请联系管理员！")
                }
            }
        }
    }

    /**
     * 显示已领取的全世代进度奖励详细信息
     */
    private fun showClaimedOverallProgressRewardsDetails(player: Player, claimedPercentages: List<Int>) {
        try {
            val config = plugin.config.getConfig()

            player.sendMessage("")
            player.sendMessage("§d§l领取的全世代进度奖励详情:")

            claimedPercentages.sorted().forEach { percentage ->
                val name = config.getString("rewards.overall-progress.$percentage.name") ?: "进度奖励"
                val descriptions = config.getStringList("rewards.overall-progress.$percentage.descriptions")

                player.sendMessage("§a✓ §e${percentage}%全世代进度 - §6$name:")
                descriptions.forEach { description ->
                    player.sendMessage("  $description")
                }
                player.sendMessage("")
            }

        } catch (e: Exception) {
            plugin.logger.warning("显示已领取全世代进度奖励详情失败: ${e.message}")
        }
    }

    /**
     * 显示所有世代进度详情 - 从本地文件读取
     */
    private fun showAllGenerationsProgressAsync(player: Player) {
        try {
            // 直接从本地文件读取数据
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            showAllGenerationsProgressWithData(player, allProgress)
        } catch (e: Exception) {
            player.sendMessage("§c读取进度数据失败，请稍后重试")
            plugin.logger.warning("读取全世代进度失败: ${e.message}")

            // 尝试重新检测 - 使用CheckPokemon方法异步检测
            plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
                try {
                    plugin.pokemonDetector.detectAndSavePlayerPokemonWithCheckMethod(player)
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        try {
                            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
                            showAllGenerationsProgressWithData(player, allProgress)
                        } catch (e2: Exception) {
                            player.sendMessage("§c重新检测后仍无法读取数据")
                        }
                    })
                } catch (e2: Exception) {
                    plugin.server.scheduler.runTask(plugin, Runnable {
                        player.sendMessage("§c重新检测失败: ${e2.message}")
                    })
                }
            })
        }
    }

    /**
     * 使用预计算数据显示所有世代进度详情
     */
    private fun showAllGenerationsProgressWithData(player: Player, allProgress: AllGenerationsProgress) {
        val canClaimAllReward = plugin.rewardManager.canClaimAllGenerationsReward(player)
        val hasClaimedAllReward = plugin.rewardManager.hasClaimedAllGenerationsReward(player)

        // 标题和分隔线
        player.sendMessage("§d§l━━━ 全世代收集进度 ━━━")
        player.sendMessage("")

        // 总体进度信息（重新排版）
        player.sendMessage("§6§l总体收集情况:")
        player.sendMessage("§7已完成世代: §a${allProgress.completedGenerations}§7/§f${allProgress.totalGenerations}")
        player.sendMessage("§7总体进度: §f${allProgress.caughtPokemon}§7/§f${allProgress.totalPokemon} §7(§a${allProgress.overallPercentage}%§7)")

        // 添加总进度条
        val totalProgressBar = getTotalProgressBar(allProgress.overallPercentage)
        player.sendMessage("§7总进度条: $totalProgressBar")
        player.sendMessage("")

        // 各世代详细进度（重新排版，改善对齐）
        player.sendMessage("§e§l各世代详细进度:")
        val generations = plugin.generationManager.getAllGenerations()
        generations.forEachIndexed { index, generation ->
            val progress = allProgress.generationProgresses[generation.id]
            if (progress != null) {
                val statusIcon = if (progress.percentage >= 100) "§a✓" else "§7○"
                val hasClaimedReward = plugin.rewardManager.hasClaimedGenerationReward(player, generation.id)
                val rewardIcon = if (progress.percentage >= 100 && hasClaimedReward) "§a[已领取]"
                                else if (progress.percentage >= 100) "§6[可领取]"
                                else "§7[未完成]"

                // 改善对齐格式：使用固定宽度格式化
                val paddedGenerationName = String.format("%-6s", generation.name)  // 世代名称6字符宽度
                val progressData = String.format("%3d/%-3d", progress.caught, progress.total)  // 进度数据固定格式
                val percentageText = String.format("(%3d%%)", progress.percentage)  // 百分比3位数字宽度
                val paddedRewardIcon = String.format("%-8s", rewardIcon)  // 奖励状态8字符宽度

                player.sendMessage("$statusIcon ${generation.color}$paddedGenerationName §7: §f$progressData §a$percentageText §7$paddedRewardIcon")
            }
        }

        player.sendMessage("")

        // 全世代奖励状态（重新排版）
        when {
            hasClaimedAllReward -> {
                player.sendMessage("§a§l✓ 精灵大师奖励已领取！")
                player.sendMessage("§e  你是真正的精灵大师！")
            }
            canClaimAllReward -> {
                player.sendMessage("§6§l★ 可以领取精灵大师奖励！")
                player.sendMessage("§e  恭喜完成所有世代收集！")
            }
            else -> {
                val remaining = allProgress.totalGenerations - allProgress.completedGenerations
                player.sendMessage("§c§l还需完成 $remaining 个世代才能领取精灵大师奖励！")
            }
        }

        player.sendMessage("§d§l━━━━━━━━━━━━━━━━━━━━━━━")
    }

    /**
     * 显示所有世代进度详情 - 从本地文件读取
     */
    private fun showAllGenerationsProgress(player: Player) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            showAllGenerationsProgressWithData(player, allProgress)
        } catch (e: Exception) {
            player.sendMessage("§c读取进度数据失败: ${e.message}")
        }
    }

    /**
     * 显示所有世代进度详情（重新排版） - 已弃用
     */
    @Deprecated("使用showAllGenerationsProgressWithData", ReplaceWith("showAllGenerationsProgressWithData(player, allProgress)"))
    private fun showAllGenerationsProgressOld(player: Player) {
        try {
            val allProgress = plugin.pokemonDetector.getAllGenerationsProgress(player)
            val canClaimAllReward = plugin.rewardManager.canClaimAllGenerationsReward(player)
            val hasClaimedAllReward = plugin.rewardManager.hasClaimedAllGenerationsReward(player)

        // 标题和分隔线
        player.sendMessage("§d§l━━━ 全世代收集进度 ━━━")
        player.sendMessage("")

        // 总体进度信息（重新排版）
        player.sendMessage("§6§l总体收集情况:")
        player.sendMessage("§7已完成世代: §a${allProgress.completedGenerations}§7/§f${allProgress.totalGenerations}")
        player.sendMessage("§7总体进度: §f${allProgress.caughtPokemon}§7/§f${allProgress.totalPokemon} §7(§a${allProgress.overallPercentage}%§7)")

        // 添加总进度条
        val totalProgressBar = getTotalProgressBar(allProgress.overallPercentage)
        player.sendMessage("§7总进度条: $totalProgressBar")
        player.sendMessage("")

        // 各世代详细进度（重新排版，改善对齐）
        player.sendMessage("§e§l各世代详细进度:")
        val generations = plugin.generationManager.getAllGenerations()
        generations.forEachIndexed { index, generation ->
            val progress = allProgress.generationProgresses[generation.id]
            if (progress != null) {
                val statusIcon = if (progress.percentage >= 100) "§a✓" else "§7○"
                val hasClaimedReward = plugin.rewardManager.hasClaimedGenerationReward(player, generation.id)
                val rewardIcon = if (progress.percentage >= 100 && hasClaimedReward) "§a[已领取]"
                                else if (progress.percentage >= 100) "§6[可领取]"
                                else "§7[未完成]"

                // 改善对齐格式：使用固定宽度格式化
                val paddedGenerationName = String.format("%-6s", generation.name)  // 世代名称6字符宽度
                val progressData = String.format("%3d/%-3d", progress.caught, progress.total)  // 进度数据固定格式
                val percentageText = String.format("(%3d%%)", progress.percentage)  // 百分比3位数字宽度
                val paddedRewardIcon = String.format("%-8s", rewardIcon)  // 奖励状态8字符宽度

                player.sendMessage("$statusIcon ${generation.color}$paddedGenerationName §7: §f$progressData §a$percentageText §7$paddedRewardIcon")
            }
        }

        player.sendMessage("")

        // 全世代奖励状态（重新排版）
        when {
            hasClaimedAllReward -> {
                player.sendMessage("§a§l✓ 精灵大师奖励已领取！")
                player.sendMessage("§e  你是真正的精灵大师！")
            }
            canClaimAllReward -> {
                player.sendMessage("§6§l★ 可以领取精灵大师奖励！")
                player.sendMessage("§e  恭喜完成所有世代收集！")
            }
            else -> {
                val remaining = allProgress.totalGenerations - allProgress.completedGenerations
                player.sendMessage("§c§l还需完成 $remaining 个世代才能领取精灵大师奖励！")
            }
        }

        player.sendMessage("§d§l━━━━━━━━━━━━━━━━━━━━━━━")
        } catch (e: Exception) {
            player.sendMessage("§c读取进度数据失败: ${e.message}")
        }
    }

    /**
     * 添加进度奖励信息到lore中（带玩家状态检查）- 显示全部奖励信息
     */
    private fun addProgressRewardInfoWithPlayer(lore: MutableList<String>, generationId: String, currentPercentage: Int, player: Player) {
        try {
            val config = plugin.config.getConfig()
            val progressSection = config.getConfigurationSection("rewards.generation.$generationId.progress")

            if (plugin.config.enableDebug) {
                plugin.logger.info("检查进度奖励 - 世代: $generationId, 当前进度: $currentPercentage%, 配置节存在: ${progressSection != null}")
            }

            if (progressSection != null) {
                val progressRewards = mutableListOf<Triple<Int, List<String>, List<String>>>()

                // 收集所有进度奖励
                for (percentageKey in progressSection.getKeys(false)) {
                    try {
                        val percentage = percentageKey.toInt()
                        val commands = config.getStringList("rewards.generation.$generationId.progress.$percentageKey.commands")
                        val descriptions = config.getStringList("rewards.generation.$generationId.progress.$percentageKey.descriptions")

                        if (plugin.config.enableDebug) {
                            plugin.logger.info("进度奖励配置 - ${generationId} ${percentage}%: 命令数=${commands.size}, 描述数=${descriptions.size}")
                        }

                        if (descriptions.isNotEmpty()) {
                            progressRewards.add(Triple(percentage, commands, descriptions))
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                        if (plugin.config.enableDebug) {
                            plugin.logger.warning("无效的进度百分比配置: $generationId.$percentageKey")
                        }
                    }
                }

                // 按百分比排序
                progressRewards.sortBy { it.first }

                if (plugin.config.enableDebug) {
                    plugin.logger.info("找到 ${progressRewards.size} 个进度奖励配置")
                }

                if (progressRewards.isNotEmpty()) {
                    lore.add("§6§l进度奖励:")

                    // 显示所有进度奖励信息，让玩家看到全部奖励内容
                    for ((percentage, commands, descriptions) in progressRewards) {
                        val rewardKey = "progress_${generationId}_${percentage}"
                        val hasClaimedProgress = plugin.rewardManager.hasClaimedProgressReward(player, rewardKey)

                        // 根据状态显示不同的图标和颜色
                        val statusIcon = when {
                            hasClaimedProgress -> "§a✓"  // 已领取
                            currentPercentage >= percentage -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedProgress -> "§a已领取"
                            currentPercentage >= percentage -> "§6可领取"
                            else -> "§c需要${percentage}%进度"
                        }

                        lore.add("$statusIcon §e${percentage}%进度奖励 §7- $statusText")
                        descriptions.forEach { description ->
                            lore.add("  $description")
                        }
                        lore.add("")
                    }

                    // 添加100%完成奖励
                    val completionDescriptions = plugin.config.getGenerationRewardDescriptions(generationId)
                    if (completionDescriptions.isNotEmpty()) {
                        val hasClaimedCompletion = plugin.rewardManager.hasClaimedGenerationReward(player, generationId)

                        val statusIcon = when {
                            hasClaimedCompletion -> "§a✓"  // 已领取
                            currentPercentage >= 100 -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedCompletion -> "§a已领取"
                            currentPercentage >= 100 -> "§6可领取"
                            else -> "§c需要100%进度"
                        }

                        lore.add("$statusIcon §e100%完成奖励 §7- $statusText")
                        completionDescriptions.forEach { description ->
                            lore.add("  $description")
                        }
                        lore.add("")
                    }
                } else {
                    // 如果没有找到进度奖励配置，添加一个测试信息
                    lore.add("§c§l[调试] 未找到进度奖励配置")
                    lore.add("§7配置路径: rewards.generation.$generationId.progress")
                    lore.add("")

                    if (plugin.config.enableDebug) {
                        plugin.logger.info("没有找到有效的进度奖励配置")
                    }
                }
            } else {
                // 如果配置节不存在，添加调试信息
                lore.add("§c§l[调试] 进度奖励配置节不存在")
                lore.add("§7路径: rewards.generation.$generationId.progress")
                lore.add("")

                if (plugin.config.enableDebug) {
                    plugin.logger.info("进度奖励配置节不存在: rewards.generation.$generationId.progress")
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("添加进度奖励信息失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 添加进度奖励信息到lore中（原方法，用于世代详情页面）
     */
    private fun addProgressRewardInfo(lore: MutableList<String>, generationId: String, currentPercentage: Int) {
        try {
            val config = plugin.config.getConfig()
            val progressSection = config.getConfigurationSection("rewards.generation.$generationId.progress")

            if (progressSection != null) {
                val progressRewards = mutableListOf<Pair<Int, List<String>>>()

                // 收集所有进度奖励
                for (percentageKey in progressSection.getKeys(false)) {
                    try {
                        val percentage = percentageKey.toInt()
                        val descriptions = config.getStringList("rewards.generation.$generationId.progress.$percentageKey.descriptions")
                        if (descriptions.isNotEmpty()) {
                            progressRewards.add(percentage to descriptions)
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                    }
                }

                // 按百分比排序
                progressRewards.sortBy { it.first }

                if (progressRewards.isNotEmpty()) {
                    lore.add("§6§l进度奖励:")

                    for ((percentage, descriptions) in progressRewards) {
                        val status = when {
                            currentPercentage >= percentage -> "§a✓"
                            else -> "§7○"
                        }

                        lore.add("$status §e${percentage}%进度奖励:")
                        descriptions.forEach { description ->
                            lore.add("  $description")
                        }
                    }
                    lore.add("")
                }
            }

            // 显示全世代进度奖励信息
            addOverallProgressRewardInfo(lore)

        } catch (e: Exception) {
            plugin.logger.warning("添加进度奖励信息失败: ${e.message}")
        }
    }

    /**
     * 添加全世代进度奖励信息 - 显示全部奖励信息
     */
    private fun addOverallProgressRewardInfo(lore: MutableList<String>) {
        try {
            val config = plugin.config.getConfig()
            val overallProgressSection = config.getConfigurationSection("rewards.overall-progress")

            if (overallProgressSection != null && config.getBoolean("rewards.overall-progress.enabled", false)) {
                val overallRewards = mutableListOf<Triple<Int, String, List<String>>>()

                // 收集所有全世代进度奖励
                for (percentageKey in overallProgressSection.getKeys(false)) {
                    if (percentageKey == "enabled") continue

                    try {
                        val percentage = percentageKey.toInt()
                        val name = config.getString("rewards.overall-progress.$percentageKey.name") ?: "进度奖励"
                        val descriptions = config.getStringList("rewards.overall-progress.$percentageKey.descriptions")
                        if (descriptions.isNotEmpty()) {
                            overallRewards.add(Triple(percentage, name, descriptions))
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                    }
                }

                // 按百分比排序
                overallRewards.sortBy { it.first }

                if (overallRewards.isNotEmpty()) {
                    lore.add("§d§l全世代进度奖励:")

                    // 显示所有全世代进度奖励信息，让玩家看到全部奖励内容
                    for ((percentage, name, descriptions) in overallRewards) {
                        lore.add("§7○ §e${percentage}%全世代进度 - §6$name")
                        descriptions.forEach { description -> // 显示全部奖励描述
                            lore.add("  $description")
                        }
                        lore.add("")
                    }
                }
            }
        } catch (e: Exception) {
            plugin.logger.warning("添加全世代进度奖励信息失败: ${e.message}")
        }
    }

    /**
     * 添加全世代进度奖励状态到lore中 - 显示全部奖励信息
     */
    private fun addOverallProgressRewardStatus(lore: MutableList<String>, player: Player, currentOverallPercentage: Int) {
        try {
            val config = plugin.config.getConfig()
            val overallProgressSection = config.getConfigurationSection("rewards.overall-progress")

            if (overallProgressSection != null && config.getBoolean("rewards.overall-progress.enabled", false)) {
                val overallRewards = mutableListOf<Triple<Int, String, List<String>>>()

                // 收集所有全世代进度奖励
                for (percentageKey in overallProgressSection.getKeys(false)) {
                    if (percentageKey == "enabled") continue

                    try {
                        val percentage = percentageKey.toInt()
                        val name = config.getString("rewards.overall-progress.$percentageKey.name") ?: "进度奖励"
                        val descriptions = config.getStringList("rewards.overall-progress.$percentageKey.descriptions")
                        if (descriptions.isNotEmpty()) {
                            overallRewards.add(Triple(percentage, name, descriptions))
                        }
                    } catch (e: NumberFormatException) {
                        // 忽略无效的百分比配置
                    }
                }

                // 按百分比排序
                overallRewards.sortBy { it.first }

                if (overallRewards.isNotEmpty()) {
                    lore.add("§d§l全世代进度奖励:")

                    // 显示所有全世代进度奖励信息，让玩家看到全部奖励内容
                    for ((percentage, name, descriptions) in overallRewards) {
                        val rewardKey = "overall_$percentage"
                        val hasClaimedOverall = plugin.rewardManager.hasClaimedOverallProgressReward(player, rewardKey)

                        // 根据状态显示不同的图标和颜色
                        val statusIcon = when {
                            hasClaimedOverall -> "§a✓"  // 已领取
                            currentOverallPercentage >= percentage -> "§6★"  // 可领取
                            else -> "§7○"  // 未达到
                        }

                        val statusText = when {
                            hasClaimedOverall -> "§a已领取"
                            currentOverallPercentage >= percentage -> "§6可领取"
                            else -> "§c需要${percentage}%全世代进度"
                        }

                        lore.add("$statusIcon §e${percentage}%全世代进度 - §6$name §7- $statusText")
                        descriptions.forEach { desc ->
                            lore.add("  $desc")
                        }
                        lore.add("")
                    }
                }
            }
        } catch (e: Exception) {
            plugin.logger.warning("添加全世代进度奖励状态失败: ${e.message}")
        }
    }

    /**
     * 添加全世代进度奖励信息（用于主菜单）
     */
    private fun addOverallProgressRewardInfoSimple(lore: MutableList<String>) {
        try {
            val config = plugin.config.getConfig()
            val overallProgressSection = config.getConfigurationSection("rewards.overall-progress")

            if (overallProgressSection != null && config.getBoolean("rewards.overall-progress.enabled", false)) {
                lore.add("§d§l全世代进度奖励:")
                lore.add("§7查看总体进度了解详情")
                lore.add("")
            }
        } catch (e: Exception) {
            plugin.logger.warning("添加全世代进度奖励信息失败: ${e.message}")
        }
    }

    /**
     * 获取总进度条
     */
    private fun getTotalProgressBar(percentage: Int, length: Int = 20): String {
        // 确保百分比在0-100范围内
        val safePercentage = percentage.coerceIn(0, 100)
        val filled = (safePercentage * length) / 100
        val empty = (length - filled).coerceAtLeast(0)
        return "§a" + "█".repeat(filled) + "§7" + "█".repeat(empty) + " §f$safePercentage%"
    }

    /**
     * 显示玩家统计 - 从本地文件读取
     */
    private fun showPlayerStats(player: Player) {
        try {
            val playerData = plugin.pokemonDetector.getPlayerPokemon(player)

            player.sendMessage("§6§l=== 你的图鉴统计 ===")
            player.sendMessage("§7总收集数: §a${playerData.totalCaught}")
            player.sendMessage("§7队伍精灵: §b${playerData.partyPokemon.size}")
            player.sendMessage("§7PC精灵: §d${playerData.pcPokemon.size}")

            if (playerData.partyPokemon.isNotEmpty()) {
                player.sendMessage("§e队伍中的精灵:")
                playerData.partyPokemon.forEach { pokemon ->
                    // 使用中文名称映射
                    val chineseName = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemon.name)
                    player.sendMessage("§7- §f$chineseName")
                }
            }
        } catch (e: Exception) {
            player.sendMessage("§c读取统计数据失败: ${e.message}")
            plugin.logger.warning("读取玩家 ${player.name} 统计数据失败: ${e.message}")
        }
    }

    @EventHandler
    fun onInventoryClose(event: org.bukkit.event.inventory.InventoryCloseEvent) {
        val player = event.player as? Player ?: return
        val guiType = openGuis[player]

        // 只清理普通菜单的GUI状态
        if (guiType == "main" || guiType?.startsWith("generation:") == true) {
            openGuis.remove(player)
        }
    }

    @EventHandler
    fun onPlayerQuit(event: org.bukkit.event.player.PlayerQuitEvent) {
        val player = event.player
        // 清理玩家数据，防止内存泄漏
        openGuis.remove(player)
        playerPages.remove(player)
        playerCooldowns.remove(player)
        playerMainInventories.remove(player)

        // 清理该玩家的所有世代菜单缓存
        val playerUUID = player.uniqueId.toString()
        val keysToRemove = playerGenerationInventories.keys.filter { it.startsWith("$playerUUID:") }
        keysToRemove.forEach { playerGenerationInventories.remove(it) }
    }
}
