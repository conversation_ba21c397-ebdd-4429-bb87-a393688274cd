name: AceDex
version: 1.0.0
main: cn.acebrand.acedex.AceDex
api-version: 1.21
author: AceBrand
description: 精灵图鉴插件 - 基于Cobblemon的精灵收集和奖励系统
website: https://github.com/AceBrand

softdepend:
  - PlaceholderAPI

commands:
  acedex:
    description: 打开精灵图鉴主界面
    usage: /acedex [info|reload]
    aliases: [dex, pokedex]
    permission: acedex.use
    permission-message: §c你没有权限使用这个命令！

permissions:
  acedex.*:
    description: 所有AceDex权限
    default: op
    children:
      acedex.use: true
      acedex.admin: true
      acedex.reward: true
  
  acedex.use:
    description: 使用基本图鉴功能
    default: true
  
  acedex.admin:
    description: 管理员权限
    default: op
  
  acedex.reload:
    description: 重载插件权限
    default: op
  
  acedex.reward:
    description: 领取奖励权限
    default: true
  
  acedex.reward.bypass:
    description: 绕过奖励冷却时间
    default: op
