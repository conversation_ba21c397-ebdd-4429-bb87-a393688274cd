/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.api

import com.mojang.serialization.Codec
import net.minecraft.util.StringRepresentable

enum class Priority : StringRepresentable {
    HIGHEST,
    HIGH,
    NORMAL,
    LOW,
    LOWEST;

    override fun getSerializedName() = this.name

    companion object {

        @JvmStatic
        val CODEC: Codec<Priority> = StringRepresentable.fromEnum(Priority::values)

    }

}