package cn.acebrand.acebiomespawner.util

/**
 * 精灵名称中文映射工具类
 * 提供英文精灵名称到中文名称的映射
 */
object PokemonNameUtil {

    private val pokemonNameMap = mapOf(
        // ========== 第一世代 (001-151) ==========
        "bulbasaur" to "妙蛙种子",
        "ivysaur" to "妙蛙草",
        "venusaur" to "妙蛙花",
        "charmander" to "小火龙",
        "charmeleon" to "火恐龙",
        "charizard" to "喷火龙",
        "squirtle" to "杰尼龟",
        "wartortle" to "卡咪龟",
        "blastoise" to "水箭龟",
        "caterpie" to "绿毛虫",
        "metapod" to "铁甲蛹",
        "butterfree" to "巴大蝶",
        "weedle" to "独角虫",
        "kakuna" to "铁壳蛹",
        "beedrill" to "大针蜂",
        "pidgey" to "波波",
        "pidgeotto" to "比比鸟",
        "pidgeot" to "大比鸟",
        "rattata" to "小拉达",
        "raticate" to "拉达",
        "spearow" to "烈雀",
        "fearow" to "大嘴雀",
        "ekans" to "阿柏蛇",
        "arbok" to "阿柏怪",
        "pikachu" to "皮卡丘",
        "raichu" to "雷丘",
        "sandshrew" to "穿山鼠",
        "sandslash" to "穿山王",
        "nidoran_f" to "尼多兰",
        "nidorina" to "尼多娜",
        "nidoqueen" to "尼多后",
        "nidoran_m" to "尼多朗",
        "nidorino" to "尼多力诺",
        "nidoking" to "尼多王",
        "clefairy" to "皮皮",
        "clefable" to "皮可西",
        "vulpix" to "六尾",
        "ninetales" to "九尾",
        "jigglypuff" to "胖丁",
        "wigglytuff" to "胖可丁",
        "zubat" to "超音蝠",
        "golbat" to "大嘴蝠",
        "oddish" to "走路草",
        "gloom" to "臭臭花",
        "vileplume" to "霸王花",
        "paras" to "派拉斯",
        "parasect" to "派拉斯特",
        "venonat" to "毛球",
        "venomoth" to "摩鲁蛾",
        "diglett" to "地鼠",
        "dugtrio" to "三地鼠",
        "meowth" to "喵喵",
        "persian" to "猫老大",
        "psyduck" to "可达鸭",
        "golduck" to "哥达鸭",
        "mankey" to "猴怪",
        "primeape" to "火爆猴",
        "growlithe" to "卡蒂狗",
        "arcanine" to "风速狗",
        "poliwag" to "蚊香蝌蚪",
        "poliwhirl" to "蚊香君",
        "poliwrath" to "蚊香泳士",
        "abra" to "凯西",
        "kadabra" to "勇基拉",
        "alakazam" to "胡地",
        "machop" to "腕力",
        "machoke" to "豪力",
        "machamp" to "怪力",
        "bellsprout" to "喇叭芽",
        "weepinbell" to "口呆花",
        "victreebel" to "大食花",
        "tentacool" to "玛瑙水母",
        "tentacruel" to "毒刺水母",
        "geodude" to "小拳石",
        "graveler" to "隆隆石",
        "golem" to "隆隆岩",
        "ponyta" to "小火马",
        "rapidash" to "烈焰马",
        "slowpoke" to "呆呆兽",
        "slowbro" to "呆壳兽",
        "magnemite" to "小磁怪",
        "magneton" to "三合一磁怪",
        "farfetchd" to "大葱鸭",
        "doduo" to "嘟嘟",
        "dodrio" to "嘟嘟利",
        "seel" to "小海狮",
        "dewgong" to "白海狮",
        "grimer" to "臭泥",
        "muk" to "臭臭泥",
        "shellder" to "大舌贝",
        "cloyster" to "刺甲贝",
        "gastly" to "鬼斯",
        "haunter" to "鬼斯通",
        "gengar" to "耿鬼",
        "onix" to "大岩蛇",
        "drowzee" to "催眠貘",
        "hypno" to "引梦貘人",
        "krabby" to "大钳蟹",
        "kingler" to "巨钳蟹",
        "voltorb" to "霹雳电球",
        "electrode" to "顽皮雷弹",
        "exeggcute" to "蛋蛋",
        "exeggutor" to "椰蛋树",
        "cubone" to "卡拉卡拉",
        "marowak" to "嘎啦嘎啦",
        "hitmonlee" to "飞腿郎",
        "hitmonchan" to "快拳郎",
        "lickitung" to "大舌头",
        "koffing" to "瓦斯弹",
        "weezing" to "双弹瓦斯",
        "rhyhorn" to "独角犀牛",
        "rhydon" to "钻角犀兽",
        "chansey" to "吉利蛋",
        "tangela" to "蔓藤怪",
        "kangaskhan" to "袋兽",
        "horsea" to "墨海马",
        "seadra" to "海刺龙",
        "goldeen" to "角金鱼",
        "seaking" to "金鱼王",
        "staryu" to "海星星",
        "starmie" to "宝石海星",
        "mr_mime" to "魔墙人偶",
        "scyther" to "飞天螳螂",
        "jynx" to "迷唇姐",
        "electabuzz" to "电击兽",
        "magmar" to "鸭嘴火兽",
        "pinsir" to "凯罗斯",
        "tauros" to "肯泰罗",
        "magikarp" to "鲤鱼王",
        "gyarados" to "暴鲤龙",
        "lapras" to "拉普拉斯",
        "ditto" to "百变怪",
        "eevee" to "伊布",
        "vaporeon" to "水伊布",
        "jolteon" to "雷伊布",
        "flareon" to "火伊布",
        "porygon" to "多边兽",
        "omanyte" to "菊石兽",
        "omastar" to "多刺菊石兽",
        "kabuto" to "化石盔",
        "kabutops" to "镰刀盔",
        "aerodactyl" to "化石翼龙",
        "snorlax" to "卡比兽",
        "articuno" to "急冻鸟",
        "zapdos" to "闪电鸟",
        "moltres" to "火焰鸟",
        "dratini" to "迷你龙",
        "dragonair" to "哈克龙",
        "dragonite" to "快龙",
        "mewtwo" to "超梦",
        "mew" to "梦幻",

        // ========== 第二世代 (152-251) ==========
        "chikorita" to "菊草叶",
        "bayleef" to "月桂叶",
        "meganium" to "大竺葵",
        "cyndaquil" to "火球鼠",
        "quilava" to "火岩鼠",
        "typhlosion" to "火暴兽",
        "totodile" to "小锯鳄",
        "croconaw" to "蓝鳄",
        "feraligatr" to "大力鳄",
        "sentret" to "尾立",
        "furret" to "大尾立",
        "hoothoot" to "咕咕",
        "noctowl" to "猫头夜鹰",
        "ledyba" to "芭瓢虫",
        "ledian" to "安瓢虫",
        "spinarak" to "圆丝蛛",
        "ariados" to "阿利多斯",
        "crobat" to "叉字蝠",
        "chinchou" to "灯笼鱼",
        "lanturn" to "电灯怪",
        "pichu" to "皮丘",
        "cleffa" to "皮宝宝",
        "igglybuff" to "宝宝丁",
        "togepi" to "波克比",
        "togetic" to "波克基古",
        "natu" to "天然雀",
        "xatu" to "天然鸟",
        "mareep" to "咩利羊",
        "flaaffy" to "茸茸羊",
        "ampharos" to "电龙",
        "bellossom" to "美丽花",
        "marill" to "玛力露",
        "azumarill" to "玛力露丽",
        "sudowoodo" to "树才怪",
        "politoed" to "蚊香蛙皇",
        "hoppip" to "毽子草",
        "skiploom" to "毽子花",
        "jumpluff" to "毽子棉",
        "aipom" to "长尾怪手",
        "sunkern" to "向日种子",
        "sunflora" to "向日花怪",
        "yanma" to "蜻蜻蜓",
        "wooper" to "乌波",
        "quagsire" to "沼王",
        "espeon" to "太阳伊布",
        "umbreon" to "月亮伊布",
        "murkrow" to "黑暗鸦",
        "slowking" to "呆呆王",
        "misdreavus" to "梦妖",
        "unown" to "未知图腾",
        "wobbuffet" to "果然翁",
        "girafarig" to "麒麟奇",
        "pineco" to "榛果球",
        "forretress" to "佛烈托斯",
        "dunsparce" to "土龙弟弟",
        "gligar" to "天蝎",
        "steelix" to "大钢蛇",
        "snubbull" to "布鲁",
        "granbull" to "布鲁皇",
        "qwilfish" to "千针鱼",
        "scizor" to "巨钳螳螂",
        "shuckle" to "壶壶",
        "heracross" to "赫拉克罗斯",
        "sneasel" to "狃拉",
        "teddiursa" to "熊宝宝",
        "ursaring" to "圈圈熊",
        "slugma" to "熔岩虫",
        "magcargo" to "熔岩蜗牛",
        "swinub" to "小山猪",
        "piloswine" to "长毛猪",
        "corsola" to "太阳珊瑚",
        "remoraid" to "铁炮鱼",
        "octillery" to "章鱼桶",
        "delibird" to "信使鸟",
        "mantine" to "巨翅飞鱼",
        "skarmory" to "盔甲鸟",
        "houndour" to "戴鲁比",
        "houndoom" to "黑鲁加",
        "kingdra" to "刺龙王",
        "phanpy" to "小小象",
        "donphan" to "顿甲",
        "porygon2" to "多边兽Ⅱ",
        "stantler" to "惊角鹿",
        "smeargle" to "图图犬",
        "tyrogue" to "无畏小子",
        "hitmontop" to "战舞郎",
        "smoochum" to "迷唇娃",
        "elekid" to "电击怪",
        "magby" to "鸭嘴宝宝",
        "miltank" to "大奶罐",
        "blissey" to "幸福蛋",
        "raikou" to "雷公",
        "entei" to "炎帝",
        "suicune" to "水君",
        "larvitar" to "幼基拉斯",
        "pupitar" to "沙基拉斯",
        "tyranitar" to "班基拉斯",
        "lugia" to "洛奇亚",
        "ho_oh" to "凤王",
        "celebi" to "时拉比",

        // ========== 第三世代 (252-386) ==========
        "treecko" to "木守宫",
        "grovyle" to "森林蜥蜴",
        "sceptile" to "蜥蜴王",
        "torchic" to "火稚鸡",
        "combusken" to "力壮鸡",
        "blaziken" to "火焰鸡",
        "mudkip" to "水跃鱼",
        "marshtomp" to "沼跃鱼",
        "swampert" to "巨沼怪",
        "poochyena" to "土狼犬",
        "mightyena" to "大狼犬",
        "zigzagoon" to "蛇纹熊",
        "linoone" to "直冲熊",
        "wurmple" to "刺尾虫",
        "silcoon" to "甲壳茧",
        "beautifly" to "狩猎凤蝶",
        "cascoon" to "盾甲茧",
        "dustox" to "毒粉蛾",
        "lotad" to "莲叶童子",
        "lombre" to "莲帽小童",
        "ludicolo" to "乐天河童",
        "seedot" to "橡实果",
        "nuzleaf" to "长鼻叶",
        "shiftry" to "狡猾天狗",
        "taillow" to "傲骨燕",
        "swellow" to "大王燕",
        "wingull" to "长翅鸥",
        "pelipper" to "大嘴鸥",
        "ralts" to "拉鲁拉丝",
        "kirlia" to "奇鲁莉安",
        "gardevoir" to "沙奈朵",
        "surskit" to "溜溜糖球",
        "masquerain" to "雨翅蛾",
        "shroomish" to "蘑蘑菇",
        "breloom" to "斗笠菇",
        "slakoth" to "懒人獭",
        "vigoroth" to "过动猿",
        "slaking" to "请假王",
        "nincada" to "土居忍士",
        "ninjask" to "铁面忍者",
        "shedinja" to "脱壳忍者",
        "whismur" to "咕妞妞",
        "loudred" to "吼爆弹",
        "exploud" to "爆音怪",
        "makuhita" to "幕下力士",
        "hariyama" to "铁掌力士",
        "azurill" to "露力丽",
        "nosepass" to "朝北鼻",
        "skitty" to "向尾喵",
        "delcatty" to "优雅猫",
        "sableye" to "勾魂眼",
        "mawile" to "大嘴娃",
        "aron" to "可可多拉",
        "lairon" to "可多拉",
        "aggron" to "波士可多拉",
        "meditite" to "玛沙那",
        "medicham" to "恰雷姆",
        "electrike" to "落雷兽",
        "manectric" to "雷电兽",
        "plusle" to "正电拍拍",
        "minun" to "负电拍拍",
        "volbeat" to "电萤虫",
        "illumise" to "甜甜萤",
        "roselia" to "毒蔷薇",
        "gulpin" to "溶食兽",
        "swalot" to "吞食兽",
        "carvanha" to "利牙鱼",
        "sharpedo" to "巨牙鲨",
        "wailmer" to "吼吼鲸",
        "wailord" to "吼鲸王",
        "numel" to "呆火驼",
        "camerupt" to "喷火驼",
        "torkoal" to "煤炭龟",
        "spoink" to "跳跳猪",
        "grumpig" to "噗噗猪",
        "spinda" to "晃晃斑",
        "trapinch" to "大颚蚁",
        "vibrava" to "超音波幼虫",
        "flygon" to "沙漠蜻蜓",
        "cacnea" to "刺球仙人掌",
        "cacturne" to "梦歌仙人掌",
        "swablu" to "青绵鸟",
        "altaria" to "七夕青鸟",
        "zangoose" to "猫鼬斩",
        "seviper" to "饭匙蛇",
        "lunatone" to "月石",
        "solrock" to "太阳岩",
        "barboach" to "泥泥鳅",
        "whiscash" to "鲶鱼王",
        "corphish" to "龙虾小兵",
        "crawdaunt" to "铁螯龙虾",
        "baltoy" to "天秤偶",
        "claydol" to "念力土偶",
        "lileep" to "触手百合",
        "cradily" to "摇篮百合",
        "anorith" to "太古羽虫",
        "armaldo" to "太古盔甲",
        "feebas" to "丑丑鱼",
        "milotic" to "美纳斯",
        "castform" to "飘浮泡泡",
        "kecleon" to "变隐龙",
        "shuppet" to "怨影娃娃",
        "banette" to "诅咒娃娃",
        "duskull" to "夜巡灵",
        "dusclops" to "彷徨夜灵",
        "tropius" to "热带龙",
        "chimecho" to "风铃铃",
        "absol" to "阿勃梭鲁",
        "wynaut" to "小果然",
        "snorunt" to "雪童子",
        "glalie" to "冰鬼护",
        "spheal" to "海豹球",
        "sealeo" to "海魔狮",
        "walrein" to "帝牙海狮",
        "clamperl" to "珍珠贝",
        "huntail" to "猎斑鱼",
        "gorebyss" to "樱花鱼",
        "relicanth" to "古空棘鱼",
        "luvdisc" to "爱心鱼",
        "bagon" to "宝贝龙",
        "shelgon" to "甲壳龙",
        "salamence" to "暴飞龙",
        "beldum" to "铁哑铃",
        "metang" to "金属怪",
        "metagross" to "巨金怪",
        "regirock" to "雷吉洛克",
        "regice" to "雷吉艾斯",
        "registeel" to "雷吉斯奇鲁",
        "latias" to "拉帝亚斯",
        "latios" to "拉帝欧斯",
        "kyogre" to "盖欧卡",
        "groudon" to "固拉多",
        "rayquaza" to "烈空坐",
        "jirachi" to "基拉祈",
        "deoxys" to "代欧奇希斯",

        // ========== 第四世代 (387-493) ==========
        "turtwig" to "草苗龟",
        "grotle" to "树林龟",
        "torterra" to "土台龟",
        "chimchar" to "小火焰猴",
        "monferno" to "猛火猴",
        "infernape" to "烈焰猴",
        "piplup" to "波加曼",
        "prinplup" to "波皇子",
        "empoleon" to "帝王拿波",
        "starly" to "姆克儿",
        "staravia" to "姆克鸟",
        "staraptor" to "姆克鹰",
        "bidoof" to "大牙狸",
        "bibarel" to "大尾狸",
        "kricketot" to "圆法师",
        "kricketune" to "音箱蟀",
        "shinx" to "小猫怪",
        "luxio" to "勒克猫",
        "luxray" to "伦琴猫",
        "budew" to "含羞苞",
        "roserade" to "罗丝雷朵",
        "cranidos" to "头盖龙",
        "rampardos" to "战槌龙",
        "shieldon" to "盾甲龙",
        "bastiodon" to "护城龙",
        "burmy" to "结草儿",
        "wormadam" to "结草贵妇",
        "mothim" to "绅士蛾",
        "combee" to "三蜜蜂",
        "vespiquen" to "蜂女王",
        "pachirisu" to "帕奇利兹",
        "buizel" to "泳圈鼬",
        "floatzel" to "浮潜鼬",
        "cherubi" to "樱花宝",
        "cherrim" to "樱花儿",
        "shellos" to "无壳海兔",
        "gastrodon" to "海兔兽",
        "ambipom" to "双尾怪手",
        "drifloon" to "飘飘球",
        "drifblim" to "随风球",
        "buneary" to "卷卷耳",
        "lopunny" to "长耳兔",
        "mismagius" to "梦妖魔",
        "honchkrow" to "乌鸦头头",
        "glameow" to "魅力喵",
        "purugly" to "东施喵",
        "chingling" to "铃铛响",
        "stunky" to "臭鼬噗",
        "skuntank" to "坦克臭鼬",
        "bronzor" to "铜镜怪",
        "bronzong" to "青铜钟",
        "bonsly" to "盆才怪",
        "mime_jr" to "魔尼尼",
        "happiny" to "小福蛋",
        "chatot" to "聒噪鸟",
        "spiritomb" to "花岩怪",
        "gible" to "圆陆鲨",
        "gabite" to "尖牙陆鲨",
        "garchomp" to "烈咬陆鲨",
        "munchlax" to "小卡比兽",
        "riolu" to "利欧路",
        "lucario" to "路卡利欧",
        "hippopotas" to "沙河马",
        "hippowdon" to "河马兽",
        "skorupi" to "钳尾蝎",
        "drapion" to "龙王蝎",
        "croagunk" to "不良蛙",
        "toxicroak" to "毒骷蛙",
        "carnivine" to "尖牙笼",
        "finneon" to "荧光鱼",
        "lumineon" to "霓虹鱼",
        "mantyke" to "小球飞鱼",
        "snover" to "雪笠怪",
        "abomasnow" to "暴雪王",
        "weavile" to "玛狃拉",
        "magnezone" to "自爆磁怪",
        "lickilicky" to "大舌舔",
        "rhyperior" to "超甲狂犀",
        "tangrowth" to "巨蔓藤",
        "electivire" to "电击魔兽",
        "magmortar" to "鸭嘴焰龙",
        "togekiss" to "波克基斯",
        "yanmega" to "远古巨蜓",
        "leafeon" to "叶伊布",
        "glaceon" to "冰伊布",
        "gliscor" to "天蝎王",
        "mamoswine" to "象牙猪",
        "porygon_z" to "多边兽Z",
        "gallade" to "艾路雷朵",
        "probopass" to "大朝北鼻",
        "dusknoir" to "黑夜魔灵",
        "froslass" to "雪妖女",
        "rotom" to "洛托姆",
        "uxie" to "由克希",
        "mesprit" to "艾姆利多",
        "azelf" to "亚克诺姆",
        "dialga" to "帝牙卢卡",
        "palkia" to "帕路奇亚",
        "heatran" to "席多蓝恩",
        "regigigas" to "雷吉奇卡斯",
        "giratina" to "骑拉帝纳",
        "cresselia" to "克雷色利亚",
        "phione" to "霏欧纳",
        "manaphy" to "玛纳霏",
        "darkrai" to "达克莱伊",
        "shaymin" to "谢米",
        "arceus" to "阿尔宙斯",

        // ========== 第五世代 (494-649) ==========
        "victini" to "比克提尼",
        "snivy" to "藤藤蛇",
        "servine" to "青藤蛇",
        "serperior" to "君主蛇",
        "tepig" to "暖暖猪",
        "pignite" to "炒炒猪",
        "emboar" to "炎武王",
        "oshawott" to "水水獭",
        "dewott" to "双刃丸",
        "samurott" to "大剑鬼",
        "patrat" to "探探鼠",
        "watchog" to "步哨鼠",
        "lillipup" to "小约克",
        "herdier" to "哈约克",
        "stoutland" to "长毛狗",
        "purrloin" to "扒手猫",
        "liepard" to "酷豹",
        "pansage" to "花椰猴",
        "simisage" to "花椰猿",
        "pansear" to "爆香猴",
        "simisear" to "爆香猿",
        "panpour" to "冷水猴",
        "simipour" to "冷水猿",
        "munna" to "食梦梦",
        "musharna" to "梦梦蚀",
        "pidove" to "豆豆鸽",
        "tranquill" to "咕咕鸽",
        "unfezant" to "高傲雉鸡",
        "blitzle" to "斑斑马",
        "zebstrika" to "雷电斑马",
        "roggenrola" to "石丸子",
        "boldore" to "地幔岩",
        "gigalith" to "庞岩怪",
        "woobat" to "滚滚蝙蝠",
        "swoobat" to "心蝙蝠",
        "drilbur" to "螺钉地鼠",
        "excadrill" to "龙头地鼠",
        "audino" to "差不多娃娃",
        "timburr" to "搬运小匠",
        "gurdurr" to "铁骨土人",
        "conkeldurr" to "修建老匠",
        "tympole" to "圆蝌蚪",
        "palpitoad" to "蓝蟾蜍",
        "seismitoad" to "蟾蜍王",
        "throh" to "投摔鬼",
        "sawk" to "打击鬼",
        "sewaddle" to "虫宝包",
        "swadloon" to "宝包茧",
        "leavanny" to "保姆虫",
        "venipede" to "百足蜈蚣",
        "whirlipede" to "车轮球",
        "scolipede" to "蜈蚣王",
        "cottonee" to "木棉球",
        "whimsicott" to "风妖精",
        "petilil" to "百合根娃娃",
        "lilligant" to "裙儿小姐",
        "basculin" to "野蛮鲈鱼",
        "sandile" to "黑眼鳄",
        "krokorok" to "混混鳄",
        "krookodile" to "流氓鳄",
        "darumaka" to "火红不倒翁",
        "darmanitan" to "达摩狒狒",
        "maractus" to "沙铃仙人掌",
        "dwebble" to "石居蟹",
        "crustle" to "岩殿居蟹",
        "scraggy" to "滑滑小蜥",
        "scrafty" to "头巾混混",
        "sigilyph" to "象征鸟",
        "yamask" to "哭哭面具",
        "cofagrigus" to "死神棺",
        "tirtouga" to "原盖海龟",
        "carracosta" to "肋骨海龟",
        "archen" to "始祖小鸟",
        "archeops" to "始祖大鸟",
        "trubbish" to "破破袋",
        "garbodor" to "灰尘山",
        "zorua" to "索罗亚",
        "zoroark" to "索罗亚克",
        "minccino" to "泡沫栗鼠",
        "cinccino" to "奇诺栗鼠",
        "gothita" to "哥德宝宝",
        "gothorita" to "哥德小童",
        "gothitelle" to "哥德小姐",
        "solosis" to "单卵细胞球",
        "duosion" to "双卵细胞球",
        "reuniclus" to "人造细胞卵",
        "ducklett" to "鸭宝宝",
        "swanna" to "舞天鹅",
        "vanillite" to "迷你冰",
        "vanillish" to "多多冰",
        "vanilluxe" to "双倍多多冰",
        "deerling" to "四季鹿",
        "sawsbuck" to "萌芽鹿",
        "emolga" to "电飞鼠",
        "karrablast" to "盖盖虫",
        "escavalier" to "骑士蜗牛",
        "foongus" to "哎呀球菇",
        "amoonguss" to "败露球菇",
        "frillish" to "轻飘飘",
        "jellicent" to "胖嘟嘟",
        "alomomola" to "保姆曼波",
        "joltik" to "电电虫",
        "galvantula" to "电蜘蛛",
        "ferroseed" to "种子铁球",
        "ferrothorn" to "坚果哑铃",
        "klink" to "齿轮儿",
        "klang" to "齿轮组",
        "klinklang" to "齿轮怪",
        "tynamo" to "麻麻小鱼",
        "eelektrik" to "麻麻鳗",
        "eelektross" to "麻麻鳗鱼王",
        "elgyem" to "小灰怪",
        "beheeyem" to "大宇怪",
        "litwick" to "烛光灵",
        "lampent" to "灯火幽灵",
        "chandelure" to "水晶灯火灵",
        "axew" to "牙牙",
        "fraxure" to "斧牙龙",
        "haxorus" to "双斧战龙",
        "cubchoo" to "喷嚏熊",
        "beartic" to "冻原熊",
        "cryogonal" to "几何雪花",
        "shelmet" to "小嘴蜗",
        "accelgor" to "敏捷虫",
        "stunfisk" to "泥巴鱼",
        "mienfoo" to "功夫鼬",
        "mienshao" to "师父鼬",
        "druddigon" to "赤面龙",
        "golett" to "泥偶小人",
        "golurk" to "泥偶巨人",
        "pawniard" to "驹刀小兵",
        "bisharp" to "劈斩司令",
        "bouffalant" to "爆炸头水牛",
        "rufflet" to "毛头小鹰",
        "braviary" to "勇士雄鹰",
        "vullaby" to "秃鹰丫头",
        "mandibuzz" to "秃鹰娜",
        "heatmor" to "熔蚁兽",
        "durant" to "铁蚁",
        "deino" to "单首龙",
        "zweilous" to "双首暴龙",
        "hydreigon" to "三首恶龙",
        "larvesta" to "燃烧虫",
        "volcarona" to "火神蛾",
        "cobalion" to "勾帕路翁",
        "terrakion" to "代拉基翁",
        "virizion" to "毕力吉翁",
        "tornadus" to "龙卷云",
        "thundurus" to "雷电云",
        "reshiram" to "莱希拉姆",
        "zekrom" to "捷克罗姆",
        "landorus" to "土地云",
        "kyurem" to "酋雷姆",
        "keldeo" to "凯路迪欧",
        "meloetta" to "美洛耶塔",
        "genesect" to "盖诺赛克特",

        // ========== 第六世代 (650-721) ==========
        "chespin" to "哈力栗",
        "quilladin" to "胖胖哈力",
        "chesnaught" to "布里卡隆",
        "fennekin" to "火狐狸",
        "braixen" to "长尾火狐",
        "delphox" to "妖火红狐",
        "froakie" to "呱呱泡蛙",
        "frogadier" to "呱头蛙",
        "greninja" to "甲贺忍蛙",
        "bunnelby" to "掘掘兔",
        "diggersby" to "掘地兔",
        "fletchling" to "小箭雀",
        "fletchinder" to "火箭雀",
        "talonflame" to "烈箭鹰",
        "scatterbug" to "粉蝶虫",
        "spewpa" to "粉蝶蛹",
        "vivillon" to "彩粉蝶",
        "litleo" to "小狮狮",
        "pyroar" to "火炎狮",
        "flabebe" to "花蓓蓓",
        "floette" to "花叶蒂",
        "florges" to "花洁夫人",
        "skiddo" to "坐骑小羊",
        "gogoat" to "坐骑山羊",
        "pancham" to "顽皮熊猫",
        "pangoro" to "霸道熊猫",
        "furfrou" to "多丽米亚",
        "espurr" to "妙喵",
        "meowstic" to "超能妙喵",
        "honedge" to "独剑鞘",
        "doublade" to "双剑鞘",
        "aegislash" to "坚盾剑怪",
        "spritzee" to "粉香香",
        "aromatisse" to "芳香精",
        "swirlix" to "绵绵泡芙",
        "slurpuff" to "胖甜妮",
        "inkay" to "好啦鱿",
        "malamar" to "乌贼王",
        "binacle" to "龟脚脚",
        "barbaracle" to "龟足巨铠",
        "skrelp" to "垃垃藻",
        "dragalge" to "毒藻龙",
        "clauncher" to "铁臂枪虾",
        "clawitzer" to "钢炮臂虾",
        "helioptile" to "伞电蜥",
        "heliolisk" to "光电伞蜥",
        "tyrunt" to "宝宝暴龙",
        "tyrantrum" to "怪颚龙",
        "amaura" to "冰雪龙",
        "aurorus" to "冰雪巨龙",
        "sylveon" to "仙子伊布",
        "hawlucha" to "摔角鹰人",
        "dedenne" to "咚咚鼠",
        "carbink" to "小碎钻",
        "goomy" to "黏黏宝",
        "sliggoo" to "黏美儿",
        "goodra" to "黏美龙",
        "klefki" to "钥圈儿",
        "phantump" to "小木灵",
        "trevenant" to "朽木妖",
        "pumpkaboo" to "南瓜精",
        "gourgeist" to "南瓜怪人",
        "bergmite" to "冰宝",
        "avalugg" to "冰岩怪",
        "noibat" to "嗡蝠",
        "noivern" to "音波龙",
        "xerneas" to "哲尔尼亚斯",
        "yveltal" to "伊裴尔塔尔",
        "zygarde" to "基格尔德",
        "diancie" to "蒂安希",
        "hoopa" to "胡帕",
        "volcanion" to "波尔凯尼恩",

        // ========== 第七世代 (722-809) ==========
        "rowlet" to "木木枭",
        "dartrix" to "投羽枭",
        "decidueye" to "狙射树枭",
        "litten" to "火斑喵",
        "torracat" to "炎热喵",
        "incineroar" to "炽焰咆哮虎",
        "popplio" to "球球海狮",
        "brionne" to "花漾海狮",
        "primarina" to "西狮海壬",
        "pikipek" to "小笃儿",
        "trumbeak" to "喇叭啄鸟",
        "toucannon" to "铳嘴大鸟",
        "yungoos" to "猫鼬少",
        "gumshoos" to "猫鼬探长",
        "grubbin" to "强颚鸡母虫",
        "charjabug" to "虫电宝",
        "vikavolt" to "锹农炮虫",
        "crabrawler" to "好胜蟹",
        "crabominable" to "毛蟹",
        "oricorio" to "花舞鸟",
        "cutiefly" to "萌虻",
        "ribombee" to "蝶结萌虻",
        "rockruff" to "岩狗狗",
        "lycanroc" to "鬃岩狼人",
        "wishiwashi" to "弱丁鱼",
        "mareanie" to "好坏星",
        "toxapex" to "超坏星",
        "mudbray" to "泥驴仔",
        "mudsdale" to "重泥挽马",
        "dewpider" to "滴蛛",
        "araquanid" to "滴蛛霸",
        "fomantis" to "伪螳草",
        "lurantis" to "兰螳花",
        "morelull" to "睡睡菇",
        "shiinotic" to "灯罩夜菇",
        "salandit" to "夜盗火蜥",
        "salazzle" to "焰后蜥",
        "stufful" to "童偶熊",
        "bewear" to "穿着熊",
        "bounsweet" to "甜竹竹",
        "steenee" to "甜舞妮",
        "tsareena" to "甜冷美后",
        "comfey" to "花疗环环",
        "oranguru" to "智挥猩",
        "passimian" to "投掷猴",
        "wimpod" to "胆小虫",
        "golisopod" to "具甲武者",
        "sandygast" to "沙丘娃",
        "palossand" to "噬沙堡爷",
        "pyukumuku" to "拳海参",
        "type_null" to "属性：空",
        "silvally" to "银伴战兽",
        "minior" to "小陨星",
        "komala" to "树枕尾熊",
        "turtonator" to "爆焰龟兽",
        "togedemaru" to "托戈德玛尔",
        "mimikyu" to "谜拟丘",
        "bruxish" to "磨牙彩皮鱼",
        "drampa" to "老翁龙",
        "dhelmise" to "破破舵轮",
        "jangmo_o" to "心鳞宝",
        "hakamo_o" to "鳞甲龙",
        "kommo_o" to "杖尾鳞甲龙",
        "tapu_koko" to "卡璞·鸣鸣",
        "tapu_lele" to "卡璞·蝶蝶",
        "tapu_bulu" to "卡璞·哞哞",
        "tapu_fini" to "卡璞·鳍鳍",
        "cosmog" to "科斯莫古",
        "cosmoem" to "科斯莫姆",
        "solgaleo" to "索尔迦雷欧",
        "lunala" to "露奈雅拉",
        "necrozma" to "奈克洛兹玛",
        "magearna" to "玛机雅娜",
        "marshadow" to "玛夏多",
        "poipole" to "毒贝比",
        "naganadel" to "四颚针龙",
        "stakataka" to "垒磊石",
        "blacephalon" to "爆头小丑",
        "zeraora" to "捷拉奥拉",
        "meltan" to "美录坦",
        "melmetal" to "美录梅塔",

        // ========== 第八世代 (810-905) ==========
        "grookey" to "敲音猴",
        "thwackey" to "啪咚猴",
        "rillaboom" to "轰擂金刚猩",
        "scorbunny" to "炎兔儿",
        "raboot" to "腾蹴小将",
        "cinderace" to "闪焰王牌",
        "sobble" to "泪眼蜥",
        "drizzile" to "变涩蜥",
        "inteleon" to "千面避役",
        "skwovet" to "贪心栗鼠",
        "greedent" to "藏饱栗鼠",
        "rookidee" to "稚山雀",
        "corvisquire" to "蓝鸦",
        "corviknight" to "钢铠鸦",
        "blipbug" to "索侦虫",
        "dottler" to "天罩虫",
        "orbeetle" to "以欧路普",
        "nickit" to "偷儿狐",
        "thievul" to "狐大盗",
        "gossifleur" to "幼棉棉",
        "eldegoss" to "白蓬蓬",
        "wooloo" to "毛辫羊",
        "dubwool" to "毛毛角羊",
        "chewtle" to "咬咬龟",
        "drednaw" to "暴噬龟",
        "yamper" to "来电汪",
        "boltund" to "逐电犬",
        "rolycoly" to "小炭仔",
        "carkol" to "大炭车",
        "coalossal" to "巨炭山",
        "applin" to "啃果虫",
        "flapple" to "苹裹龙",
        "appletun" to "丰蜜龙",
        "silicobra" to "沙包蛇",
        "sandaconda" to "沙螺蟒",
        "cramorant" to "古月鸟",
        "arrokuda" to "刺梭鱼",
        "barraskewda" to "戽斗尖梭",
        "toxel" to "毒电婴",
        "toxtricity" to "颤弦蝾螈",
        "sizzlipede" to "烧火蚣",
        "centiskorch" to "焚焰蚣",
        "clobbopus" to "拳拳蛸",
        "grapploct" to "八爪武师",
        "sinistea" to "来悲茶",
        "polteageist" to "怖思壶",
        "hatenna" to "迷布莉姆",
        "hattrem" to "提布莉姆",
        "hatterene" to "布莉姆温",
        "impidimp" to "捣蛋小妖",
        "morgrem" to "诈唬魔",
        "grimmsnarl" to "长毛巨魔",
        "obstagoon" to "堵拦熊",
        "perrserker" to "喵头目",
        "cursola" to "魔灵珊瑚",
        "sirfetchd" to "葱游兵",
        "mr_rime" to "踏冰人偶",
        "runerigus" to "死神板",
        "milcery" to "小仙奶",
        "alcremie" to "霜奶仙",
        "falinks" to "列阵兵",
        "pincurchin" to "啪嚓海胆",
        "snom" to "雪吞虫",
        "frosmoth" to "雪绒蛾",
        "stonjourner" to "巨石丁",
        "eiscue" to "冰砌鹅",
        "indeedee" to "爱管侍",
        "morpeko" to "莫鲁贝可",
        "cufant" to "铜象",
        "copperajah" to "大王铜象",
        "dracozolt" to "雷鸟龙",
        "arctozolt" to "雷鸟海兽",
        "dracovish" to "鳃鱼龙",
        "arctovish" to "鳃鱼海兽",
        "duraludon" to "铝钢龙",
        "dreepy" to "多龙梅西亚",
        "drakloak" to "多龙奇",
        "dragapult" to "多龙巴鲁托",
        "zacian" to "苍响",
        "zamazenta" to "藏玛然特",
        "eternatus" to "无极汰那",
        "kubfu" to "熊徒弟",
        "urshifu" to "武道熊师",
        "zarude" to "萨戮德",
        "regieleki" to "雷吉艾勒奇",
        "regidrago" to "雷吉铎拉戈",
        "glastrier" to "雪暴马",
        "spectrier" to "灵幽马",
        "calyrex" to "蕾冠王",

        // ========== 第九世代 (906-1025) ==========
        "sprigatito" to "新叶喵",
        "floragato" to "蒂蕾喵",
        "meowscarada" to "魅力喵",
        "fuecoco" to "呆火鳄",
        "crocalor" to "炙烫鳄",
        "skeledirge" to "骨纹巨声鳄",
        "quaxly" to "润水鸭",
        "quaxwell" to "涌跃鸭",
        "quaquaval" to "狂欢浪舞鸭",
        "lechonk" to "爱吃豚",
        "oinkologne" to "飘香豚",
        "tarountula" to "团珠蛛",
        "spidops" to "操陷蛛",
        "nymble" to "豆蟋蟀",
        "lokix" to "烈腿蝗",
        "pawmi" to "布拨",
        "pawmo" to "布土拨",
        "pawmot" to "巴布土拨",
        "tandemaus" to "一对鼠",
        "maushold" to "一家鼠",
        "fidough" to "狗仔包",
        "dachsbun" to "麻花犬",
        "smoliv" to "迷你芙",
        "dolliv" to "奥利纽",
        "arboliva" to "奥利瓦",
        "squawkabilly" to "怒鹦哥",
        "nacli" to "盐石宝",
        "naclstack" to "盐石垒",
        "garganacl" to "盐石巨灵",
        "charcadet" to "炭小侍",
        "armarouge" to "红莲铠骑",
        "ceruledge" to "苍炎刃鬼",
        "tadbulb" to "光蚪仔",
        "bellibolt" to "电肚蛙",
        "wattrel" to "飞水跃鱼",
        "kilowattrel" to "大电海燕",
        "maschiff" to "偶叫獒",
        "mabosstiff" to "獒教父",
        "shroodle" to "涂标客",
        "grafaiai" to "涂标猿",
        "bramblin" to "纳噬草",
        "brambleghast" to "怖纳噬草",
        "toedscool" to "走鲸",
        "toedscruel" to "陆地鲸",
        "klawf" to "毛崖蟹",
        "capsakid" to "热辣娃",
        "scovillain" to "狠辣椒",
        "rellor" to "虫滚泥",
        "rabsca" to "虫甲圣",
        "flittle" to "飘飘雏",
        "espathra" to "超能艳鸵",
        "tinkatink" to "小锻匠",
        "tinkaton" to "巨锻匠",
        "wiglett" to "海地鼠",
        "wugtrio" to "三海地鼠",
        "bombirdier" to "下石鸟",
        "finizen" to "波普海豚",
        "palafin" to "海豚侠",
        "varoom" to "普隆隆",
        "revavroom" to "普隆隆姆",
        "cyclizar" to "摩托蜥",
        "orthworm" to "拖拖蚓",
        "glimmet" to "晶光芽",
        "glimmora" to "晶光花",
        "greavard" to "墓仔狗",
        "houndstone" to "墓扬犬",
        "flamigo" to "火烈鸟",
        "cetoddle" to "海豚球",
        "cetitan" to "浩大鲸",
        "veluza" to "轻身鳕",
        "dondozo" to "吃吼霸",
        "tatsugiri" to "米立龙",
        "annihilape" to "弃世猴",
        "clodsire" to "土王",
        "farigiraf" to "长颈鹿",
        "dudunsparce" to "土龙节节",
        "kingambit" to "仆刀将军",
        "great_tusk" to "雄伟牙",
        "scream_tail" to "吼叫尾",
        "brute_bonnet" to "猛恶菇",
        "flutter_mane" to "振翼发",
        "slither_wing" to "爬地翅",
        "sandy_shocks" to "沙铁皮",
        "iron_treads" to "铁辙迹",
        "iron_bundle" to "铁包袱",
        "iron_hands" to "铁臂膀",
        "iron_jugulis" to "铁脖颈",
        "iron_moth" to "铁毒蛾",
        "iron_thorns" to "铁荆棘",
        "frigibax" to "凉脊龙",
        "arctibax" to "冻脊龙",
        "baxcalibur" to "戟脊龙",
        "gimmighoul" to "索财灵",
        "gholdengo" to "赛富豪",
        "wo_chien" to "古简蜗",
        "chien_pao" to "古剑豹",
        "ting_lu" to "古鼎鹿",
        "chi_yu" to "古玉鱼",
        "roaring_moon" to "轰鸣月",
        "iron_valiant" to "铁勇者",
        "koraidon" to "故勒顿",
        "miraidon" to "密勒顿",
        "walking_wake" to "踏浪",
        "iron_leaves" to "铁斑叶",
        "dipplin" to "裹蜜虫",
        "poltchageist" to "斯魅茶",
        "sinistcha" to "来悲粗茶",
        "okidogi" to "够赞狗",
        "munkidori" to "愿增猿",
        "fezandipiti" to "吉雉鸡",
        "ogerpon" to "厄诡椪",
        "archaludon" to "铝钢桥龙",
        "hydrapple" to "蜜集大蛇",
        "gouging_fire" to "破空焰",
        "raging_bolt" to "奔雷号",
        "iron_boulder" to "铁磐岩",
        "iron_crown" to "铁头壳",
        "terapagos" to "太乐巴戈斯",
        "pecharunt" to "桃歹郎"
    )

    /**
     * 获取精灵的中文名称
     * @param englishName 英文名称（小写）
     * @return 中文名称，如果没有映射则返回格式化的英文名称
     */
    fun getChineseName(englishName: String): String {
        val lowerName = englishName.lowercase()
        return pokemonNameMap[lowerName] ?: formatEnglishName(englishName)
    }

    /**
     * 格式化英文精灵名称
     * 将下划线替换为空格，首字母大写
     */
    private fun formatEnglishName(englishName: String): String {
        return englishName.replace("_", " ").split(" ").joinToString(" ") { word ->
            word.lowercase().replaceFirstChar {
                if (it.isLowerCase()) it.titlecase() else it.toString()
            }
        }
    }

    /**
     * 检查是否有该精灵的中文名称
     */
    fun hasChineseName(englishName: String): Boolean {
        return pokemonNameMap.containsKey(englishName.lowercase())
    }

    /**
     * 获取所有支持的精灵列表
     */
    fun getAllSupportedPokemon(): Set<String> {
        return pokemonNameMap.keys
    }

    /**
     * 获取映射数量
     */
    fun getMappingCount(): Int {
        return pokemonNameMap.size
    }
}
