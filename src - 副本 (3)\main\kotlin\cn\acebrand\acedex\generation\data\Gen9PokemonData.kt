/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation.data

import cn.acebrand.acedex.generation.PokemonData

/**
 * 第九代精灵数据 (帕底亚地区)
 * 包含全国图鉴编号 906-1025 的精灵数据
 */
object Gen9PokemonData {
    val data = mapOf(
        // 906-914 (帕底亚御三家及其进化)
        "sprigatito" to PokemonData(906, "草", "草猫精灵", "male", "花草草原, 魔法森林"),
        "floragato" to PokemonData(907, "草", "草猫精灵", "male", "花草草原, 魔法森林"),
        "meowscarada" to PokemonData(908, "草/恶", "魔术师精灵", "male", "花草草原, 魔法森林"),
        "fuecoco" to PokemonData(909, "火", "火鳄精灵", "male", "恶地"),
        "crocalor" to PokemonData(910, "火", "火鳄精灵", "male", "恶地"),
        "skeledirge" to PokemonData(911, "火/幽灵", "歌手精灵", "male", "恶地"),
        "quaxly" to PokemonData(912, "水", "鸭子精灵", "male", "森林, 草原, 河流, 淡水, 丛林"),
        "quaxwell" to PokemonData(913, "水", "练习精灵", "male", "森林, 草原, 河流, 淡水, 丛林"),
        "quaquaval" to PokemonData(914, "水/格斗", "舞者精灵", "male", "森林, 草原, 河流, 淡水, 丛林"),

        // 915-924
        "lechonk" to PokemonData(915, "一般", "猪精灵", "male", "蘑菇岛, 温带, 沼泽"),
        "oinkologne" to PokemonData(916, "一般", "猪精灵", "male", "蘑菇岛, 温带, 沼泽"),
        "tarountula" to PokemonData(917, "虫", "弦球精灵", "male", "森林"),
        "spidops" to PokemonData(918, "虫", "陷阱精灵", "male", "森林"),
        "nymble" to PokemonData(919, "虫", "蚱蜢精灵", "male", "草原"),
        "lokix" to PokemonData(920, "虫/恶", "蚱蜢精灵", "male", "草原"),
        "pawmi" to PokemonData(921, "电", "鼠精灵", "male", "平原"),
        "pawmo" to PokemonData(922, "电/格斗", "鼠精灵", "male", "平原"),
        "pawmot" to PokemonData(923, "电/格斗", "手精灵", "male", "平原"),
        "tandemaus" to PokemonData(924, "一般", "夫妇精灵", "male", "温带, 城市, 村庄"),

        // 925-934
        "maushold" to PokemonData(925, "一般", "家族精灵", "male", "温带, 城市, 村庄"),
        "fidough" to PokemonData(926, "妖精", "小狗精灵", "male", "城市, 村庄"),
        "dachsbun" to PokemonData(927, "妖精", "狗精灵", "male", "城市, 村庄"),
        "smoliv" to PokemonData(928, "草/一般", "橄榄精灵", "male", "地中海"),
        "dolliv" to PokemonData(929, "草/一般", "橄榄精灵", "male", "地中海"),
        "arboliva" to PokemonData(930, "草/一般", "橄榄精灵", "male", "地中海"),
        "squawkabilly" to PokemonData(931, "一般/飞行", "鹦鹉精灵", "male", "丛林, 热带草原, 热带岛屿, 城市, 村庄"),
        "nacli" to PokemonData(932, "岩石", "岩盐精灵", "male", "恶地"),
        "naclstack" to PokemonData(933, "岩石", "岩盐精灵", "male", "恶地"),
        "garganacl" to PokemonData(934, "岩石", "岩盐精灵", "male", "恶地"),

        // 935-944
        "charcadet" to PokemonData(935, "火", "幼炭精灵", "male", "下界"),
        "armarouge" to PokemonData(936, "火/超能力", "战士精灵", "male", "下界"),
        "ceruledge" to PokemonData(937, "火/幽灵", "剑客精灵", "male", "下界"),
        "tadbulb" to PokemonData(938, "电", "蝌蚪精灵", "male", "沼泽"),
        "bellibolt" to PokemonData(939, "电", "电蛙精灵", "male", "沼泽"),
        "wattrel" to PokemonData(940, "电/飞行", "暴风雨精灵", "male", "热带岛屿, 海洋, 海岸"),
        "kilowattrel" to PokemonData(941, "电/飞行", "海鸟精灵", "male", "热带岛屿, 海洋, 海岸"),
        "maschiff" to PokemonData(942, "恶", "小狗精灵", "male", "城市, 村庄"),
        "mabosstiff" to PokemonData(943, "恶", "老大精灵", "male", "城市, 村庄"),
        "shroodle" to PokemonData(944, "毒/一般", "鼩鼱精灵", "male", "丛林, 热带岛屿"),

        // 945-954
        "grafaiai" to PokemonData(945, "毒/一般", "猴精灵", "male", "丛林, 热带岛屿"),
        "bramblin" to PokemonData(946, "草/幽灵", "荆棘球精灵", "male", "沙漠"),
        "brambleghast" to PokemonData(947, "草/幽灵", "荆棘精灵", "male", "沙漠"),
        "toedscool" to PokemonData(948, "地面/草", "林地精灵", "male", "森林"),
        "toedscruel" to PokemonData(949, "地面/草", "林地精灵", "male", "森林"),
        "klawf" to PokemonData(950, "岩石", "伏击蟹精灵", "male", "恶地"),
        "capsakid" to PokemonData(951, "草", "辣椒精灵", "male", "沙漠"),
        "scovillain" to PokemonData(952, "草/火", "辣椒精灵", "male", "沙漠"),
        "rellor" to PokemonData(953, "虫", "滚球精灵", "male", "沙漠"),
        "rabsca" to PokemonData(954, "虫/超能力", "滚球精灵", "male", "沙漠"),

        // 955-964
        "flittle" to PokemonData(955, "超能力", "鸵鸟精灵", "male", "干旱"),
        "espathra" to PokemonData(956, "超能力", "鸵鸟精灵", "male", "干旱"),
        "tinkatink" to PokemonData(957, "妖精/钢", "锤子精灵", "female", "地下"),
        "tinkaton" to PokemonData(958, "妖精/钢", "锤子精灵", "female", "地下"),
        "tinkaton" to PokemonData(959, "妖精/钢", "锤子精灵", "female", "地下"),
        "wiglett" to PokemonData(960, "水", "花园鳗精灵", "male", "海滩, 温暖海洋"),
        "wugtrio" to PokemonData(961, "水", "花园鳗精灵", "male", "海滩, 温暖海洋"),
        "bombirdier" to PokemonData(962, "飞行/恶", "运输鸟精灵", "male", "山地"),
        "finizen" to PokemonData(963, "水", "海豚精灵", "male", "海洋"),
        "palafin" to PokemonData(964, "水", "海豚精灵", "male", "海洋"),

        // 965-974
        "varoom" to PokemonData(965, "钢/毒", "引擎精灵", "male", "恶地, 城市"),
        "revavroom" to PokemonData(966, "钢/毒", "多缸精灵", "male", "恶地, 城市"),
        "cyclizar" to PokemonData(967, "龙/一般", "坐骑精灵", "male", "城市"),
        "orthworm" to PokemonData(968, "钢", "蚯蚓精灵", "male", "地下"),
        "glimmet" to PokemonData(969, "岩石/毒", "矿石精灵", "male", "地下"),
        "glimmora" to PokemonData(970, "岩石/毒", "矿石精灵", "male", "地下"),
        "greavard" to PokemonData(971, "幽灵", "小狗精灵", "male", "阴森森林"),
        "houndstone" to PokemonData(972, "幽灵", "幽灵犬精灵", "male", "阴森森林"),
        "flamigo" to PokemonData(973, "飞行/格斗", "同步精灵", "male", "丛林, 热带草原, 沼泽, 热带岛屿"),
        "cetoddle" to PokemonData(974, "冰", "陆鲸精灵", "male", "冰冻海洋, 苔原"),

        // 975-984
        "cetitan" to PokemonData(975, "冰", "冰山精灵", "male", "冰冻海洋, 苔原"),
        "veluza" to PokemonData(976, "水/超能力", "切片精灵", "male", "海洋"),
        "dondozo" to PokemonData(977, "水", "大鲶鱼精灵", "male", "沼泽, 竹林, 森林, 丛林"),
        "tatsugiri" to PokemonData(978, "龙/水", "拟态精灵", "male", "沼泽, 竹林, 森林, 丛林"),
        "annihilape" to PokemonData(979, "格斗/幽灵", "愤怒猴精灵", "male", "丘陵, 丛林"),
        "clodsire" to PokemonData(980, "毒/地面", "毒鱼精灵", "male", "泥泞, 热带草原, 干旱"),
        "farigiraf" to PokemonData(981, "一般/超能力", "长颈精灵", "male", "热带草原"),
        "dudunsparce" to PokemonData(982, "一般", "土龙精灵", "male", "地下"),
        "kingambit" to PokemonData(983, "恶/钢", "大将精灵", "male", "山地"),
        "greattusk" to PokemonData(984, "地面/格斗", "悖谬精灵", "genderless", "传说区域"),

        // 985-994
        "screamtail" to PokemonData(985, "妖精/超能力", "悖谬精灵", "genderless", "传说区域"),
        "brutebonnet" to PokemonData(986, "草/恶", "悖谬精灵", "genderless", "传说区域"),
        "fluttermane" to PokemonData(987, "幽灵/妖精", "悖谬精灵", "genderless", "传说区域"),
        "slitherwing" to PokemonData(988, "虫/格斗", "悖谬精灵", "genderless", "传说区域"),
        "sandyshocks" to PokemonData(989, "电/地面", "悖谬精灵", "genderless", "传说区域"),
        "irontreads" to PokemonData(990, "地面/钢", "悖谬精灵", "genderless", "传说区域"),
        "ironbundle" to PokemonData(991, "冰/水", "悖谬精灵", "genderless", "传说区域"),
        "ironhands" to PokemonData(992, "格斗/电", "悖谬精灵", "genderless", "传说区域"),
        "ironjugulis" to PokemonData(993, "恶/飞行", "悖谬精灵", "genderless", "传说区域"),
        "ironmoth" to PokemonData(994, "火/毒", "悖谬精灵", "genderless", "传说区域"),

        // 995-1004
        "ironthorns" to PokemonData(995, "岩石/电", "悖谬精灵", "genderless", "传说区域"),
        "frigibax" to PokemonData(996, "龙/冰", "冰鳍精灵", "male", "洞穴"),
        "arctibax" to PokemonData(997, "龙/冰", "冰鳍精灵", "male", "洞穴"),
        "baxcalibur" to PokemonData(998, "龙/冰", "冰龙精灵", "male", "洞穴"),
        "gimmighoul" to PokemonData(999, "幽灵", "宝箱精灵", "genderless", "地下"),
        "gholdengo" to PokemonData(1000, "钢/幽灵", "硬币精灵", "genderless", "地下"),
        "wochien" to PokemonData(1001, "恶/草", "灾祸精灵", "genderless", "传说区域"),
        "chienpao" to PokemonData(1002, "恶/冰", "灾祸精灵", "genderless", "传说区域"),
        "tinglu" to PokemonData(1003, "恶/地面", "灾祸精灵", "genderless", "传说区域"),
        "chiyu" to PokemonData(1004, "恶/火", "灾祸精灵", "genderless", "传说区域"),

        // 1005-1014
        "roaringmoon" to PokemonData(1005, "龙/恶", "悖谬精灵", "genderless", "传说区域"),
        "ironvaliant" to PokemonData(1006, "妖精/格斗", "悖谬精灵", "genderless", "传说区域"),
        "koraidon" to PokemonData(1007, "格斗/龙", "悖谬精灵", "genderless", "传说区域"),
        "miraidon" to PokemonData(1008, "电/龙", "悖谬精灵", "genderless", "传说区域"),
        "walkingwake" to PokemonData(1009, "水/龙", "悖谬精灵", "genderless", "传说区域"),
        "ironleaves" to PokemonData(1010, "草/超能力", "悖谬精灵", "genderless", "传说区域"),
        "dipplin" to PokemonData(1011, "草/龙", "糖浆精灵", "male", "森林"),
        "poltchageist" to PokemonData(1012, "草/幽灵", "抹茶精灵", "genderless", "竹林"),
        "sinistcha" to PokemonData(1013, "草/幽灵", "抹茶精灵", "genderless", "竹林"),
        "okidogi" to PokemonData(1014, "毒/格斗", "忠犬精灵", "male", "传说区域"),

        // 1015-1025
        "munkidori" to PokemonData(1015, "毒/超能力", "报复猴精灵", "male", "传说区域"),
        "fezandipiti" to PokemonData(1016, "毒/妖精", "雉鸡精灵", "male", "传说区域"),
        "ogerpon" to PokemonData(1017, "草", "面具精灵", "female", "传说区域"),
        "archaludon" to PokemonData(1018, "钢/龙", "合金精灵", "male", "山地"),
        "hydrapple" to PokemonData(1019, "草/龙", "苹果九头龙精灵", "male", "森林"),
        "gougingfire" to PokemonData(1020, "火/龙", "悖谬精灵", "genderless", "传说区域"),
        "ragingbolt" to PokemonData(1021, "电/龙", "悖谬精灵", "genderless", "传说区域"),
        "ironboulder" to PokemonData(1022, "岩石/超能力", "悖谬精灵", "genderless", "传说区域"),
        "ironcrown" to PokemonData(1023, "钢/超能力", "悖谬精灵", "genderless", "传说区域"),
        "terapagos" to PokemonData(1024, "一般", "太晶精灵", "genderless", "传说区域"),
        "pecharunt" to PokemonData(1025, "毒/幽灵", "桃子精灵", "genderless", "传说区域")
    )
}
