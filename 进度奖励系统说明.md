# 精灵图鉴进度奖励系统

## 功能概述

新增了自定义进度奖励系统，支持：
1. **每个世代进度奖励** - 可配置多个进度节点（如25%、50%、75%）
2. **全世代进度奖励** - 基于总体收集进度的奖励系统

## 配置说明

### 世代进度奖励配置

每个世代现在支持两种奖励：

```yaml
rewards:
  generation:
    gen1:  # 第一世代示例
      # 100%完成奖励（原有功能）
      completion:
        commands:
          - "give {player} minecraft:diamond 1"
          - "give {player} minecraft:emerald 2"
        descriptions:
          - "§7• §b钻石 x1"
          - "§7• §a绿宝石 x2"
      
      # 进度奖励（新增功能）
      progress:
        25:  # 25%进度奖励
          commands:
            - "give {player} minecraft:iron_ingot 5"
            - "give {player} minecraft:coal 10"
          descriptions:
            - "§7• §f铁锭 x5"
            - "§7• §8煤炭 x10"
        
        50:  # 50%进度奖励
          commands:
            - "give {player} minecraft:gold_ingot 3"
          descriptions:
            - "§7• §6金锭 x3"
        
        75:  # 75%进度奖励
          commands:
            - "give {player} minecraft:diamond 1"
          descriptions:
            - "§7• §b钻石 x1"
```

### 全世代进度奖励配置

```yaml
rewards:
  # 全世代进度奖励（新增功能）
  overall-progress:
    enabled: true
    
    25:  # 全世代总进度25%奖励
      name: "图鉴新手"
      description: "全世代总进度达到25%"
      commands:
        - "give {player} minecraft:diamond 5"
        - "give {player} minecraft:emerald 10"
        - "tellraw {player} {\"text\":\"恭喜！全世代收集进度达到25%！\",\"color\":\"green\",\"bold\":true}"
      descriptions:
        - "§7• §b钻石 x5"
        - "§7• §a绿宝石 x10"
        - "§7• §a图鉴新手称号"
    
    50:  # 全世代总进度50%奖励
      name: "图鉴专家"
      description: "全世代总进度达到50%"
      commands:
        - "give {player} minecraft:diamond 15"
        - "give {player} minecraft:netherite_ingot 3"
      descriptions:
        - "§7• §b钻石 x15"
        - "§7• §5下界合金锭 x3"
        - "§7• §6图鉴专家称号"
```

## 功能特点

### 1. 灵活配置
- 可以为每个世代配置任意数量的进度节点
- 每个进度节点可以配置多个奖励命令
- 支持自定义奖励描述和称号

### 2. 自动发放
- 玩家捕获精灵时自动检查进度
- 达到进度节点时自动发放奖励
- 防止重复领取同一进度奖励

### 3. GUI显示
- 在世代详情界面显示进度奖励状态
- 在总体进度界面显示全世代进度奖励
- 清晰标识已领取、可领取、未达成状态

### 4. 数据持久化
- 奖励领取状态保存到本地文件
- 服务器重启后数据不丢失
- 支持玩家数据备份和恢复

## 使用示例

### 添加新的进度节点

如果想为第一世代添加10%和90%的进度奖励：

```yaml
gen1:
  progress:
    10:  # 10%进度奖励
      commands:
        - "give {player} minecraft:bread 5"
      descriptions:
        - "§7• §6面包 x5"
    
    90:  # 90%进度奖励
      commands:
        - "give {player} minecraft:diamond 2"
        - "give {player} minecraft:enchanted_golden_apple 1"
      descriptions:
        - "§7• §b钻石 x2"
        - "§7• §6附魔金苹果 x1"
```

### 自定义全世代进度奖励

```yaml
overall-progress:
  enabled: true
  
  10:  # 全世代10%进度
    name: "图鉴入门者"
    description: "开始你的精灵收集之旅"
    commands:
      - "give {player} minecraft:iron_ingot 10"
    descriptions:
      - "§7• §f铁锭 x10"
      - "§7• §7图鉴入门者称号"
```

## 注意事项

1. **进度百分比必须是整数**（如25、50、75）
2. **奖励只能领取一次**，不会重复发放
3. **配置修改后需要重启服务器**或使用reload命令
4. **commands支持所有Minecraft命令**，包括give、tellraw等
5. **占位符{player}会被替换为玩家名称**

## 兼容性

- 完全兼容原有的100%完成奖励系统
- 不影响现有的世代奖励配置
- 可以逐步为各个世代添加进度奖励
