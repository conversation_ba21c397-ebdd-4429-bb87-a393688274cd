package cn.acebrand.acebiomespawner.listener

import cn.acebrand.acebiomespawner.AceBiomeSpawner
import org.bukkit.ChatColor
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.player.PlayerJoinEvent
import org.bukkit.event.player.PlayerQuitEvent
import org.bukkit.scheduler.BukkitRunnable

/**
 * 玩家事件监听器
 */
class PlayerEventListener : Listener {
    
    private val logger = AceBiomeSpawner.INSTANCE.logger
    
    /**
     * 玩家加入服务器事件
     */
    @EventHandler
    fun onPlayerJoin(event: PlayerJoinEvent) {
        val player = event.player
        logger.info("玩家 ${player.name} 加入了服务器")
        
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        if (config.enableAnnouncement && config.enabled) {
            // 延迟发送欢迎消息，避免与其他插件冲突
            object : BukkitRunnable() {
                override fun run() {
                    sendWelcomeMessage(player)
                }
            }.runTaskLater(AceBiomeSpawner.INSTANCE, 40L) // 延迟2秒（40 ticks）
        }
    }
    
    /**
     * 玩家离开服务器事件
     */
    @EventHandler
    fun onPlayerLeave(event: PlayerQuitEvent) {
        val player = event.player
        logger.info("玩家 ${player.name} 离开了服务器")
    }
    
    /**
     * 发送欢迎消息
     */
    private fun sendWelcomeMessage(player: Player) {
        try {
            val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
            
            if (!config.enabled) {
                return
            }
            
            val welcomeMessage = buildWelcomeMessage()
            player.sendMessage(welcomeMessage)
            
        } catch (e: Exception) {
            logger.severe("发送欢迎消息时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 构建欢迎消息
     */
    private fun buildWelcomeMessage(): String {
        val config = AceBiomeSpawner.CONFIG_MANAGER.getConfig()
        val message = StringBuilder()
        
        // 添加前缀
        if (config.announcementPrefix.isNotEmpty()) {
            message.append("${config.announcementPrefix} ")
        }
        
        // 添加欢迎内容
        message.append("${ChatColor.GREEN}欢迎来到服务器！${ChatColor.RESET}")
        message.append(" ${ChatColor.WHITE}生物群系精灵生成系统已启用，${ChatColor.RESET}")
        message.append("${ChatColor.YELLOW}每 ${config.spawnInterval} 秒${ChatColor.RESET}")
        message.append(" ${ChatColor.WHITE}会在随机玩家附近生成精灵！${ChatColor.RESET}")
        
        return message.toString()
    }
}
