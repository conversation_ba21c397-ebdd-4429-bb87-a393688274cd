handler=Block #GG, types=[Ljava/io/IOException;], range=[Block #AG, Block #AF]
handler=Block #GK, types=[Ljava/lang/IllegalAccessException;], range=[Block #AJ, Block #AI]
handler=Block #GO, types=[Ljava/lang/IllegalAccessException;], range=[Block #AM, Block #AL]
handler=Block #GS, types=[Ljava/lang/IllegalAccessException;], range=[Block #AP, Block #AO]
handler=Block #GW, types=[Ljava/lang/RuntimeException;], range=[Block #AS, Block #AR]
handler=Block #HA, types=[Ljava/lang/IllegalAccessException;], range=[Block #AV, Block #AU]
handler=Block #HE, types=[Ljava/io/IOException;], range=[Block #AY, Block #AX]
handler=Block #HI, types=[Ljava/lang/RuntimeException;], range=[Block #BB, Block #BA]
handler=Block #HM, types=[Ljava/lang/IllegalAccessException;], range=[Block #BE, Block #BD]
handler=Block #HQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #BH, Block #BG]
handler=Block #HU, types=[Ljava/lang/RuntimeException;], range=[Block #BK, Block #BJ]
handler=Block #HY, types=[Ljava/lang/RuntimeException;], range=[Block #BN, Block #BM]
handler=Block #IC, types=[Ljava/lang/RuntimeException;], range=[Block #BQ, Block #BP]
handler=Block #IG, types=[Ljava/io/IOException;], range=[Block #BT, Block #BS]
handler=Block #IK, types=[Ljava/lang/RuntimeException;], range=[Block #BW, Block #BV]
handler=Block #IO, types=[Ljava/io/IOException;], range=[Block #BZ, Block #BY]
handler=Block #IS, types=[Ljava/lang/IllegalAccessException;], range=[Block #CC, Block #CB]
handler=Block #IW, types=[Ljava/lang/RuntimeException;], range=[Block #CF, Block #CE]
===#Block A(size=4, flags=1)===
   0. lvar46 = {991863582 ^ {973541794 ^ 240084018}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. lvar46 = {226674685 ^ lvar46};
      -> Immediate #A -> #B
===#Block B(size=6, flags=0)===
   0. lvar3 = lvar1;
   1. lvar5 = lvar3;
   2. lvar6 = lvar5;
   3. lvar7 = lvar6.hashCode();
   4. svar48 = {lvar7 ^ lvar46};
   5. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(svar48)) {
      case 45483012:
      	 goto	#FU
      case 45483013:
      	 goto	#FV
      case 45483059:
      	 goto	#FW
      case 45483064:
      	 goto	#FX
      case 45483065:
      	 goto	#FY
      case 45483068:
      	 goto	#FZ
      case 45483070:
      	 goto	#GB
      case 45483071:
      	 goto	#GD
      default:
      	 goto	#GE
   }
      -> Switch[45483012] #B -> #FU
      -> Switch[45483065] #B -> #FY
      -> Switch[45483064] #B -> #FX
      -> Switch[45483068] #B -> #FZ
      -> Switch[45483070] #B -> #GB
      -> Switch[45483013] #B -> #FV
      -> Switch[45483071] #B -> #GD
      -> DefaultSwitch #B -> #GE
      -> Switch[45483059] #B -> #FW
      <- Immediate #A -> #B
===#Block FW(size=2, flags=10100)===
   0. lvar46 = {1907670134 ^ lvar46};
   1. goto AA
      -> UnconditionalJump[GOTO] #FW -> #AA
      <- Switch[45483059] #B -> #FW
===#Block AA(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar34 = lvar5;
   2. lvar44 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.tddtwlkzeopxwot(), lvar46);
   3. lvar35 = lvar34.equals(lvar44);
   4. if (lvar35 != {1935715589 ^ lvar46})
      goto FB
   5. lvar46 = {1489213461 ^ lvar46};
      -> ConditionalJump[IF_ICMPNE] #AA -> #FB
      -> Immediate #AA -> #AB
      <- UnconditionalJump[GOTO] #FW -> #AA
===#Block AB(size=1, flags=0)===
   0. goto EW
      -> UnconditionalJump[GOTO] #AB -> #EW
      <- Immediate #AA -> #AB
===#Block EW(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 185101630);
   1. goto CF
      -> UnconditionalJump[GOTO] #EW -> #CF
      <- UnconditionalJump[GOTO] #AB -> #EW
===#Block CF(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 256629362)
      goto CE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CF -> #CE
      -> TryCatch range: [CF...CE] -> IW ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #EW -> #CF
===#Block CE(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [CF...CE] -> IW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #CF -> #CE
===#Block IW(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -2021636494:
      	 goto	#IY
      case 89833841:
      	 goto	#IX
      default:
      	 goto	#IZ
   }
      -> Switch[-2021636494] #IW -> #IY
      -> Switch[89833841] #IW -> #IX
      -> DefaultSwitch #IW -> #IZ
      <- TryCatch range: [CF...CE] -> IW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [CF...CE] -> IW ([Ljava/lang/RuntimeException;])
===#Block IZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #IW -> #IZ
===#Block IX(size=2, flags=10100)===
   0. lvar46 = {1157565384 ^ lvar46};
   1. goto CG
      -> UnconditionalJump[GOTO] #IX -> #CG
      <- Switch[89833841] #IW -> #IX
===#Block IY(size=2, flags=10100)===
   0. lvar46 = {883186472 ^ lvar46};
   1. goto CG
      -> UnconditionalJump[GOTO] #IY -> #CG
      <- Switch[-2021636494] #IW -> #IY
===#Block CG(size=2, flags=0)===
   0. _consume(catch());
   1. goto DZ
      -> UnconditionalJump[GOTO] #CG -> #DZ
      <- UnconditionalJump[GOTO] #IX -> #CG
      <- UnconditionalJump[GOTO] #IY -> #CG
===#Block DZ(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 40044249:
      	 goto	#EA
      case 209596718:
      	 goto	#AC
      case 804633422:
      	 goto	#CI
      case 831443186:
      	 goto	#DZ
      default:
      	 goto	#CI
   }
      -> Switch[804633422] #DZ -> #CI
      -> DefaultSwitch #DZ -> #CI
      -> Switch[209596718] #DZ -> #AC
      -> Immediate #DZ -> #EA
      -> Switch[831443186] #DZ -> #DZ
      -> Switch[40044249] #DZ -> #EA
      <- Switch[831443186] #DZ -> #DZ
      <- UnconditionalJump[GOTO] #CG -> #DZ
===#Block EA(size=2, flags=100)===
   0. lvar46 = {1518622928 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #EA -> #AC
      <- Immediate #DZ -> #EA
      <- Switch[40044249] #DZ -> #EA
===#Block FB(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 260783193:
      	 goto	#FC
      case 989788360:
      	 goto	#FB
      case 1256865767:
      	 goto	#CI
      case 1365265971:
      	 goto	#CK
      default:
      	 goto	#CI
   }
      -> Immediate #FB -> #FC
      -> Switch[1365265971] #FB -> #CK
      -> DefaultSwitch #FB -> #CI
      -> Switch[989788360] #FB -> #FB
      -> Switch[1256865767] #FB -> #CI
      -> Switch[260783193] #FB -> #FC
      <- ConditionalJump[IF_ICMPNE] #AA -> #FB
      <- Switch[989788360] #FB -> #FB
===#Block FC(size=2, flags=100)===
   0. lvar46 = {116376559 ^ lvar46};
   1. goto CK
      -> UnconditionalJump[GOTO] #FC -> #CK
      <- Immediate #FB -> #FC
      <- Switch[260783193] #FB -> #FC
===#Block CK(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1401194669)
      goto FK
   1. goto EO
      -> UnconditionalJump[GOTO] #CK -> #EO
      -> ConditionalJump[IF_ICMPEQ] #CK -> #FK
      <- UnconditionalJump[GOTO] #FC -> #CK
      <- Switch[1365265971] #FB -> #CK
===#Block FK(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 63279978:
      	 goto	#FL
      case 587353169:
      	 goto	#FK
      case 939383778:
      	 goto	#AD
      case 1121556300:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Switch[63279978] #FK -> #FL
      -> Immediate #FK -> #FL
      -> Switch[1121556300] #FK -> #CI
      -> Switch[587353169] #FK -> #FK
      -> DefaultSwitch #FK -> #CI
      -> Switch[939383778] #FK -> #AD
      <- ConditionalJump[IF_ICMPEQ] #CK -> #FK
      <- Switch[587353169] #FK -> #FK
===#Block FL(size=2, flags=100)===
   0. lvar46 = {677448506 ^ lvar46};
   1. goto AD
      -> UnconditionalJump[GOTO] #FL -> #AD
      <- Switch[63279978] #FK -> #FL
      <- Immediate #FK -> #FL
===#Block AD(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar36 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar36.getULTRA_BALL();
   3. goto EC
      -> UnconditionalJump[GOTO] #AD -> #EC
      <- UnconditionalJump[GOTO] #FL -> #AD
      <- Switch[939383778] #FK -> #AD
===#Block EC(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 100513965:
      	 goto	#ED
      case 477102644:
      	 goto	#AY
      case 605709020:
      	 goto	#CI
      case 1450464833:
      	 goto	#EC
      default:
      	 goto	#CI
   }
      -> Immediate #EC -> #ED
      -> Switch[1450464833] #EC -> #EC
      -> Switch[100513965] #EC -> #ED
      -> Switch[477102644] #EC -> #AY
      -> Switch[605709020] #EC -> #CI
      -> DefaultSwitch #EC -> #CI
      <- Switch[1450464833] #EC -> #EC
      <- UnconditionalJump[GOTO] #AD -> #EC
===#Block ED(size=2, flags=100)===
   0. lvar46 = {1289784108 ^ lvar46};
   1. goto AY
      -> UnconditionalJump[GOTO] #ED -> #AY
      <- Immediate #EC -> #ED
      <- Switch[100513965] #EC -> #ED
===#Block AY(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 17628678)
      goto AX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AY -> #AX
      -> TryCatch range: [AY...AX] -> HE ([Ljava/io/IOException;])
      <- Switch[477102644] #EC -> #AY
      <- UnconditionalJump[GOTO] #ED -> #AY
===#Block AX(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AY...AX] -> HE ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AY -> #AX
===#Block HE(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -2005436448:
      	 goto	#HF
      case -545695862:
      	 goto	#HG
      default:
      	 goto	#HH
   }
      -> DefaultSwitch #HE -> #HH
      -> Switch[-545695862] #HE -> #HG
      -> Switch[-2005436448] #HE -> #HF
      <- TryCatch range: [AY...AX] -> HE ([Ljava/io/IOException;])
      <- TryCatch range: [AY...AX] -> HE ([Ljava/io/IOException;])
===#Block HF(size=2, flags=10100)===
   0. lvar46 = {312257002 ^ lvar46};
   1. goto AZ
      -> UnconditionalJump[GOTO] #HF -> #AZ
      <- Switch[-2005436448] #HE -> #HF
===#Block HG(size=2, flags=10100)===
   0. lvar46 = {1484615143 ^ lvar46};
   1. goto AZ
      -> UnconditionalJump[GOTO] #HG -> #AZ
      <- Switch[-545695862] #HE -> #HG
===#Block AZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto EJ
      -> UnconditionalJump[GOTO] #AZ -> #EJ
      <- UnconditionalJump[GOTO] #HF -> #AZ
      <- UnconditionalJump[GOTO] #HG -> #AZ
===#Block EJ(size=2, flags=10100)===
   0. lvar46 = {273679329 ^ lvar46};
   1. goto AE
      -> UnconditionalJump[GOTO] #EJ -> #AE
      <- UnconditionalJump[GOTO] #AZ -> #EJ
===#Block HH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #HE -> #HH
===#Block EO(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 63279978:
      	 goto	#EP
      case 332917395:
      	 goto	#CI
      case 1156201438:
      	 goto	#EO
      case 1709750851:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> DefaultSwitch #EO -> #CI
      -> Switch[332917395] #EO -> #CI
      -> Switch[63279978] #EO -> #EP
      -> Switch[1156201438] #EO -> #EO
      -> Immediate #EO -> #EP
      <- UnconditionalJump[GOTO] #CK -> #EO
      <- Switch[1156201438] #EO -> #EO
===#Block EP(size=2, flags=100)===
   0. lvar46 = {835999769 ^ lvar46};
   1. goto CI
      -> UnconditionalJump[GOTO] #EP -> #CI
      <- Switch[63279978] #EO -> #EP
      <- Immediate #EO -> #EP
===#Block GE(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 44487311:
      	 goto	#GF
      case 150495022:
      	 goto	#CI
      case 336413150:
      	 goto	#GE
      case 350610228:
      	 goto	#AC
      default:
      	 goto	#CI
   }
      -> Immediate #GE -> #GF
      -> Switch[44487311] #GE -> #GF
      -> Switch[350610228] #GE -> #AC
      -> Switch[150495022] #GE -> #CI
      -> DefaultSwitch #GE -> #CI
      -> Switch[336413150] #GE -> #GE
      <- DefaultSwitch #B -> #GE
      <- Switch[336413150] #GE -> #GE
===#Block GF(size=2, flags=100)===
   0. lvar46 = {1006901829 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #GF -> #AC
      <- Immediate #GE -> #GF
      <- Switch[44487311] #GE -> #GF
===#Block GD(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1375055811);
   1. goto I
      -> UnconditionalJump[GOTO] #GD -> #I
      <- Switch[45483071] #B -> #GD
===#Block I(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar15 = lvar5;
   2. lvar38 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.hgkdmuiepnqekqg(), lvar46);
   3. lvar16 = lvar15.equals(lvar38);
   4. if (lvar16 != {1394720432 ^ lvar46})
      goto FM
   5. lvar46 = {1098996502 ^ lvar46};
      -> ConditionalJump[IF_ICMPNE] #I -> #FM
      -> Immediate #I -> #J
      <- UnconditionalJump[GOTO] #GD -> #I
===#Block J(size=1, flags=0)===
   0. goto CX
      -> UnconditionalJump[GOTO] #J -> #CX
      <- Immediate #I -> #J
===#Block CX(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1301028583);
   1. goto AP
      -> UnconditionalJump[GOTO] #CX -> #AP
      <- UnconditionalJump[GOTO] #J -> #CX
===#Block AP(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 140999137)
      goto AO
   1. throw nullconst;
      -> TryCatch range: [AP...AO] -> GS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AP -> #AO
      <- UnconditionalJump[GOTO] #CX -> #AP
===#Block AO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AP...AO] -> GS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AP -> #AO
===#Block GS(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -110634486:
      	 goto	#GT
      case 1546296690:
      	 goto	#GU
      default:
      	 goto	#GV
   }
      -> Switch[-110634486] #GS -> #GT
      -> Switch[1546296690] #GS -> #GU
      -> DefaultSwitch #GS -> #GV
      <- TryCatch range: [AP...AO] -> GS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AP...AO] -> GS ([Ljava/lang/IllegalAccessException;])
===#Block GV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #GS -> #GV
===#Block GU(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1141781257);
   1. goto AQ
      -> UnconditionalJump[GOTO] #GU -> #AQ
      <- Switch[1546296690] #GS -> #GU
===#Block GT(size=2, flags=10100)===
   0. lvar46 = {1353161702 ^ lvar46};
   1. goto AQ
      -> UnconditionalJump[GOTO] #GT -> #AQ
      <- Switch[-110634486] #GS -> #GT
===#Block AQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto DT
      -> UnconditionalJump[GOTO] #AQ -> #DT
      <- UnconditionalJump[GOTO] #GU -> #AQ
      <- UnconditionalJump[GOTO] #GT -> #AQ
===#Block DT(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 236848328:
      	 goto	#DU
      case 1825911774:
      	 goto	#DT
      case 1938649939:
      	 goto	#CI
      case 2081810626:
      	 goto	#AC
      default:
      	 goto	#CI
   }
      -> Immediate #DT -> #DU
      -> Switch[236848328] #DT -> #DU
      -> DefaultSwitch #DT -> #CI
      -> Switch[1938649939] #DT -> #CI
      -> Switch[2081810626] #DT -> #AC
      -> Switch[1825911774] #DT -> #DT
      <- UnconditionalJump[GOTO] #AQ -> #DT
      <- Switch[1825911774] #DT -> #DT
===#Block DU(size=2, flags=100)===
   0. lvar46 = {828074897 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #DU -> #AC
      <- Immediate #DT -> #DU
      <- Switch[236848328] #DT -> #DU
===#Block FM(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1225087374);
   1. goto CH
      -> UnconditionalJump[GOTO] #FM -> #CH
      <- ConditionalJump[IF_ICMPNE] #I -> #FM
===#Block CH(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -785958416)
      goto FS
   1. goto EE
      -> ConditionalJump[IF_ICMPEQ] #CH -> #FS
      -> UnconditionalJump[GOTO] #CH -> #EE
      <- UnconditionalJump[GOTO] #FM -> #CH
===#Block EE(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 44538412:
      	 goto	#EF
      case 369102321:
      	 goto	#CI
      case 939883534:
      	 goto	#EE
      case 1828231202:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Switch[44538412] #EE -> #EF
      -> Immediate #EE -> #EF
      -> Switch[939883534] #EE -> #EE
      -> Switch[369102321] #EE -> #CI
      -> DefaultSwitch #EE -> #CI
      <- Switch[939883534] #EE -> #EE
      <- UnconditionalJump[GOTO] #CH -> #EE
===#Block EF(size=2, flags=100)===
   0. lvar46 = {1585435085 ^ lvar46};
   1. goto CI
      -> UnconditionalJump[GOTO] #EF -> #CI
      <- Switch[44538412] #EE -> #EF
      <- Immediate #EE -> #EF
===#Block FS(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 71676662);
   1. goto K
      -> UnconditionalJump[GOTO] #FS -> #K
      <- ConditionalJump[IF_ICMPEQ] #CH -> #FS
===#Block K(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar17.getLUXURY_BALL();
   3. goto EI
      -> UnconditionalJump[GOTO] #K -> #EI
      <- UnconditionalJump[GOTO] #FS -> #K
===#Block EI(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1080071097);
   1. goto BK
      -> UnconditionalJump[GOTO] #EI -> #BK
      <- UnconditionalJump[GOTO] #K -> #EI
===#Block BK(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 102968955)
      goto BJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BK -> #BJ
      -> TryCatch range: [BK...BJ] -> HU ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #EI -> #BK
===#Block BJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BK...BJ] -> HU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BK -> #BJ
===#Block HU(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -267504758:
      	 goto	#HV
      case -146528871:
      	 goto	#HW
      default:
      	 goto	#HX
   }
      -> Switch[-267504758] #HU -> #HV
      -> DefaultSwitch #HU -> #HX
      -> Switch[-146528871] #HU -> #HW
      <- TryCatch range: [BK...BJ] -> HU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BK...BJ] -> HU ([Ljava/lang/RuntimeException;])
===#Block HW(size=2, flags=10100)===
   0. lvar46 = {915364157 ^ lvar46};
   1. goto BL
      -> UnconditionalJump[GOTO] #HW -> #BL
      <- Switch[-146528871] #HU -> #HW
===#Block HX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #HU -> #HX
===#Block HV(size=2, flags=10100)===
   0. lvar46 = {1449532415 ^ lvar46};
   1. goto BL
      -> UnconditionalJump[GOTO] #HV -> #BL
      <- Switch[-267504758] #HU -> #HV
===#Block BL(size=2, flags=0)===
   0. _consume(catch());
   1. goto DM
      -> UnconditionalJump[GOTO] #BL -> #DM
      <- UnconditionalJump[GOTO] #HW -> #BL
      <- UnconditionalJump[GOTO] #HV -> #BL
===#Block DM(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 463837561);
   1. goto AE
      -> UnconditionalJump[GOTO] #DM -> #AE
      <- UnconditionalJump[GOTO] #BL -> #DM
===#Block FV(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1772697163);
   1. goto R
      -> UnconditionalJump[GOTO] #FV -> #R
      <- Switch[45483013] #B -> #FV
===#Block R(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar24 = lvar5;
   2. lvar41 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.epjtqrdtqayqgvs(), lvar46);
   3. lvar25 = lvar24.equals(lvar41);
   4. if (lvar25 != {1803364152 ^ lvar46})
      goto FH
   5. lvar46 = {826691114 ^ lvar46};
      -> Immediate #R -> #T
      -> ConditionalJump[IF_ICMPNE] #R -> #FH
      <- UnconditionalJump[GOTO] #FV -> #R
===#Block FH(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 39562867);
   1. goto CP
      -> UnconditionalJump[GOTO] #FH -> #CP
      <- ConditionalJump[IF_ICMPNE] #R -> #FH
===#Block CP(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == 1228163675)
      goto FP
   1. goto DG
      -> UnconditionalJump[GOTO] #CP -> #DG
      -> ConditionalJump[IF_ICMPEQ] #CP -> #FP
      <- UnconditionalJump[GOTO] #FH -> #CP
===#Block FP(size=2, flags=10100)===
   0. lvar46 = {1663329257 ^ lvar46};
   1. goto S
      -> UnconditionalJump[GOTO] #FP -> #S
      <- ConditionalJump[IF_ICMPEQ] #CP -> #FP
===#Block S(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar26.getDUSK_BALL();
   3. goto DH
      -> UnconditionalJump[GOTO] #S -> #DH
      <- UnconditionalJump[GOTO] #FP -> #S
===#Block DH(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 56994971:
      	 goto	#DI
      case 185710177:
      	 goto	#CI
      case 1072044398:
      	 goto	#DH
      case 1854908652:
      	 goto	#BN
      default:
      	 goto	#CI
   }
      -> Switch[56994971] #DH -> #DI
      -> Switch[1854908652] #DH -> #BN
      -> Immediate #DH -> #DI
      -> Switch[185710177] #DH -> #CI
      -> Switch[1072044398] #DH -> #DH
      -> DefaultSwitch #DH -> #CI
      <- Switch[1072044398] #DH -> #DH
      <- UnconditionalJump[GOTO] #S -> #DH
===#Block DI(size=2, flags=100)===
   0. lvar46 = {957950935 ^ lvar46};
   1. goto BN
      -> UnconditionalJump[GOTO] #DI -> #BN
      <- Switch[56994971] #DH -> #DI
      <- Immediate #DH -> #DI
===#Block BN(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 50722249)
      goto BM
   1. throw nullconst;
      -> TryCatch range: [BN...BM] -> HY ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #BN -> #BM
      <- UnconditionalJump[GOTO] #DI -> #BN
      <- Switch[1854908652] #DH -> #BN
===#Block BM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BN...BM] -> HY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BN -> #BM
===#Block HY(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -2116148901:
      	 goto	#IA
      case -1730278487:
      	 goto	#HZ
      default:
      	 goto	#IB
   }
      -> Switch[-1730278487] #HY -> #HZ
      -> Switch[-2116148901] #HY -> #IA
      -> DefaultSwitch #HY -> #IB
      <- TryCatch range: [BN...BM] -> HY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BN...BM] -> HY ([Ljava/lang/RuntimeException;])
===#Block IB(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #HY -> #IB
===#Block IA(size=2, flags=10100)===
   0. lvar46 = {1341795685 ^ lvar46};
   1. goto BO
      -> UnconditionalJump[GOTO] #IA -> #BO
      <- Switch[-2116148901] #HY -> #IA
===#Block HZ(size=2, flags=10100)===
   0. lvar46 = {215623739 ^ lvar46};
   1. goto BO
      -> UnconditionalJump[GOTO] #HZ -> #BO
      <- Switch[-1730278487] #HY -> #HZ
===#Block BO(size=2, flags=0)===
   0. _consume(catch());
   1. goto EN
      -> UnconditionalJump[GOTO] #BO -> #EN
      <- UnconditionalJump[GOTO] #IA -> #BO
      <- UnconditionalJump[GOTO] #HZ -> #BO
===#Block EN(size=2, flags=10100)===
   0. lvar46 = {738438585 ^ lvar46};
   1. goto AE
      -> UnconditionalJump[GOTO] #EN -> #AE
      <- UnconditionalJump[GOTO] #BO -> #EN
===#Block DG(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 763215800);
   1. goto CI
      -> UnconditionalJump[GOTO] #DG -> #CI
      <- UnconditionalJump[GOTO] #CP -> #DG
===#Block T(size=1, flags=0)===
   0. goto DK
      -> UnconditionalJump[GOTO] #T -> #DK
      <- Immediate #R -> #T
===#Block DK(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1918155014);
   1. goto BT
      -> UnconditionalJump[GOTO] #DK -> #BT
      <- UnconditionalJump[GOTO] #T -> #DK
===#Block BT(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 240683849)
      goto BS
   1. throw nullconst;
      -> TryCatch range: [BT...BS] -> IG ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #BT -> #BS
      <- UnconditionalJump[GOTO] #DK -> #BT
===#Block BS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BT...BS] -> IG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BT -> #BS
===#Block IG(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -1104377806:
      	 goto	#II
      case 1132363937:
      	 goto	#IH
      default:
      	 goto	#IJ
   }
      -> DefaultSwitch #IG -> #IJ
      -> Switch[1132363937] #IG -> #IH
      -> Switch[-1104377806] #IG -> #II
      <- TryCatch range: [BT...BS] -> IG ([Ljava/io/IOException;])
      <- TryCatch range: [BT...BS] -> IG ([Ljava/io/IOException;])
===#Block II(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 788046887);
   1. goto BU
      -> UnconditionalJump[GOTO] #II -> #BU
      <- Switch[-1104377806] #IG -> #II
===#Block IH(size=2, flags=10100)===
   0. lvar46 = {1364387125 ^ lvar46};
   1. goto BU
      -> UnconditionalJump[GOTO] #IH -> #BU
      <- Switch[1132363937] #IG -> #IH
===#Block BU(size=2, flags=0)===
   0. _consume(catch());
   1. goto CU
      -> UnconditionalJump[GOTO] #BU -> #CU
      <- UnconditionalJump[GOTO] #II -> #BU
      <- UnconditionalJump[GOTO] #IH -> #BU
===#Block CU(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1206730263);
   1. goto AC
      -> UnconditionalJump[GOTO] #CU -> #AC
      <- UnconditionalJump[GOTO] #BU -> #CU
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #IG -> #IJ
===#Block GB(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 44487311:
      	 goto	#GC
      case 541638573:
      	 goto	#F
      case 1807570979:
      	 goto	#GB
      case 1975611570:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Immediate #GB -> #GC
      -> Switch[44487311] #GB -> #GC
      -> Switch[1975611570] #GB -> #CI
      -> DefaultSwitch #GB -> #CI
      -> Switch[1807570979] #GB -> #GB
      -> Switch[541638573] #GB -> #F
      <- Switch[1807570979] #GB -> #GB
      <- Switch[45483070] #B -> #GB
===#Block GC(size=2, flags=100)===
   0. lvar46 = {1250455439 ^ lvar46};
   1. goto F
      -> UnconditionalJump[GOTO] #GC -> #F
      <- Immediate #GB -> #GC
      <- Switch[44487311] #GB -> #GC
===#Block F(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar5;
   2. lvar37 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.efdyklbpeajqtgq(), lvar46);
   3. lvar13 = lvar12.equals(lvar37);
   4. if (lvar13 != {1214021372 ^ lvar46})
      goto FE
   5. lvar46 = {2075730067 ^ lvar46};
      -> Immediate #F -> #G
      -> ConditionalJump[IF_ICMPNE] #F -> #FE
      <- UnconditionalJump[GOTO] #GC -> #F
      <- Switch[541638573] #GB -> #F
===#Block FE(size=2, flags=10100)===
   0. lvar46 = {233970733 ^ lvar46};
   1. goto CM
      -> UnconditionalJump[GOTO] #FE -> #CM
      <- ConditionalJump[IF_ICMPNE] #F -> #FE
===#Block CM(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == 762517130)
      goto FN
   1. goto DA
      -> UnconditionalJump[GOTO] #CM -> #DA
      -> ConditionalJump[IF_ICMPEQ] #CM -> #FN
      <- UnconditionalJump[GOTO] #FE -> #CM
===#Block FN(size=2, flags=10100)===
   0. lvar46 = {1094643026 ^ lvar46};
   1. goto H
      -> UnconditionalJump[GOTO] #FN -> #H
      <- ConditionalJump[IF_ICMPEQ] #CM -> #FN
===#Block H(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar14 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar14.getPREMIER_BALL();
   3. goto EG
      -> UnconditionalJump[GOTO] #H -> #EG
      <- UnconditionalJump[GOTO] #FN -> #H
===#Block EG(size=2, flags=10100)===
   0. lvar46 = {157904279 ^ lvar46};
   1. goto BB
      -> UnconditionalJump[GOTO] #EG -> #BB
      <- UnconditionalJump[GOTO] #H -> #EG
===#Block BB(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 185871359)
      goto BA
   1. throw nullconst;
      -> TryCatch range: [BB...BA] -> HI ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #BB -> #BA
      <- UnconditionalJump[GOTO] #EG -> #BB
===#Block BA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BB...BA] -> HI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BB -> #BA
===#Block HI(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -175697080:
      	 goto	#HK
      case 1875841184:
      	 goto	#HJ
      default:
      	 goto	#HL
   }
      -> Switch[-175697080] #HI -> #HK
      -> Switch[1875841184] #HI -> #HJ
      -> DefaultSwitch #HI -> #HL
      <- TryCatch range: [BB...BA] -> HI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BB...BA] -> HI ([Ljava/lang/RuntimeException;])
===#Block HL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #HI -> #HL
===#Block HJ(size=2, flags=10100)===
   0. lvar46 = {726657923 ^ lvar46};
   1. goto BC
      -> UnconditionalJump[GOTO] #HJ -> #BC
      <- Switch[1875841184] #HI -> #HJ
===#Block HK(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 939977726);
   1. goto BC
      -> UnconditionalJump[GOTO] #HK -> #BC
      <- Switch[-175697080] #HI -> #HK
===#Block BC(size=2, flags=0)===
   0. _consume(catch());
   1. goto DN
      -> UnconditionalJump[GOTO] #BC -> #DN
      <- UnconditionalJump[GOTO] #HJ -> #BC
      <- UnconditionalJump[GOTO] #HK -> #BC
===#Block DN(size=2, flags=10100)===
   0. lvar46 = {896814432 ^ lvar46};
   1. goto AE
      -> UnconditionalJump[GOTO] #DN -> #AE
      <- UnconditionalJump[GOTO] #BC -> #DN
===#Block DA(size=2, flags=10100)===
   0. lvar46 = {32855074 ^ lvar46};
   1. goto CI
      -> UnconditionalJump[GOTO] #DA -> #CI
      <- UnconditionalJump[GOTO] #CM -> #DA
===#Block G(size=1, flags=0)===
   0. goto EB
      -> UnconditionalJump[GOTO] #G -> #EB
      <- Immediate #F -> #G
===#Block EB(size=2, flags=10100)===
   0. lvar46 = {1641334402 ^ lvar46};
   1. goto AV
      -> UnconditionalJump[GOTO] #EB -> #AV
      <- UnconditionalJump[GOTO] #G -> #EB
===#Block AV(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 255871610)
      goto AU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AV -> #AU
      -> TryCatch range: [AV...AU] -> HA ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #EB -> #AV
===#Block AU(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AV...AU] -> HA ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AV -> #AU
===#Block HA(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -1853044886:
      	 goto	#HB
      case 576719432:
      	 goto	#HC
      default:
      	 goto	#HD
   }
      -> DefaultSwitch #HA -> #HD
      -> Switch[576719432] #HA -> #HC
      -> Switch[-1853044886] #HA -> #HB
      <- TryCatch range: [AV...AU] -> HA ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AV...AU] -> HA ([Ljava/lang/IllegalAccessException;])
===#Block HB(size=2, flags=10100)===
   0. lvar46 = {761597432 ^ lvar46};
   1. goto AW
      -> UnconditionalJump[GOTO] #HB -> #AW
      <- Switch[-1853044886] #HA -> #HB
===#Block HC(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 2065208412);
   1. goto AW
      -> UnconditionalJump[GOTO] #HC -> #AW
      <- Switch[576719432] #HA -> #HC
===#Block AW(size=2, flags=0)===
   0. _consume(catch());
   1. goto DB
      -> UnconditionalJump[GOTO] #AW -> #DB
      <- UnconditionalJump[GOTO] #HC -> #AW
      <- UnconditionalJump[GOTO] #HB -> #AW
===#Block DB(size=2, flags=10100)===
   0. lvar46 = {1099210275 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #DB -> #AC
      <- UnconditionalJump[GOTO] #AW -> #DB
===#Block HD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #HA -> #HD
===#Block FZ(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 12628391:
      	 goto	#CI
      case 44487311:
      	 goto	#GA
      case 1553388433:
      	 goto	#FZ
      case 1926176788:
      	 goto	#X
      default:
      	 goto	#CI
   }
      -> DefaultSwitch #FZ -> #CI
      -> Switch[1553388433] #FZ -> #FZ
      -> Switch[12628391] #FZ -> #CI
      -> Switch[1926176788] #FZ -> #X
      -> Immediate #FZ -> #GA
      -> Switch[44487311] #FZ -> #GA
      <- Switch[1553388433] #FZ -> #FZ
      <- Switch[45483068] #B -> #FZ
===#Block GA(size=2, flags=100)===
   0. lvar46 = {1630310060 ^ lvar46};
   1. goto X
      -> UnconditionalJump[GOTO] #GA -> #X
      <- Immediate #FZ -> #GA
      <- Switch[44487311] #FZ -> #GA
===#Block X(size=6, flags=0)===
   0. // Frame: locals[1] [java/lang/String] stack[0] []
   1. lvar31 = lvar5;
   2. lvar43 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.dmuhlmudvtqnhgp(), lvar46);
   3. lvar32 = lvar31.equals(lvar43);
   4. if (lvar32 != {1677238239 ^ lvar46})
      goto EZ
   5. lvar46 = {601084404 ^ lvar46};
      -> ConditionalJump[IF_ICMPNE] #X -> #EZ
      -> Immediate #X -> #Z
      <- Switch[1926176788] #FZ -> #X
      <- UnconditionalJump[GOTO] #GA -> #X
===#Block Z(size=1, flags=0)===
   0. goto DE
      -> UnconditionalJump[GOTO] #Z -> #DE
      <- Immediate #X -> #Z
===#Block DE(size=2, flags=10100)===
   0. lvar46 = {588774353 ^ lvar46};
   1. goto BH
      -> UnconditionalJump[GOTO] #DE -> #BH
      <- UnconditionalJump[GOTO] #Z -> #DE
===#Block BH(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 4572072)
      goto BG
   1. throw nullconst;
      -> TryCatch range: [BH...BG] -> HQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #BH -> #BG
      <- UnconditionalJump[GOTO] #DE -> #BH
===#Block BG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BH...BG] -> HQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BH -> #BG
===#Block HQ(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -1176473072:
      	 goto	#HS
      case 434483155:
      	 goto	#HR
      default:
      	 goto	#HT
   }
      -> DefaultSwitch #HQ -> #HT
      -> Switch[-1176473072] #HQ -> #HS
      -> Switch[434483155] #HQ -> #HR
      <- TryCatch range: [BH...BG] -> HQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BH...BG] -> HQ ([Ljava/lang/IllegalAccessException;])
===#Block HR(size=2, flags=10100)===
   0. lvar46 = {938623667 ^ lvar46};
   1. goto BI
      -> UnconditionalJump[GOTO] #HR -> #BI
      <- Switch[434483155] #HQ -> #HR
===#Block HS(size=2, flags=10100)===
   0. lvar46 = {1139996939 ^ lvar46};
   1. goto BI
      -> UnconditionalJump[GOTO] #HS -> #BI
      <- Switch[-1176473072] #HQ -> #HS
===#Block BI(size=2, flags=0)===
   0. _consume(catch());
   1. goto CW
      -> UnconditionalJump[GOTO] #BI -> #CW
      <- UnconditionalJump[GOTO] #HS -> #BI
      <- UnconditionalJump[GOTO] #HR -> #BI
===#Block CW(size=2, flags=10100)===
   0. lvar46 = {1780415615 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #CW -> #AC
      <- UnconditionalJump[GOTO] #BI -> #CW
===#Block HT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #HQ -> #HT
===#Block EZ(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 6862730:
      	 goto	#CI
      case 28431928:
      	 goto	#FA
      case 376220482:
      	 goto	#CJ
      case 1981961449:
      	 goto	#EZ
      default:
      	 goto	#CI
   }
      -> Immediate #EZ -> #FA
      -> Switch[376220482] #EZ -> #CJ
      -> Switch[28431928] #EZ -> #FA
      -> Switch[6862730] #EZ -> #CI
      -> DefaultSwitch #EZ -> #CI
      -> Switch[1981961449] #EZ -> #EZ
      <- ConditionalJump[IF_ICMPNE] #X -> #EZ
      <- Switch[1981961449] #EZ -> #EZ
===#Block FA(size=2, flags=100)===
   0. lvar46 = {1345867092 ^ lvar46};
   1. goto CJ
      -> UnconditionalJump[GOTO] #FA -> #CJ
      <- Immediate #EZ -> #FA
      <- Switch[28431928] #EZ -> #FA
===#Block CJ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1643735975)
      goto FR
   1. goto DV
      -> UnconditionalJump[GOTO] #CJ -> #DV
      -> ConditionalJump[IF_ICMPEQ] #CJ -> #FR
      <- Switch[376220482] #EZ -> #CJ
      <- UnconditionalJump[GOTO] #FA -> #CJ
===#Block FR(size=2, flags=10100)===
   0. lvar46 = {334904559 ^ lvar46};
   1. goto Y
      -> UnconditionalJump[GOTO] #FR -> #Y
      <- ConditionalJump[IF_ICMPEQ] #CJ -> #FR
===#Block Y(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar33 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar33.getPOKE_BALL();
   3. goto ES
      -> UnconditionalJump[GOTO] #Y -> #ES
      <- UnconditionalJump[GOTO] #FR -> #Y
===#Block ES(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 241847767:
      	 goto	#ET
      case 440105377:
      	 goto	#ES
      case 867726712:
      	 goto	#BZ
      case 1609047776:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Switch[440105377] #ES -> #ES
      -> Immediate #ES -> #ET
      -> Switch[867726712] #ES -> #BZ
      -> DefaultSwitch #ES -> #CI
      -> Switch[241847767] #ES -> #ET
      -> Switch[1609047776] #ES -> #CI
      <- Switch[440105377] #ES -> #ES
      <- UnconditionalJump[GOTO] #Y -> #ES
===#Block ET(size=2, flags=100)===
   0. lvar46 = {939205432 ^ lvar46};
   1. goto BZ
      -> UnconditionalJump[GOTO] #ET -> #BZ
      <- Immediate #ES -> #ET
      <- Switch[241847767] #ES -> #ET
===#Block BZ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 236890671)
      goto BY
   1. throw nullconst;
      -> TryCatch range: [BZ...BY] -> IO ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #BZ -> #BY
      <- Switch[867726712] #ES -> #BZ
      <- UnconditionalJump[GOTO] #ET -> #BZ
===#Block BY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BZ...BY] -> IO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BZ -> #BY
===#Block IO(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -1100059936:
      	 goto	#IP
      case 1063848818:
      	 goto	#IQ
      default:
      	 goto	#IR
   }
      -> Switch[1063848818] #IO -> #IQ
      -> DefaultSwitch #IO -> #IR
      -> Switch[-1100059936] #IO -> #IP
      <- TryCatch range: [BZ...BY] -> IO ([Ljava/io/IOException;])
      <- TryCatch range: [BZ...BY] -> IO ([Ljava/io/IOException;])
===#Block IP(size=2, flags=10100)===
   0. lvar46 = {1732045146 ^ lvar46};
   1. goto CA
      -> UnconditionalJump[GOTO] #IP -> #CA
      <- Switch[-1100059936] #IO -> #IP
===#Block IR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #IO -> #IR
===#Block IQ(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 924583272);
   1. goto CA
      -> UnconditionalJump[GOTO] #IQ -> #CA
      <- Switch[1063848818] #IO -> #IQ
===#Block CA(size=2, flags=0)===
   0. _consume(catch());
   1. goto DJ
      -> UnconditionalJump[GOTO] #CA -> #DJ
      <- UnconditionalJump[GOTO] #IP -> #CA
      <- UnconditionalJump[GOTO] #IQ -> #CA
===#Block DJ(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1664311537);
   1. goto AE
      -> UnconditionalJump[GOTO] #DJ -> #AE
      <- UnconditionalJump[GOTO] #CA -> #DJ
===#Block DV(size=2, flags=10100)===
   0. lvar46 = {2006705272 ^ lvar46};
   1. goto CI
      -> UnconditionalJump[GOTO] #DV -> #CI
      <- UnconditionalJump[GOTO] #CJ -> #DV
===#Block FX(size=2, flags=10100)===
   0. lvar46 = {1245911053 ^ lvar46};
   1. goto U
      -> UnconditionalJump[GOTO] #FX -> #U
      <- Switch[45483064] #B -> #FX
===#Block U(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = lvar5;
   2. lvar42 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.pvqtpbmzxaawgyr(), lvar46);
   3. lvar28 = lvar27.equals(lvar42);
   4. if (lvar28 != {1217857918 ^ lvar46})
      goto FF
   5. lvar46 = {19876770 ^ lvar46};
      -> ConditionalJump[IF_ICMPNE] #U -> #FF
      -> Immediate #U -> #W
      <- UnconditionalJump[GOTO] #FX -> #U
===#Block W(size=1, flags=0)===
   0. goto DR
      -> UnconditionalJump[GOTO] #W -> #DR
      <- Immediate #U -> #W
===#Block DR(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1742624704);
   1. goto CC
      -> UnconditionalJump[GOTO] #DR -> #CC
      <- UnconditionalJump[GOTO] #W -> #DR
===#Block CC(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 166061696)
      goto CB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CC -> #CB
      -> TryCatch range: [CC...CB] -> IS ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #DR -> #CC
===#Block CB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [CC...CB] -> IS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #CC -> #CB
===#Block IS(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case 547907210:
      	 goto	#IU
      case 1932568801:
      	 goto	#IT
      default:
      	 goto	#IV
   }
      -> Switch[547907210] #IS -> #IU
      -> Switch[1932568801] #IS -> #IT
      -> DefaultSwitch #IS -> #IV
      <- TryCatch range: [CC...CB] -> IS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [CC...CB] -> IS ([Ljava/lang/IllegalAccessException;])
===#Block IV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #IS -> #IV
===#Block IT(size=2, flags=10100)===
   0. lvar46 = {2135308808 ^ lvar46};
   1. goto CD
      -> UnconditionalJump[GOTO] #IT -> #CD
      <- Switch[1932568801] #IS -> #IT
===#Block IU(size=2, flags=10100)===
   0. lvar46 = {355804101 ^ lvar46};
   1. goto CD
      -> UnconditionalJump[GOTO] #IU -> #CD
      <- Switch[547907210] #IS -> #IU
===#Block CD(size=2, flags=0)===
   0. _consume(catch());
   1. goto EU
      -> UnconditionalJump[GOTO] #CD -> #EU
      <- UnconditionalJump[GOTO] #IT -> #CD
      <- UnconditionalJump[GOTO] #IU -> #CD
===#Block EU(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 221121046:
      	 goto	#EV
      case 348815687:
      	 goto	#CI
      case 498480187:
      	 goto	#EU
      case 1563373011:
      	 goto	#AC
      default:
      	 goto	#CI
   }
      -> Switch[221121046] #EU -> #EV
      -> Immediate #EU -> #EV
      -> DefaultSwitch #EU -> #CI
      -> Switch[1563373011] #EU -> #AC
      -> Switch[498480187] #EU -> #EU
      -> Switch[348815687] #EU -> #CI
      <- Switch[498480187] #EU -> #EU
      <- UnconditionalJump[GOTO] #CD -> #EU
===#Block EV(size=2, flags=100)===
   0. lvar46 = {1878008866 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #EV -> #AC
      <- Switch[221121046] #EU -> #EV
      <- Immediate #EU -> #EV
===#Block FF(size=2, flags=10100)===
   0. lvar46 = {1529636750 ^ lvar46};
   1. goto CN
      -> UnconditionalJump[GOTO] #FF -> #CN
      <- ConditionalJump[IF_ICMPNE] #U -> #FF
===#Block CN(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1646561408)
      goto FI
   1. goto EL
      -> UnconditionalJump[GOTO] #CN -> #EL
      -> ConditionalJump[IF_ICMPEQ] #CN -> #FI
      <- UnconditionalJump[GOTO] #FF -> #CN
===#Block FI(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 104543562:
      	 goto	#FJ
      case 772332230:
      	 goto	#FI
      case 957511856:
      	 goto	#CI
      case 1088870277:
      	 goto	#V
      default:
      	 goto	#CI
   }
      -> Switch[104543562] #FI -> #FJ
      -> Switch[1088870277] #FI -> #V
      -> Switch[772332230] #FI -> #FI
      -> Immediate #FI -> #FJ
      -> DefaultSwitch #FI -> #CI
      -> Switch[957511856] #FI -> #CI
      <- Switch[772332230] #FI -> #FI
      <- ConditionalJump[IF_ICMPEQ] #CN -> #FI
===#Block FJ(size=2, flags=100)===
   0. lvar46 = {1162283784 ^ lvar46};
   1. goto V
      -> UnconditionalJump[GOTO] #FJ -> #V
      <- Switch[104543562] #FI -> #FJ
      <- Immediate #FI -> #FJ
===#Block V(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar29.getTIMER_BALL();
   3. goto CT
      -> UnconditionalJump[GOTO] #V -> #CT
      <- Switch[1088870277] #FI -> #V
      <- UnconditionalJump[GOTO] #FJ -> #V
===#Block CT(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1133769126);
   1. goto AJ
      -> UnconditionalJump[GOTO] #CT -> #AJ
      <- UnconditionalJump[GOTO] #V -> #CT
===#Block AJ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 159922713)
      goto AI
   1. throw nullconst;
      -> TryCatch range: [AJ...AI] -> GK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AJ -> #AI
      <- UnconditionalJump[GOTO] #CT -> #AJ
===#Block AI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AJ...AI] -> GK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AJ -> #AI
===#Block GK(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -1417895184:
      	 goto	#GL
      case -636410966:
      	 goto	#GM
      default:
      	 goto	#GN
   }
      -> DefaultSwitch #GK -> #GN
      -> Switch[-1417895184] #GK -> #GL
      -> Switch[-636410966] #GK -> #GM
      <- TryCatch range: [AJ...AI] -> GK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AJ...AI] -> GK ([Ljava/lang/IllegalAccessException;])
===#Block GM(size=2, flags=10100)===
   0. lvar46 = {1263586229 ^ lvar46};
   1. goto AK
      -> UnconditionalJump[GOTO] #GM -> #AK
      <- Switch[-636410966] #GK -> #GM
===#Block GL(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 92109726);
   1. goto AK
      -> UnconditionalJump[GOTO] #GL -> #AK
      <- Switch[-1417895184] #GK -> #GL
===#Block AK(size=2, flags=0)===
   0. _consume(catch());
   1. goto EQ
      -> UnconditionalJump[GOTO] #AK -> #EQ
      <- UnconditionalJump[GOTO] #GM -> #AK
      <- UnconditionalJump[GOTO] #GL -> #AK
===#Block EQ(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 254058646:
      	 goto	#ER
      case 550783225:
      	 goto	#AE
      case 850608069:
      	 goto	#EQ
      case 1823861715:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Switch[850608069] #EQ -> #EQ
      -> Switch[550783225] #EQ -> #AE
      -> Switch[1823861715] #EQ -> #CI
      -> Immediate #EQ -> #ER
      -> DefaultSwitch #EQ -> #CI
      -> Switch[254058646] #EQ -> #ER
      <- Switch[850608069] #EQ -> #EQ
      <- UnconditionalJump[GOTO] #AK -> #EQ
===#Block ER(size=2, flags=100)===
   0. lvar46 = {64006967 ^ lvar46};
   1. goto AE
      -> UnconditionalJump[GOTO] #ER -> #AE
      <- Immediate #EQ -> #ER
      <- Switch[254058646] #EQ -> #ER
===#Block GN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #GK -> #GN
===#Block EL(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 104543562:
      	 goto	#EM
      case 594403661:
      	 goto	#EL
      case 765076231:
      	 goto	#CI
      case 1472047261:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> DefaultSwitch #EL -> #CI
      -> Switch[1472047261] #EL -> #CI
      -> Switch[104543562] #EL -> #EM
      -> Immediate #EL -> #EM
      -> Switch[594403661] #EL -> #EL
      <- UnconditionalJump[GOTO] #CN -> #EL
      <- Switch[594403661] #EL -> #EL
===#Block EM(size=2, flags=100)===
   0. lvar46 = {1474320387 ^ lvar46};
   1. goto CI
      -> UnconditionalJump[GOTO] #EM -> #CI
      <- Switch[104543562] #EL -> #EM
      <- Immediate #EL -> #EM
===#Block FY(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1576070463);
   1. goto O
      -> UnconditionalJump[GOTO] #FY -> #O
      <- Switch[45483065] #B -> #FY
===#Block O(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar5;
   2. lvar40 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.fufmafzfcwlfmxk(), lvar46);
   3. lvar22 = lvar21.equals(lvar40);
   4. if (lvar22 != {1596259404 ^ lvar46})
      goto FD
   5. lvar46 = {2008759641 ^ lvar46};
      -> Immediate #O -> #Q
      -> ConditionalJump[IF_ICMPNE] #O -> #FD
      <- UnconditionalJump[GOTO] #FY -> #O
===#Block FD(size=2, flags=10100)===
   0. lvar46 = {1835867717 ^ lvar46};
   1. goto CL
      -> UnconditionalJump[GOTO] #FD -> #CL
      <- ConditionalJump[IF_ICMPNE] #O -> #FD
===#Block CL(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -1840312247)
      goto EX
   1. goto CS
      -> UnconditionalJump[GOTO] #CL -> #CS
      -> ConditionalJump[IF_ICMPEQ] #CL -> #EX
      <- UnconditionalJump[GOTO] #FD -> #CL
===#Block EX(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 24057784:
      	 goto	#EY
      case 591922990:
      	 goto	#P
      case 714978774:
      	 goto	#CI
      case 1045262970:
      	 goto	#EX
      default:
      	 goto	#CI
   }
      -> Immediate #EX -> #EY
      -> Switch[714978774] #EX -> #CI
      -> DefaultSwitch #EX -> #CI
      -> Switch[24057784] #EX -> #EY
      -> Switch[1045262970] #EX -> #EX
      -> Switch[591922990] #EX -> #P
      <- ConditionalJump[IF_ICMPEQ] #CL -> #EX
      <- Switch[1045262970] #EX -> #EX
===#Block EY(size=2, flags=100)===
   0. lvar46 = {1312873161 ^ lvar46};
   1. goto P
      -> UnconditionalJump[GOTO] #EY -> #P
      <- Immediate #EX -> #EY
      <- Switch[24057784] #EX -> #EY
===#Block P(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar23.getMASTER_BALL();
   3. goto DW
      -> UnconditionalJump[GOTO] #P -> #DW
      <- UnconditionalJump[GOTO] #EY -> #P
      <- Switch[591922990] #EX -> #P
===#Block DW(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 5349885:
      	 goto	#DX
      case 560968862:
      	 goto	#AG
      case 1328164359:
      	 goto	#DW
      case 2126894560:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Switch[560968862] #DW -> #AG
      -> Switch[2126894560] #DW -> #CI
      -> DefaultSwitch #DW -> #CI
      -> Switch[5349885] #DW -> #DX
      -> Switch[1328164359] #DW -> #DW
      -> Immediate #DW -> #DX
      <- UnconditionalJump[GOTO] #P -> #DW
      <- Switch[1328164359] #DW -> #DW
===#Block DX(size=2, flags=100)===
   0. lvar46 = {1725821843 ^ lvar46};
   1. goto AG
      -> UnconditionalJump[GOTO] #DX -> #AG
      <- Switch[5349885] #DW -> #DX
      <- Immediate #DW -> #DX
===#Block AG(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 66868004)
      goto AF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AG -> #AF
      -> TryCatch range: [AG...AF] -> GG ([Ljava/io/IOException;])
      <- Switch[560968862] #DW -> #AG
      <- UnconditionalJump[GOTO] #DX -> #AG
===#Block AF(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AG...AF] -> GG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AG -> #AF
===#Block GG(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -693740904:
      	 goto	#GH
      case 388010761:
      	 goto	#GI
      default:
      	 goto	#GJ
   }
      -> DefaultSwitch #GG -> #GJ
      -> Switch[388010761] #GG -> #GI
      -> Switch[-693740904] #GG -> #GH
      <- TryCatch range: [AG...AF] -> GG ([Ljava/io/IOException;])
      <- TryCatch range: [AG...AF] -> GG ([Ljava/io/IOException;])
===#Block GH(size=2, flags=10100)===
   0. lvar46 = {818277692 ^ lvar46};
   1. goto AH
      -> UnconditionalJump[GOTO] #GH -> #AH
      <- Switch[-693740904] #GG -> #GH
===#Block GI(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 150287374);
   1. goto AH
      -> UnconditionalJump[GOTO] #GI -> #AH
      <- Switch[388010761] #GG -> #GI
===#Block AH(size=2, flags=0)===
   0. _consume(catch());
   1. goto DS
      -> UnconditionalJump[GOTO] #AH -> #DS
      <- UnconditionalJump[GOTO] #GI -> #AH
      <- UnconditionalJump[GOTO] #GH -> #AH
===#Block DS(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 970154648);
   1. goto AE
      -> UnconditionalJump[GOTO] #DS -> #AE
      <- UnconditionalJump[GOTO] #AH -> #DS
===#Block GJ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #GG -> #GJ
===#Block CS(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1980945658);
   1. goto CI
      -> UnconditionalJump[GOTO] #CS -> #CI
      <- UnconditionalJump[GOTO] #CL -> #CS
===#Block Q(size=1, flags=0)===
   0. goto CY
      -> UnconditionalJump[GOTO] #Q -> #CY
      <- Immediate #O -> #Q
===#Block CY(size=1, flags=10100)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46)) {
      case 180640104:
      	 goto	#CY
      case 246787719:
      	 goto	#CZ
      case 319505541:
      	 goto	#AS
      case 1427302579:
      	 goto	#CI
      default:
      	 goto	#CI
   }
      -> Switch[180640104] #CY -> #CY
      -> Switch[1427302579] #CY -> #CI
      -> Switch[246787719] #CY -> #CZ
      -> DefaultSwitch #CY -> #CI
      -> Switch[319505541] #CY -> #AS
      -> Immediate #CY -> #CZ
      <- Switch[180640104] #CY -> #CY
      <- UnconditionalJump[GOTO] #Q -> #CY
===#Block CZ(size=2, flags=100)===
   0. lvar46 = {1196115259 ^ lvar46};
   1. goto AS
      -> UnconditionalJump[GOTO] #CZ -> #AS
      <- Switch[246787719] #CY -> #CZ
      <- Immediate #CY -> #CZ
===#Block AS(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 145469389)
      goto AR
   1. throw nullconst;
      -> TryCatch range: [AS...AR] -> GW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AS -> #AR
      <- Switch[319505541] #CY -> #AS
      <- UnconditionalJump[GOTO] #CZ -> #AS
===#Block AR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AS...AR] -> GW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AS -> #AR
===#Block GW(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case 1438921865:
      	 goto	#GY
      case 2124685683:
      	 goto	#GX
      default:
      	 goto	#GZ
   }
      -> Switch[1438921865] #GW -> #GY
      -> DefaultSwitch #GW -> #GZ
      -> Switch[2124685683] #GW -> #GX
      <- TryCatch range: [AS...AR] -> GW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AS...AR] -> GW ([Ljava/lang/RuntimeException;])
===#Block GX(size=2, flags=10100)===
   0. lvar46 = {1561376212 ^ lvar46};
   1. goto AT
      -> UnconditionalJump[GOTO] #GX -> #AT
      <- Switch[2124685683] #GW -> #GX
===#Block GZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #GW -> #GZ
===#Block GY(size=2, flags=10100)===
   0. lvar46 = {410826603 ^ lvar46};
   1. goto AT
      -> UnconditionalJump[GOTO] #GY -> #AT
      <- Switch[1438921865] #GW -> #GY
===#Block AT(size=2, flags=0)===
   0. _consume(catch());
   1. goto DD
      -> UnconditionalJump[GOTO] #AT -> #DD
      <- UnconditionalJump[GOTO] #GY -> #AT
      <- UnconditionalJump[GOTO] #GX -> #AT
===#Block DD(size=2, flags=10100)===
   0. lvar46 = {202652364 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #DD -> #AC
      <- UnconditionalJump[GOTO] #AT -> #DD
===#Block FU(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1854425642);
   1. goto C
      -> UnconditionalJump[GOTO] #FU -> #C
      <- Switch[45483012] #B -> #FU
===#Block C(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar8 = lvar5;
   2. lvar4 = cn.acebrand.acedex.gui.PokeBallItemCreator.nqdkahatem(cn.acebrand.acedex.gui.PokeBallItemCreator.jfprqgwjffzlpld(), lvar46);
   3. lvar9 = lvar8.equals(lvar4);
   4. if (lvar9 != {1817992025 ^ lvar46})
      goto FG
   5. lvar46 = {1533637584 ^ lvar46};
      -> Immediate #C -> #D
      -> ConditionalJump[IF_ICMPNE] #C -> #FG
      <- UnconditionalJump[GOTO] #FU -> #C
===#Block FG(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 353385760);
   1. goto CO
      -> UnconditionalJump[GOTO] #FG -> #CO
      <- ConditionalJump[IF_ICMPNE] #C -> #FG
===#Block CO(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.soigfgduzeadyziw(lvar46) == -899468341)
      goto FO
   1. goto DF
      -> ConditionalJump[IF_ICMPEQ] #CO -> #FO
      -> UnconditionalJump[GOTO] #CO -> #DF
      <- UnconditionalJump[GOTO] #FG -> #CO
===#Block DF(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1024938122);
   1. goto CI
      -> UnconditionalJump[GOTO] #DF -> #CI
      <- UnconditionalJump[GOTO] #CO -> #DF
===#Block CI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      <- UnconditionalJump[GOTO] #EF -> #CI
      <- UnconditionalJump[GOTO] #DP -> #CI
      <- Switch[1823861715] #EQ -> #CI
      <- DefaultSwitch #FB -> #CI
      <- DefaultSwitch #EQ -> #CI
      <- Switch[1256865767] #FB -> #CI
      <- DefaultSwitch #EL -> #CI
      <- Switch[1472047261] #EL -> #CI
      <- Switch[2126894560] #DW -> #CI
      <- UnconditionalJump[GOTO] #EP -> #CI
      <- DefaultSwitch #DW -> #CI
      <- DefaultSwitch #FZ -> #CI
      <- Switch[12628391] #FZ -> #CI
      <- DefaultSwitch #DT -> #CI
      <- Switch[1938649939] #DT -> #CI
      <- UnconditionalJump[GOTO] #DA -> #CI
      <- UnconditionalJump[GOTO] #DV -> #CI
      <- Switch[185710177] #DH -> #CI
      <- Switch[1975611570] #GB -> #CI
      <- DefaultSwitch #GB -> #CI
      <- DefaultSwitch #DH -> #CI
      <- Switch[605709020] #EC -> #CI
      <- DefaultSwitch #EC -> #CI
      <- Switch[804633422] #DZ -> #CI
      <- DefaultSwitch #DZ -> #CI
      <- Switch[1427302579] #CY -> #CI
      <- UnconditionalJump[GOTO] #EM -> #CI
      <- DefaultSwitch #CY -> #CI
      <- Switch[6862730] #EZ -> #CI
      <- DefaultSwitch #EZ -> #CI
      <- Switch[150495022] #GE -> #CI
      <- DefaultSwitch #GE -> #CI
      <- DefaultSwitch #FI -> #CI
      <- Switch[957511856] #FI -> #CI
      <- Switch[814156815] #DO -> #CI
      <- UnconditionalJump[GOTO] #CS -> #CI
      <- DefaultSwitch #EO -> #CI
      <- UnconditionalJump[GOTO] #DG -> #CI
      <- Switch[332917395] #EO -> #CI
      <- Switch[714978774] #EX -> #CI
      <- DefaultSwitch #EX -> #CI
      <- DefaultSwitch #EU -> #CI
      <- Switch[1121556300] #FK -> #CI
      <- Switch[348815687] #EU -> #CI
      <- DefaultSwitch #FK -> #CI
      <- DefaultSwitch #ES -> #CI
      <- Switch[369102321] #EE -> #CI
      <- DefaultSwitch #EE -> #CI
      <- UnconditionalJump[GOTO] #DF -> #CI
      <- Switch[1609047776] #ES -> #CI
      <- DefaultSwitch #DO -> #CI
===#Block FO(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 648481211);
   1. goto E
      -> UnconditionalJump[GOTO] #FO -> #E
      <- ConditionalJump[IF_ICMPEQ] #CO -> #FO
===#Block E(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar11 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar11.getQUICK_BALL();
   3. goto EK
      -> UnconditionalJump[GOTO] #E -> #EK
      <- UnconditionalJump[GOTO] #FO -> #E
===#Block EK(size=2, flags=10100)===
   0. lvar46 = {1015109835 ^ lvar46};
   1. goto BQ
      -> UnconditionalJump[GOTO] #EK -> #BQ
      <- UnconditionalJump[GOTO] #E -> #EK
===#Block BQ(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 10332619)
      goto BP
   1. throw nullconst;
      -> TryCatch range: [BQ...BP] -> IC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #BQ -> #BP
      <- UnconditionalJump[GOTO] #EK -> #BQ
===#Block BP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BQ...BP] -> IC ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BQ -> #BP
===#Block IC(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case 458340427:
      	 goto	#ID
      case 903860003:
      	 goto	#IE
      default:
      	 goto	#IF
   }
      -> DefaultSwitch #IC -> #IF
      -> Switch[458340427] #IC -> #ID
      -> Switch[903860003] #IC -> #IE
      <- TryCatch range: [BQ...BP] -> IC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BQ...BP] -> IC ([Ljava/lang/RuntimeException;])
===#Block IE(size=2, flags=10100)===
   0. lvar46 = {1663991628 ^ lvar46};
   1. goto BR
      -> UnconditionalJump[GOTO] #IE -> #BR
      <- Switch[903860003] #IC -> #IE
===#Block ID(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 1728033185);
   1. goto BR
      -> UnconditionalJump[GOTO] #ID -> #BR
      <- Switch[458340427] #IC -> #ID
===#Block BR(size=2, flags=0)===
   0. _consume(catch());
   1. goto DC
      -> UnconditionalJump[GOTO] #BR -> #DC
      <- UnconditionalJump[GOTO] #IE -> #BR
      <- UnconditionalJump[GOTO] #ID -> #BR
===#Block DC(size=2, flags=10100)===
   0. lvar46 = cn.acebrand.acedex.gui.PokeBallItemCreator.zmujkmpyavdnkgeo(lvar46, 374850143);
   1. goto AE
      -> UnconditionalJump[GOTO] #DC -> #AE
      <- UnconditionalJump[GOTO] #BR -> #DC
===#Block IF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #IC -> #IF
===#Block D(size=1, flags=0)===
   0. goto DL
      -> UnconditionalJump[GOTO] #D -> #DL
      <- Immediate #C -> #D
===#Block DL(size=2, flags=10100)===
   0. lvar46 = {718997653 ^ lvar46};
   1. goto BW
      -> UnconditionalJump[GOTO] #DL -> #BW
      <- UnconditionalJump[GOTO] #D -> #DL
===#Block BW(size=2, flags=0)===
   0. if (zznpoqoieuobaiop.bigkctpthfdcjycw.nnsxpzvrglbwclru(lvar46) == 167592920)
      goto BV
   1. throw nullconst;
      -> TryCatch range: [BW...BV] -> IK ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #BW -> #BV
      <- UnconditionalJump[GOTO] #DL -> #BW
===#Block BV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BW...BV] -> IK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BW -> #BV
===#Block IK(size=1, flags=0)===
   0. switch (zznpoqoieuobaiop.bigkctpthfdcjycw.byppskjxfoukjgbs(lvar46)) {
      case -277806880:
      	 goto	#IL
      case 1220805594:
      	 goto	#IM
      default:
      	 goto	#IN
   }
      -> Switch[-277806880] #IK -> #IL
      -> Switch[1220805594] #IK -> #IM
      -> DefaultSwitch #IK -> #IN
      <- TryCatch range: [BW...BV] -> IK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BW...BV] -> IK ([Ljava/lang/RuntimeException;])
===#Block IN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #IK -> #IN
===#Block IM(size=2, flags=10100)===
   0. lvar46 = {821817123 ^ lvar46};
   1. goto BX
      -> UnconditionalJump[GOTO] #IM -> #BX
      <- Switch[1220805594] #IK -> #IM
===#Block IL(size=2, flags=10100)===
   0. lvar46 = {1678593860 ^ lvar46};
   1. goto BX
      -> UnconditionalJump[GOTO] #IL -> #BX
      <- Switch[-277806880] #IK -> #IL
===#Block BX(size=2, flags=0)===
   0. _consume(catch());
   1. goto DQ
      -> UnconditionalJump[GOTO] #BX -> #DQ
      <- UnconditionalJump[GOTO] #IL -> #BX
      <- UnconditionalJump[GOTO] #IM -> #BX
===#Block DQ(size=2, flags=10100)===
   0. lvar46 = {1194550382 ^ lvar46};
   1. goto AC
      -> UnconditionalJump[GOTO] #DQ -> #AC
      <- UnconditionalJump[GOTO] #BX -> #DQ
===#Block AC(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar10 = com.cobblemon.mod.common.api.pokeball.PokeBalls.INSTANCE;
   2. lvar30 = lvar10.getPOKE_BALL();
   3. lvar46 = {756174785 ^ lvar46};
      -> Immediate #AC -> #AE
      <- UnconditionalJump[GOTO] #DB -> #AC
      <- Switch[350610228] #GE -> #AC
      <- Switch[2081810626] #DT -> #AC
      <- UnconditionalJump[GOTO] #EV -> #AC
      <- Switch[209596718] #DZ -> #AC
      <- UnconditionalJump[GOTO] #CW -> #AC
      <- UnconditionalJump[GOTO] #EA -> #AC
      <- UnconditionalJump[GOTO] #DU -> #AC
      <- UnconditionalJump[GOTO] #CU -> #AC
      <- Switch[1563373011] #EU -> #AC
      <- UnconditionalJump[GOTO] #DQ -> #AC
      <- UnconditionalJump[GOTO] #DD -> #AC
      <- UnconditionalJump[GOTO] #CV -> #AC
      <- UnconditionalJump[GOTO] #GF -> #AC
===#Block AE(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[1] [com/cobblemon/mod/common/pokeball/PokeBall]
   1. return lvar30;
      <- Immediate #AC -> #AE
      <- UnconditionalJump[GOTO] #DJ -> #AE
      <- UnconditionalJump[GOTO] #DN -> #AE
      <- UnconditionalJump[GOTO] #DS -> #AE
      <- UnconditionalJump[GOTO] #DC -> #AE
      <- UnconditionalJump[GOTO] #CR -> #AE
      <- Switch[550783225] #EQ -> #AE
      <- UnconditionalJump[GOTO] #EN -> #AE
      <- UnconditionalJump[GOTO] #ER -> #AE
      <- UnconditionalJump[GOTO] #EJ -> #AE
      <- UnconditionalJump[GOTO] #DM -> #AE
