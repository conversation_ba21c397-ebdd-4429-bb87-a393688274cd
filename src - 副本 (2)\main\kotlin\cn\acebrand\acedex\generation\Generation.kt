/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.generation

/**
 * 世代数据类
 */
data class Generation(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val region: String,
    val pokemonRange: IntRange,
    val iconMaterial: String, // 使用Cobblemon的材质ID
    val color: String
) {
    private val pokemonList = mutableMapOf<String, Int>() // 精灵名称 -> 全国图鉴编号
    
    /**
     * 添加精灵到世代
     */
    fun addPokemon(name: String, nationalDex: Int) {
        pokemonList[name] = nationalDex
    }
    
    /**
     * 获取世代中的所有精灵
     */
    fun getAllPokemon(): Map<String, Int> = pokemonList.toMap()
    
    /**
     * 获取世代中精灵的总数
     */
    fun getTotalPokemon(): Int = pokemonList.size
    
    /**
     * 检查精灵是否属于这个世代
     */
    fun containsPokemon(pokemonName: String): Boolean = pokemonList.containsKey(pokemonName)

    /**
     * 根据 Showdown ID 检查精灵是否属于这个世代
     * 使用标准化的名称匹配（移除非字母数字字符）
     */
    fun containsPokemonByShowdownId(showdownId: String): Boolean {
        return pokemonList.keys.any { pokemonName ->
            normalizeShowdownId(pokemonName) == showdownId
        }
    }

    /**
     * 标准化精灵名称为 Showdown ID 格式
     */
    private fun normalizeShowdownId(name: String): String {
        return name.lowercase().replace(Regex("[^a-z0-9]+"), "")
    }

    /**
     * 根据全国图鉴编号检查是否属于这个世代
     */
    fun containsNationalDex(nationalDex: Int): Boolean = nationalDex in pokemonRange
    
    /**
     * 获取格式化的显示信息
     */
    fun getFormattedInfo(): List<String> {
        return listOf(
            "$color$name",
            "§7地区: §f$region",
            "§7描述: §f$description",
            "§7图鉴范围: §f${pokemonRange.first}-${pokemonRange.last}",
            "§7精灵数量: §f${getTotalPokemon()}"
        )
    }
}


