/**
 * AceDex - 精灵图鉴插件
 * 精灵物品创建器
 */

package cn.acebrand.acedex.gui

import cn.acebrand.acedex.AceDex
import cn.acebrand.acedex.generation.PokemonData
import cn.acebrand.acedex.gui.builder.PokemonDisplayItemBuilder
import cn.acebrand.acedex.util.CobblemonItemHelper

import org.bukkit.Material
import org.bukkit.enchantments.Enchantment
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemFlag
import org.bukkit.inventory.ItemStack
import org.bukkit.inventory.meta.ItemMeta
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import org.bukkit.configuration.file.YamlConfiguration

/**
 * 精灵物品创建器
 * 优化版本：集成预加载管理器，使用物品实例保存到本地文件，添加按钮ID和标识符判断机制
 */
class PokemonItemCreator(private val plugin: AceDex) {

    // 使用构建器模式创建不同类型的精灵物品
    private val defaultBuilder = PokemonDisplayItemBuilder.createDefault(plugin)
    private val simpleBuilder = PokemonDisplayItemBuilder.createSimple(plugin)

    // 精灵物品缓存，避免重复创建导致卡顿 - 改为线程安全的ConcurrentHashMap
    private val caughtPokemonItemCache = ConcurrentHashMap<String, ItemStack>()
    private val uncaughtPokemonItemCache = ConcurrentHashMap<String, ItemStack>()

    // 按钮ID到精灵名称的映射
    private val buttonIdToPokemonName = ConcurrentHashMap<String, String>()

    // 本地缓存文件 - 改为YAML格式存储元数据
    private val cacheDir = File(plugin.dataFolder, "item_cache")
    private val cacheStatusFile = File(cacheDir, "cache_status.yml")
    private val buttonIdCacheFile = File(cacheDir, "button_ids.yml")

    init {
        // 初始化精灵物品创建器
        initializeCacheDirectory()
        loadCacheFromFiles()
    }
    
    /**
     * 初始化缓存目录
     */
    private fun initializeCacheDirectory() {
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }

    /**
     * 从本地文件加载缓存
     */
    private fun loadCacheFromFiles() {
        try {
            // 加载缓存状态
            if (cacheStatusFile.exists()) {
                val statusConfig = YamlConfiguration.loadConfiguration(cacheStatusFile)
                val lastCacheTime = statusConfig.getLong("last_cache_time", 0)
                val caughtCount = statusConfig.getInt("caught_count", 0)
                val uncaughtCount = statusConfig.getInt("uncaught_count", 0)

                // 如果缓存时间太久远（超过1天），清理缓存
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastCacheTime > 24 * 60 * 60 * 1000) {
                    plugin.logger.info("精灵物品缓存已过期，将重新生成")
                    return
                }

                plugin.logger.info("精灵物品缓存状态: 已收集=$caughtCount, 未收集=$uncaughtCount")
            }

            // 加载按钮ID映射缓存
            if (buttonIdCacheFile.exists()) {
                val buttonConfig = YamlConfiguration.loadConfiguration(buttonIdCacheFile)
                val buttonSection = buttonConfig.getConfigurationSection("button_ids")
                if (buttonSection != null) {
                    for (key in buttonSection.getKeys(false)) {
                        val pokemonName = buttonSection.getString(key)
                        if (pokemonName != null) {
                            buttonIdToPokemonName[key] = pokemonName
                        }
                    }
                }
            }

        } catch (e: Exception) {
            plugin.logger.warning("加载精灵物品缓存失败: ${e.message}")
        }
    }

    /**
     * 保存缓存到本地文件
     */
    private fun saveCacheToFiles() {
        try {
            // 保存缓存状态
            val statusConfig = YamlConfiguration()
            statusConfig.set("last_cache_time", System.currentTimeMillis())
            statusConfig.set("caught_count", caughtPokemonItemCache.size)
            statusConfig.set("uncaught_count", uncaughtPokemonItemCache.size)
            statusConfig.save(cacheStatusFile)

            // 保存按钮ID映射缓存
            val buttonConfig = YamlConfiguration()
            val buttonSection = buttonConfig.createSection("button_ids")
            for ((buttonId, pokemonName) in buttonIdToPokemonName) {
                buttonSection.set(buttonId, pokemonName)
            }
            buttonConfig.save(buttonIdCacheFile)

            plugin.logger.info("精灵物品缓存状态已保存到本地文件")
        } catch (e: Exception) {
            plugin.logger.warning("保存精灵物品缓存失败: ${e.message}")
        }
    }

    /**
     * 创建精灵物品（主要方法）
     * 优化版本：优先使用预加载的模型，使用缓存机制避免重复创建导致卡顿
     */
    fun createPokemonItem(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 首先尝试从预加载管理器获取
        val preloadedItem = plugin.pokemonModelPreloader.getPreloadedPokemonItem(pokemonName, nationalDex, hasCaught)
        if (preloadedItem != null) {
            // 生成按钮ID并缓存映射
            val buttonId = generateButtonId(pokemonName)
            buttonIdToPokemonName[buttonId] = pokemonName
            return preloadedItem
        }

        // 如果预加载不可用，使用原有的缓存机制
        return createPokemonItemWithCache(pokemonName, nationalDex, hasCaught)
    }

    /**
     * 创建精灵物品（带获得方式信息）
     * 用于区分捕捉获得和交换获得的精灵显示
     */
    fun createPokemonItemWithAcquisition(pokemonName: String, nationalDex: Int, hasCaught: Boolean, acquisitionMethod: cn.acebrand.acedex.util.PokemonAcquisitionMethod? = null): ItemStack {
        // 如果有获得方式信息且为交换获得，则显示为未收集状态但添加特殊标记
        val displayAsCaught = hasCaught && (acquisitionMethod == null || acquisitionMethod == cn.acebrand.acedex.util.PokemonAcquisitionMethod.CAUGHT)

        // 首先尝试从预加载管理器获取基础物品
        val preloadedItem = plugin.pokemonModelPreloader.getPreloadedPokemonItem(pokemonName, nationalDex, displayAsCaught)
        val baseItem = preloadedItem ?: createPokemonItemWithCache(pokemonName, nationalDex, displayAsCaught)

        // 如果是交换获得的精灵，需要特殊处理显示信息
        if (hasCaught && acquisitionMethod == cn.acebrand.acedex.util.PokemonAcquisitionMethod.TRADED) {
            return applyTradedPokemonDisplayInfo(baseItem, pokemonName, nationalDex)
        }

        return baseItem
    }

    /**
     * 使用缓存机制创建精灵物品（备用方法）
     */
    private fun createPokemonItemWithCache(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 生成缓存键
        val baseCacheKey = "${pokemonName}_${nationalDex}_normal"

        if (hasCaught) {
            // 已收集精灵的缓存键，包含自定义材质配置状态
            val customMaterialStatus = if (plugin.config.useCustomMaterialForCaught) {
                "custom_${plugin.config.customCaughtMaterial}"
            } else {
                "default"
            }
            val caughtCacheKey = "${baseCacheKey}_caught_${customMaterialStatus}"

            // 先检查已收集精灵缓存
            val cachedCaughtItem = caughtPokemonItemCache[caughtCacheKey]
            if (cachedCaughtItem != null) {
                return cachedCaughtItem.clone()
            }

            // 检查是否有未收集的缓存可以复用
            val uncaughtCacheKey = "${baseCacheKey}_uncaught"
            val cachedUncaughtItem = uncaughtPokemonItemCache[uncaughtCacheKey]

            if (cachedUncaughtItem != null) {
                // 复用未收集精灵的物品，只更新材质和显示信息
                val caughtItem = updateItemForCaughtStatus(cachedUncaughtItem.clone(), pokemonName, nationalDex, true)
                caughtPokemonItemCache[caughtCacheKey] = caughtItem.clone()
                return caughtItem
            } else {
                // 创建新的已收集精灵物品
                val newCaughtItem = createNewPokemonItem(pokemonName, nationalDex, true)
                caughtPokemonItemCache[caughtCacheKey] = newCaughtItem.clone()
                return newCaughtItem
            }
        } else {
            // 未收集精灵的缓存键
            val uncaughtCacheKey = "${baseCacheKey}_uncaught"

            // 先检查未收集精灵缓存
            val cachedUncaughtItem = uncaughtPokemonItemCache[uncaughtCacheKey]
            if (cachedUncaughtItem != null) {
                return cachedUncaughtItem.clone()
            }

            // 创建新的未收集精灵物品并缓存
            val newUncaughtItem = createNewPokemonItem(pokemonName, nationalDex, false)
            uncaughtPokemonItemCache[uncaughtCacheKey] = newUncaughtItem.clone()
            return newUncaughtItem
        }
    }

    /**
     * 生成按钮ID
     */
    private fun generateButtonId(pokemonName: String): String {
        return "pokemon:${pokemonName.lowercase()}"
    }

    /**
     * 根据按钮ID获取精灵名称
     */
    fun getPokemonNameByButtonId(buttonId: String): String? {
        return buttonIdToPokemonName[buttonId] ?: plugin.pokemonModelPreloader.getPokemonNameByButtonId(buttonId)
    }

    /**
     * 检查按钮ID是否为精灵按钮
     */
    fun isPokemonButton(buttonId: String): Boolean {
        return buttonId.startsWith("pokemon:") &&
               (buttonIdToPokemonName.containsKey(buttonId) || plugin.pokemonModelPreloader.isPokemonButton(buttonId))
    }

    /**
     * 更新物品的收集状态，只改变材质和显示信息，不重新创建模型
     */
    private fun updateItemForCaughtStatus(baseItem: ItemStack, pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 如果配置了自定义材质且精灵已收集，则创建新的自定义材质物品
        val finalItem = if (hasCaught && plugin.config.useCustomMaterialForCaught) {
            try {
                // 使用 parseCustomMaterial 方法正确处理自定义材质配置
                val customItem = parseCustomMaterial(plugin.config.customCaughtMaterial)
                // 保持原物品的数量
                customItem.amount = baseItem.amount
                customItem
            } catch (e: Exception) {
                plugin.logger.warning("创建自定义材质物品失败: ${e.message}")
                baseItem
            }
        } else {
            baseItem
        }

        // 使用新的显示信息应用方法
        return applyPokemonDisplayInfo(finalItem, pokemonName, nationalDex, hasCaught)
    }

    /**
     * 为交换获得的精灵应用特殊显示信息
     */
    private fun applyTradedPokemonDisplayInfo(item: ItemStack, pokemonName: String, nationalDex: Int): ItemStack {
        val meta = item.itemMeta ?: return item

        // 构建占位符映射
        val placeholders = buildPokemonPlaceholders(pokemonName, nationalDex, false) // 显示为未收集状态

        // 设置显示名称 - 交换获得的精灵显示为未收集但有特殊标记
        val nameTemplate = "§7{pokemon_name} §6(已交换)"

        // 构建 lore 模板
        val loreTemplate = mutableListOf<String>()
        loreTemplate.add("§7图鉴编号: §f#{poke_dex}")
        loreTemplate.add("§7类型: §e{pokemon_type}")
        loreTemplate.add("§7刷新地: §b{pokemon_habitat}")
        loreTemplate.add("")
        loreTemplate.add("§6✓ 已通过交换获得")
        loreTemplate.add("§c✗ 未通过捕捉收集")
        loreTemplate.add("")
        loreTemplate.add("§e请自己捕捉收集此精灵")
        loreTemplate.add("§7捕捉后才算完成收集")

        // 替换占位符
        meta.setDisplayName(replacePlaceholders(nameTemplate, placeholders))

        // 处理 lore，过滤掉空的信息行
        val processedLore = loreTemplate
            .map { replacePlaceholders(it, placeholders) }
            .filter { line ->
                !line.contains("§7类型: §e") || !line.endsWith("§e")
            }
            .filter { line ->
                !line.contains("§7刷新地: §b") || !line.endsWith("§b")
            }
        meta.lore = processedLore

        // 不添加附魔效果，因为交换获得的不算已收集
        item.itemMeta = meta
        return item
    }

    /**
     * 创建新的精灵物品（内部方法）- 模仿 LGPokemonMenu 的做法
     */
    private fun createNewPokemonItem(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 方法1：使用 Cobblemon 原生精灵物品（模仿 LGPokemonMenu）
        val cobblemonItem = CobblemonItemHelper.createPokemonItemByName(pokemonName, 1)
        if (cobblemonItem != null) {
            // 只替换显示信息，保留原生精灵模型
            return applyPokemonDisplayInfo(cobblemonItem, pokemonName, nationalDex, hasCaught)
        }

        // 方法2：尝试使用构建器创建
        val builderItem = defaultBuilder.build(pokemonName, hasCaught)
        if (builderItem != null) {
            return applyPokemonDisplayInfo(builderItem, pokemonName, nationalDex, hasCaught)
        }

        // 方法3：使用备用方案
        return createFallbackItem(pokemonName, nationalDex, hasCaught)
    }

    /**
     * 应用精灵显示信息 - 模仿 LGPokemonMenu 的占位符系统
     */
    private fun applyPokemonDisplayInfo(item: ItemStack, pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 如果启用了自定义材质且精灵已收集，则替换物品材质
        val finalItem = if (hasCaught && plugin.config.useCustomMaterialForCaught) {
            val customItem = createCustomMaterialItem(pokemonName)
            // 保留原物品的数量
            customItem.amount = item.amount
            customItem
        } else {
            item
        }

        val meta = finalItem.itemMeta ?: return finalItem

        // 构建占位符映射 - 模仿 LGPokemonMenu 的 PlaceholderUtil
        val placeholders = buildPokemonPlaceholders(pokemonName, nationalDex, hasCaught)

        // 使用配置模板替换显示信息
        val nameTemplate = if (hasCaught) {
            "§a{pokemon_name}"
        } else {
            "§7{pokemon_name} §8(未收集)"
        }

        // 构建动态 lore 模板
        val loreTemplate = mutableListOf<String>()
        loreTemplate.add("§7图鉴编号: §f#{poke_dex}")
        loreTemplate.add("§7英文名: §f{pokemon_english_name}")

        // 动态添加精灵信息（在占位符替换后检查）
        loreTemplate.add("§7类型: §e{pokemon_type}")
        loreTemplate.add("§7刷新地: §b{pokemon_habitat}")

        loreTemplate.add("")
        loreTemplate.add(if (hasCaught) "§a✓ 已收集" else "§c✗ 未收集")

        // 替换占位符
        meta.setDisplayName(replacePlaceholders(nameTemplate, placeholders))

        // 处理 lore，过滤掉空的信息行
        val processedLore = loreTemplate.map { replacePlaceholders(it, placeholders) }
            .filter { line ->
                // 过滤掉包含空占位符的行
                !line.contains("§7类型: §e") && !line.contains("§7刷新地: §b") ||
                (line.contains("§7类型: §e") && !line.endsWith("§e")) ||
                (line.contains("§7刷新地: §b") && !line.endsWith("§b"))
            }
        meta.lore = processedLore

        // 为已收集精灵添加附魔效果
        if (hasCaught) {
            meta.addEnchant(org.bukkit.enchantments.Enchantment.UNBREAKING, 1, true)
            meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS)
        }

        // 隐藏其他不必要的信息
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ATTRIBUTES)
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_DESTROYS)
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_PLACED_ON)
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_UNBREAKABLE)
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_DYE)

        finalItem.itemMeta = meta
        return finalItem
    }

    /**
     * 构建精灵占位符映射 - 模仿 LGPokemonMenu 的 PlaceholderUtil.buildPokemonInfoPlaceholders
     */
    private fun buildPokemonPlaceholders(pokemonName: String, nationalDex: Int, hasCaught: Boolean): Map<String, String> {
        val placeholders = mutableMapOf<String, String>()

        // 基础信息
        placeholders["{pokemon_name}"] = cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonName)
        placeholders["{pokemon_english_name}"] = pokemonName
        placeholders["{poke_dex}"] = String.format("%03d", nationalDex)
        placeholders["{national_dex}"] = nationalDex.toString()
        placeholders["{caught_status}"] = if (hasCaught) "已收集" else "未收集"
        placeholders["{caught_color}"] = if (hasCaught) "§a" else "§7"

        // 添加精灵类型和刷新地信息
        val pokemonData = plugin.generationManager.getPokemonData(pokemonName)
        if (pokemonData != null) {
            placeholders["{pokemon_type}"] = pokemonData.type
            placeholders["{pokemon_habitat}"] = pokemonData.habitat
        } else {
            placeholders["{pokemon_type}"] = ""
            placeholders["{pokemon_habitat}"] = ""
        }

        return placeholders
    }

    /**
     * 替换占位符 - 模仿 LGPokemonMenu 的 PlaceholderUtil.replacePlaceholders
     */
    private fun replacePlaceholders(template: String, placeholders: Map<String, String>): String {
        var result = template
        placeholders.forEach { (placeholder, value) ->
            result = result.replace(placeholder, value)
        }
        return result
    }

    /**
     * 创建简化版精灵物品
     */
    fun createSimplePokemonItem(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 使用简化构建器
        val cobblemonItem = simpleBuilder.build(pokemonName, hasCaught)
        if (cobblemonItem != null) {
            return cobblemonItem
        }

        // 备用方案
        return createFallbackItem(pokemonName, nationalDex, hasCaught)
    }

    /**
     * 为 Cobblemon 创建的物品应用自定义显示信息
     */
    private fun applyCustomDisplay(item: ItemStack, pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        // 如果启用了自定义材质且精灵已收集，则替换物品材质
        val finalItem = if (hasCaught && plugin.config.useCustomMaterialForCaught) {
            val customItem = createCustomMaterialItem(pokemonName)
            // 保留原物品的数量
            customItem.amount = item.amount
            customItem
        } else {
            item
        }

        // 使用新的显示信息应用方法
        return applyPokemonDisplayInfo(finalItem, pokemonName, nationalDex, hasCaught)

    }



    /**
     * 创建备用物品（当 Cobblemon API 失败时使用）
     */
    private fun createFallbackItem(pokemonName: String, nationalDex: Int, hasCaught: Boolean): ItemStack {
        val item = if (hasCaught && plugin.config.useCustomMaterialForCaught) {
            createCustomMaterialItem(pokemonName)
        } else {
            createSimplifiedPokemonItem(pokemonName)
        }
        val meta = item.itemMeta

        if (meta != null) {
            // 设置显示名称
            val displayName = if (hasCaught) {
                "§a#${String.format("%03d", nationalDex)} ${getPokemonDisplayName(pokemonName)}"
            } else {
                "§7#${String.format("%03d", nationalDex)} ${getPokemonDisplayName(pokemonName)}"
            }
            meta.setDisplayName(displayName)

            // 设置描述
            val lore = mutableListOf<String>()
            val pokemonData = plugin.generationManager.getPokemonData(pokemonName)
            if (pokemonData != null) {
                lore.add("§f类型: §e${pokemonData.type}")
                lore.add("")
                if (hasCaught) {
                    lore.add("§a已收集")
                    lore.add("§e点击查看详情或领取奖励")
                } else {
                    lore.add("§7尚未收集")
                    lore.add("§7捕获精灵来解锁奖励")
                }
            } else {
                if (hasCaught) {
                    lore.add("§a已收集")
                    lore.add("§e点击查看详情")
                } else {
                    lore.add("§7尚未收集")
                    lore.add("§7捕获精灵来解锁")
                }
            }
            lore.add("")
            lore.add("§8备用显示模式")
            meta.lore = lore

            // 先设置基础 meta（备用模式）
            item.itemMeta = meta

            // 为已收集的精灵添加附魔效果（备用模式，模仿PokePlate插件的方式）
            if (hasCaught) {
                // 方法1：先直接在物品上添加附魔（使用UNBREAKING，更兼容）
                if (item.enchantments.isEmpty()) {
                    item.addUnsafeEnchantment(Enchantment.UNBREAKING, 1)
                }

                // 方法2：然后处理ItemMeta和ItemFlag
                val finalMeta = item.itemMeta
                if (finalMeta != null) {
                    // 添加HIDE_ENCHANTS标志来隐藏附魔信息，但保留光效
                    finalMeta.addItemFlags(ItemFlag.HIDE_ENCHANTS)
                    // 隐藏所有不必要的调试信息
                    finalMeta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
                    finalMeta.addItemFlags(ItemFlag.HIDE_DESTROYS)
                    finalMeta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
                    finalMeta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
                    finalMeta.addItemFlags(ItemFlag.HIDE_DYE)
                    finalMeta.addItemFlags(ItemFlag.HIDE_ADDITIONAL_TOOLTIP)

                    // 重新设置meta
                    item.itemMeta = finalMeta

                    // 验证附魔状态
                    val hasEnchant = item.enchantments.isNotEmpty()

                    // 验证ItemFlag状态
                    val currentFlags = item.itemMeta?.itemFlags ?: emptySet()
                    if (currentFlags.contains(ItemFlag.HIDE_ENCHANTS)) {
                    } else {
                        finalMeta.addItemFlags(ItemFlag.HIDE_ENCHANTS)
                        finalMeta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
                        finalMeta.addItemFlags(ItemFlag.HIDE_DESTROYS)
                        finalMeta.addItemFlags(ItemFlag.HIDE_PLACED_ON)
                        finalMeta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE)
                        finalMeta.addItemFlags(ItemFlag.HIDE_DYE)
                        finalMeta.addItemFlags(ItemFlag.HIDE_ADDITIONAL_TOOLTIP)
                        item.itemMeta = finalMeta
                    }
                } else {
                }
            } else {
            }
        }

        return item
    }






    
    /**
     * 创建简化版精灵物品
     */
    private fun createSimplifiedPokemonItem(pokemonName: String): ItemStack {
        // 根据精灵类型选择材质
        val material = when {
            pokemonName.contains("fire") || pokemonName in listOf("charmander", "charmeleon", "charizard", "growlithe", "arcanine", "ponyta", "rapidash", "magmar", "flareon", "moltres") -> 
                Material.BLAZE_POWDER
            pokemonName.contains("water") || pokemonName in listOf("squirtle", "wartortle", "blastoise", "psyduck", "golduck", "poliwag", "poliwhirl", "poliwrath", "tentacool", "tentacruel", "slowpoke", "slowbro", "seel", "dewgong", "shellder", "cloyster", "krabby", "kingler", "horsea", "seadra", "goldeen", "seaking", "staryu", "starmie", "magikarp", "gyarados", "lapras", "vaporeon", "omanyte", "omastar", "kabuto", "kabutops") -> 
                Material.PRISMARINE_SHARD
            pokemonName.contains("grass") || pokemonName in listOf("bulbasaur", "ivysaur", "venusaur", "oddish", "gloom", "vileplume", "paras", "parasect", "bellsprout", "weepinbell", "victreebel", "exeggcute", "exeggutor", "tangela") -> 
                Material.EMERALD
            pokemonName.contains("electric") || pokemonName in listOf("pikachu", "raichu", "magnemite", "magneton", "voltorb", "electrode", "electabuzz", "jolteon", "zapdos") -> 
                Material.GLOWSTONE_DUST
            else -> Material.DIAMOND
        }
        
        return ItemStack(material)
    }
    
    /**
     * 创建自定义材质物品（用于已收集的精灵）
     */
    private fun createCustomMaterialItem(pokemonName: String): ItemStack {
        val materialConfig = plugin.config.customCaughtMaterial
        return parseCustomMaterial(materialConfig)
    }

    /**
     * 解析自定义材质配置
     * 支持多种格式：
     * 1. 普通材质: "DIAMOND"
     * 2. 普通材质+CustomModelData: "DIAMOND:12345"
     * 3. Paper CustomModelData: "PAPER:CustomModelData数字"
     */
    private fun parseCustomMaterial(materialConfig: String): ItemStack {
        return when {
            // Paper CustomModelData 格式: PAPER:CustomModelData数字
            materialConfig.startsWith("PAPER:", ignoreCase = true) -> {
                createPaperCustomModelDataItem(materialConfig)
            }

            // 普通 Minecraft 材质，支持 CustomModelData
            else -> {
                // 支持 "EMERALD:1234" 这种格式
                val parts = materialConfig.split(":")
                val material = try { Material.valueOf(parts[0].uppercase()) } catch (e: Exception) { Material.EMERALD }
                val item = ItemStack(material)
                if (parts.size > 1) {
                    val customModelData = parts[1].toIntOrNull()
                    if (customModelData != null) {
                        val meta = item.itemMeta
                        if (meta != null) {
                            meta.setCustomModelData(customModelData)
                            item.itemMeta = meta
                        }
                    }
                }
                item
            }
        }
    }

    /**
     * 创建 Paper CustomModelData 物品
     * 格式: PAPER:CustomModelData数字
     * 例如: PAPER:12345
     */
    private fun createPaperCustomModelDataItem(config: String): ItemStack {
        return try {
            // 解析格式: PAPER:CustomModelData数字
            val parts = config.split(":")
            if (parts.size >= 2) {
                val customModelData = parts[1].toIntOrNull()

                if (customModelData != null) {
                    // 使用 PAPER 材质
                    val item = ItemStack(Material.PAPER)
                    val meta = item.itemMeta

                    if (meta != null) {
                        // 设置 CustomModelData
                        meta.setCustomModelData(customModelData)
                        item.itemMeta = meta

                        return item
                    } else {
                        return ItemStack(Material.PAPER)
                    }
                } else {
                    return ItemStack(Material.PAPER)
                }
            } else {
                return ItemStack(Material.EMERALD)
            }
        } catch (e: Exception) {
            return ItemStack(Material.EMERALD)
        }
    }





    /**
     * 创建精灵球物品
     */
    private fun createPokeBallItem(): ItemStack {
        return ItemStack(Material.ENDER_PEARL) // 使用末影珍珠代表精灵球
    }
    
    /**
     * 获取精灵显示名称
     * 使用 PokemonDetector 中的完整中文映射
     */
    private fun getPokemonDisplayName(pokemonName: String): String {
        // 使用 PokemonNameMapping 中的完整1-9世代中文映射
        return cn.acebrand.acedex.pokemon.PokemonNameMapping.getPokemonChineseNameFromEnglish(pokemonName)
    }

    /**
     * 清理指定精灵的缓存（当收集状态发生变化时调用）
     * 只清理已收集精灵的缓存，保留未收集精灵的缓存以避免重新创建模型
     */
    fun clearPokemonCache(pokemonName: String, nationalDex: Int) {
        val caughtKeysToRemove = caughtPokemonItemCache.keys.filter {
            it.startsWith("${pokemonName}_${nationalDex}_")
        }
        caughtKeysToRemove.forEach { key ->
            caughtPokemonItemCache.remove(key)
        }

        // 注意：不清理未收集精灵的缓存，因为模型不会变化
        // 这样可以避免重新创建模型导致的卡顿

        // 保存缓存到文件
        saveCacheToFiles()
    }

    /**
     * 清理物品缓存（重命名避免冲突）
     */
    fun clearItemCache() {
        caughtPokemonItemCache.clear()
        uncaughtPokemonItemCache.clear()
        buttonIdToPokemonName.clear()

        // 删除本地缓存文件
        if (cacheStatusFile.exists()) {
            cacheStatusFile.delete()
        }
        if (buttonIdCacheFile.exists()) {
            buttonIdCacheFile.delete()
        }

        plugin.logger.info("已清理所有精灵物品缓存")
    }

    /**
     * 获取物品缓存统计信息（重命名避免冲突）
     */
    fun getItemCacheStats(): String {
        return buildString {
            appendLine("已收集精灵物品缓存: ${caughtPokemonItemCache.size}")
            appendLine("未收集精灵物品缓存: ${uncaughtPokemonItemCache.size}")
            appendLine("按钮ID映射缓存: ${buttonIdToPokemonName.size}")
            appendLine("本地缓存文件:")
            appendLine("  - 缓存状态文件: ${if (cacheStatusFile.exists()) "存在" else "不存在"}")
            appendLine("  - 按钮ID映射: ${if (buttonIdCacheFile.exists()) "存在" else "不存在"}")
        }
    }

    /**
     * 保存当前缓存到文件（公共方法）
     */
    fun saveCache() {
        saveCacheToFiles()
    }

    /**
     * 清理所有缓存
     */
    fun clearAllCache() {
        val caughtCacheSize = caughtPokemonItemCache.size
        val uncaughtCacheSize = uncaughtPokemonItemCache.size

        caughtPokemonItemCache.clear()
        uncaughtPokemonItemCache.clear()

        plugin.logger.info("已清理精灵物品缓存: 已收集${caughtCacheSize}个, 未收集${uncaughtCacheSize}个")
    }

    /**
     * 只清理已收集精灵的缓存，保留未收集精灵的缓存
     */
    fun clearCaughtPokemonCache() {
        val cacheSize = caughtPokemonItemCache.size
        caughtPokemonItemCache.clear()
        plugin.logger.info("已清理已收集精灵缓存: ${cacheSize}个")
    }

    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): String {
        return "精灵物品缓存: 已收集${caughtPokemonItemCache.size}个, 未收集${uncaughtPokemonItemCache.size}个, 总计${caughtPokemonItemCache.size + uncaughtPokemonItemCache.size}个"
    }



    /**
     * 测试方法：验证 Cobblemon 集成
     */
    fun testCobblemonIntegration(): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()

        plugin.logger.info("=== 开始测试 Cobblemon 集成 ===")

        // 测试我们自己的 Cobblemon 集成
        plugin.logger.info("测试自定义 Cobblemon 集成...")
        val cobblemonResults = CobblemonItemHelper.testCobblemonIntegration()
        results.putAll(cobblemonResults.mapKeys { "自定义-${it.key}" })

        // 不再使用 LGPokemonMenu 集成

        // 测试完整的物品创建流程
        plugin.logger.info("测试完整物品创建流程...")
        val testPokemon = listOf("pikachu", "charizard", "blastoise")
        testPokemon.forEach { pokemonName ->
            try {
                val item = createPokemonItem(pokemonName, 1, true)
                val success = item != null && item.type.name.contains("COBBLEMON") || item.type != Material.EMERALD
                results["完整流程-$pokemonName"] = success

                if (success) {
                    plugin.logger.info("✓ $pokemonName: 完整流程成功")
                } else {
                    plugin.logger.warning("✗ $pokemonName: 完整流程失败")
                }
            } catch (e: Exception) {
                results["完整流程-$pokemonName"] = false
                plugin.logger.warning("✗ $pokemonName: 完整流程异常 - ${e.message}")
            }
        }

        val successCount = results.values.count { it }
        plugin.logger.info("=== 集成测试完成: $successCount/${results.size} 成功 ===")

        return results
    }

}
