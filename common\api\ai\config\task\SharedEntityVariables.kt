/*
 * Copyright (C) 2023 Cobblemon Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.cobblemon.mod.common.api.ai.config.task

object SharedEntityVariables {
    const val MOVEMENT_CATEGORY = "movement"
    const val FEAR_CATEGORY = "fear"
    const val ATTACKING_CATEGORY = "attacking"
    const val BATTLING_CATEGORY = "battling"
    const val LOOKING_CATEGORY = "looking"

    const val WALK_SPEED = "walk_speed"
    const val FLEE_SPEED_MULTIPLIER = "flee_speed_multiplier"
    const val FLEE_DESIRED_DISTANCE = "flee_desired_distance"
    const val SEE_DISTANCE = "see_distance"
}
