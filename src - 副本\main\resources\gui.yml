gui:
  title: "§6§lAceDex §7- 精灵图鉴%img_offset_-48%"
  size: 54
  
  # 9个世代独立标题配置
  generation-titles:
    gen1: "§c§l关都地区%img_offset_-32%"
    gen2: "§6§l城都地区%img_offset_-32%"
    gen3: "§e§l丰缘地区%img_offset_-32%"
    gen4: "§b§l神奥地区%img_offset_-32%"
    gen5: "§5§l合众地区%img_offset_-32%"
    gen6: "§d§l卡洛斯地区%img_offset_-32%"
    gen7: "§a§l阿罗拉地区%img_offset_-32%"
    gen8: "§9§l伽勒尔地区%img_offset_-32%"
    gen9: "§f§l帕底亚地区%img_offset_-32%"
  
  # 世代精灵球在主菜单中的位置配置 (0-53的槽位) - 3x3布局
  generation-slots:
    gen1: 11  # 第2行第3列
    gen2: 13  # 第2行第5列
    gen3: 15  # 第2行第7列
    gen4: 20  # 第3行第3列
    gen5: 22  # 第3行第5列
    gen6: 24  # 第3行第7列
    gen7: 29  # 第4行第3列
    gen8: 31  # 第4行第5列
    gen9: 33  # 第4行第7列
  
  # 世代精灵球的材质配置 - 使用 Cobblemon 精灵球
  # 支持格式：
  # 1. 原版材质: "ENDER_PEARL", "DIAMOND", "EMERALD"
  # 2. 自定义模型: "PAPER:12345" (PAPER材质 + CustomModelData)
  # 3. 原版材质+模型: "DIAMOND:1001" (钻石材质 + CustomModelData)
  # 4. Cobblemon 物品: "COBBLEMON_POKE_BALL" (Cobblemon 模组物品)
  generation-materials:
    gen1: "COBBLEMON_POKE_BALL"     # 关都 - 精灵球
    gen2: "COBBLEMON_GREAT_BALL"    # 城都 - 超级球
    gen3: "COBBLEMON_ULTRA_BALL"    # 丰缘 - 高级球
    gen4: "COBBLEMON_MASTER_BALL"   # 神奥 - 大师球
    gen5: "COBBLEMON_TIMER_BALL"    # 合众 - 计时球
    gen6: "COBBLEMON_LUXURY_BALL"   # 卡洛斯 - 豪华球
    gen7: "COBBLEMON_PREMIER_BALL"  # 阿罗拉 - 纪念球
    gen8: "COBBLEMON_DUSK_BALL"     # 伽勒尔 - 黄昏球
    gen9: "COBBLEMON_QUICK_BALL"    # 帕底亚 - 先机球
  
  # 主菜单功能按钮配置
  main-menu-buttons:
    # 个人统计按钮
    stats-button:
      slot: 40                    # 按钮位置 (0-53)
      material: "PAPER:10030"     # 按钮材质
    
    # 关闭按钮
    close-button:
      slot: 49                    # 按钮位置 (0-53)
      material: "PAPER:10030"     # 按钮材质
    
    # 全世界收集进度按钮
    progress-button:
      slot: 4                     # 按钮位置 (0-53)
      # 根据完成进度自动显示不同精灵球
      materials:
        0-25: "COBBLEMON_POKE_BALL"     # 0-25% 完成度 - 精灵球
        25-50: "COBBLEMON_GREAT_BALL"   # 25-50% 完成度 - 超级球
        50-75: "COBBLEMON_ULTRA_BALL"   # 50-75% 完成度 - 高级球
        75-99: "COBBLEMON_MASTER_BALL"  # 75-99% 完成度 - 大师球
        100: "COBBLEMON_PREMIER_BALL"             # 100% 完成度 - 纪念球
  
  # GUI装饰设置
  decoration-material: "GRAY_STAINED_GLASS_PANE"     # 边框材质，设置为 AIR 可隐藏边框
  
  # 分页按钮设置
  previous-page-material: "ARROW"
  next-page-material: "ARROW"
  
  # GUI导航物品设置
  page-indicator-material: "PAPER"
  back-to-main-menu-material: "BARRIER"
  progress-info-material: "EXPERIENCE_BOTTLE"
  
  # 点击冷却设置 (毫秒)
  click-cooldown:
    stats-button: 2000      # 统计按钮冷却时间
    progress-button: 3000   # 进度按钮冷却时间
    generation-reward: 2500 # 世代奖励冷却时间

# 精灵显示设置
pokemon:
  display:
    # 是否为已收集精灵使用自定义材质
    use-custom-material-for-caught: false
    # 已收集精灵的自定义材质 (支持 EMERALD, PAPER:12345 等格式)
    custom-caught-material: "EMERALD"
    # 自定义材质示例说明
    custom-material-examples:
      - "# === 普通 Minecraft 材质 ==="
      - "# EMERALD - 绿宝石（默认）"
      - "# DIAMOND - 钻石"
      - "# GOLD_INGOT - 金锭"
      - "# === 自定义模型数据 ==="
      - "# PAPER:12345 - 纸张材质 + CustomModelData 12345"
      - "# DIAMOND:1001 - 钻石材质 + CustomModelData 1001"
      - "# === Cobblemon 模组物品 ==="
      - "# COBBLEMON_POKE_BALL - Cobblemon 精灵球"
      - "# COBBLEMON_MASTER_BALL - Cobblemon 大师球"
