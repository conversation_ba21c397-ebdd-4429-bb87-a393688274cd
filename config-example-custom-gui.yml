# AceDex GUI自定义物品配置示例
# 这个文件展示了如何自定义GUI中的各种导航物品

# GUI设置
gui:
  enabled: true
  title: "§6§lAceDex §7- 精灵图鉴"
  size: 54

  # GUI装饰材质 - 用于世代菜单的玻璃板边框装饰
  decoration-material: "GRAY_STAINED_GLASS_PANE"

  # 分页按钮材质 - 用于世代菜单的上一页和下一页按钮
  previous-page-material: "ARROW"  # 上一页按钮材质
  next-page-material: "ARROW"      # 下一页按钮材质

  # GUI导航物品材质 - 用于各种GUI导航元素
  page-indicator-material: "PAPER"           # 页码指示器材质（显示第几页）
  back-to-main-menu-material: "BARRIER"     # 返回主菜单按钮材质
  progress-info-material: "EXPERIENCE_BOTTLE" # 收集进度显示材质

  # 主菜单物品材质 - 用于主菜单界面的物品
  close-button-material: "BARRIER"          # 关闭按钮材质
  stats-button-material: "ENCHANTED_BOOK"   # 个人统计按钮材质

# ========================================
# 自定义材质示例配置
# ========================================

# 示例1：使用普通Minecraft材质
gui-example-1:
  page-indicator-material: "BOOK"           # 使用书本作为页码指示器
  back-to-main-menu-material: "RED_WOOL"   # 使用红色羊毛作为返回按钮
  progress-info-material: "ENCHANTED_BOOK" # 使用附魔书作为进度显示
  close-button-material: "RED_CONCRETE"    # 使用红色混凝土作为关闭按钮
  stats-button-material: "COMPASS"         # 使用指南针作为统计按钮

# 示例2：使用CustomModelData
gui-example-2:
  page-indicator-material: "PAPER:1001"     # 使用纸张+自定义模型1001
  back-to-main-menu-material: "PAPER:1002" # 使用纸张+自定义模型1002
  progress-info-material: "PAPER:1003"     # 使用纸张+自定义模型1003
  close-button-material: "PAPER:1004"      # 使用纸张+自定义模型1004
  stats-button-material: "PAPER:1005"      # 使用纸张+自定义模型1005

# 示例3：混合使用不同材质
gui-example-3:
  page-indicator-material: "WRITABLE_BOOK"  # 使用书与笔
  back-to-main-menu-material: "PAPER:2001" # 使用自定义模型
  progress-info-material: "KNOWLEDGE_BOOK" # 使用知识之书

# ========================================
# 配置说明
# ========================================

# 支持的材质格式：
# 1. 普通材质: "MATERIAL_NAME"
#    例如: "PAPER", "BARRIER", "EXPERIENCE_BOTTLE"
#
# 2. 普通材质+CustomModelData: "MATERIAL_NAME:数字"
#    例如: "EMERALD:12345", "DIAMOND:6789"
#
# 3. Paper CustomModelData: "PAPER:数字"
#    例如: "PAPER:1001" - 使用PAPER材质，CustomModelData为1001

# 常用材质推荐：
# 页码指示器: PAPER, BOOK, WRITABLE_BOOK, MAP
# 返回按钮: BARRIER, RED_WOOL, REDSTONE, ARROW
# 进度显示: EXPERIENCE_BOTTLE, ENCHANTED_BOOK, KNOWLEDGE_BOOK, COMPASS

# 注意事项：
# 1. 如果材质名称错误，将使用默认材质
# 2. CustomModelData需要资源包支持才能正确显示
# 3. 修改配置后需要重载插件或重启服务器
# 4. 所有物品都会保留原有的显示名称和描述
