/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.acedex.listener

import cn.acebrand.acedex.AceDex
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.InventoryCloseEvent
import org.bukkit.event.inventory.InventoryOpenEvent
import org.bukkit.event.player.PlayerJoinEvent
import org.bukkit.event.player.PlayerQuitEvent

/**
 * Cobblemon 事件监听器
 * 监听精灵相关事件并更新图鉴数据
 */
class CobblemonListener(private val plugin: AceDex) : Listener {
    
    /**
     * 玩家加入事件
     */
    @EventHandler
    fun onPlayerJoin(event: PlayerJoinEvent) {
        val player = event.player

        // 检查是否是第一次加入
        if (!player.hasPlayedBefore()) {
            // 发送欢迎消息
            plugin.server.scheduler.runTaskLater(plugin, Runnable {
                player.sendMessage("§6§l=== 欢迎使用 AceDex ===")
                player.sendMessage("§e这是一个精灵图鉴插件！")
                player.sendMessage("§7捕获精灵会自动更新你的图鉴进度")
                player.sendMessage("§7使用 §f/acedex §7查看更多信息")
            }, 40L) // 2秒后发送
        }

        // 异步检查并更新玩家数据（如果本地文件不存在或过期）
        plugin.server.scheduler.runTaskAsynchronously(plugin, Runnable {
            val lastModified = plugin.pokemonDetector.getPlayerDataFileLastModified(player)
            val currentTime = System.currentTimeMillis()

            // 如果文件不存在或超过1小时未更新，重新检测
            if (lastModified == 0L || (currentTime - lastModified) > 3600000L) {
                plugin.pokemonDetector.detectAndSavePlayerPokemon(player)
            }
        })
    }

    /**
     * 玩家退出事件 - 清理冷却数据防止内存泄漏
     */
    @EventHandler
    fun onPlayerQuit(event: PlayerQuitEvent) {
        val player = event.player

        // GUI相关的冷却数据清理已在DexMainGui的onPlayerQuit事件中处理

        // 清理精灵物品缓存中与该玩家相关的数据
        // 注意：这里不清理全部缓存，只清理玩家相关的数据
        // 精灵物品缓存是全局的，不需要按玩家清理

        if (plugin.config.enableDebug) {
        }
    }

    /**
     * 监听玩家关闭背包/PC界面
     * 只在关闭时检测，减少不必要的检测
     */
    @EventHandler
    fun onInventoryClose(event: InventoryCloseEvent) {
        val player = event.player as? org.bukkit.entity.Player ?: return
        val title = event.view.title

        // 检测是否是精灵相关的界面（PC、背包等）
        if (isPokemonRelatedInventory(title)) {
            // 延迟检测数据变化，确保数据已同步
            plugin.server.scheduler.runTaskLater(plugin, Runnable {
                // 强制刷新玩家数据并保存到本地文件
                plugin.pokemonDetector.detectAndSavePlayerPokemon(player)

                // 刷新图鉴界面
                plugin.mainGui.refreshCurrentGui(player)
            }, 40L) // 2秒后检测
        }
    }

    /**
     * 判断是否是精灵相关的界面
     */
    private fun isPokemonRelatedInventory(title: String): Boolean {
        val lowerTitle = title.lowercase()
        return lowerTitle.contains("pc") ||
               lowerTitle.contains("box") ||
               lowerTitle.contains("storage") ||
               lowerTitle.contains("精灵") ||
               lowerTitle.contains("宝可梦") ||
               lowerTitle.contains("pokemon") ||
               lowerTitle.contains("cobblemon")
    }

    // TODO: 添加Cobblemon精灵捕获事件监听
    // 由于需要Cobblemon API，这里暂时注释掉
    /*
    @EventHandler
    fun onPokemonCapture(event: PokemonCaptureEvent) {
        val player = event.player
        val pokemon = event.pokemon
        
        // 获取精灵所属世代
        val generation = plugin.generationManager.getGenerationByPokemon(pokemon.species.name)
        if (generation != null) {
            // 更新玩家进度
            val progress = plugin.generationManager.getPlayerProgress(player, generation.id)
            if (progress != null) {
                // 检查奖励
                plugin.rewardManager.checkGenerationRewards(player, generation.id, progress.percentage)
                
                // 如果是异色精灵，检查异色奖励
                if (pokemon.shiny) {
                    val shinyCount = getPlayerShinyCount(player) // TODO: 实现获取异色精灵数量
                    plugin.rewardManager.checkShinyRewards(player, shinyCount)
                }
                
                // 发送进度消息
                player.sendMessage("§a§l[图鉴] §f捕获了 ${pokemon.species.name}！")
                player.sendMessage("§7${generation.displayName} 进度: §a${progress.percentage}%")
            }
        }
    }
    */
}
