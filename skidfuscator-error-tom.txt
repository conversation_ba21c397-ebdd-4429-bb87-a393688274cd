handler=Block #CR, types=[Ljava/io/IOException;], range=[Block #CQ, Block #CP]
handler=Block #CU, types=[Ljava/io/IOException;], range=[Block #CT, Block #CS]
handler=Block #CX, types=[Ljava/io/IOException;], range=[Block #CW, Block #CV]
handler=Block #DA, types=[Ljava/io/IOException;], range=[Block #CZ, Block #CY]
handler=Block #DD, types=[Ljava/lang/IllegalAccessException;], range=[Block #DC, Block #DB]
handler=Block #DG, types=[Ljava/io/IOException;], range=[Block #DF, Block #DE]
handler=Block #DJ, types=[Ljava/lang/IllegalAccessException;], range=[Block #DI, Block #DH]
handler=Block #DM, types=[Ljava/io/IOException;], range=[Block #DL, Block #DK]
handler=Block #DP, types=[Ljava/lang/RuntimeException;], range=[Block #DO, Block #DN]
handler=Block #DS, types=[Ljava/lang/IllegalAccessException;], range=[Block #DR, Block #DQ]
handler=Block #DV, types=[Ljava/lang/IllegalAccessException;], range=[Block #DU, Block #DT]
handler=Block #DY, types=[Ljava/lang/RuntimeException;], range=[Block #DX, Block #DW]
handler=Block #EB, types=[Ljava/lang/IllegalAccessException;], range=[Block #EA, Block #DZ]
handler=Block #EE, types=[Ljava/lang/RuntimeException;], range=[Block #ED, Block #EC]
handler=Block #EH, types=[Ljava/lang/IllegalAccessException;], range=[Block #EG, Block #EF]
handler=Block #EK, types=[Ljava/lang/IllegalAccessException;], range=[Block #EJ, Block #EI]
handler=Block #EN, types=[Ljava/io/IOException;], range=[Block #EM, Block #EL]
handler=Block #EQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #EP, Block #EO]
handler=Block #ET, types=[Ljava/lang/IllegalAccessException;], range=[Block #ES, Block #ER]
handler=Block #EW, types=[Ljava/io/IOException;], range=[Block #EV, Block #EU]
handler=Block #EZ, types=[Ljava/io/IOException;], range=[Block #EY, Block #EX]
handler=Block #FC, types=[Ljava/io/IOException;], range=[Block #FB, Block #FA]
handler=Block #FF, types=[Ljava/lang/IllegalAccessException;], range=[Block #FE, Block #FD]
handler=Block #FI, types=[Ljava/io/IOException;], range=[Block #FH, Block #FG]
handler=Block #FL, types=[Ljava/io/IOException;], range=[Block #FK, Block #FJ]
handler=Block #FO, types=[Ljava/io/IOException;], range=[Block #FN, Block #FM]
handler=Block #FR, types=[Ljava/lang/RuntimeException;], range=[Block #FQ, Block #FP]
handler=Block #FU, types=[Ljava/lang/IllegalAccessException;], range=[Block #FT, Block #FS]
handler=Block #FX, types=[Ljava/lang/RuntimeException;], range=[Block #FW, Block #FV]
handler=Block #GA, types=[Ljava/io/IOException;], range=[Block #FZ, Block #FY]
handler=Block #GD, types=[Ljava/lang/IllegalAccessException;], range=[Block #GC, Block #GB]
handler=Block #GG, types=[Ljava/lang/RuntimeException;], range=[Block #GF, Block #GE]
handler=Block #GJ, types=[Ljava/lang/RuntimeException;], range=[Block #GI, Block #GH]
handler=Block #GM, types=[Ljava/lang/RuntimeException;], range=[Block #GL, Block #GK]
handler=Block #GP, types=[Ljava/lang/IllegalAccessException;], range=[Block #GO, Block #GN]
handler=Block #GS, types=[Ljava/lang/RuntimeException;], range=[Block #GR, Block #GQ]
handler=Block #GV, types=[Ljava/lang/IllegalAccessException;], range=[Block #GU, Block #GT]
handler=Block #GY, types=[Ljava/lang/IllegalAccessException;], range=[Block #GX, Block #GW]
handler=Block #HB, types=[Ljava/io/IOException;], range=[Block #HA, Block #GZ]
handler=Block #HE, types=[Ljava/io/IOException;], range=[Block #HD, Block #HC]
handler=Block #HH, types=[Ljava/lang/IllegalAccessException;], range=[Block #HG, Block #HF]
handler=Block #HK, types=[Ljava/lang/IllegalAccessException;], range=[Block #HJ, Block #HI]
handler=Block #HN, types=[Ljava/io/IOException;], range=[Block #HM, Block #HL]
handler=Block #HQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #HP, Block #HO]
handler=Block #HT, types=[Ljava/lang/RuntimeException;], range=[Block #HS, Block #HR]
handler=Block #HW, types=[Ljava/lang/IllegalAccessException;], range=[Block #HV, Block #HU]
handler=Block #HZ, types=[Ljava/lang/IllegalAccessException;], range=[Block #HY, Block #HX]
handler=Block #IC, types=[Ljava/io/IOException;], range=[Block #IB, Block #IA]
handler=Block #IF, types=[Ljava/lang/IllegalAccessException;], range=[Block #IE, Block #ID]
handler=Block #II, types=[Ljava/lang/IllegalAccessException;], range=[Block #IH, Block #IG]
handler=Block #IL, types=[Ljava/lang/IllegalAccessException;], range=[Block #IK, Block #IJ]
handler=Block #IO, types=[Ljava/lang/RuntimeException;], range=[Block #IN, Block #IM]
handler=Block #IR, types=[Ljava/io/IOException;], range=[Block #IQ, Block #IP]
handler=Block #IU, types=[Ljava/lang/RuntimeException;], range=[Block #IT, Block #IS]
handler=Block #IX, types=[Ljava/lang/IllegalAccessException;], range=[Block #IW, Block #IV]
handler=Block #JA, types=[Ljava/lang/RuntimeException;], range=[Block #IZ, Block #IY]
===#Block A(size=4, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
   2. synth(lvar2 = lvar2);
   3. synth(lvar3 = lvar3);
      -> Immediate #A -> #B
===#Block B(size=0, flags=0)===
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=2, flags=0)===
   0. lvar5 = lvar3;
   1. if (lvar5 == {553760295 ^ lvar105})
      goto JM
      -> Immediate #C -> #BK
      -> ConditionalJump[IF_ICMPEQ] #C -> #JM
      <- Immediate #B -> #C
===#Block JM(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -476073461)
      goto D
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JM -> #D
      -> UnconditionalJump[GOTO] #JM -> #JC
      <- ConditionalJump[IF_ICMPEQ] #C -> #JM
===#Block D(size=3, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar10 = lvar2;
   2. if (lvar10 == {181177764 ^ lvar105})
      goto JR
      -> ConditionalJump[IF_ICMPEQ] #D -> #JR
      -> Immediate #D -> #E
      <- ConditionalJump[IF_ICMPEQ] #JM -> #D
===#Block E(size=6, flags=0)===
   0. lvar11 = lvar1;
   1. lvar9 = lvar11;
   2. lvar12 = lvar9;
   3. lvar13 = lvar12.hashCode();
   4. svar107 = {lvar13 ^ lvar105};
   5. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(svar107)) {
      case 83435472:
      	 goto	#F
      case 83435473:
      	 goto	#R
      case 83435474:
      	 goto	#I
      case 83435496:
      	 goto	#AA
      case 83435499:
      	 goto	#AD
      case 83435500:
      	 goto	#L
      case 83435501:
      	 goto	#X
      case 83435503:
      	 goto	#U
      default:
      	 goto	#AG
   }
      -> DefaultSwitch #E -> #AG
      -> Switch[83435499] #E -> #AD
      -> Switch[83435496] #E -> #AA
      -> Switch[83435501] #E -> #X
      -> Switch[83435503] #E -> #U
      -> Switch[83435473] #E -> #R
      -> Switch[83435500] #E -> #L
      -> Switch[83435474] #E -> #I
      -> Switch[83435472] #E -> #F
      <- Immediate #D -> #E
===#Block F(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar14 = lvar9;
   2. lvar6 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.zeqrxqweskmmcwd(), lvar105);
   3. lvar15 = lvar14.equals(lvar6);
   4. if (lvar15 != {2018663765 ^ lvar105})
      goto JG
      -> ConditionalJump[IF_ICMPNE] #F -> #JG
      -> Immediate #F -> #H
      <- Switch[83435472] #E -> #F
===#Block H(size=1, flags=0)===
   0. goto HD
      -> UnconditionalJump[GOTO] #H -> #HD
      <- Immediate #F -> #H
===#Block HD(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 229045820)
      goto HC
   1. throw nullconst;
      -> TryCatch range: [HD...HC] -> HE ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HD -> #HC
      <- UnconditionalJump[GOTO] #H -> #HD
===#Block HC(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HD...HC] -> HE ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HD -> #HC
===#Block HE(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #HE -> #AG
      <- TryCatch range: [HD...HC] -> HE ([Ljava/io/IOException;])
      <- TryCatch range: [HD...HC] -> HE ([Ljava/io/IOException;])
===#Block JG(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1951668279)
      goto G
   1. goto JC
      -> UnconditionalJump[GOTO] #JG -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JG -> #G
      <- ConditionalJump[IF_ICMPNE] #F -> #JG
===#Block G(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_GLAZED_TERRACOTTA;
   2. goto IZ
      -> UnconditionalJump[GOTO] #G -> #IZ
      <- ConditionalJump[IF_ICMPEQ] #JG -> #G
===#Block IZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 70660700)
      goto IY
   1. throw nullconst;
      -> TryCatch range: [IZ...IY] -> JA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #IZ -> #IY
      <- UnconditionalJump[GOTO] #G -> #IZ
===#Block IY(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IZ...IY] -> JA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IZ -> #IY
===#Block JA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #JA -> #CN
      <- TryCatch range: [IZ...IY] -> JA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IZ...IY] -> JA ([Ljava/lang/RuntimeException;])
===#Block I(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar9;
   2. lvar76 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.dtgshdrykivjlph(), lvar105);
   3. lvar18 = lvar17.equals(lvar76);
   4. if (lvar18 != {458657577 ^ lvar105})
      goto JN
      -> Immediate #I -> #K
      -> ConditionalJump[IF_ICMPNE] #I -> #JN
      <- Switch[83435474] #E -> #I
===#Block JN(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1092786845)
      goto J
   1. goto JC
      -> UnconditionalJump[GOTO] #JN -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JN -> #J
      <- ConditionalJump[IF_ICMPNE] #I -> #JN
===#Block J(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_GLAZED_TERRACOTTA;
   2. goto DF
      -> UnconditionalJump[GOTO] #J -> #DF
      <- ConditionalJump[IF_ICMPEQ] #JN -> #J
===#Block DF(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 55497428)
      goto DE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DF -> #DE
      -> TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #J -> #DF
===#Block DE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DF -> #DE
===#Block DG(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DG -> #CN
      <- TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
      <- TryCatch range: [DF...DE] -> DG ([Ljava/io/IOException;])
===#Block K(size=1, flags=0)===
   0. goto IH
      -> UnconditionalJump[GOTO] #K -> #IH
      <- Immediate #I -> #K
===#Block IH(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 171297528)
      goto IG
   1. throw nullconst;
      -> TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IH -> #IG
      <- UnconditionalJump[GOTO] #K -> #IH
===#Block IG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IH -> #IG
===#Block II(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #II -> #AG
      <- TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IH...IG] -> II ([Ljava/lang/IllegalAccessException;])
===#Block L(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar9;
   2. lvar77 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.xisougacrxermzt(), lvar105);
   3. lvar20 = lvar19.equals(lvar77);
   4. if (lvar20 != {1476584113 ^ lvar105})
      goto KE
      -> Immediate #L -> #N
      -> ConditionalJump[IF_ICMPNE] #L -> #KE
      <- Switch[83435500] #E -> #L
===#Block KE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 912497256)
      goto M
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KE -> #M
      -> UnconditionalJump[GOTO] #KE -> #JC
      <- ConditionalJump[IF_ICMPNE] #L -> #KE
===#Block M(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_GLAZED_TERRACOTTA;
   2. goto CQ
      -> UnconditionalJump[GOTO] #M -> #CQ
      <- ConditionalJump[IF_ICMPEQ] #KE -> #M
===#Block CQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 97711334)
      goto CP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CQ -> #CP
      -> TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #M -> #CQ
===#Block CP(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CQ -> #CP
===#Block CR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #CR -> #CN
      <- TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
      <- TryCatch range: [CQ...CP] -> CR ([Ljava/io/IOException;])
===#Block N(size=1, flags=0)===
   0. goto HY
      -> UnconditionalJump[GOTO] #N -> #HY
      <- Immediate #L -> #N
===#Block HY(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 186870831)
      goto HX
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HY -> #HX
      -> TryCatch range: [HY...HX] -> HZ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #N -> #HY
===#Block HX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HY...HX] -> HZ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HY -> #HX
===#Block HZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #HZ -> #AG
      <- TryCatch range: [HY...HX] -> HZ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HY...HX] -> HZ ([Ljava/lang/IllegalAccessException;])
===#Block R(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = lvar9;
   2. lvar79 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.hrvishchoqoyptr(), lvar105);
   3. lvar24 = lvar23.equals(lvar79);
   4. if (lvar24 != {1299448510 ^ lvar105})
      goto JL
      -> Immediate #R -> #S
      -> ConditionalJump[IF_ICMPNE] #R -> #JL
      <- Switch[83435473] #E -> #R
===#Block JL(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -687123327)
      goto T
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JL -> #T
      -> UnconditionalJump[GOTO] #JL -> #JC
      <- ConditionalJump[IF_ICMPNE] #R -> #JL
===#Block T(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_GLAZED_TERRACOTTA;
   2. goto HS
      -> UnconditionalJump[GOTO] #T -> #HS
      <- ConditionalJump[IF_ICMPEQ] #JL -> #T
===#Block HS(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 7305969)
      goto HR
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HS -> #HR
      -> TryCatch range: [HS...HR] -> HT ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #T -> #HS
===#Block HR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [HS...HR] -> HT ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #HS -> #HR
===#Block HT(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HT -> #CN
      <- TryCatch range: [HS...HR] -> HT ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [HS...HR] -> HT ([Ljava/lang/RuntimeException;])
===#Block S(size=1, flags=0)===
   0. goto ES
      -> UnconditionalJump[GOTO] #S -> #ES
      <- Immediate #R -> #S
===#Block ES(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 123691830)
      goto ER
   1. throw nullconst;
      -> TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #ES -> #ER
      <- UnconditionalJump[GOTO] #S -> #ES
===#Block ER(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #ES -> #ER
===#Block ET(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #ET -> #AG
      <- TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [ES...ER] -> ET ([Ljava/lang/IllegalAccessException;])
===#Block U(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar9;
   2. lvar80 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ztkdqnegeuafzau(), lvar105);
   3. lvar26 = lvar25.equals(lvar80);
   4. if (lvar26 != {1688697690 ^ lvar105})
      goto JY
      -> Immediate #U -> #W
      -> ConditionalJump[IF_ICMPNE] #U -> #JY
      <- Switch[83435503] #E -> #U
===#Block JY(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -949543015)
      goto V
   1. goto JC
      -> UnconditionalJump[GOTO] #JY -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JY -> #V
      <- ConditionalJump[IF_ICMPNE] #U -> #JY
===#Block V(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_GLAZED_TERRACOTTA;
   2. goto FZ
      -> UnconditionalJump[GOTO] #V -> #FZ
      <- ConditionalJump[IF_ICMPEQ] #JY -> #V
===#Block FZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 58997877)
      goto FY
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FZ -> #FY
      -> TryCatch range: [FZ...FY] -> GA ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #V -> #FZ
===#Block FY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FZ...FY] -> GA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FZ -> #FY
===#Block GA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GA -> #CN
      <- TryCatch range: [FZ...FY] -> GA ([Ljava/io/IOException;])
      <- TryCatch range: [FZ...FY] -> GA ([Ljava/io/IOException;])
===#Block W(size=1, flags=0)===
   0. goto IB
      -> UnconditionalJump[GOTO] #W -> #IB
      <- Immediate #U -> #W
===#Block IB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 166559328)
      goto IA
   1. throw nullconst;
      -> TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #IB -> #IA
      <- UnconditionalJump[GOTO] #W -> #IB
===#Block IA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IB -> #IA
===#Block IC(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #IC -> #AG
      <- TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
      <- TryCatch range: [IB...IA] -> IC ([Ljava/io/IOException;])
===#Block X(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = lvar9;
   2. lvar81 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.nzrvbcchmgmjipx(), lvar105);
   3. lvar28 = lvar27.equals(lvar81);
   4. if (lvar28 != {1582854273 ^ lvar105})
      goto KC
      -> ConditionalJump[IF_ICMPNE] #X -> #KC
      -> Immediate #X -> #Y
      <- Switch[83435501] #E -> #X
===#Block Y(size=1, flags=0)===
   0. goto CZ
      -> UnconditionalJump[GOTO] #Y -> #CZ
      <- Immediate #X -> #Y
===#Block CZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 30886835)
      goto CY
   1. throw nullconst;
      -> TryCatch range: [CZ...CY] -> DA ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CZ -> #CY
      <- UnconditionalJump[GOTO] #Y -> #CZ
===#Block CY(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CZ...CY] -> DA ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CZ -> #CY
===#Block DA(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #DA -> #AG
      <- TryCatch range: [CZ...CY] -> DA ([Ljava/io/IOException;])
      <- TryCatch range: [CZ...CY] -> DA ([Ljava/io/IOException;])
===#Block KC(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -304666096)
      goto Z
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KC -> #Z
      -> UnconditionalJump[GOTO] #KC -> #JC
      <- ConditionalJump[IF_ICMPNE] #X -> #KC
===#Block Z(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_GLAZED_TERRACOTTA;
   2. goto EJ
      -> UnconditionalJump[GOTO] #Z -> #EJ
      <- ConditionalJump[IF_ICMPEQ] #KC -> #Z
===#Block EJ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 10354620)
      goto EI
   1. throw nullconst;
      -> TryCatch range: [EJ...EI] -> EK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EJ -> #EI
      <- UnconditionalJump[GOTO] #Z -> #EJ
===#Block EI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EJ...EI] -> EK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EJ -> #EI
===#Block EK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EK -> #CN
      <- TryCatch range: [EJ...EI] -> EK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EJ...EI] -> EK ([Ljava/lang/IllegalAccessException;])
===#Block AA(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar9;
   2. lvar82 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.bqvombrsmtgxach(), lvar105);
   3. lvar30 = lvar29.equals(lvar82);
   4. if (lvar30 != {2138893230 ^ lvar105})
      goto JO
      -> ConditionalJump[IF_ICMPNE] #AA -> #JO
      -> Immediate #AA -> #AB
      <- Switch[83435496] #E -> #AA
===#Block AB(size=1, flags=0)===
   0. goto FH
      -> UnconditionalJump[GOTO] #AB -> #FH
      <- Immediate #AA -> #AB
===#Block FH(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 24089236)
      goto FG
   1. throw nullconst;
      -> TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #FH -> #FG
      <- UnconditionalJump[GOTO] #AB -> #FH
===#Block FG(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FH -> #FG
===#Block FI(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #FI -> #AG
      <- TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
      <- TryCatch range: [FH...FG] -> FI ([Ljava/io/IOException;])
===#Block JO(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 400559235)
      goto AC
   1. goto JC
      -> UnconditionalJump[GOTO] #JO -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JO -> #AC
      <- ConditionalJump[IF_ICMPNE] #AA -> #JO
===#Block AC(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_GLAZED_TERRACOTTA;
   2. goto CW
      -> UnconditionalJump[GOTO] #AC -> #CW
      <- ConditionalJump[IF_ICMPEQ] #JO -> #AC
===#Block CW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 65316465)
      goto CV
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #CW -> #CV
      -> TryCatch range: [CW...CV] -> CX ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AC -> #CW
===#Block CV(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CW...CV] -> CX ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CW -> #CV
===#Block CX(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #CX -> #CN
      <- TryCatch range: [CW...CV] -> CX ([Ljava/io/IOException;])
      <- TryCatch range: [CW...CV] -> CX ([Ljava/io/IOException;])
===#Block AD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar31 = lvar9;
   2. lvar83 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.dgikvsphsbmhdtx(), lvar105);
   3. lvar32 = lvar31.equals(lvar83);
   4. if (lvar32 != {1937590793 ^ lvar105})
      goto JZ
      -> Immediate #AD -> #AF
      -> ConditionalJump[IF_ICMPNE] #AD -> #JZ
      <- Switch[83435499] #E -> #AD
===#Block JZ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1577830448)
      goto AE
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JZ -> #AE
      -> UnconditionalJump[GOTO] #JZ -> #JC
      <- ConditionalJump[IF_ICMPNE] #AD -> #JZ
===#Block AE(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.MAGENTA_GLAZED_TERRACOTTA;
   2. goto HJ
      -> UnconditionalJump[GOTO] #AE -> #HJ
      <- ConditionalJump[IF_ICMPEQ] #JZ -> #AE
===#Block HJ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 70275316)
      goto HI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HJ -> #HI
      -> TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AE -> #HJ
===#Block HI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HJ -> #HI
===#Block HK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HK -> #CN
      <- TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HJ...HI] -> HK ([Ljava/lang/IllegalAccessException;])
===#Block AF(size=1, flags=0)===
   0. goto DC
      -> UnconditionalJump[GOTO] #AF -> #DC
      <- Immediate #AD -> #AF
===#Block DC(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 167359037)
      goto DB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DC -> #DB
      -> TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AF -> #DC
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DC -> #DB
===#Block DD(size=2, flags=0)===
   0. _consume(catch());
   1. goto AG
      -> UnconditionalJump[GOTO] #DD -> #AG
      <- TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DC...DB] -> DD ([Ljava/lang/IllegalAccessException;])
===#Block AG(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_GLAZED_TERRACOTTA;
   2. goto DL
      -> UnconditionalJump[GOTO] #AG -> #DL
      <- DefaultSwitch #E -> #AG
      <- UnconditionalJump[GOTO] #IC -> #AG
      <- UnconditionalJump[GOTO] #HZ -> #AG
      <- UnconditionalJump[GOTO] #DA -> #AG
      <- UnconditionalJump[GOTO] #FI -> #AG
      <- UnconditionalJump[GOTO] #II -> #AG
      <- UnconditionalJump[GOTO] #HE -> #AG
      <- UnconditionalJump[GOTO] #ET -> #AG
      <- UnconditionalJump[GOTO] #DD -> #AG
      <- UnconditionalJump[GOTO] #IU -> #AG
===#Block DL(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 152040158)
      goto DK
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DL -> #DK
      -> TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AG -> #DL
===#Block DK(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #DL -> #DK
===#Block DM(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DM -> #CN
      <- TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
      <- TryCatch range: [DL...DK] -> DM ([Ljava/io/IOException;])
===#Block JR(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -264200749)
      goto AH
   1. goto JC
      -> UnconditionalJump[GOTO] #JR -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JR -> #AH
      <- ConditionalJump[IF_ICMPEQ] #D -> #JR
===#Block AH(size=7, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar33 = lvar1;
   2. lvar102 = lvar33;
   3. lvar34 = lvar102;
   4. lvar35 = lvar34.hashCode();
   5. svar107 = {lvar35 ^ lvar105};
   6. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(svar107)) {
      case 33641536:
      	 goto	#BA
      case 33641537:
      	 goto	#AO
      case 33641542:
      	 goto	#AI
      case 33641544:
      	 goto	#AR
      case 33641546:
      	 goto	#AX
      case 33641548:
      	 goto	#AL
      case 33641550:
      	 goto	#BG
      case 33641556:
      	 goto	#BD
      case 33641558:
      	 goto	#AU
      default:
      	 goto	#BJ
   }
      -> Switch[33641550] #AH -> #BG
      -> Switch[33641556] #AH -> #BD
      -> Switch[33641536] #AH -> #BA
      -> Switch[33641546] #AH -> #AX
      -> Switch[33641558] #AH -> #AU
      -> DefaultSwitch #AH -> #BJ
      -> Switch[33641544] #AH -> #AR
      -> Switch[33641537] #AH -> #AO
      -> Switch[33641548] #AH -> #AL
      -> Switch[33641542] #AH -> #AI
      <- ConditionalJump[IF_ICMPEQ] #JR -> #AH
===#Block AI(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar36 = lvar102;
   2. lvar84 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ryojhfxxmxgkmrf(), lvar105);
   3. lvar37 = lvar36.equals(lvar84);
   4. if (lvar37 != {589994108 ^ lvar105})
      goto JU
      -> ConditionalJump[IF_ICMPNE] #AI -> #JU
      -> Immediate #AI -> #AJ
      <- Switch[33641542] #AH -> #AI
===#Block AJ(size=1, flags=0)===
   0. goto FN
      -> UnconditionalJump[GOTO] #AJ -> #FN
      <- Immediate #AI -> #AJ
===#Block FN(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 68669723)
      goto FM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FN -> #FM
      -> TryCatch range: [FN...FM] -> FO ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AJ -> #FN
===#Block FM(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FN...FM] -> FO ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FN -> #FM
===#Block FO(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #FO -> #BJ
      <- TryCatch range: [FN...FM] -> FO ([Ljava/io/IOException;])
      <- TryCatch range: [FN...FM] -> FO ([Ljava/io/IOException;])
===#Block JU(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -449872693)
      goto AK
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JU -> #AK
      -> UnconditionalJump[GOTO] #JU -> #JC
      <- ConditionalJump[IF_ICMPNE] #AI -> #JU
===#Block AK(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.RED_CONCRETE_POWDER;
   2. goto GR
      -> UnconditionalJump[GOTO] #AK -> #GR
      <- ConditionalJump[IF_ICMPEQ] #JU -> #AK
===#Block GR(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 79632869)
      goto GQ
   1. throw nullconst;
      -> TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GR -> #GQ
      <- UnconditionalJump[GOTO] #AK -> #GR
===#Block GQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GR -> #GQ
===#Block GS(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GS -> #CN
      <- TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GR...GQ] -> GS ([Ljava/lang/RuntimeException;])
===#Block AL(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar38 = lvar102;
   2. lvar85 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.jvdnwlmwnvtlqtl(), lvar105);
   3. lvar39 = lvar38.equals(lvar85);
   4. if (lvar39 != {174569096 ^ lvar105})
      goto JX
      -> ConditionalJump[IF_ICMPNE] #AL -> #JX
      -> Immediate #AL -> #AN
      <- Switch[33641548] #AH -> #AL
===#Block AN(size=1, flags=0)===
   0. goto EA
      -> UnconditionalJump[GOTO] #AN -> #EA
      <- Immediate #AL -> #AN
===#Block EA(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 85303182)
      goto DZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EA -> #DZ
      -> TryCatch range: [EA...DZ] -> EB ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AN -> #EA
===#Block DZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EA...DZ] -> EB ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EA -> #DZ
===#Block EB(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #EB -> #BJ
      <- TryCatch range: [EA...DZ] -> EB ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EA...DZ] -> EB ([Ljava/lang/IllegalAccessException;])
===#Block JX(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1501103107)
      goto AM
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JX -> #AM
      -> UnconditionalJump[GOTO] #JX -> #JC
      <- ConditionalJump[IF_ICMPNE] #AL -> #JX
===#Block AM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPLE_CONCRETE_POWDER;
   2. goto EV
      -> UnconditionalJump[GOTO] #AM -> #EV
      <- ConditionalJump[IF_ICMPEQ] #JX -> #AM
===#Block EV(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 139748292)
      goto EU
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EV -> #EU
      -> TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AM -> #EV
===#Block EU(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EV -> #EU
===#Block EW(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EW -> #CN
      <- TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
      <- TryCatch range: [EV...EU] -> EW ([Ljava/io/IOException;])
===#Block AO(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar40 = lvar102;
   2. lvar86 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ifcochvaojtmvrn(), lvar105);
   3. lvar41 = lvar40.equals(lvar86);
   4. if (lvar41 != {491316057 ^ lvar105})
      goto JB
      -> ConditionalJump[IF_ICMPNE] #AO -> #JB
      -> Immediate #AO -> #AP
      <- Switch[33641537] #AH -> #AO
===#Block AP(size=1, flags=0)===
   0. goto DU
      -> UnconditionalJump[GOTO] #AP -> #DU
      <- Immediate #AO -> #AP
===#Block DU(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 101977960)
      goto DT
   1. throw nullconst;
      -> TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DU -> #DT
      <- UnconditionalJump[GOTO] #AP -> #DU
===#Block DT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DU -> #DT
===#Block DV(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #DV -> #BJ
      <- TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DU...DT] -> DV ([Ljava/lang/IllegalAccessException;])
===#Block JB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1393801033)
      goto AQ
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JB -> #AQ
      -> UnconditionalJump[GOTO] #JB -> #JC
      <- ConditionalJump[IF_ICMPNE] #AO -> #JB
===#Block AQ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.YELLOW_CONCRETE_POWDER;
   2. goto FT
      -> UnconditionalJump[GOTO] #AQ -> #FT
      <- ConditionalJump[IF_ICMPEQ] #JB -> #AQ
===#Block FT(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 127697511)
      goto FS
   1. throw nullconst;
      -> TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #FT -> #FS
      <- UnconditionalJump[GOTO] #AQ -> #FT
===#Block FS(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FT -> #FS
===#Block FU(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FU -> #CN
      <- TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FT...FS] -> FU ([Ljava/lang/IllegalAccessException;])
===#Block AR(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar42 = lvar102;
   2. lvar87 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.jhoayhdbevbwmmg(), lvar105);
   3. lvar43 = lvar42.equals(lvar87);
   4. if (lvar43 != {851416966 ^ lvar105})
      goto JQ
      -> Immediate #AR -> #AT
      -> ConditionalJump[IF_ICMPNE] #AR -> #JQ
      <- Switch[33641544] #AH -> #AR
===#Block JQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1016443970)
      goto AS
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JQ -> #AS
      -> UnconditionalJump[GOTO] #JQ -> #JC
      <- ConditionalJump[IF_ICMPNE] #AR -> #JQ
===#Block AS(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GREEN_CONCRETE_POWDER;
   2. goto EY
      -> UnconditionalJump[GOTO] #AS -> #EY
      <- ConditionalJump[IF_ICMPEQ] #JQ -> #AS
===#Block EY(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 266304818)
      goto EX
   1. throw nullconst;
      -> TryCatch range: [EY...EX] -> EZ ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #EY -> #EX
      <- UnconditionalJump[GOTO] #AS -> #EY
===#Block EX(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EY...EX] -> EZ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EY -> #EX
===#Block EZ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EZ -> #CN
      <- TryCatch range: [EY...EX] -> EZ ([Ljava/io/IOException;])
      <- TryCatch range: [EY...EX] -> EZ ([Ljava/io/IOException;])
===#Block AT(size=1, flags=0)===
   0. goto IN
      -> UnconditionalJump[GOTO] #AT -> #IN
      <- Immediate #AR -> #AT
===#Block IN(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 111876799)
      goto IM
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IN -> #IM
      -> TryCatch range: [IN...IM] -> IO ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AT -> #IN
===#Block IM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [IN...IM] -> IO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #IN -> #IM
===#Block IO(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #IO -> #BJ
      <- TryCatch range: [IN...IM] -> IO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [IN...IM] -> IO ([Ljava/lang/RuntimeException;])
===#Block AU(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar44 = lvar102;
   2. lvar88 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.pnczhzkzhwzcxye(), lvar105);
   3. lvar45 = lvar44.equals(lvar88);
   4. if (lvar45 != {89319299 ^ lvar105})
      goto JJ
      -> ConditionalJump[IF_ICMPNE] #AU -> #JJ
      -> Immediate #AU -> #AV
      <- Switch[33641558] #AH -> #AU
===#Block AV(size=1, flags=0)===
   0. goto GL
      -> UnconditionalJump[GOTO] #AV -> #GL
      <- Immediate #AU -> #AV
===#Block GL(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 217672847)
      goto GK
   1. throw nullconst;
      -> TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GL -> #GK
      <- UnconditionalJump[GOTO] #AV -> #GL
===#Block GK(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GL -> #GK
===#Block GM(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #GM -> #BJ
      <- TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GL...GK] -> GM ([Ljava/lang/RuntimeException;])
===#Block JJ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1884007749)
      goto AW
   1. goto JC
      -> UnconditionalJump[GOTO] #JJ -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JJ -> #AW
      <- ConditionalJump[IF_ICMPNE] #AU -> #JJ
===#Block AW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.CYAN_CONCRETE_POWDER;
   2. goto GO
      -> UnconditionalJump[GOTO] #AW -> #GO
      <- ConditionalJump[IF_ICMPEQ] #JJ -> #AW
===#Block GO(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 116902828)
      goto GN
   1. throw nullconst;
      -> TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #GO -> #GN
      <- UnconditionalJump[GOTO] #AW -> #GO
===#Block GN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GO -> #GN
===#Block GP(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GP -> #CN
      <- TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GO...GN] -> GP ([Ljava/lang/IllegalAccessException;])
===#Block AX(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar46 = lvar102;
   2. lvar89 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.uluftgmjjdrqdib(), lvar105);
   3. lvar47 = lvar46.equals(lvar89);
   4. if (lvar47 != {1054654776 ^ lvar105})
      goto JE
      -> ConditionalJump[IF_ICMPNE] #AX -> #JE
      -> Immediate #AX -> #AY
      <- Switch[33641546] #AH -> #AX
===#Block AY(size=1, flags=0)===
   0. goto EM
      -> UnconditionalJump[GOTO] #AY -> #EM
      <- Immediate #AX -> #AY
===#Block EM(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 168820227)
      goto EL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #EM -> #EL
      -> TryCatch range: [EM...EL] -> EN ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #AY -> #EM
===#Block EL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [EM...EL] -> EN ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #EM -> #EL
===#Block EN(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #EN -> #BJ
      <- TryCatch range: [EM...EL] -> EN ([Ljava/io/IOException;])
      <- TryCatch range: [EM...EL] -> EN ([Ljava/io/IOException;])
===#Block JE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 359591938)
      goto AZ
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JE -> #AZ
      -> UnconditionalJump[GOTO] #JE -> #JC
      <- ConditionalJump[IF_ICMPNE] #AX -> #JE
===#Block AZ(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.WHITE_CONCRETE_POWDER;
   2. goto DO
      -> UnconditionalJump[GOTO] #AZ -> #DO
      <- ConditionalJump[IF_ICMPEQ] #JE -> #AZ
===#Block DO(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 181588649)
      goto DN
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DO -> #DN
      -> TryCatch range: [DO...DN] -> DP ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #AZ -> #DO
===#Block DN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DO...DN] -> DP ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DO -> #DN
===#Block DP(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #DP -> #CN
      <- TryCatch range: [DO...DN] -> DP ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DO...DN] -> DP ([Ljava/lang/RuntimeException;])
===#Block BA(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar48 = lvar102;
   2. lvar90 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.vnooadmixdmqxzh(), lvar105);
   3. lvar49 = lvar48.equals(lvar90);
   4. if (lvar49 != {394253820 ^ lvar105})
      goto JW
      -> Immediate #BA -> #BC
      -> ConditionalJump[IF_ICMPNE] #BA -> #JW
      <- Switch[33641536] #AH -> #BA
===#Block JW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -240023287)
      goto BB
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JW -> #BB
      -> UnconditionalJump[GOTO] #JW -> #JC
      <- ConditionalJump[IF_ICMPNE] #BA -> #JW
===#Block BB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLUE_CONCRETE_POWDER;
   2. goto GC
      -> UnconditionalJump[GOTO] #BB -> #GC
      <- ConditionalJump[IF_ICMPEQ] #JW -> #BB
===#Block GC(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 86818935)
      goto GB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GC -> #GB
      -> TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BB -> #GC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GC -> #GB
===#Block GD(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GD -> #CN
      <- TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GC...GB] -> GD ([Ljava/lang/IllegalAccessException;])
===#Block BC(size=1, flags=0)===
   0. goto IE
      -> UnconditionalJump[GOTO] #BC -> #IE
      <- Immediate #BA -> #BC
===#Block IE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 254151275)
      goto ID
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IE -> #ID
      -> TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BC -> #IE
===#Block ID(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IE -> #ID
===#Block IF(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #IF -> #BJ
      <- TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IE...ID] -> IF ([Ljava/lang/IllegalAccessException;])
===#Block BD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar50 = lvar102;
   2. lvar91 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.bmmkvsxmrdvgbdj(), lvar105);
   3. lvar51 = lvar50.equals(lvar91);
   4. if (lvar51 != {1702337190 ^ lvar105})
      goto JS
      -> ConditionalJump[IF_ICMPNE] #BD -> #JS
      -> Immediate #BD -> #BE
      <- Switch[33641556] #AH -> #BD
===#Block BE(size=1, flags=0)===
   0. goto DX
      -> UnconditionalJump[GOTO] #BE -> #DX
      <- Immediate #BD -> #BE
===#Block DX(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 259667225)
      goto DW
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #DX -> #DW
      -> TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BE -> #DX
===#Block DW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #DX -> #DW
===#Block DY(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #DY -> #BJ
      <- TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [DX...DW] -> DY ([Ljava/lang/RuntimeException;])
===#Block JS(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 618566834)
      goto BF
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JS -> #BF
      -> UnconditionalJump[GOTO] #JS -> #JC
      <- ConditionalJump[IF_ICMPNE] #BD -> #JS
===#Block BF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.MAGENTA_CONCRETE_POWDER;
   2. goto GX
      -> UnconditionalJump[GOTO] #BF -> #GX
      <- ConditionalJump[IF_ICMPEQ] #JS -> #BF
===#Block GX(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 220463425)
      goto GW
   1. throw nullconst;
      -> TryCatch range: [GX...GW] -> GY ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #GX -> #GW
      <- UnconditionalJump[GOTO] #BF -> #GX
===#Block GW(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GX...GW] -> GY ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GX -> #GW
===#Block GY(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GY -> #CN
      <- TryCatch range: [GX...GW] -> GY ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GX...GW] -> GY ([Ljava/lang/IllegalAccessException;])
===#Block BG(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar52 = lvar102;
   2. lvar92 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.asyufbtclwkyocu(), lvar105);
   3. lvar53 = lvar52.equals(lvar92);
   4. if (lvar53 != {1634585088 ^ lvar105})
      goto JD
      -> ConditionalJump[IF_ICMPNE] #BG -> #JD
      -> Immediate #BG -> #BI
      <- Switch[33641550] #AH -> #BG
===#Block BI(size=1, flags=0)===
   0. goto HM
      -> UnconditionalJump[GOTO] #BI -> #HM
      <- Immediate #BG -> #BI
===#Block HM(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 245885088)
      goto HL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HM -> #HL
      -> TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BI -> #HM
===#Block HL(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HM -> #HL
===#Block HN(size=2, flags=0)===
   0. _consume(catch());
   1. goto BJ
      -> UnconditionalJump[GOTO] #HN -> #BJ
      <- TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
      <- TryCatch range: [HM...HL] -> HN ([Ljava/io/IOException;])
===#Block BJ(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GRAY_CONCRETE_POWDER;
      -> Immediate #BJ -> #CN
      <- UnconditionalJump[GOTO] #HN -> #BJ
      <- UnconditionalJump[GOTO] #EN -> #BJ
      <- UnconditionalJump[GOTO] #FO -> #BJ
      <- UnconditionalJump[GOTO] #IF -> #BJ
      <- UnconditionalJump[GOTO] #DY -> #BJ
      <- DefaultSwitch #AH -> #BJ
      <- UnconditionalJump[GOTO] #DV -> #BJ
      <- UnconditionalJump[GOTO] #GM -> #BJ
      <- UnconditionalJump[GOTO] #IO -> #BJ
      <- UnconditionalJump[GOTO] #EB -> #BJ
===#Block JD(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1598542319)
      goto BH
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JD -> #BH
      -> UnconditionalJump[GOTO] #JD -> #JC
      <- ConditionalJump[IF_ICMPNE] #BG -> #JD
===#Block BH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.BLACK_CONCRETE_POWDER;
   2. goto GU
      -> UnconditionalJump[GOTO] #BH -> #GU
      <- ConditionalJump[IF_ICMPEQ] #JD -> #BH
===#Block GU(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 163463298)
      goto GT
   1. throw nullconst;
      -> TryCatch range: [GU...GT] -> GV ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #GU -> #GT
      <- UnconditionalJump[GOTO] #BH -> #GU
===#Block GT(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [GU...GT] -> GV ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #GU -> #GT
===#Block GV(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GV -> #CN
      <- TryCatch range: [GU...GT] -> GV ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [GU...GT] -> GV ([Ljava/lang/IllegalAccessException;])
===#Block BK(size=6, flags=0)===
   0. lvar54 = lvar1;
   1. lvar103 = lvar54;
   2. lvar55 = lvar103;
   3. lvar56 = lvar55.hashCode();
   4. svar107 = {lvar56 ^ lvar105};
   5. switch (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(svar107)) {
      case 152027202:
      	 goto	#BO
      case 152027212:
      	 goto	#CD
      case 152027216:
      	 goto	#CJ
      case 152027218:
      	 goto	#CA
      case 152027222:
      	 goto	#BL
      case 152027224:
      	 goto	#BX
      case 152027226:
      	 goto	#BU
      case 152027227:
      	 goto	#BR
      case 152027230:
      	 goto	#CG
      default:
      	 goto	#CM
   }
      -> DefaultSwitch #BK -> #CM
      -> Switch[152027216] #BK -> #CJ
      -> Switch[152027230] #BK -> #CG
      -> Switch[152027212] #BK -> #CD
      -> Switch[152027218] #BK -> #CA
      -> Switch[152027224] #BK -> #BX
      -> Switch[152027226] #BK -> #BU
      -> Switch[152027227] #BK -> #BR
      -> Switch[152027202] #BK -> #BO
      -> Switch[152027222] #BK -> #BL
      <- Immediate #C -> #BK
===#Block BL(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar57 = lvar103;
   2. lvar93 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.xgphahrxtskkhmq(), lvar105);
   3. lvar58 = lvar57.equals(lvar93);
   4. if (lvar58 != {1210964682 ^ lvar105})
      goto KA
      -> ConditionalJump[IF_ICMPNE] #BL -> #KA
      -> Immediate #BL -> #BM
      <- Switch[152027222] #BK -> #BL
===#Block BM(size=1, flags=0)===
   0. goto IK
      -> UnconditionalJump[GOTO] #BM -> #IK
      <- Immediate #BL -> #BM
===#Block IK(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 149736638)
      goto IJ
   1. throw nullconst;
      -> TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IK -> #IJ
      <- UnconditionalJump[GOTO] #BM -> #IK
===#Block IJ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IK -> #IJ
===#Block IL(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #IL -> #CM
      <- TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IK...IJ] -> IL ([Ljava/lang/IllegalAccessException;])
===#Block KA(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 610311851)
      goto BN
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KA -> #BN
      -> UnconditionalJump[GOTO] #KA -> #JC
      <- ConditionalJump[IF_ICMPNE] #BL -> #KA
===#Block BN(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.GOLD_BLOCK;
   2. goto IQ
      -> UnconditionalJump[GOTO] #BN -> #IQ
      <- ConditionalJump[IF_ICMPEQ] #KA -> #BN
===#Block IQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 146564264)
      goto IP
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #IQ -> #IP
      -> TryCatch range: [IQ...IP] -> IR ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BN -> #IQ
===#Block IP(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [IQ...IP] -> IR ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #IQ -> #IP
===#Block IR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IR -> #CN
      <- TryCatch range: [IQ...IP] -> IR ([Ljava/io/IOException;])
      <- TryCatch range: [IQ...IP] -> IR ([Ljava/io/IOException;])
===#Block BO(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar59 = lvar103;
   2. lvar94 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ndroujsrmuwuslo(), lvar105);
   3. lvar60 = lvar59.equals(lvar94);
   4. if (lvar60 != {927883381 ^ lvar105})
      goto KB
      -> Immediate #BO -> #BQ
      -> ConditionalJump[IF_ICMPNE] #BO -> #KB
      <- Switch[152027202] #BK -> #BO
===#Block KB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1193589453)
      goto BP
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KB -> #BP
      -> UnconditionalJump[GOTO] #KB -> #JC
      <- ConditionalJump[IF_ICMPNE] #BO -> #KB
===#Block BP(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PRISMARINE;
   2. goto EG
      -> UnconditionalJump[GOTO] #BP -> #EG
      <- ConditionalJump[IF_ICMPEQ] #KB -> #BP
===#Block EG(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 50785612)
      goto EF
   1. throw nullconst;
      -> TryCatch range: [EG...EF] -> EH ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EG -> #EF
      <- UnconditionalJump[GOTO] #BP -> #EG
===#Block EF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EG...EF] -> EH ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EG -> #EF
===#Block EH(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EH -> #CN
      <- TryCatch range: [EG...EF] -> EH ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EG...EF] -> EH ([Ljava/lang/IllegalAccessException;])
===#Block BQ(size=1, flags=0)===
   0. goto HP
      -> UnconditionalJump[GOTO] #BQ -> #HP
      <- Immediate #BO -> #BQ
===#Block HP(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 259624774)
      goto HO
   1. throw nullconst;
      -> TryCatch range: [HP...HO] -> HQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HP -> #HO
      <- UnconditionalJump[GOTO] #BQ -> #HP
===#Block HO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HP...HO] -> HQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HP -> #HO
===#Block HQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #HQ -> #CM
      <- TryCatch range: [HP...HO] -> HQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HP...HO] -> HQ ([Ljava/lang/IllegalAccessException;])
===#Block BR(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar61 = lvar103;
   2. lvar95 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.mtdriadqvclixuv(), lvar105);
   3. lvar62 = lvar61.equals(lvar95);
   4. if (lvar62 != {1894862535 ^ lvar105})
      goto JH
      -> Immediate #BR -> #BT
      -> ConditionalJump[IF_ICMPNE] #BR -> #JH
      <- Switch[152027227] #BK -> #BR
===#Block JH(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -1769475110)
      goto BS
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JH -> #BS
      -> UnconditionalJump[GOTO] #JH -> #JC
      <- ConditionalJump[IF_ICMPNE] #BR -> #JH
===#Block BS(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.DIAMOND_BLOCK;
   2. goto FW
      -> UnconditionalJump[GOTO] #BS -> #FW
      <- ConditionalJump[IF_ICMPEQ] #JH -> #BS
===#Block FW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 121429904)
      goto FV
   1. throw nullconst;
      -> TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FW -> #FV
      <- UnconditionalJump[GOTO] #BS -> #FW
===#Block FV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FW -> #FV
===#Block FX(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FX -> #CN
      <- TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FW...FV] -> FX ([Ljava/lang/RuntimeException;])
===#Block BT(size=1, flags=0)===
   0. goto CT
      -> UnconditionalJump[GOTO] #BT -> #CT
      <- Immediate #BR -> #BT
===#Block CT(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 264961410)
      goto CS
   1. throw nullconst;
      -> TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #CT -> #CS
      <- UnconditionalJump[GOTO] #BT -> #CT
===#Block CS(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #CT -> #CS
===#Block CU(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #CU -> #CM
      <- TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
      <- TryCatch range: [CT...CS] -> CU ([Ljava/io/IOException;])
===#Block BU(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar63 = lvar103;
   2. lvar96 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.fjglrpvmxfgfwqm(), lvar105);
   3. lvar64 = lvar63.equals(lvar96);
   4. if (lvar64 != {399811963 ^ lvar105})
      goto JT
      -> Immediate #BU -> #BV
      -> ConditionalJump[IF_ICMPNE] #BU -> #JT
      <- Switch[152027226] #BK -> #BU
===#Block JT(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -458017429)
      goto BW
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JT -> #BW
      -> UnconditionalJump[GOTO] #JT -> #JC
      <- ConditionalJump[IF_ICMPNE] #BU -> #JT
===#Block BW(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.OBSIDIAN;
   2. goto GF
      -> UnconditionalJump[GOTO] #BW -> #GF
      <- ConditionalJump[IF_ICMPEQ] #JT -> #BW
===#Block GF(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 128192012)
      goto GE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #GF -> #GE
      -> TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BW -> #GF
===#Block GE(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GF -> #GE
===#Block GG(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #GG -> #CN
      <- TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GF...GE] -> GG ([Ljava/lang/RuntimeException;])
===#Block BV(size=1, flags=0)===
   0. goto FK
      -> UnconditionalJump[GOTO] #BV -> #FK
      <- Immediate #BU -> #BV
===#Block FK(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 232200660)
      goto FJ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FK -> #FJ
      -> TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BV -> #FK
===#Block FJ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FK -> #FJ
===#Block FL(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #FL -> #CM
      <- TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
      <- TryCatch range: [FK...FJ] -> FL ([Ljava/io/IOException;])
===#Block BX(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar65 = lvar103;
   2. lvar97 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.cnyvlzwhgbdegzw(), lvar105);
   3. lvar66 = lvar65.equals(lvar97);
   4. if (lvar66 != {2021702116 ^ lvar105})
      goto JI
      -> Immediate #BX -> #BZ
      -> ConditionalJump[IF_ICMPNE] #BX -> #JI
      <- Switch[152027224] #BK -> #BX
===#Block JI(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1270009547)
      goto BY
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #JI -> #BY
      -> UnconditionalJump[GOTO] #JI -> #JC
      <- ConditionalJump[IF_ICMPNE] #BX -> #JI
===#Block BY(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.EMERALD_BLOCK;
   2. goto FB
      -> UnconditionalJump[GOTO] #BY -> #FB
      <- ConditionalJump[IF_ICMPEQ] #JI -> #BY
===#Block FB(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 102834857)
      goto FA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #FB -> #FA
      -> TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BY -> #FB
===#Block FA(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #FB -> #FA
===#Block FC(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FC -> #CN
      <- TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
      <- TryCatch range: [FB...FA] -> FC ([Ljava/io/IOException;])
===#Block BZ(size=1, flags=0)===
   0. goto GI
      -> UnconditionalJump[GOTO] #BZ -> #GI
      <- Immediate #BX -> #BZ
===#Block GI(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 36182864)
      goto GH
   1. throw nullconst;
      -> TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #GI -> #GH
      <- UnconditionalJump[GOTO] #BZ -> #GI
===#Block GH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #GI -> #GH
===#Block GJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #GJ -> #CM
      <- TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [GI...GH] -> GJ ([Ljava/lang/RuntimeException;])
===#Block CA(size=5, flags=0)===
   0. // Frame: locals[2] [0, java/lang/String] stack[0] []
   1. lvar67 = lvar103;
   2. lvar98 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.rlhhtqlhxjzlrpv(), lvar105);
   3. lvar68 = lvar67.equals(lvar98);
   4. if (lvar68 != {364091223 ^ lvar105})
      goto JF
      -> ConditionalJump[IF_ICMPNE] #CA -> #JF
      -> Immediate #CA -> #CC
      <- Switch[152027218] #BK -> #CA
===#Block CC(size=1, flags=0)===
   0. goto DR
      -> UnconditionalJump[GOTO] #CC -> #DR
      <- Immediate #CA -> #CC
===#Block DR(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 42944493)
      goto DQ
   1. throw nullconst;
      -> TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #DR -> #DQ
      <- UnconditionalJump[GOTO] #CC -> #DR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #DR -> #DQ
===#Block DS(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #DS -> #CM
      <- TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [DR...DQ] -> DS ([Ljava/lang/IllegalAccessException;])
===#Block JF(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 1704364115)
      goto CB
   1. goto JC
      -> UnconditionalJump[GOTO] #JF -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JF -> #CB
      <- ConditionalJump[IF_ICMPNE] #CA -> #JF
===#Block CB(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.REDSTONE_BLOCK;
   2. goto EP
      -> UnconditionalJump[GOTO] #CB -> #EP
      <- ConditionalJump[IF_ICMPEQ] #JF -> #CB
===#Block EP(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 15725369)
      goto EO
   1. throw nullconst;
      -> TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #EP -> #EO
      <- UnconditionalJump[GOTO] #CB -> #EP
===#Block EO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #EP -> #EO
===#Block EQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EQ -> #CN
      <- TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [EP...EO] -> EQ ([Ljava/lang/IllegalAccessException;])
===#Block CD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar69 = lvar103;
   2. lvar99 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.ndlkpolztlpreph(), lvar105);
   3. lvar70 = lvar69.equals(lvar99);
   4. if (lvar70 != {1443359491 ^ lvar105})
      goto KD
      -> ConditionalJump[IF_ICMPNE] #CD -> #KD
      -> Immediate #CD -> #CE
      <- Switch[152027212] #BK -> #CD
===#Block CE(size=1, flags=0)===
   0. goto HG
      -> UnconditionalJump[GOTO] #CE -> #HG
      <- Immediate #CD -> #CE
===#Block HG(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 33622876)
      goto HF
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #HG -> #HF
      -> TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CE -> #HG
===#Block HF(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HG -> #HF
===#Block HH(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #HH -> #CM
      <- TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HG...HF] -> HH ([Ljava/lang/IllegalAccessException;])
===#Block KD(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == -2089684238)
      goto CF
   1. goto JC
      -> ConditionalJump[IF_ICMPEQ] #KD -> #CF
      -> UnconditionalJump[GOTO] #KD -> #JC
      <- ConditionalJump[IF_ICMPNE] #CD -> #KD
===#Block CF(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.PURPUR_BLOCK;
   2. goto ED
      -> UnconditionalJump[GOTO] #CF -> #ED
      <- ConditionalJump[IF_ICMPEQ] #KD -> #CF
===#Block ED(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 96716620)
      goto EC
   1. throw nullconst;
      -> TryCatch range: [ED...EC] -> EE ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #ED -> #EC
      <- UnconditionalJump[GOTO] #CF -> #ED
===#Block EC(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [ED...EC] -> EE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #ED -> #EC
===#Block EE(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #EE -> #CN
      <- TryCatch range: [ED...EC] -> EE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [ED...EC] -> EE ([Ljava/lang/RuntimeException;])
===#Block CG(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar71 = lvar103;
   2. lvar100 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.tlbymtvwtamllfs(), lvar105);
   3. lvar72 = lvar71.equals(lvar100);
   4. if (lvar72 != {940573465 ^ lvar105})
      goto JK
      -> Immediate #CG -> #CI
      -> ConditionalJump[IF_ICMPNE] #CG -> #JK
      <- Switch[152027230] #BK -> #CG
===#Block JK(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 2012211649)
      goto CH
   1. goto JC
      -> UnconditionalJump[GOTO] #JK -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JK -> #CH
      <- ConditionalJump[IF_ICMPNE] #CG -> #JK
===#Block CH(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.QUARTZ_BLOCK;
   2. goto HV
      -> UnconditionalJump[GOTO] #CH -> #HV
      <- ConditionalJump[IF_ICMPEQ] #JK -> #CH
===#Block HV(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 5342418)
      goto HU
   1. throw nullconst;
      -> TryCatch range: [HV...HU] -> HW ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #HV -> #HU
      <- UnconditionalJump[GOTO] #CH -> #HV
===#Block HU(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [HV...HU] -> HW ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #HV -> #HU
===#Block HW(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #HW -> #CN
      <- TryCatch range: [HV...HU] -> HW ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [HV...HU] -> HW ([Ljava/lang/IllegalAccessException;])
===#Block CI(size=1, flags=0)===
   0. goto FQ
      -> UnconditionalJump[GOTO] #CI -> #FQ
      <- Immediate #CG -> #CI
===#Block FQ(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 98715020)
      goto FP
   1. throw nullconst;
      -> TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #FQ -> #FP
      <- UnconditionalJump[GOTO] #CI -> #FQ
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #FQ -> #FP
===#Block FR(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #FR -> #CM
      <- TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [FQ...FP] -> FR ([Ljava/lang/RuntimeException;])
===#Block CJ(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar73 = lvar103;
   2. lvar101 = cn.acebrand.acedex.gui.PokeBallItemCreator.khzbnlgmwu(cn.acebrand.acedex.gui.PokeBallItemCreator.vfnfrbygnvmxidu(), lvar105);
   3. lvar74 = lvar73.equals(lvar101);
   4. if (lvar74 != {1186324038 ^ lvar105})
      goto JP
      -> ConditionalJump[IF_ICMPNE] #CJ -> #JP
      -> Immediate #CJ -> #CK
      <- Switch[152027216] #BK -> #CJ
===#Block CK(size=1, flags=0)===
   0. goto HA
      -> UnconditionalJump[GOTO] #CK -> #HA
      <- Immediate #CJ -> #CK
===#Block HA(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 138184856)
      goto GZ
   1. throw nullconst;
      -> TryCatch range: [HA...GZ] -> HB ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #HA -> #GZ
      <- UnconditionalJump[GOTO] #CK -> #HA
===#Block GZ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [HA...GZ] -> HB ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #HA -> #GZ
===#Block HB(size=2, flags=0)===
   0. _consume(catch());
   1. goto CM
      -> UnconditionalJump[GOTO] #HB -> #CM
      <- TryCatch range: [HA...GZ] -> HB ([Ljava/io/IOException;])
      <- TryCatch range: [HA...GZ] -> HB ([Ljava/io/IOException;])
===#Block JP(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.cwqmsybypqbasbbr(lvar105) == 761066185)
      goto CL
   1. goto JC
      -> UnconditionalJump[GOTO] #JP -> #JC
      -> ConditionalJump[IF_ICMPEQ] #JP -> #CL
      <- ConditionalJump[IF_ICMPNE] #CJ -> #JP
===#Block CL(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.LAPIS_BLOCK;
   2. goto FE
      -> UnconditionalJump[GOTO] #CL -> #FE
      <- ConditionalJump[IF_ICMPEQ] #JP -> #CL
===#Block FE(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 151991372)
      goto FD
   1. throw nullconst;
      -> TryCatch range: [FE...FD] -> FF ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #FE -> #FD
      <- UnconditionalJump[GOTO] #CL -> #FE
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [FE...FD] -> FF ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #FE -> #FD
===#Block FF(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #FF -> #CN
      <- TryCatch range: [FE...FD] -> FF ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [FE...FD] -> FF ([Ljava/lang/IllegalAccessException;])
===#Block JC(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      <- UnconditionalJump[GOTO] #JU -> #JC
      <- UnconditionalJump[GOTO] #JK -> #JC
      <- UnconditionalJump[GOTO] #JO -> #JC
      <- UnconditionalJump[GOTO] #JD -> #JC
      <- UnconditionalJump[GOTO] #JJ -> #JC
      <- UnconditionalJump[GOTO] #JW -> #JC
      <- UnconditionalJump[GOTO] #JT -> #JC
      <- UnconditionalJump[GOTO] #JE -> #JC
      <- UnconditionalJump[GOTO] #KB -> #JC
      <- UnconditionalJump[GOTO] #KE -> #JC
      <- UnconditionalJump[GOTO] #JB -> #JC
      <- UnconditionalJump[GOTO] #JI -> #JC
      <- UnconditionalJump[GOTO] #JV -> #JC
      <- UnconditionalJump[GOTO] #JZ -> #JC
      <- UnconditionalJump[GOTO] #JS -> #JC
      <- UnconditionalJump[GOTO] #JP -> #JC
      <- UnconditionalJump[GOTO] #JQ -> #JC
      <- UnconditionalJump[GOTO] #KA -> #JC
      <- UnconditionalJump[GOTO] #JL -> #JC
      <- UnconditionalJump[GOTO] #KD -> #JC
      <- UnconditionalJump[GOTO] #JG -> #JC
      <- UnconditionalJump[GOTO] #JR -> #JC
      <- UnconditionalJump[GOTO] #JF -> #JC
      <- UnconditionalJump[GOTO] #JY -> #JC
      <- UnconditionalJump[GOTO] #JN -> #JC
      <- UnconditionalJump[GOTO] #JX -> #JC
      <- UnconditionalJump[GOTO] #JH -> #JC
      <- UnconditionalJump[GOTO] #KC -> #JC
      <- UnconditionalJump[GOTO] #JM -> #JC
===#Block CM(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar75 = org.bukkit.Material.IRON_BLOCK;
   2. goto IW
      -> UnconditionalJump[GOTO] #CM -> #IW
      <- DefaultSwitch #BK -> #CM
      <- UnconditionalJump[GOTO] #HQ -> #CM
      <- UnconditionalJump[GOTO] #FL -> #CM
      <- UnconditionalJump[GOTO] #HH -> #CM
      <- UnconditionalJump[GOTO] #GJ -> #CM
      <- UnconditionalJump[GOTO] #HB -> #CM
      <- UnconditionalJump[GOTO] #DS -> #CM
      <- UnconditionalJump[GOTO] #IL -> #CM
      <- UnconditionalJump[GOTO] #CU -> #CM
      <- UnconditionalJump[GOTO] #FR -> #CM
===#Block IW(size=2, flags=0)===
   0. if (gmtogjqvkuzruxxu.iudroizpqzlaayuu.bbubcxujroebameg(lvar105) == 120201758)
      goto IV
   1. throw nullconst;
      -> TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #IW -> #IV
      <- UnconditionalJump[GOTO] #CM -> #IW
===#Block IV(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #IW -> #IV
===#Block IX(size=2, flags=0)===
   0. _consume(catch());
   1. goto CN
      -> UnconditionalJump[GOTO] #IX -> #CN
      <- TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [IW...IV] -> IX ([Ljava/lang/IllegalAccessException;])
===#Block CN(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[1] [org/bukkit/Material]
   1. lvar8 = lvar75;
      -> Immediate #CN -> #CO
      <- UnconditionalJump[GOTO] #EQ -> #CN
      <- UnconditionalJump[GOTO] #IX -> #CN
      <- UnconditionalJump[GOTO] #GV -> #CN
      <- UnconditionalJump[GOTO] #EE -> #CN
      <- UnconditionalJump[GOTO] #DG -> #CN
      <- UnconditionalJump[GOTO] #GS -> #CN
      <- UnconditionalJump[GOTO] #FU -> #CN
      <- UnconditionalJump[GOTO] #CX -> #CN
      <- UnconditionalJump[GOTO] #GP -> #CN
      <- UnconditionalJump[GOTO] #GG -> #CN
      <- Immediate #BJ -> #CN
      <- UnconditionalJump[GOTO] #GA -> #CN
      <- UnconditionalJump[GOTO] #DM -> #CN
      <- UnconditionalJump[GOTO] #EW -> #CN
      <- UnconditionalJump[GOTO] #EK -> #CN
      <- UnconditionalJump[GOTO] #FF -> #CN
      <- UnconditionalJump[GOTO] #HW -> #CN
      <- UnconditionalJump[GOTO] #CR -> #CN
      <- UnconditionalJump[GOTO] #GD -> #CN
      <- UnconditionalJump[GOTO] #DJ -> #CN
      <- UnconditionalJump[GOTO] #EZ -> #CN
      <- UnconditionalJump[GOTO] #DP -> #CN
      <- UnconditionalJump[GOTO] #FC -> #CN
      <- UnconditionalJump[GOTO] #EH -> #CN
      <- UnconditionalJump[GOTO] #JA -> #CN
      <- UnconditionalJump[GOTO] #IR -> #CN
      <- UnconditionalJump[GOTO] #GY -> #CN
      <- UnconditionalJump[GOTO] #HT -> #CN
      <- UnconditionalJump[GOTO] #HK -> #CN
      <- UnconditionalJump[GOTO] #FX -> #CN
===#Block CO(size=4, flags=0)===
   0. lvar16 = new org.bukkit.inventory.ItemStack;
   1. lvar7 = lvar8;
   2. _consume(lvar16.<init>(lvar7));
   3. return lvar16;
      <- Immediate #CN -> #CO
